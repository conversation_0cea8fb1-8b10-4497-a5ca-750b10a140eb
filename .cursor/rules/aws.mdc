---
description:
globs:
alwaysApply: true
---

\*\* AWS Config

- Dev Aws API gateway url is:@https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev
- aws credential is in folder ~/.aws/credentials.
- cognito client id is 2jh76f894g6lv9vrus4qbb9hu7, NOT 31diks051hnbcidq18qigchgj9 which was old one and has been deleted from aws

\*\* Python env

- conda environment ff_env should be used for all python scripts, including pip command
- Pydantic V2 should be used when generate code with Pydantic to avoid any deprecated code

\*\* GIT

- do NOT automatically commit the change
