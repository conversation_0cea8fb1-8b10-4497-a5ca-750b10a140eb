name: Deploy FundFlow Infrastructure

on:
  push:
    branches:
      - main
      - develop
      - "release/**"
    paths:
      - "template.yaml"
      - "parameters/**"
      - "src/**"
      - "layers/**"
      - "scripts/**"
  pull_request:
    branches:
      - main
    paths:
      - "template.yaml"
      - "parameters/**"
      - "src/**"
      - "layers/**"
      - "scripts/**"
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to deploy to"
        required: true
        default: "dev"
        type: choice
        options:
          - dev
          - staging
          - prod
      dry_run:
        description: "Perform dry run (preview changes only)"
        required: false
        default: false
        type: boolean

env:
  AWS_REGION: us-east-1
  SAM_CLI_TELEMETRY: 0

jobs:
  validate:
    name: Validate Template and Configuration
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.determine-env.outputs.environment }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Setup AWS SAM CLI
        uses: aws-actions/setup-sam@v2
        with:
          use-installer: true

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Determine target environment
        id: determine-env
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "environment=prod" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == refs/heads/release/* ]]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
          else
            echo "environment=dev" >> $GITHUB_OUTPUT
          fi

      - name: Make scripts executable
        run: |
          chmod +x scripts/*.sh

      - name: Validate SAM template
        run: |
          sam validate --region ${{ env.AWS_REGION }}

      - name: Build SAM application
        run: |
          sam build --region ${{ env.AWS_REGION }}

      # Add security scanning and enhanced validation
      - name: Install security scanning tools
        run: |
          # Install CloudFormation Linter
          pip install cfn-lint

          # Install cfn-nag (requires Ruby)
          gem install cfn-nag

          # Install Checkov for security scanning
          pip install checkov

      - name: Run CloudFormation linting
        run: |
          echo "Running cfn-lint..."
          cfn-lint template.yaml --ignore-checks W3002

      - name: Run cfn-nag security scanning
        run: |
          echo "Running cfn-nag security scan..."
          cfn_nag_scan --input-path template.yaml --output-format json > cfn-nag-results.json || true

          # Display results
          cat cfn-nag-results.json

          # Check for high/medium severity issues
          violations=$(jq '[.[] | select(.violations != null) | .violations[] | select(.type == "FAIL")] | length' cfn-nag-results.json)
          if [ "$violations" -gt 0 ]; then
            echo "❌ Security violations found: $violations"
            echo "Review the cfn-nag results above."
            # For now, continue with warnings - can be made stricter
          else
            echo "✅ No security violations found"
          fi

      - name: Run Checkov security scanning
        run: |
          echo "Running Checkov security scan..."
          checkov -f template.yaml --framework cloudformation --output cli --quiet || true

          # Run again for JSON output
          checkov -f template.yaml --framework cloudformation --output json > checkov-results.json || true

          # Check for failed checks
          failed_checks=$(jq '.results.failed_checks | length' checkov-results.json 2>/dev/null || echo "0")
          echo "Checkov found $failed_checks failed security checks"

      - name: Validate parameter files
        run: |
          echo "Validating parameter files..."
          for env in dev staging prod; do
            param_file="parameters/${env}.json"
            if [ -f "$param_file" ]; then
              echo "Validating $param_file"
              # Validate JSON syntax
              jq empty "$param_file"
              
              # Validate parameters against template
              sam validate --parameter-overrides file://"$param_file" --region ${{ env.AWS_REGION }}
              echo "✅ $param_file is valid"
            else
              echo "❌ Missing parameter file: $param_file"
              exit 1
            fi
          done

      - name: Run environment validation
        env:
          ENVIRONMENT: ${{ steps.determine-env.outputs.environment }}
        run: |
          # Skip AWS profile checks in CI
          sed -i 's/check_aws_profile/#check_aws_profile/g' scripts/validate-deployment.sh
          ./scripts/validate-deployment.sh -e $ENVIRONMENT

  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: validate
    if: |
      (github.event_name == 'push' && github.ref == 'refs/heads/develop') ||
      (github.event_name == 'pull_request' && github.base_ref == 'main') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'dev')
    environment: development
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Setup AWS SAM CLI
        uses: aws-actions/setup-sam@v2
        with:
          use-installer: true

      - name: Configure AWS credentials for Dev
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Make scripts executable
        run: |
          chmod +x scripts/*.sh

      - name: Deploy to Development
        run: |
          if [[ "${{ github.event.inputs.dry_run }}" == "true" ]]; then
            ./scripts/deploy.sh -e dev -d
          else
            ./scripts/deploy.sh -e dev
          fi

      - name: Post-deployment testing
        run: |
          echo "Running comprehensive post-deployment tests..."
          ./scripts/post-deployment-tests.sh -e dev -r ${{ env.AWS_REGION }}

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: validate
    if: |
      (github.event_name == 'push' && (github.ref == 'refs/heads/develop' || startsWith(github.ref, 'refs/heads/release/'))) ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Setup AWS SAM CLI
        uses: aws-actions/setup-sam@v2
        with:
          use-installer: true

      - name: Configure AWS credentials for Staging
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_STAGING }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_STAGING }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Make scripts executable
        run: |
          chmod +x scripts/*.sh

      - name: Deploy to Staging
        run: |
          if [[ "${{ github.event.inputs.dry_run }}" == "true" ]]; then
            ./scripts/deploy.sh -e staging -d
          else
            ./scripts/deploy.sh -e staging
          fi

      - name: Post-deployment testing
        run: |
          echo "Running comprehensive post-deployment tests..."
          ./scripts/post-deployment-tests.sh -e staging -r ${{ env.AWS_REGION }}

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [validate, deploy-staging]
    if: |
      (github.event_name == 'push' && github.ref == 'refs/heads/main') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'prod')
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Setup AWS SAM CLI
        uses: aws-actions/setup-sam@v2
        with:
          use-installer: true

      - name: Configure AWS credentials for Production
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Make scripts executable
        run: |
          chmod +x scripts/*.sh

      - name: Deploy to Production
        run: |
          if [[ "${{ github.event.inputs.dry_run }}" == "true" ]]; then
            ./scripts/deploy.sh -e prod -d
          else
            ./scripts/deploy.sh -e prod
          fi

            - name: Post-deployment testing
        run: |
          echo "Running comprehensive post-deployment tests..."
          # Wait extra time for production stabilization
          sleep 30
          ./scripts/post-deployment-tests.sh -e prod -r ${{ env.AWS_REGION }} -t 180

      - name: Rollback on validation failure
        if: failure()
        run: |
          echo "⚠️ Post-deployment validation failed. Initiating rollback..."

          STACK_NAME="fundflow-prod"

          # Get the previous successful version
          STACK_EVENTS=$(aws cloudformation describe-stack-events \
            --stack-name "$STACK_NAME" \
            --region ${{ env.AWS_REGION }} \
            --query 'StackEvents[?ResourceStatus==`UPDATE_COMPLETE_CLEANUP_IN_PROGRESS`][0].EventId' \
            --output text)

          if [[ "$STACK_EVENTS" != "None" && -n "$STACK_EVENTS" ]]; then
            echo "Attempting to rollback stack..."
            
            # Cancel current update if in progress
            aws cloudformation cancel-update-stack \
              --stack-name "$STACK_NAME" \
              --region ${{ env.AWS_REGION }} || echo "No update to cancel"
            
            # Wait for cancel to complete
            sleep 30
            
            echo "❌ Deployment validation failed and rollback initiated"
            echo "Please check the AWS CloudFormation console for rollback status"
          else
            echo "❌ Cannot initiate automatic rollback - manual intervention required"
          fi

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [validate, deploy-dev, deploy-staging, deploy-production]
    if: always()

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Comprehensive Deployment Report
        run: |
          echo "🚀 FundFlow Infrastructure Deployment Report"
          echo "=============================================="
          echo ""
          echo "📋 Workflow Information:"
          echo "  • Trigger: ${{ github.event_name }}"
          echo "  • Branch: ${{ github.ref_name }}"
          echo "  • Commit: ${{ github.sha }}"
          echo "  • Actor: ${{ github.actor }}"
          echo "  • Run ID: ${{ github.run_id }}"
          echo ""

          # Validation Results
          echo "🔍 Validation Phase:"
          VALIDATION_STATUS="${{ needs.validate.result || 'skipped' }}"
          case $VALIDATION_STATUS in
            "success")
              echo "  ✅ Template validation: PASSED"
              echo "  ✅ Security scanning: COMPLETED"
              echo "  ✅ Parameter validation: PASSED"
              ;;
            "failure")
              echo "  ❌ Validation: FAILED"
              echo "  ⚠️  Deployment blocked due to validation errors"
              ;;
            *)
              echo "  ⚠️  Validation: $VALIDATION_STATUS"
              ;;
          esac
          echo ""

          # Environment Deployment Results
          echo "🌍 Environment Deployments:"

          # Development
          DEV_STATUS="${{ needs.deploy-dev.result || 'skipped' }}"
          case $DEV_STATUS in
            "success") echo "  ✅ Development: DEPLOYED" ;;
            "failure") echo "  ❌ Development: FAILED" ;;
            "skipped") echo "  ⏭️  Development: SKIPPED" ;;
            *) echo "  ⚠️  Development: $DEV_STATUS" ;;
          esac

          # Staging
          STAGING_STATUS="${{ needs.deploy-staging.result || 'skipped' }}"
          case $STAGING_STATUS in
            "success") echo "  ✅ Staging: DEPLOYED" ;;
            "failure") echo "  ❌ Staging: FAILED" ;;
            "skipped") echo "  ⏭️  Staging: SKIPPED" ;;
            *) echo "  ⚠️  Staging: $STAGING_STATUS" ;;
          esac

          # Production
          PROD_STATUS="${{ needs.deploy-production.result || 'skipped' }}"
          case $PROD_STATUS in
            "success") echo "  ✅ Production: DEPLOYED" ;;
            "failure") echo "  ❌ Production: FAILED (Rollback may have been initiated)" ;;
            "skipped") echo "  ⏭️  Production: SKIPPED" ;;
            *) echo "  ⚠️  Production: $PROD_STATUS" ;;
          esac
          echo ""

          # Overall Status
          echo "📊 Overall Deployment Status:"
          if [[ "$DEV_STATUS" == "success" || "$STAGING_STATUS" == "success" || "$PROD_STATUS" == "success" ]]; then
            if [[ "$PROD_STATUS" == "success" ]]; then
              echo "  🎉 SUCCESS: Production deployment completed successfully"
            elif [[ "$STAGING_STATUS" == "success" ]]; then
              echo "  ✅ SUCCESS: Staging deployment completed successfully"
            else
              echo "  ✅ SUCCESS: Development deployment completed successfully"
            fi
          else
            echo "  ❌ FAILURE: No environments were successfully deployed"
          fi
          echo ""

          # Next Steps
          echo "📝 Next Steps:"
          if [[ "$VALIDATION_STATUS" == "failure" ]]; then
            echo "  • Review validation errors in the workflow logs"
            echo "  • Fix security scanning issues if present"
            echo "  • Correct template or parameter validation errors"
          elif [[ "$PROD_STATUS" == "failure" ]]; then
            echo "  • Check production deployment logs for errors"
            echo "  • Verify CloudFormation console for rollback status"
            echo "  • Review post-deployment validation failures"
          elif [[ "$PROD_STATUS" == "success" ]]; then
            echo "  • Monitor application health and metrics"
            echo "  • Verify all API endpoints are functioning"
            echo "  • Check CloudWatch logs for any issues"
          else
            echo "  • Review workflow logs for any warnings"
            echo "  • Consider promoting successful deployments to next environment"
          fi
          echo ""

          # Quick Access Links
          echo "🔗 Quick Access:"
          echo "  • Workflow Run: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          echo "  • CloudFormation Console: https://console.aws.amazon.com/cloudformation/"
          echo "  • API Gateway Console: https://console.aws.amazon.com/apigateway/"
          echo "  • DynamoDB Console: https://console.aws.amazon.com/dynamodb/"
          echo ""

          # Deployment Time
          echo "⏰ Deployment Time: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"

      - name: Post to Slack/Teams (if configured)
        if: always()
        run: |
          # This is a placeholder for webhook integration
          # Replace with actual Slack/Teams webhook URL if needed
          echo "📱 Notification: Deployment report generated"
          echo "Configure SLACK_WEBHOOK_URL or TEAMS_WEBHOOK_URL secrets to enable notifications"

          # Example Slack notification (uncomment and configure webhook)
          # if [[ -n "${{ secrets.SLACK_WEBHOOK_URL }}" ]]; then
          #   curl -X POST -H 'Content-type: application/json' \
          #     --data '{"text":"FundFlow deployment completed: ${{ github.ref_name }}"}' \
          #     ${{ secrets.SLACK_WEBHOOK_URL }}
          # fi
