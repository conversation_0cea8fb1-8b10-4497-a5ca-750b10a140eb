# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
layers/

# Dependency directories
node_modules/

# Environment variables
.env
.env.*
!.env.example

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

# Task files
tasks.json
tasks/

# AWS SAM
.aws-sam/
samconfig.toml.bak

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# AWS credentials (should never be committed)
.aws/
credentials
config
tests/integration/.env_test

# Sensitive configuration files
.taskmaster/config.json
.taskmaster/.taskmaster/config.json
.cursor/mcp.json
.claude/settings.local.json

# API keys and secrets
**/api_keys.json
**/secrets.json
**/*secret*
**/*credential*
**/*password*

# SSL certificates and keys
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx
*.jks

# AWS SAM build artifacts (may contain sensitive data)
.aws-sam/

#samples
sample/