please generate a detail PRD for the follow app based on the template example_prd.txt

A fund information management web app that allows users (fund managers, asset managers) to input, update, generate analysis reports for clients.
the front end should be devloped using next.JS, include the following screens
- log in screen
- fund search list, with dynamic filters on multiple attributes 
- fund details screen, with detail information of the fund as well as analysis, charts etc
- fund edit screen which allow users to update the fund information
- user admin page

the backend is built using python. and depployed to AWS. service used include lambda functions, api gateway, SAM, dynamodb, S3. they should be updated using CLI tool as much as possible

 