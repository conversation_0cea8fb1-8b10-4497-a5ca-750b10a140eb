# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Backend Development

**AWS SAM Deployment:**

```bash
# Build and deploy to dev environment
souce .env && sam build && sam deploy --config-env dev

# Local API testing
sam local start-api --parameter-overrides "Environment=dev"

# Invoke specific Lambda function
sam local invoke FundsAPIFunction --event tests/events/fund-event.json
```

**Python Development:**

```bash
# Python version 3.13

# IMPORTANT: Conda Environment Activation
# The ff_env conda environment contains Python 3.13 and is required for all Python operations
# Standard conda activate may fail due to shell initialization issues

# Method 1: Direct PATH export (RECOMMENDED - always works)
export PATH="/Users/<USER>/miniconda3/envs/ff_env/bin:$PATH"

# Method 2: If conda is properly initialized
conda activate ff_env

# Method 3: Initialize conda first, then activate
/opt/anaconda3/bin/conda init bash
# Restart shell or source ~/.bashrc, then:
# conda activate ff_env

# Verify correct Python version (should show 3.13.x)
python --version

# Install dependencies
pip install -r requirements.txt
pip install -r src/requirements.txt

# Run validation tests
python validate_fund_response.py

# Run comprehensive API tests
python tests/integration/comprehensive_validation.py

# Test specific endpoints
python tests/integration/test_funds_direct.py
```

### Frontend Development

**Next.js Commands:**

```bash
cd frontend

# Development server with SSL
npm run dev

# Type checking
npm run type-check

# Linting and formatting
npm run lint:fix
npm run format

# Build production
npm run build
```

**Mock Development Mode:**

```bash
# Toggle mock mode for local development
npm run toggle-mock

# Test mock authentication
npm run test:mock-login
```

## Architecture Overview

### Backend Architecture (AWS Serverless)

**Core Stack:**

- **Runtime**: Python 3.13 with AWS Lambda Powertools, it is available in local conda env named ff_env, do NOT use any other version
- **Framework**: AWS SAM (Serverless Application Model)
- **Database**: DynamoDB with single-table design patterns
- **Authentication**: AWS Cognito with API Gateway JWT validation
- **AI Integration**: OpenRouter API for PDF fund extraction

**Lambda Functions Structure:**

- `FundsAPIFunction`: CRUD operations for fund management
- `FundExtractorFunction`: AI-powered PDF fund data extraction (1024MB memory, 5min timeout)
- `AuthAPIFunction`: Authentication and session management
- `UsersAPIFunction`, `PortfoliosAPIFunction`, `ReportsAPIFunction`: Domain-specific APIs
- `JobsFunction`: Async PDF processing job management

**Data Models (src/shared/models/):**

- `Fund`: Complex financial instrument model with performance metrics, holdings, market data
- `MarketData`: Comprehensive market data with price, valuation, risk analytics
- `User`, `Portfolio`, `Report`: Supporting domain models
- All models use Pydantic v2 with strict validation and DynamoDB serialization

**DynamoDB Tables:**

- `fundflow-{env}-funds`: Primary fund data with GSIs on fund_type and status
- `fundflow-{env}-pdf-jobs`: PDF processing job tracking with TTL
- `fundflow-{env}-users`, `portfolios`, `reports`: Supporting tables

### Frontend Architecture (Next.js 15 App Router)

**Core Stack:**

- **Framework**: Next.js 15.3.3 with App Router and TypeScript 5
- **Styling**: Tailwind CSS v4 with custom components
- **State Management**: Redux Toolkit with typed hooks
- **Authentication**: NextAuth.js with Cognito integration
- **Internationalization**: next-intl for multi-language support

**Key Architectural Patterns:**

- **Page Structure**: App Router with feature-based organization (`funds/`, `portfolios/`, `reports/`)
- **Component Architecture**: Compound components (Card with Header/Content/Footer), typed UI library
- **API Integration**: Proxy routes (`api/proxy/funds/`) for backend communication with CORS handling
- **Mock Development**: Complete mock authentication and data layer for local development

**State Management:**

- Redux store with feature slices (`fundsSlice`, `portfoliosSlice`, `themeSlice`)
- Typed hooks (`useAppDispatch`, `useAppSelector`) for type safety
- Theme provider with dark/light mode support

### Authentication Flow

**Production Authentication:**

- Cognito User Pool with public client configuration (no client secret)
- API Gateway with JWT authorizer using standard scopes (`aws.cognito.signin.user.admin`)
- Frontend uses Authorization Code flow with PKCE
- Session management with automatic token refresh

**Development Authentication:**

- Mock login mode bypasses Cognito for local development
- Environment variables control mock vs real authentication
- Complete API mocking available for offline development

### PDF Extraction Feature

**AI-Powered Fund Data Extraction:**

- Uses OpenRouter API with vision models for PDF analysis
- Async job processing with DynamoDB job tracking
- Supports complex fund documents with structured data extraction
- Job status tracking and error handling for large PDF processing

### Testing Strategy

**Backend Testing:**

- Integration tests in `tests/integration/` for API validation
- Mocked AWS services using moto library
- Direct DynamoDB testing with real AWS resources in dev environment
- Fund validation with comprehensive test data

**Frontend Testing:**

- Component tests using React Testing Library patterns
- Mock API integration for development and testing
- End-to-end testing capability with mock authentication

## Environment Configuration

**Development Environment (ap-northeast-1):**

- Stack name: `fundflow-dev`
- API Gateway: `https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev`
- Cognito User Pool: `ap-northeast-1_H2kKHGUAT`
- DynamoDB tables with PAY_PER_REQUEST billing

**Environment Variables:**

- Backend: Managed through SAM parameters and `samconfig.toml`
- Frontend: `.env.local` with proxy configuration and feature flags
- OpenRouter API key required for PDF extraction feature

## Development Workflow

**Backend Changes:**

1. **Activate conda environment first**: `export PATH="/Users/<USER>/miniconda3/envs/ff_env/bin:$PATH"`
2. Modify Python code in `src/` directory
3. Update data models in `src/shared/models/` if needed
4. Run local validation: `python validate_fund_response.py`
5. Deploy: `sam build && sam deploy --config-env dev`
6. Test with integration scripts

**Frontend Changes:**

1. Modify React components in `frontend/src/`
2. Run type checking: `npm run type-check`
3. Test locally: `npm run dev`
4. Use mock mode for rapid development without AWS dependencies

**Full-Stack Testing:**

1. Enable production mode: Set `NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=false`
2. Run comprehensive validation: `python tests/integration/comprehensive_validation.py`
3. Test frontend integration with real APIs

## Key Considerations

**Data Handling:**

- All financial data uses Python `Decimal` type for precision
- DynamoDB items require Decimal-to-string serialization
- Frontend handles currency formatting and number precision
- Comprehensive data validation at model level with Pydantic

**Performance Optimizations:**

- Market data and relationships are loaded separately to avoid large payloads
- DynamoDB GSIs for efficient querying by fund_type and status
- CloudFront distribution for static assets
- API Gateway caching available for production

**Security:**

- JWT validation at API Gateway level
- CORS properly configured for cross-origin requests
- Sensitive data excluded from logs and responses
- Environment-specific configurations prevent credential leakage

**Folders:**

- Any .md file created, put it in docs folder
- Any test file created, put it in tests or tests/integration folder
