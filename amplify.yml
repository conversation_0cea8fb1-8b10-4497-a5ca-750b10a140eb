version: 1
applications:
  - appRoot: frontend
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci --cache .npm --prefer-offline
        build:
          commands:
            - echo "Building with environment variables:"
            - echo "NEXTAUTH_URL=$NEXTAUTH_URL"
            - echo "COGNITO_CLIENT_ID=$COGNITO_CLIENT_ID"
            - npm run build
      artifacts:
        baseDirectory: .next
        files:
          - '**/*'
      cache:
        paths:
          - .next/cache/**/*
          - .npm/**/*
    backend:
      phases:
        build:
          commands:
            - echo "No backend build required - using existing AWS infrastructure"