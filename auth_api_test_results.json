{"summary": {"total_tests": 7, "passed": 3, "failed": 4, "success_rate": "42.9%", "test_timestamp": "2025-07-06 07:47:58"}, "test_results": [{"test_name": "Auth Health Check", "success": false, "message": "Unexpected status code: 403", "timestamp": "2025-07-06 07:47:56", "response_data": {"status_code": 403, "response": "{\"message\":\"Missing Authentication Token\"}"}}, {"test_name": "<PERSON><PERSON>", "success": true, "message": "Cognito authentication successful using ADMIN_USER_PASSWORD_AUTH", "timestamp": "2025-07-06 07:47:57", "response_data": {"auth_flow": "ADMIN_USER_PASSWORD_AUTH", "has_access_token": true, "token_length": 1086}}, {"test_name": "Auth User Profile", "success": false, "message": "Profile request failed with status: 403", "timestamp": "2025-07-06 07:47:57", "response_data": {"status_code": 403, "response": "{\"message\":\"Invalid key=value pair (missing equal-sign) in Authorization header (hashed with SHA-256 and encoded with Base64): '72+RHh8VQR2XklIJn+yosJVPN2b40wyer+oN/UYvopM='.\"}"}}, {"test_name": "Auth Token Validation", "success": false, "message": "Token validation failed with status: 403", "timestamp": "2025-07-06 07:47:58", "response_data": {"status_code": 403, "response": "{\"message\":\"Invalid key=value pair (missing equal-sign) in Authorization header (hashed with SHA-256 and encoded with Base64): '72+RHh8VQR2XklIJn+yosJVPN2b40wyer+oN/UYvopM='.\"}"}}, {"test_name": "<PERSON><PERSON>", "success": false, "message": "Logout failed with status: 403", "timestamp": "2025-07-06 07:47:58", "response_data": {"status_code": 403, "response": "{\"message\":\"Invalid key=value pair (missing equal-sign) in Authorization header (hashed with SHA-256 and encoded with Base64): '72+RHh8VQR2XklIJn+yosJVPN2b40wyer+oN/UYvopM='.\"}"}}, {"test_name": "Auth Invalid Credentials", "success": true, "message": "Correctly rejected invalid credentials with status: 403", "timestamp": "2025-07-06 07:47:58", "response_data": {"status_code": 403}}, {"test_name": "Auth Unauthorized Access", "success": true, "message": "Correctly rejected unauthorized access with status: 403", "timestamp": "2025-07-06 07:47:58", "response_data": {"status_code": 403}}]}