#!/usr/bin/env python
"""Debug the datetime serialization issue."""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone
from decimal import Decimal
from src.shared.models.fund import Fund, FundType, FundStatus, Currency, FundDynamoDBItem, FundUpdate

# Simulate what happens in handle_update_fund

# 1. Create existing fund (simulating what comes from DB)
existing_fund = Fund(
    fund_id="test-123",
    name="Original Fund Name",
    fund_type=FundType.EQUITY,
    status=FundStatus.ACTIVE,
    currency=Currency.USD,
    created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
    updated_at=datetime(2024, 6, 1, tzinfo=timezone.utc)
)

print("1. Existing fund:")
print(f"   updated_at: {existing_fund.updated_at}")
print(f"   Type: {type(existing_fund.updated_at)}")

# 2. Create update request
update_request = FundUpdate(
    name="Updated Fund Name",
    nav=Decimal("123.45")
)

# 3. Apply updates (like in the handler)
update_data = update_request.dict(exclude_unset=True)
if update_data:
    for field, value in update_data.items():
        if hasattr(existing_fund, field):
            setattr(existing_fund, field, value)

print("\n2. After applying updates:")
print(f"   name: {existing_fund.name}")
print(f"   nav: {existing_fund.nav}")

# 4. Set updated timestamp (THIS IS WHERE THE ISSUE MIGHT BE)
existing_fund.updated_at = datetime.now(timezone.utc)

print("\n3. After setting updated_at:")
print(f"   updated_at: {existing_fund.updated_at}")
print(f"   Type: {type(existing_fund.updated_at)}")

# 5. Test serialization
print("\n4. Testing serialization:")
try:
    serialized = FundDynamoDBItem.to_dynamodb_item(existing_fund)
    print(f"   Serialized updated_at: {serialized.get('updated_at')}")
    print(f"   Type: {type(serialized.get('updated_at'))}")
    
    # Check if any nested objects might have datetime
    for key, value in serialized.items():
        if isinstance(value, datetime):
            print(f"   WARNING: Found datetime in {key}: {value}")
        elif isinstance(value, dict):
            for k, v in value.items():
                if isinstance(v, datetime):
                    print(f"   WARNING: Found datetime in {key}.{k}: {v}")
                    
except Exception as e:
    print(f"   Error during serialization: {e}")
    import traceback
    traceback.print_exc()

# 6. Test what dict() returns
print("\n5. Testing .dict() method:")
try:
    dict_result = existing_fund.dict()
    print(f"   dict() updated_at: {dict_result.get('updated_at')}")
    print(f"   Type: {type(dict_result.get('updated_at'))}")
except Exception as e:
    print(f"   Error: {e}")

# 7. Test model_dump
print("\n6. Testing .model_dump() method:")
try:
    dump_result = existing_fund.model_dump()
    print(f"   model_dump() updated_at: {dump_result.get('updated_at')}")
    print(f"   Type: {type(dump_result.get('updated_at'))}")
except Exception as e:
    print(f"   Error: {e}")