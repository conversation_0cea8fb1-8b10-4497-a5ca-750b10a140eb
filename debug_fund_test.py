#!/usr/bin/env python3
"""
Debug test to identify which field is causing the 500 error.
"""

import json
import boto3
import requests
import time
from botocore.exceptions import ClientError

# API Gateway configuration
api_base_url = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
api_headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
}

# Cognito configuration
cognito_client_id = "2jh76f894g6lv9vrus4qbb9hu7"
cognito_client = boto3.client("cognito-idp", region_name="ap-northeast-1")

def authenticate():
    """Authenticate with Cognito to get a valid JWT token"""
    print("🔐 Authenticating with AWS Cognito...")
    
    test_username = "<EMAIL>"
    test_password = "TestPassword123!"
    user_pool_id = "ap-northeast-1_H2kKHGUAT"
    
    try:
        # First, try to create the test user if it doesn't exist
        try:
            print("🔧 Creating test user if it doesn't exist...")
            cognito_client.admin_create_user(
                UserPoolId=user_pool_id,
                Username=test_username,
                TemporaryPassword=test_password,
                MessageAction="SUPPRESS",  # Don't send welcome email
                UserAttributes=[
                    {"Name": "email", "Value": test_username},
                    {"Name": "email_verified", "Value": "true"},
                ],
            )
            print("✅ Test user created successfully")

            # Set permanent password
            cognito_client.admin_set_user_password(
                UserPoolId=user_pool_id,
                Username=test_username,
                Password=test_password,
                Permanent=True,
            )
            print("✅ Test user password set")

        except ClientError as create_error:
            error_code = create_error.response.get("Error", {}).get("Code", "")
            if error_code == "UsernameExistsException":
                print("ℹ️  Test user already exists, proceeding with authentication")
            else:
                print(f"⚠️  Error creating test user: {create_error}")
                print("   Proceeding with authentication attempt...")
        
        # Try to authenticate
        print("🔑 Authenticating test user...")
        response = cognito_client.admin_initiate_auth(
            UserPoolId=user_pool_id,
            ClientId=cognito_client_id,
            AuthFlow="ADMIN_NO_SRP_AUTH",
            AuthParameters={"USERNAME": test_username, "PASSWORD": test_password},
        )
        
        if "AuthenticationResult" in response:
            access_token = response["AuthenticationResult"].get("AccessToken")
            if access_token:
                api_headers["Authorization"] = f"Bearer {access_token}"
                print("✅ Successfully authenticated")
                return True
                
    except ClientError as auth_error:
        print(f"❌ Authentication error: {auth_error}")
        return False
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False

def test_fund_creation_incremental():
    """Test fund creation with incremental field addition"""
    
    # Base fund data that works
    base_data = {
        "fund_id": f"DEBUG-{int(time.time())}",
        "name": "Debug Test Fund",
        "fund_type": "equity"
    }
    
    # Additional fields to test incrementally
    additional_fields = [
        {"description": "Sample fund created for testing"},
        {"nav": 100.0},
        {"minimum_investment": 1000.0},
        {"expense_ratio": 0.75},
        {"total_assets": 50000000.0},
        {"fund_manager": "Test Fund Manager"},
        {"inception_date": "2020-01-01"},
        {"risk_level": "moderate"},
        {"status": "active"},
        {"symbol": "TF123"},
        {"category": "Equity"},
        {"sub_category": "Large Cap"},
        {"previous_nav": 99.50},
        {"aum": 50000000.0},
        {"rating": 4.2},
        {"fund_manager_photo": "https://example.com/photo.jpg"},
        {"fund_manager_introduction": "Experienced fund manager"},
    ]
    
    current_data = base_data.copy()
    
    for i, additional_field in enumerate(additional_fields):
        current_data.update(additional_field)
        
        # Update fund_id to make it unique
        current_data["fund_id"] = f"DEBUG-{int(time.time())}-{i}"
        
        print(f"\n🧪 Test {i+1}: Adding {list(additional_field.keys())[0]}")
        print(f"📦 Current data: {json.dumps(current_data, indent=2)}")
        
        try:
            response = requests.post(
                f"{api_base_url}/funds",
                headers=api_headers,
                json=current_data,
                timeout=30,
            )
            
            print(f"📊 Response status: {response.status_code}")
            if response.status_code in [200, 201]:
                print(f"✅ Success with field: {list(additional_field.keys())[0]}")
            else:
                print(f"❌ Failed with field: {list(additional_field.keys())[0]}")
                print(f"📄 Response: {response.text}")
                break
                
        except Exception as e:
            print(f"❌ Error with field {list(additional_field.keys())[0]}: {e}")
            break

def main():
    """Main function"""
    print("🚀 Starting debug fund creation test...")
    
    # Authenticate
    if not authenticate():
        print("❌ Authentication failed, stopping tests")
        return
    
    # Test incremental fund creation
    test_fund_creation_incremental()

if __name__ == "__main__":
    main()