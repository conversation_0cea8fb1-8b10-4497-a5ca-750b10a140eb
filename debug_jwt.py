#!/usr/bin/env python3
"""
JWT Token Decoder for debugging authentication issues
"""

import jwt
import json
import sys
from datetime import datetime

def decode_jwt_token(token):
    """Decode JWT token without verification to inspect contents"""
    try:
        # Decode without verification to see contents
        payload = jwt.decode(token, options={"verify_signature": False})
        header = jwt.get_unverified_header(token)
        
        print("🔍 JWT Token Analysis")
        print("=" * 50)
        
        print("\n📋 Header:")
        print(json.dumps(header, indent=2))
        
        print("\n📋 Payload:")
        print(json.dumps(payload, indent=2, default=str))
        
        print("\n🔑 Key Information:")
        print(f"   Token Type: {payload.get('token_use', 'N/A')}")
        print(f"   Client ID: {payload.get('client_id', 'N/A')}")
        print(f"   Audience: {payload.get('aud', 'N/A')}")
        print(f"   Subject: {payload.get('sub', 'N/A')}")
        print(f"   Issuer: {payload.get('iss', 'N/A')}")
        print(f"   Scope: {payload.get('scope', 'N/A')}")
        
        # Check expiration
        if 'exp' in payload:
            exp_time = datetime.fromtimestamp(payload['exp'])
            now = datetime.now()
            is_expired = now > exp_time
            print(f"   Expires: {exp_time}")
            print(f"   Is Expired: {is_expired}")
            if not is_expired:
                time_left = exp_time - now
                print(f"   Time Left: {time_left}")
        
        return payload
        
    except Exception as e:
        print(f"❌ Error decoding token: {e}")
        return None

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python debug_jwt.py <jwt_token>")
        print("Example: python debug_jwt.py eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...")
        sys.exit(1)
    
    token = sys.argv[1]
    decode_jwt_token(token)
