#!/bin/bash

# FundFlow SAM Deployment Script for Dev Environment
# This script handles the complete deployment process for the FundFlow application

set -e  # Exit on any error

echo "🚀 Starting FundFlow deployment to dev environment..."

# Check if SAM CLI is available
if ! command -v sam &> /dev/null; then
    echo "❌ SAM CLI is not installed or not in PATH"
    echo "Please install SAM CLI first: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html"
    exit 1
fi

# Check if AWS CLI is available
if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI is not installed or not in PATH"
    echo "Please install AWS CLI first: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
    exit 1
fi

# Verify AWS profile exists
echo "🔍 Checking AWS profile configuration..."
if ! aws configure list --profile fundflow-dev &> /dev/null; then
    echo "❌ AWS profile 'fundflow-dev' not found"
    echo "Please configure your AWS profile first:"
    echo "aws configure --profile fundflow-dev"
    exit 1
fi

echo "✅ AWS profile 'fundflow-dev' found"

# Clean previous build artifacts
echo "🧹 Cleaning previous build artifacts..."
rm -rf .aws-sam/

# Build the application
echo "🔨 Building SAM application..."
if ! sam build --parallel; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build completed successfully"

# Validate the template
echo "🔍 Validating SAM template..."
if ! sam validate --profile fundflow-dev; then
    echo "❌ Template validation failed"
    exit 1
fi

echo "✅ Template validation passed"

# Deploy the application
echo "🚀 Deploying to AWS..."
echo "Using configuration from samconfig.toml [dev] environment"

if ! sam deploy --config-env dev; then
    echo "❌ Deployment failed"
    echo ""
    echo "Common solutions:"
    echo "1. Check if the S3 bucket region matches your AWS profile region"
    echo "2. Ensure you have proper IAM permissions"
    echo "3. Try running: sam deploy --guided --profile fundflow-dev"
    exit 1
fi

echo "✅ Deployment completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Check the CloudFormation stack in AWS Console"
echo "2. Test the API endpoints"
echo "3. Review the outputs for important URLs and resource names"

# Display stack outputs
echo "📊 Stack outputs:"
aws cloudformation describe-stacks \
    --stack-name fundflow-dev \
    --profile fundflow-dev \
    --query 'Stacks[0].Outputs[*].[OutputKey,OutputValue]' \
    --output table

echo "🎉 FundFlow deployment completed!" 

#sam validate --profile fundflow-dev
#sam deploy --config-env dev
