# Cognito and API Gateway Setup Guide

## Overview

This document provides a comprehensive guide for setting up AWS Cognito and API Gateway authentication for the FundFlow application. This guide is based on extensive troubleshooting and the current working configuration.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Current Working Configuration](#current-working-configuration)
3. [Cognito Setup Requirements](#cognito-setup-requirements)
4. [API Gateway Configuration](#api-gateway-configuration)
5. [Lambda Function Configuration](#lambda-function-configuration)
6. [Authentication Flow](#authentication-flow)
7. [Troubleshooting Guide](#troubleshooting-guide)
8. [Common Issues and Solutions](#common-issues-and-solutions)

## Architecture Overview

The FundFlow application uses the following authentication architecture:

```
Frontend/Client → Cognito Authentication → API Gateway (Cognito Authorizer) → Lambda Functions
```

**Key Components:**

- **AWS Cognito User Pool**: Manages user authentication and token generation
- **Cognito User Pool Client**: Configured for the application's authentication flows
- **API Gateway**: Acts as the entry point with Cognito authorization
- **Lambda Functions**: Backend services that process authenticated requests

## Current Working Configuration

### Environment Details

- **Region**: `ap-northeast-1`
- **User Pool ID**: `ap-northeast-1_H2kKHGUAT`
- **Client ID**: `2jh76f894g6lv9vrus4qbb9hu7`
- **API Gateway URL**: `https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev`

### Cognito Client Configuration

```json
{
  "ClientType": "Public",
  "ClientName": "fundflow-dev-client",
  "HasSecret": false,
  "SupportedAuthFlows": [
    "ALLOW_ADMIN_USER_PASSWORD_AUTH",
    "ALLOW_REFRESH_TOKEN_AUTH",
    "ALLOW_USER_AUTH",
    "ALLOW_USER_PASSWORD_AUTH",
    "ALLOW_USER_SRP_AUTH"
  ],
  "OAuthFlows": ["code"],
  "OAuthScopes": [
    "aws.cognito.signin.user.admin",
    "email",
    "fundflow-api/admin:all",
    "fundflow-api/funds:read",
    "fundflow-api/funds:write",
    "fundflow-api/reports:read",
    "fundflow-api/reports:write",
    "fundflow-api/users:read",
    "fundflow-api/users:write",
    "openid",
    "profile"
  ]
}
```

### API Gateway Authorizer Configuration

```yaml
Auth:
  DefaultAuthorizer: CognitoAuthorizer
  Authorizers:
    CognitoAuthorizer:
      UserPoolArn: !Sub "arn:aws:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/${ExistingUserPoolId}"
      AuthorizationScopes:
        # Standard Cognito User Scopes (for public clients)
        - "aws.cognito.signin.user.admin"
        - "email"
        - "openid"
        - "profile"
```

## Cognito Setup Requirements

### 1. User Pool Configuration

**Required Settings:**

- **Pool Name**: `fundflow-user-pool` (or similar)
- **Region**: Must match your API Gateway region
- **Attribute Requirements**: Email, username
- **Password Policy**: Configure as needed for security
- **MFA**: Optional but recommended for production

### 2. User Pool Client Configuration

**Critical Settings:**

```
Client Type: Public (no client secret)
Auth Flows Enabled:
  ✅ ALLOW_USER_PASSWORD_AUTH
  ✅ ALLOW_ADMIN_USER_PASSWORD_AUTH
  ✅ ALLOW_REFRESH_TOKEN_AUTH
  ✅ ALLOW_USER_SRP_AUTH
  ✅ ALLOW_USER_AUTH

OAuth Settings:
  ✅ Authorization code grant
  ✅ Allow implicit grant (optional)

OAuth Scopes:
  ✅ email
  ✅ openid
  ✅ profile
  ✅ aws.cognito.signin.user.admin

Callback URLs: Configure for your frontend
Logout URLs: Configure for your frontend
```

### 3. Resource Server (Optional but Recommended)

If you want to use custom scopes, set up a resource server:

```
Resource Server Identifier: fundflow-api
Name: FundFlow API
Custom Scopes:
  - admin:all (Full administrative access)
  - funds:read (Read fund data)
  - funds:write (Create/update fund data)
  - users:read (Read user data)
  - users:write (Create/update user data)
  - reports:read (Read reports)
  - reports:write (Generate reports)
```

**Important**: If using resource server scopes, ensure your client is configured to request these scopes AND your API Gateway authorizer includes them.

## API Gateway Configuration

### 1. Cognito Authorizer Setup

**Working Configuration (SAM Template):**

```yaml
ApiGateway:
  Type: AWS::Serverless::Api
  Properties:
    Auth:
      DefaultAuthorizer: CognitoAuthorizer
      Authorizers:
        CognitoAuthorizer:
          UserPoolArn: !Sub "arn:aws:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/${UserPoolId}"
          AuthorizationScopes:
            - "aws.cognito.signin.user.admin"
            - "email"
            - "openid"
            - "profile"
```

### 2. API Routes Configuration

Each API route should include the authorizer:

```yaml
Events:
  ListFunds:
    Type: Api
    Properties:
      RestApiId: !Ref ApiGateway
      Path: /funds
      Method: get
      Auth:
        Authorizer: CognitoAuthorizer
```

### 3. CORS Configuration

Ensure CORS is properly configured for frontend integration:

```yaml
Cors:
  AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
  AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
  AllowOrigin: "'*'" # Restrict this in production
  MaxAge: "'600'"
```

## Lambda Function Configuration

### Environment Variables

Each Lambda function needs these environment variables:

```yaml
Environment:
  Variables:
    # Cognito Configuration
    COGNITO_USER_POOL_ID: !Ref UserPoolId
    COGNITO_APP_CLIENT_ID: !Ref UserPoolClientId

    # DynamoDB Tables
    FUND_TABLE: !Ref FundTable
    USER_TABLE: !Ref UserTable
    REPORT_TABLE: !Ref ReportTable

    # Application Settings
    ENVIRONMENT: !Ref Environment
    LOG_LEVEL: !Ref LogLevel

    # Powertools Configuration
    POWERTOOLS_SERVICE_NAME: fundflow
    POWERTOOLS_METRICS_NAMESPACE: FundFlow
```

### IAM Permissions

Lambda functions need these permissions:

```yaml
Policies:
  - DynamoDBCrudPolicy:
      TableName: !Ref FundTable
  - DynamoDBReadPolicy:
      TableName: !Ref UserTable
  # Add other necessary policies
```

## Authentication Flow

### 1. Frontend Authentication

```javascript
// Example frontend authentication
const authResult = await Auth.signIn(username, password);
const accessToken = authResult.getAccessToken().getJwtToken();
```

### 2. API Request

```javascript
// Include token in API requests
const response = await fetch("/api/funds", {
  headers: {
    Authorization: `Bearer ${accessToken}`,
    "Content-Type": "application/json",
  },
});
```

### 3. Backend Token Validation

```python
# Lambda function receives validated user context
def handler(event, context):
    # Token is validated by API Gateway
    # User info available in event['requestContext']['authorizer']
    user_context = create_user_context(
        event.get('requestContext', {}).get('authorizer', {})
    )
```

## Troubleshooting Guide

### Common Authentication Issues

#### 1. 401 Unauthorized Errors

**Symptoms**: API Gateway returns 401 before reaching Lambda
**Causes**:

- Token scope mismatch between client and API Gateway
- Expired or invalid tokens
- Incorrect authorizer configuration

**Solutions**:

```bash
# Check client scopes
aws cognito-idp describe-user-pool-client \
  --user-pool-id ap-northeast-1_H2kKHGUAT \
  --client-id 2jh76f894g6lv9vrus4qbb9hu7

# Verify API Gateway authorizer configuration
# Ensure scopes match between client and authorizer
```

#### 2. Token Scope Mismatch

**Problem**: API Gateway expects resource server scopes but client only has standard scopes
**Solution**: Choose one approach:

**Option A**: Use Standard Scopes (Recommended for public clients)

```yaml
AuthorizationScopes:
  - "aws.cognito.signin.user.admin"
  - "email"
  - "openid"
  - "profile"
```

**Option B**: Use Resource Server Scopes (Requires confidential client)

```yaml
AuthorizationScopes:
  - "fundflow-api/admin:all"
  - "fundflow-api/funds:read"
  - "fundflow-api/funds:write"
```

#### 3. Client Configuration Issues

**Public vs Confidential Clients**:

- **Public clients**: No client secret, use standard scopes
- **Confidential clients**: Have client secret, can use resource server scopes

### Debugging Steps

1. **Check Cognito Configuration**:

```bash
aws cognito-idp describe-user-pool-client \
  --user-pool-id YOUR_POOL_ID \
  --client-id YOUR_CLIENT_ID
```

2. **Test Authentication**:

```python
# Use the test script
python tests/integration/test_aws_api_client.py
```

3. **Check Lambda Logs**:

```bash
aws logs filter-log-events \
  --log-group-name "/aws/lambda/your-function-name" \
  --start-time $(date -d '1 hour ago' +%s)
```

4. **Validate Token**:

```bash
# Decode JWT token (replace with actual token)
echo "YOUR_JWT_TOKEN" | cut -d. -f2 | base64 -d | jq
```

## Common Issues and Solutions

### Issue 1: Scope Mismatch

**Error**: `401 Unauthorized` at API Gateway level
**Solution**: Ensure API Gateway authorizer scopes match client OAuth scopes

### Issue 2: Public Client with Resource Server Scopes

**Error**: Client credentials flow not available
**Solution**: Either use standard scopes or create confidential client

### Issue 3: Missing Environment Variables

**Error**: `500 Internal Server Error` in Lambda
**Solution**: Ensure all required environment variables are set in SAM template

### Issue 4: Case Sensitivity

**Error**: `422 Validation Error` for enum values
**Solution**: Use lowercase values (`equity` not `EQUITY`, `active` not `ACTIVE`)

### Issue 5: Token Validation in Lambda

**Error**: Custom token validation failing
**Solution**: Let API Gateway handle validation, remove custom JWT validation in Lambda

## Production Considerations

### Security Best Practices

1. **Use HTTPS Only**: Never send tokens over HTTP
2. **Token Expiration**: Configure appropriate token lifetimes
3. **CORS Configuration**: Restrict origins in production
4. **Rate Limiting**: Implement API throttling
5. **Monitoring**: Set up CloudWatch alarms for auth failures

### Performance Optimization

1. **Token Caching**: Cache decoded tokens appropriately
2. **Connection Pooling**: Use connection pooling for DynamoDB
3. **Cold Start Optimization**: Use provisioned concurrency if needed

### Monitoring and Alerting

```yaml
# CloudWatch Alarms
HighErrorRateAlarm:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: !Sub "${AWS::StackName}-HighErrorRate"
    MetricName: 4XXError
    Namespace: AWS/ApiGateway
    Statistic: Sum
    Period: 300
    EvaluationPeriods: 2
    Threshold: 10
    ComparisonOperator: GreaterThanThreshold
```

## Testing and Validation

### Automated Testing

Use the provided test script:

```bash
python tests/integration/test_aws_api_client.py
```

### Manual Testing

1. Authenticate user via Cognito
2. Extract access token
3. Make API request with Bearer token
4. Verify successful response

## Conclusion

This setup provides a robust authentication system for the FundFlow application. The key to success is ensuring consistency between:

- Cognito client OAuth scopes
- API Gateway authorizer scopes
- Lambda function environment variables
- Frontend token usage

Regular testing and monitoring will help maintain a reliable authentication system.
