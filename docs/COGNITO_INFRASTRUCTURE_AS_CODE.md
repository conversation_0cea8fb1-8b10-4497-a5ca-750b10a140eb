# Cognito Infrastructure as Code Setup

## Overview

The FundFlow application now includes complete Cognito infrastructure management through CloudFormation. This ensures that all Cognito resources (User Pool, Resource Server, Client) with custom scopes are managed as Infrastructure as Code and won't be lost during deployments.

## 🚀 Quick Start

### Option 1: Create New Cognito Resources (Recommended)

Deploy with new managed Cognito resources:

```bash
sam deploy --config-env dev
```

This will create:

- ✅ New Cognito User Pool (`fundflow-dev-fundflow-user-pool`)
- ✅ Resource Server with custom scopes (`fundflow-api`)
- ✅ User Pool Client configured for OAuth flows
- ✅ All custom scopes preserved and managed

### Option 2: Use Existing Cognito Resources

To continue using your existing Cognito setup:

```bash
sam deploy --config-env dev \
  --parameter-overrides CreateCognitoResources=false \
  ExistingUserPoolId=ap-northeast-1_H2kKHGUAT \
  ExistingUserPoolClientId=2jh76f894g6lv9vrus4qbb9hu7
```

## 📋 What Changed

### 1. Template.yaml Enhancements

**New Cognito Resources Added:**

- `UserPool`: Complete user pool with password policies
- `UserPoolResourceServer`: Defines custom API scopes
- `UserPoolClient`: OAuth-enabled client with all scopes

**Custom Scopes Defined:**

```yaml
Scopes:
  - ScopeName: "admin:all"
    ScopeDescription: "Full administrative access"
  - ScopeName: "funds:read"
    ScopeDescription: "Read fund data"
  - ScopeName: "funds:write"
    ScopeDescription: "Create and update fund data"
  - ScopeName: "users:read"
    ScopeDescription: "Read user data"
  - ScopeName: "users:write"
    ScopeDescription: "Create and update user data"
  - ScopeName: "reports:read"
    ScopeDescription: "Read reports"
  - ScopeName: "reports:write"
    ScopeDescription: "Generate and update reports"
```

### 2. Configuration Parameters

**New Parameters:**

- `CreateCognitoResources`: Toggle between new/existing resources
- `CognitoUserPoolName`: Name for the User Pool
- `CognitoClientName`: Name for the Client
- `CognitoResourceServerName`: Resource server identifier
- `CallbackURLs`: OAuth callback URLs
- `LogoutURLs`: OAuth logout URLs

### 3. Conditional Logic

All Lambda functions and API Gateway now use conditional references:

```yaml
USER_POOL_ID: !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
USER_POOL_CLIENT_ID:
  !If [CreateNewCognito, !Ref UserPoolClient, !Ref ExistingUserPoolClientId]
```

## 🔧 Configuration Options

### Development Environment (samconfig.toml)

```toml
[dev.deploy.parameters]
parameter_overrides = "CreateCognitoResources=\"true\" CognitoUserPoolName=\"fundflow-user-pool\" CognitoClientName=\"fundflow-client\" CognitoResourceServerName=\"fundflow-api\" CallbackURLs=\"http://localhost:3000/auth/callback\" LogoutURLs=\"http://localhost:3000/auth/signout\""
```

### Environment Variables

Update your frontend environment variables after deployment:

```env
NEXT_PUBLIC_COGNITO_USER_POOL_ID=[Check CloudFormation Outputs]
NEXT_PUBLIC_COGNITO_CLIENT_ID=[Check CloudFormation Outputs]
NEXT_PUBLIC_COGNITO_REGION=ap-northeast-1
```

## 📊 Resource Output

After deployment, check these CloudFormation outputs:

- `UserPoolId`: New User Pool ID
- `UserPoolClientId`: New Client ID
- `UserPoolResourceServerIdentifier`: Resource server name
- `ApiGatewayUrl`: Updated API endpoint

## 🔄 Migration from Existing Setup

### Step 1: Deploy with New Resources

```bash
# This creates NEW Cognito resources alongside existing ones
sam deploy --config-env dev
```

### Step 2: Update Frontend Configuration

Update your frontend to use the new Cognito resources:

```javascript
// Update your Auth configuration
const authConfig = {
  userPoolId: "NEW_USER_POOL_ID", // From CloudFormation outputs
  userPoolWebClientId: "NEW_CLIENT_ID", // From CloudFormation outputs
  region: "ap-northeast-1",
};
```

### Step 3: Migrate Users (Optional)

You can:

- **Option A**: Keep existing users in old pool, create new users in new pool
- **Option B**: Use Cognito User Pool Migration (requires additional setup)
- **Option C**: Export/import users (complex, requires careful handling)

### Step 4: Update API Authorization

The API Gateway now supports custom scopes. Update your frontend to request appropriate scopes:

```javascript
// Example: Request specific scopes
const signInResult = await Auth.signIn(username, password);
// Token will include:
// - Standard scopes: email, openid, profile, aws.cognito.signin.user.admin
// - Custom scopes: fundflow-api/funds:read, fundflow-api/funds:write, etc.
```

## 🛡️ Security Features

### Password Policy

```yaml
PasswordPolicy:
  MinimumLength: 8
  RequireUppercase: true
  RequireLowercase: true
  RequireNumbers: true
  RequireSymbols: false
```

### OAuth Configuration

- ✅ Authorization code flow
- ✅ Implicit flow (optional)
- ✅ Refresh token support
- ✅ PKCE support for SPAs
- ✅ Proper callback/logout URL validation

### Token Settings

- Access Token: 60 minutes
- ID Token: 60 minutes
- Refresh Token: 30 days

## 🐛 Troubleshooting

### Issue: Custom Scopes Not Working

**Problem**: API returns 401 Unauthorized
**Solution**: Ensure your frontend requests the correct scopes:

```javascript
// Make sure your Auth.signIn includes scope configuration
// The client is pre-configured with all available scopes
```

### Issue: Frontend Can't Connect

**Problem**: User Pool/Client ID changed
**Solution**: Update frontend environment variables with new values from CloudFormation outputs

### Issue: Deployment Conflicts

**Problem**: CloudFormation can't update existing resources
**Solution**:

1. Delete existing manual Cognito resources (backup users first!)
2. Deploy with `CreateCognitoResources=true`
3. Or use `CreateCognitoResources=false` to keep existing setup

### Issue: Users Lost After Deployment

**Problem**: New User Pool doesn't have existing users
**Solution**: Choose migration strategy:

- Keep both pools temporarily
- Use Cognito Migration Lambda
- Export/import users manually

## 📚 API Gateway Scopes Usage

### Standard Scopes (Always Available)

```yaml
AuthorizationScopes:
  - "aws.cognito.signin.user.admin"
  - "email"
  - "openid"
  - "profile"
```

### Custom Scopes (When Using New Cognito)

To enable custom scopes in API Gateway, uncomment these lines in `template.yaml`:

```yaml
# Uncomment to enable custom scopes validation
# - !If [CreateNewCognito, !Sub "${CognitoResourceServerName}/admin:all", !Ref "AWS::NoValue"]
# - !If [CreateNewCognito, !Sub "${CognitoResourceServerName}/funds:read", !Ref "AWS::NoValue"]
# - !If [CreateNewCognito, !Sub "${CognitoResourceServerName}/funds:write", !Ref "AWS::NoValue"]
```

**Note**: Only enable custom scope validation if your frontend is configured to request them!

## 🎯 Benefits

✅ **Infrastructure as Code**: All Cognito config managed in Git
✅ **Version Control**: Track changes to authentication setup
✅ **Reproducible Deployments**: Same setup across environments
✅ **Custom Scopes Preserved**: No more lost configuration
✅ **Flexible Setup**: Choose new or existing resources
✅ **Complete OAuth Support**: Full OAuth 2.0 + OIDC compliance

## 📞 Next Steps

1. **Deploy with new resources**: `sam deploy --config-env dev`
2. **Test authentication**: Verify login still works
3. **Update frontend**: Use new User Pool/Client IDs
4. **Test API calls**: Ensure authorization works
5. **Plan user migration**: If needed
6. **Update documentation**: Record new configuration

This setup ensures your Cognito configuration is never lost again and provides a solid foundation for authentication across all environments! 🚀
