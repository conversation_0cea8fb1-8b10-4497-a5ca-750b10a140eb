# Cognito & API Gateway Quick Reference

## Current Working Configuration

### Environment

- **Region**: `ap-northeast-1`
- **User Pool ID**: `ap-northeast-1_H2kKHGUAT`
- **Client ID**: `2jh76f894g6lv9vrus4qbb9hu7`
- **API URL**: `https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev`

### Working SAM Template Configuration

```yaml
# API Gateway Authorizer (WORKING)
Auth:
  DefaultAuthorizer: CognitoAuthorizer
  Authorizers:
    CognitoAuthorizer:
      UserPoolArn: !Sub "arn:aws:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/${ExistingUserPoolId}"
      AuthorizationScopes:
        - "aws.cognito.signin.user.admin"
        - "email"
        - "openid"
        - "profile"
```

### Lambda Environment Variables

```yaml
Environment:
  Variables:
    COGNITO_USER_POOL_ID: !Ref ExistingUserPoolId
    COGNITO_APP_CLIENT_ID: !Ref ExistingUserPoolClientId
    FUND_TABLE: !Ref FundTable
    USER_TABLE: !Ref UserTable
    ENVIRONMENT: !Ref Environment
    LOG_LEVEL: !Ref LogLevel
```

## Quick Commands

### Check Cognito Client Configuration

```bash
aws cognito-idp describe-user-pool-client \
  --user-pool-id ap-northeast-1_H2kKHGUAT \
  --client-id 2jh76f894g6lv9vrus4qbb9hu7 \
  --region ap-northeast-1
```

### Test Authentication

```bash
python tests/integration/test_aws_api_client.py
```

### Deploy Changes

```bash
sam build && sam deploy --config-env dev
```

### Check Lambda Logs

```bash
aws logs filter-log-events \
  --log-group-name "/aws/lambda/fundflow-dev-FundsAPIFunction-ZX9ysjZIVfHZ" \
  --start-time $(($(date +%s) - 3600)) \
  --region ap-northeast-1
```

## Common Issues & Quick Fixes

### 401 Unauthorized at API Gateway

❌ **Problem**: Scope mismatch between client and API Gateway authorizer
✅ **Fix**: Use standard scopes in API Gateway authorizer (see SAM template above)

### 500 Internal Server Error in Lambda

❌ **Problem**: Missing environment variables
✅ **Fix**: Ensure Lambda functions have required environment variables

### 422 Validation Error

❌ **Problem**: Case sensitivity in API parameters
✅ **Fix**: Use lowercase values (`equity` not `EQUITY`, `active` not `ACTIVE`)

## Authentication Success Indicators

When authentication is working correctly, you should see:

```
✅ Access token works with API!
✅ Public client authentication successful!
```

## Test Cases

Use these parameter values in tests:

- `fund_type`: `equity` (lowercase)
- `status`: `active` (lowercase)
- All enum values should be lowercase

## Architecture Summary

```
Client → Cognito (get token) → API Gateway (validate token) → Lambda (process request)
```

**Key Success Factor**: Consistent scope configuration across all components.
