# Cognito Redirect Error Fix

## Problem
You're getting a `redirect_mismatch` error because your Cognito User Pool Client doesn't have the correct callback URLs configured for your pinggy.link tunnel.

## Error Details
- **Error URL**: `https://ap-northeast-1h2kkhguat.auth.ap-northeast-1.amazoncognito.com/error?error=redirect_mismatch&client_id=2jh76f894g6lv9vrus4qbb9hu7`
- **Your App**: Running on `localhost:3000` and mapped to `https://rmzqmzxtlz.a.pinggy.link`

## What I've Fixed

### 1. Updated Environment Configuration
✅ **Updated `frontend/.env.local`**:
- Changed `NEXTAUTH_URL` from `http://localhost:3001` to `https://rmzqmzxtlz.a.pinggy.link`
- Changed `NEXT_PUBLIC_APP_URL` to match the pinggy.link URL
- This ensures NextAuth.js uses the correct callback URLs

## What You Need to Do Manually

### 2. Update Cognito User Pool Client Settings

**I've opened the AWS Cognito Console for you.** Follow these steps:

1. **In the AWS Console** (should be open in your browser):
   - You should see your User Pool Client configuration page
   - If not, navigate to: Cognito → User Pools → `ap-northeast-1_H2kKHGUAT` → App integration → App clients

2. **Click "Edit" in the "Hosted UI" section**

3. **Update "Allowed callback URLs"** to include both:
   ```
   http://localhost:3000/api/auth/callback/cognito
   https://rmzqmzxtlz.a.pinggy.link/api/auth/callback/cognito
   ```

4. **Update "Allowed sign-out URLs"** to include both:
   ```
   http://localhost:3000
   https://rmzqmzxtlz.a.pinggy.link
   ```

5. **Ensure these OAuth settings are enabled**:
   - OAuth flows: ✅ Authorization code grant
   - OAuth scopes: ✅ openid, profile, email
   - Identity providers: ✅ Cognito User Pool

6. **Click "Save changes"**

## After Making Changes

1. **Restart your Next.js development server**:
   ```bash
   # Stop the current server (Ctrl+C)
   # Then restart
   npm run dev
   # or
   yarn dev
   ```

2. **Test the authentication**:
   - Try signing in through your app
   - The redirect should now work with both localhost:3000 and pinggy.link

## Configuration Summary

### Current Setup
- **User Pool ID**: `ap-northeast-1_H2kKHGUAT`
- **Client ID**: `2jh76f894g6lv9vrus4qbb9hu7`
- **Region**: `ap-northeast-1`
- **Local URL**: `http://localhost:3000`
- **Public URL**: `https://rmzqmzxtlz.a.pinggy.link`

### Required Callback URLs
- `http://localhost:3000/api/auth/callback/cognito` (for local development)
- `https://rmzqmzxtlz.a.pinggy.link/api/auth/callback/cognito` (for pinggy.link access)

### Required Logout URLs
- `http://localhost:3000` (for local development)
- `https://rmzqmzxtlz.a.pinggy.link` (for pinggy.link access)

## Troubleshooting

If you still get redirect errors after making these changes:

1. **Check the exact callback URL** in your browser's network tab when the error occurs
2. **Ensure the callback URL exactly matches** what you configured in Cognito (including `/api/auth/callback/cognito`)
3. **Clear your browser cache** and cookies for the Cognito domain
4. **Wait a few minutes** for AWS changes to propagate

## Direct Links
- **Cognito Console**: https://ap-northeast-1.console.aws.amazon.com/cognito/v2/idp/user-pools/ap-northeast-1_H2kKHGUAT/app-integration/clients/2jh76f894g6lv9vrus4qbb9hu7
- **Your App (Local)**: http://localhost:3000
- **Your App (Public)**: https://rmzqmzxtlz.a.pinggy.link
