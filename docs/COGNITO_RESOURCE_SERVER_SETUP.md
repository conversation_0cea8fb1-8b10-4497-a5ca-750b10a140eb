# Cognito Resource Server Setup Guide

## Problem Description

When testing the FundFlow API Gateway endpoints, you may encounter `401 Unauthorized` errors even after successful Cognito authentication. This typically happens because:

1. **Resource Server Missing**: The Cognito User Pool doesn't have a resource server configured
2. **Scopes Not Configured**: The API Gateway authorizer expects specific scopes in JWT tokens
3. **Token Type Mismatch**: Using the wrong token type (ID token vs Access token with scopes)

## Solution Overview

The fix involves:

1. Creating a Resource Server in Cognito
2. Configuring scopes for API access
3. Updating the User Pool Client to support these scopes
4. Using OAuth2 client credentials flow for machine-to-machine authentication

## Step 1: Run the Setup Script

We've created an automated setup script that configures everything needed:

```bash
./scripts/setup-cognito-resource-server.sh
```

### What the Script Does

1. **Creates Resource Server**:

   - ID: `fundflow-api`
   - Name: `FundFlow API Resource Server`

2. **Defines Scopes**:

   - `fundflow-api/funds:read` - Read access to funds API
   - `fundflow-api/funds:write` - Write access to funds API
   - `fundflow-api/users:read` - Read access to users API
   - `fundflow-api/users:write` - Write access to users API
   - `fundflow-api/reports:read` - Read access to reports API
   - `fundflow-api/reports:write` - Write access to reports API
   - `fundflow-api/admin:all` - Full administrative access

3. **Updates User Pool Client**:
   - Enables OAuth2 flows including `client_credentials`
   - Adds the resource server scopes
   - Generates a client secret (required for machine-to-machine auth)

## Step 2: Update API Gateway (Already Done)

The `template.yaml` has been updated to include authorization scopes in the Cognito authorizer configuration:

```yaml
Auth:
  DefaultAuthorizer: CognitoAuthorizer
  Authorizers:
    CognitoAuthorizer:
      UserPoolArn: !Sub "arn:aws:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/${ExistingUserPoolId}"
      AuthorizationScopes:
        - "fundflow-api/funds:read"
        - "fundflow-api/funds:write"
        - "fundflow-api/users:read"
        - "fundflow-api/users:write"
        - "fundflow-api/reports:read"
        - "fundflow-api/reports:write"
        - "fundflow-api/admin:all"
```

## Step 3: Deploy the Updated Template

After running the setup script, deploy the updated CloudFormation template:

```bash
sam deploy --config-env dev
```

## Step 4: Test with Updated Test Script

The test script (`tests/integration/test_aws_api_client.py`) has been updated to:

1. **Try OAuth2 Client Credentials First**: This is the recommended approach for API testing
2. **Use Proper Scopes**: Requests tokens with the appropriate scopes
3. **Handle Client Secrets**: Automatically retrieves and uses the client secret

Run the test:

```bash
cd tests/integration
python test_aws_api_client.py
```

## Authentication Flow Details

### OAuth2 Client Credentials Flow

This is the recommended flow for machine-to-machine authentication:

1. **Encode Credentials**: Base64 encode `client_id:client_secret`
2. **Request Token**: POST to Cognito's OAuth2 token endpoint
3. **Include Scopes**: Specify which scopes you need access to
4. **Use Access Token**: Include the token in API requests

### Token Endpoint

```
https://{user_pool_domain}.auth.{region}.amazoncognito.com/oauth2/token
```

### Request Format

```http
POST /oauth2/token
Authorization: Basic {base64_encoded_credentials}
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials&scope=fundflow-api/admin:all
```

## Troubleshooting

### Error: "Client secret not found"

**Solution**: Run the setup script again. The script generates a client secret which is required for OAuth2 flows.

### Error: "invalid_scope"

**Solution**: Ensure the resource server exists and the requested scope is properly formatted as `resource_server_id/scope_name`.

### Error: "invalid_client"

**Possible causes**:

1. Client ID is incorrect
2. Client secret is incorrect
3. Client doesn't support client_credentials flow

**Solution**: Check the client configuration in Cognito console.

### Error: "unauthorized" (401) from API Gateway

**Possible causes**:

1. Token doesn't contain required scopes
2. API Gateway authorizer configuration doesn't match token scopes
3. Using wrong token type (ID token instead of access token)

**Solution**:

1. Verify token contains correct scopes using JWT decoder
2. Check API Gateway authorizer configuration
3. Ensure using access token from OAuth2 flow

## Manual Setup (Alternative)

If you prefer to set up manually via AWS Console:

### 1. Create Resource Server

1. Go to AWS Cognito → User Pools → Your Pool
2. Navigate to **App integration** → **Resource servers**
3. Click **Create resource server**
4. Set:
   - **Name**: `FundFlow API Resource Server`
   - **Identifier**: `fundflow-api`
5. Add scopes as listed above

### 2. Update App Client

1. Go to **App integration** → **App clients**
2. Select your app client
3. Click **Edit**
4. Under **Hosted UI settings**:
   - Enable **Authorization code grant**
   - Enable **Implicit grant**
   - Enable **Client credentials**
5. Under **OpenID Connect scopes**:
   - Select **email**, **openid**, **profile**
6. Under **Custom scopes**:
   - Select all the `fundflow-api/*` scopes
7. **Generate client secret** if not already done
8. Save changes

### 3. Test Configuration

Use the test script or tools like Postman to verify the setup works.

## Security Considerations

1. **Client Secret**: Store securely, never commit to version control
2. **Scopes**: Use principle of least privilege - only grant necessary scopes
3. **Token Expiration**: Configure appropriate token validity periods
4. **Environment Separation**: Use different clients for dev/staging/prod

## Next Steps

After completing this setup:

1. ✅ API Gateway authentication should work
2. ✅ Test script should pass authorization
3. ✅ Frontend can be updated to use the same scopes
4. ✅ CI/CD pipelines can use client credentials for automated testing

## References

- [AWS Cognito Resource Servers](https://docs.aws.amazon.com/cognito/latest/developerguide/cognito-user-pools-resource-servers.html)
- [OAuth 2.0 Client Credentials](https://tools.ietf.org/html/rfc6749#section-4.4)
- [API Gateway Cognito Authorizers](https://docs.aws.amazon.com/apigateway/latest/developerguide/apigateway-integrate-with-cognito.html)
