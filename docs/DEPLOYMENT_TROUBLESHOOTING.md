# FundFlow Deployment Troubleshooting Guide

## Common Issues and Solutions

### 1. S3 Bucket Does Not Exist Error

**Error:** `S3 Bucket does not exist`

**Causes:**

- Region mismatch between AWS profile and SAM configuration
- Managed S3 bucket creation failed
- Missing S3 permissions

**Solutions:**

#### Option A: Use Guided Deployment

```bash
chmod +x deploy-guided.sh
./deploy-guided.sh
```

#### Option B: Specify Explicit S3 Bucket

```bash
# Create a bucket in your region first
aws s3 mb s3://fundflow-dev-deployment-bucket-$(date +%s) --region ap-northeast-1 --profile fundflow-dev

# Deploy with explicit bucket
sam deploy --s3-bucket fundflow-dev-deployment-bucket-$(date +%s) --profile fundflow-dev --config-env dev
```

#### Option C: Fix Region Configuration

Update `samconfig.toml` to ensure region consistency:

```toml
[dev.deploy.parameters]
region = "ap-northeast-1"  # Match your AWS profile region
```

### 2. AWS CLI/SAM CLI Not Found

**Error:** Command not found

**Solutions:**

#### Install SAM CLI (macOS)

```bash
# Using Homebrew
brew install aws-sam-cli

# Or download installer
curl -L https://github.com/aws/aws-sam-cli/releases/latest/download/aws-sam-cli-macos-x86_64.pkg -o aws-sam-cli-macos-x86_64.pkg
sudo installer -pkg aws-sam-cli-macos-x86_64.pkg -target /
```

#### Install AWS CLI (macOS)

```bash
# Using Homebrew
brew install awscli

# Or download installer
curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
sudo installer -pkg AWSCLIV2.pkg -target /
```

### 3. Missing AWS Credentials

**Error:** Unable to locate credentials

**Solutions:**

#### Configure AWS Profile

```bash
aws configure --profile fundflow-dev
```

Enter:

- AWS Access Key ID
- AWS Secret Access Key
- Default region: `ap-northeast-1`
- Default output format: `json`

#### Verify Configuration

```bash
aws configure list --profile fundflow-dev
aws sts get-caller-identity --profile fundflow-dev
```

### 4. IAM Permission Issues

**Error:** Access denied or insufficient permissions

**Required IAM Permissions:**

- CloudFormation: Full access
- Lambda: Full access
- API Gateway: Full access
- DynamoDB: Full access
- S3: Full access (or specific bucket permissions)
- IAM: Create/manage roles
- CloudWatch: Logs and metrics
- Cognito: User pool management

### 5. Template Validation Errors

**Solutions:**

#### Validate Template

```bash
sam validate --profile fundflow-dev
```

#### Common Template Issues

- Check YAML syntax
- Verify resource references
- Ensure parameter types match

### 6. Build Issues

**Error:** Build failed

**Solutions:**

#### Clean and Rebuild

```bash
rm -rf .aws-sam/
sam build --parallel
```

#### Check Dependencies

Ensure `requirements.txt` files exist:

- `src/requirements.txt` - Lambda function dependencies
- `layers/powertools/requirements.txt` - Layer dependencies

### 7. PowertoolsLayer Issues

**Error:** Unable to upload PowertoolsLayer

**Solutions:**

#### Option A: Remove Layer (Quick Fix)

Comment out the PowertoolsLayer in `template.yaml`:

```yaml
# PowertoolsLayer:
#   Type: AWS::Serverless::LayerVersion
#   Properties:
#     LayerName: !Sub "${AWS::StackName}-powertools"
#     # ... rest of configuration
```

And remove layer references from Lambda functions:

```yaml
# Layers:
#   - !Ref PowertoolsLayer
```

#### Option B: Use Pre-built Layer

Replace with AWS managed layer:

```yaml
Layers:
  - arn:aws:lambda:ap-northeast-1:017000801446:layer:AWSLambdaPowertoolsPythonV2:21
```

### 8. CloudFormation Stack Rollback

**Error:** Stack creation/update failed and rolled back

**Solutions:**

#### Check CloudFormation Events

```bash
aws cloudformation describe-stack-events \
  --stack-name fundflow-dev \
  --profile fundflow-dev \
  --query 'StackEvents[?ResourceStatus==`CREATE_FAILED`]'
```

#### Delete Failed Stack

```bash
aws cloudformation delete-stack \
  --stack-name fundflow-dev \
  --profile fundflow-dev
```

## Manual Deployment Steps

If automation fails, try manual deployment:

1. **Clean Environment**

   ```bash
   rm -rf .aws-sam/
   ```

2. **Build Application**

   ```bash
   sam build --parallel
   ```

3. **Validate Template**

   ```bash
   sam validate --profile fundflow-dev
   ```

4. **Deploy with Guided Mode**

   ```bash
   sam deploy --guided --profile fundflow-dev
   ```

5. **Or Deploy with Specific Parameters**
   ```bash
   sam deploy \
     --stack-name fundflow-dev \
     --region ap-northeast-1 \
     --profile fundflow-dev \
     --capabilities CAPABILITY_IAM \
     --parameter-overrides \
       Environment=dev \
       LogLevel=DEBUG \
       DynamoDBBillingMode=PAY_PER_REQUEST
   ```

## Verification Commands

After successful deployment:

```bash
# Check stack status
aws cloudformation describe-stacks \
  --stack-name fundflow-dev \
  --profile fundflow-dev

# Test API health endpoint
curl $(aws cloudformation describe-stacks \
  --stack-name fundflow-dev \
  --profile fundflow-dev \
  --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
  --output text)/health

# List created resources
aws cloudformation list-stack-resources \
  --stack-name fundflow-dev \
  --profile fundflow-dev
```

## Getting Help

If issues persist:

1. Check AWS CloudFormation console for detailed error messages
2. Review CloudWatch logs for Lambda functions
3. Verify IAM permissions and policy attachments
4. Consider deploying to a different region as a test
