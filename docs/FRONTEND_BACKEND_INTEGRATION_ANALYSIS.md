# Frontend-Backend Integration Analysis

## Overview

This document outlines critical communication issues between the FundFlow frontend and backend that need to be resolved for proper functionality.

## 🚨 Critical Issues

### 1. Missing API Endpoints

**Problem**: Frontend expects endpoints that don't exist in the backend.

#### Missing Endpoints:

- **`GET /funds/{id}/details`** - Frontend calls `getFundDetails()` expecting rich fund data
- **`GET /funds/{id}/historical`** - Frontend calls `getFundHistoricalData(id, period)`
- **`GET /funds/search`** - Frontend calls `searchFunds(query)`

#### Current Backend Endpoints:

- ✅ `GET /funds` - List funds
- ✅ `GET /funds/{fund_id}` - Get basic fund info
- ✅ `POST /funds` - Create fund
- ✅ `PUT /funds/{fund_id}` - Update fund
- ✅ `DELETE /funds/{fund_id}` - Delete fund
- ✅ `POST /funds/bulk-update` - Bulk updates

### 2. Data Structure Mismatches

#### Frontend Expectations vs Backend Reality

**Frontend Type: `FundDetails`**

```typescript
interface FundDetails extends Fund {
  analytics: {
    kpis: {
      totalReturn: number;
      annualizedReturn: number;
      volatility: number;
      sharpeRatio: number;
      alpha: number;
      beta: number;
      maxDrawdown: number;
      informationRatio: number;
    };
    riskMetrics: {
      standardDeviation: number;
      downSideRisk: number;
      varRisk: number;
      sortRatio: number;
      calmarRatio: number;
    };
    assetAllocation: {
      stocks: number;
      bonds: number;
      cash: number;
      other: number;
    };
    geographicAllocation: {
      domestic: number;
      international: number;
      emerging: number;
    };
    topHoldings: Array<{
      id: string;
      name: string;
      symbol: string;
      percentage: number;
      marketValue: number;
      sector: string;
    }>;
    sectorAllocation: Array<{
      name: string;
      percentage: number;
      marketValue: number;
      change: number;
    }>;
  };
  historicalData: ChartDataPoint[];
  benchmark: {
    name: string;
    symbol: string;
    performance: {
      oneDay: number;
      oneWeek: number;
      oneMonth: number;
      threeMonths: number;
      sixMonths: number;
      oneYear: number;
      threeYears: number;
      fiveYears: number;
    };
  };
  documents: Array<{
    id: string;
    name: string;
    type: "factsheet" | "annual_report" | "prospectus" | "scheme_info";
    url: string;
    uploadDate: Date;
  }>;
}
```

**Backend Model: `Fund`**

```python
class Fund(PowertoolsBaseModel):
    fund_id: str
    name: str
    fund_type: FundType
    status: FundStatus
    nav: Optional[Decimal]
    currency: Currency
    inception_date: Optional[datetime]
    total_assets: Optional[Decimal]
    risk_level: Optional[RiskLevel]
    performance_metrics: Optional[PerformanceMetrics]
    holdings: Optional[Holdings]
    fund_manager: Optional[str]
    management_company: Optional[str]
    expense_ratio: Optional[Decimal]
    minimum_investment: Optional[Decimal]
    # ... basic fields only
```

### 3. Field Name Mapping Issues

| Frontend Field  | Backend Field      | Status       | Notes                                     |
| --------------- | ------------------ | ------------ | ----------------------------------------- |
| `id`            | `fund_id`          | ❌ Mismatch  | Primary key inconsistency                 |
| `aum`           | `total_assets`     | ❌ Mismatch  | Asset management field                    |
| `symbol`        | `bloomberg_ticker` | ❌ Mismatch  | Fund symbol field                         |
| `riskLevel`     | `risk_level`       | ⚠️ Enum diff | Frontend: string, Backend: RiskLevel enum |
| `changePercent` | -                  | ❌ Missing   | No backend calculation                    |
| `change`        | -                  | ❌ Missing   | No backend calculation                    |
| `volume`        | -                  | ❌ Missing   | In custom_fields                          |
| `rating`        | -                  | ❌ Missing   | In custom_fields                          |

### 4. Response Format Inconsistencies

**Backend Response Format:**

```json
{
  "message": "Fund retrieved successfully",
  "data": {
    "fund": {
      "fund_id": "FUND-001",
      "name": "Growth Fund"
      // ... fund data
    }
  }
}
```

**Frontend Access Pattern:**

```typescript
// Frontend expects direct access to fund data
const response = await fundApi.getFundDetails(fundId);
setFundDetails(response.data); // But backend returns response.data.fund
```

### 5. Missing Historical Data Endpoints

**Backend Has Models But No Endpoints:**

- `MonthlyReturn` - Monthly performance data
- `CalendarYearReturn` - Yearly performance data
- `ExposureSnapshot` - Position exposure data
- `MarketCapExposure` - Market cap distribution

**Frontend Expects:**

```typescript
// Called from fund details page
const response = await fundApi.getFundHistoricalData(fundId, period);
setHistoricalData(response.data);
```

## 🔧 Required Fixes

### 1. Add Missing API Endpoints

#### A. Fund Details Endpoint

```python
# In funds.py - Add new handler
@tracer.capture_method
def handle_get_fund_details(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle GET /funds/{fund_id}/details - Get comprehensive fund details"""
    # Combine Fund + FundStats + Holdings + Performance data
    # Return enriched FundDetails structure
```

#### B. Historical Data Endpoint

```python
@tracer.capture_method
def handle_get_fund_historical_data(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle GET /funds/{fund_id}/historical - Get historical performance data"""
    # Query MonthlyReturn, CalendarYearReturn models
    # Filter by time period parameter
    # Return ChartDataPoint array
```

#### C. Search Endpoint

```python
@tracer.capture_method
def handle_search_funds(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle GET /funds/search - Search funds by query"""
    # Implement search across fund names, descriptions, managers
    # Return filtered fund list
```

### 2. Create Data Transformation Layer

#### Backend Response Adapter

```python
class FundDetailsResponse(BaseModel):
    """Enhanced fund response with analytics for frontend"""

    # Core fund data
    id: str  # Map from fund_id
    name: str
    symbol: str  # Map from bloomberg_ticker
    aum: float  # Map from total_assets

    # Analytics computed from related models
    analytics: FundAnalytics
    historicalData: List[ChartDataPoint]
    benchmark: BenchmarkData
```

#### Frontend API Client Updates

```typescript
// Update fundApi to handle new endpoints
export const fundApi = {
  getFundDetails: async (fundId: string): Promise<ApiResponse<FundDetails>> => {
    return apiRequest<ApiResponse<FundDetails>>(`/funds/${fundId}/details`);
  },

  getFundHistoricalData: async (
    fundId: string,
    period: TimePeriod
  ): Promise<ApiResponse<ChartDataPoint[]>> => {
    return apiRequest<ApiResponse<ChartDataPoint[]>>(
      `/funds/${fundId}/historical?period=${period}`
    );
  },

  searchFunds: async (query: string): Promise<ApiResponse<Fund[]>> => {
    return apiRequest<ApiResponse<Fund[]>>(
      `/funds/search?q=${encodeURIComponent(query)}`
    );
  },
};
```

### 3. Update Backend Route Handler

```python
# In funds.py handler function - Add new routes
def handler(event: Dict[str, Any], context: LambdaContext) -> Dict[str, Any]:
    method = event.get("httpMethod", "")
    path = event.get("path", "")

    if method == "GET":
        if path.endswith("/details"):
            # GET /funds/{fund_id}/details
            return handle_get_fund_details(event)
        elif "/historical" in path:
            # GET /funds/{fund_id}/historical
            return handle_get_fund_historical_data(event)
        elif "/search" in path:
            # GET /funds/search
            return handle_search_funds(event)
        # ... existing routes
```

### 4. Fix Field Mapping

#### Option A: Backend Compatibility Layer

```python
class FundFrontendResponse(BaseModel):
    """Frontend-compatible fund response"""

    id: str = Field(alias="fund_id")
    aum: float = Field(alias="total_assets")
    symbol: str = Field(alias="bloomberg_ticker")
    riskLevel: str = Field(computed from risk_level enum)
    # ... other mappings
```

#### Option B: Frontend Adapter (Current Implementation)

Update the existing `convertBackendFundToFrontend()` function in `frontend/src/lib/api.ts` to handle all field mappings correctly.

## 🎯 Implementation Priority

### Phase 1 (Critical - Immediate)

1. ✅ Add `/funds/{id}/details` endpoint returning enriched fund data
2. ✅ Fix field name mappings (id/fund_id, aum/total_assets, etc.)
3. ✅ Update frontend API client to call correct endpoints

### Phase 2 (High Priority)

1. ✅ Add `/funds/{id}/historical` endpoint for chart data
2. ✅ Add `/funds/search` endpoint for fund search
3. ✅ Implement analytics calculation in backend

### Phase 3 (Medium Priority)

1. ✅ Add real-time data updates
2. ✅ Implement caching for performance
3. ✅ Add comprehensive error handling

## 📝 Testing Strategy

### Backend API Tests

```python
def test_fund_details_endpoint():
    """Test GET /funds/{id}/details returns FundDetails structure"""

def test_historical_data_endpoint():
    """Test GET /funds/{id}/historical returns ChartDataPoint array"""

def test_search_endpoint():
    """Test GET /funds/search returns filtered fund list"""
```

### Frontend Integration Tests

```typescript
describe("Fund API Integration", () => {
  test("getFundDetails returns complete fund analytics", async () => {
    // Test frontend can consume backend FundDetails response
  });

  test("getFundHistoricalData returns chart data", async () => {
    // Test historical data endpoint integration
  });
});
```

## 🔍 Current Workarounds

The frontend currently uses mock data generation in `api.ts`:

- `generateMockFunds()` - Creates fake fund list
- `generateMockFundDetails()` - Creates fake analytics data
- `generateMockHistoricalData()` - Creates fake chart data

These should be removed once backend endpoints are implemented.

## ⚠️ Risk Assessment

**High Risk Issues:**

1. **Fund Details Page Broken** - Missing analytics data causes component failures
2. **Edit Functionality Limited** - Field mapping issues prevent proper updates
3. **Search Not Working** - No backend search implementation
4. **Performance Charts Empty** - No historical data endpoint

**Medium Risk Issues:**

1. **Type Safety** - Frontend types don't match backend models
2. **Error Handling** - Inconsistent error response formats
3. **Data Consistency** - Field name mismatches cause data loss

This analysis should be used to prioritize development efforts to restore full frontend-backend communication.
