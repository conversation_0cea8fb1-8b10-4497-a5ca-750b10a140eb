# Fund Extractor Lambda Function

## Overview

The Fund Extractor Lambda function provides AI-powered extraction of fund information from PDF documents. It accepts PDF files via API Gateway and uses the deepseek/deepseek-r1-0528:free model to extract structured fund data.

## Features

- **Direct PDF Processing**: Sends PDF files directly to AI model without preprocessing
- **Cognito Authentication**: Integrated with existing Cognito User Pool authentication
- **Structured Output**: Returns standardized Fund model data
- **Database Integration**: Optional saving of extracted funds to DynamoDB
- **Error Handling**: Comprehensive error handling and logging
- **Metrics**: CloudWatch metrics for monitoring extraction success/failure

## API Endpoint

**POST** `/funds/extract-pdf`

### Authentication

Requires valid JWT token from Cognito User Pool in Authorization header:

```
Authorization: Bearer <jwt_token>
```

### Request Format

- **Content-Type**: `multipart/form-data`
- **File Field**: `file` (PDF file)
- **Query Parameters**:
  - `save=true` (optional): Save extracted fund to database

### Example Request

```bash
curl -X POST \
  https://your-api-gateway-url/dev/funds/extract-pdf?save=true \
  -H "Authorization: Bearer <jwt_token>" \
  -F "file=@fund_document.pdf"
```

### Response Format

#### Success Response (200)

```json
{
  "data": {
    "fund_id": "vanguard-500-index",
    "name": "Vanguard 500 Index Fund",
    "fund_type": "index",
    "currency": "USD",
    "nav": "450.25",
    "total_assets": "850000000000",
    "inception_date": "1976-08-31T00:00:00+00:00",
    "fund_manager": "Vanguard Group",
    "expense_ratio": "0.03",
    "performance_metrics": {
      "ytd_return": "12.5",
      "one_year_return": "18.2",
      "three_year_return": "10.1"
    },
    "saved_to_database": true,
    "database_fund_id": "vanguard-500-index"
  },
  "message": "Fund information successfully extracted from PDF",
  "timestamp": "2025-01-20T10:30:00Z"
}
```

#### Error Response (400/500)

```json
{
  "error": "VALIDATION_ERROR",
  "message": "Extracted fund data failed validation",
  "details": {
    "validation_errors": "Field 'fund_type' is required"
  },
  "timestamp": "2025-01-20T10:30:00Z"
}
```

## Configuration

### Environment Variables

The Lambda function requires these environment variables:

- `OPENROUTER_API_KEY`: API key for OpenRouter AI service
- `FUND_TABLE`: DynamoDB table name for funds
- `USER_TABLE`: DynamoDB table name for users
- `COGNITO_USER_POOL_ID`: Cognito User Pool ID
- `COGNITO_APP_CLIENT_ID`: Cognito App Client ID
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR)
- `POWERTOOLS_SERVICE_NAME`: Service name for AWS Lambda Powertools
- `POWERTOOLS_METRICS_NAMESPACE`: Metrics namespace

### Lambda Configuration

- **Runtime**: Python 3.11
- **Memory**: 1024 MB (increased for AI processing)
- **Timeout**: 300 seconds (5 minutes for AI API calls)
- **Layers**: PowertoolsLayer (includes aws-lambda-powertools and requests)

### IAM Permissions

The Lambda function has the following permissions:

- DynamoDB CRUD operations on Fund table
- DynamoDB read operations on User table
- CloudWatch Logs write permissions
- X-Ray tracing permissions

## AI Model Integration

### Model Used

- **Provider**: OpenRouter
- **Model**: `deepseek/deepseek-r1-0528:free`
- **Input**: Direct PDF file (base64 encoded)
- **Output**: Structured JSON with fund information

### Extraction Fields

The AI model extracts the following fund information:

#### Required Fields

- `fund_id`: Unique identifier
- `name`: Fund name
- `fund_type`: Type (equity, bond, mixed, etc.)

#### Financial Details

- `nav`: Net Asset Value
- `currency`: Base currency
- `total_assets`: Assets under management
- `inception_date`: Fund inception date

#### Management Information

- `fund_manager`: Manager name
- `management_company`: Management company
- `expense_ratio`: Annual expense ratio
- `minimum_investment`: Minimum investment amount

#### Performance Metrics (if available)

- Return percentages (YTD, 1M, 3M, 6M, 1Y, 3Y, 5Y)
- Risk metrics (volatility, Sharpe ratio, max drawdown)
- Benchmark comparisons (alpha, beta)

#### Holdings Information (if available)

- Top holdings list
- Sector allocation
- Geographic allocation

## Error Handling

The function handles various error scenarios:

1. **Authentication Errors**: Invalid or missing JWT tokens
2. **File Upload Errors**: Missing or invalid PDF files
3. **AI API Errors**: OpenRouter service unavailable or errors
4. **Validation Errors**: Extracted data doesn't match Fund model
5. **Database Errors**: Issues saving to DynamoDB

## Monitoring

### CloudWatch Metrics

The function emits the following custom metrics:

- `PDFExtractionAttempt`: Number of extraction attempts
- `PDFExtractionSuccess`: Number of successful extractions
- `PDFExtractionAPIError`: AI API errors
- `PDFExtractionParseError`: JSON parsing errors
- `PDFExtractionValidationError`: Data validation errors
- `PDFExtractionUnexpectedError`: Unexpected errors

### Logging

Comprehensive logging is provided via AWS Lambda Powertools:

- Request/response logging
- Error details and stack traces
- Performance metrics
- User context information

## Deployment

The function is deployed as part of the SAM template:

```yaml
FundExtractorFunction:
  Type: AWS::Serverless::Function
  Properties:
    CodeUri: src/
    Handler: functions.api.fund_extractor.handler
    Runtime: python3.13
    MemorySize: 1024
    Timeout: 300
    Events:
      ExtractPDF:
        Type: Api
        Properties:
          RestApiId: !Ref ApiGateway
          Path: /funds/extract-pdf
          Method: post
          Auth:
            Authorizer: CognitoAuthorizer
```

## Usage Examples

### Frontend Integration

```javascript
// Upload PDF file for fund extraction
const uploadPDF = async (file, saveToDatabase = false) => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await fetch(
    `/api/funds/extract-pdf?save=${saveToDatabase}`,
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      body: formData,
    }
  );

  return response.json();
};
```

### CLI Usage

```bash
# Extract fund data from PDF
curl -X POST \
  https://api.fundflow.com/dev/funds/extract-pdf \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -F "file=@fund_prospectus.pdf"
```

## Limitations

1. **File Size**: Limited by API Gateway payload size (10MB)
2. **Processing Time**: 5-minute timeout for complex documents
3. **AI Model Accuracy**: Depends on PDF quality and structure
4. **Language Support**: Primarily optimized for English documents
5. **PDF Format**: Works best with text-based PDFs (not scanned images)

## Security Considerations

- All requests require valid Cognito authentication
- API key for OpenRouter is stored as environment variable
- PDF content is sent to external AI service (OpenRouter)
- No persistent storage of PDF files
- Comprehensive input validation and sanitization
