# Production Authentication Troubleshooting Guide

This guide provides step-by-step instructions to fix authentication issues when frontend applications deployed to production environments fail to authenticate with AWS API Gateway, while local development works correctly.

## Problem Description

**Symptoms:**
- Local development authentication works perfectly
- Production environment returns 401 Unauthorized errors
- API Gateway logs show "Unauthorized" responses
- Lambda logs show "Session validation failed: Invalid or expired access token"
- Debug endpoints confirm valid session with both access and ID tokens

**Root Cause:**
Token type mismatch between frontend and backend expectations in production vs development environments.

## Solution Overview

The fix involves updating both frontend and backend to handle environment-specific token requirements:

- **Production**: Use ID tokens (standard Cognito User Pool JWT flow)
- **Development**: Use access tokens (for backward compatibility)

## Step-by-Step Fix

### 1. Frontend Token Utility Fix

**File:** `frontend/src/lib/token-utils.ts`

Update the token selection logic to be environment-aware:

```typescript
/**
 * Get the appropriate token for API Gateway based on environment
 * - Production: Use ID token (standard Cognito User Pool authorization)
 * - Development: Use access token (for compatibility with local testing)
 */
export function getApiToken(session: Session | null): string | undefined {
  if (!session) return undefined;

  // In production, use ID token for Cognito User Pool JWT authorization
  // In development, use access token for backwards compatibility
  if (process.env.NODE_ENV === 'production') {
    console.log('🔑 Using ID token for API Gateway (production environment)');
    return session.idToken;
  } else {
    console.log('🔑 Using access token for API Gateway (development environment)');
    return session.accessToken;
  }
}

/**
 * Check if we have the required token for API authorization based on environment
 */
export function hasValidTokenForEnvironment(session: Session | null): boolean {
  if (!session) return false;

  // Check for the appropriate token based on environment
  if (process.env.NODE_ENV === 'production') {
    return !!session.idToken;
  } else {
    return !!session.accessToken;
  }
}
```

### 2. Backend Session Manager Fix

**File:** `src/shared/security/session_manager.py`

Update the session validation to trust API Gateway's authorization when available:

```python
def validate_session(self, event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate user session from Lambda event.
    """
    try:
        # Check if request was already authorized by API Gateway
        request_context = event.get("requestContext", {})
        authorizer = request_context.get("authorizer", {})
        
        # If API Gateway already validated the token (has claims), trust it
        if authorizer and (authorizer.get("claims") or authorizer.get("sub")):
            logger.info("Request already authorized by API Gateway, trusting authorizer context")
            
            # Extract user info from authorizer claims
            claims = authorizer.get("claims", authorizer)
            user_info = {
                "username": claims.get("cognito:username", claims.get("sub")),
                "user_sub": claims.get("sub"),
                "email": claims.get("email"),
                "email_verified": claims.get("email_verified") == "true",
                "user_status": "CONFIRMED",  # Assumed if token is valid
                "attributes": claims,
            }
            
            # Create session info
            session_info = {
                "session_start": int(time.time()),
                "session_duration": 0,
                "time_remaining": self.session_timeout,
                "requires_refresh": False,
                "last_activity": int(time.time()),
            }
            
            return {
                "valid": True,
                "user_info": user_info,
                "session_info": session_info,
                "requires_refresh": False,
            }
        
        # Otherwise, fall back to traditional access token validation
        # (existing code continues...)
```

### 3. Deployment Steps

#### Backend Deployment:
```bash
# Activate Python 3.13 environment
export PATH="/Users/<USER>/miniconda3/envs/ff_env/bin:$PATH"

# Build and deploy SAM application
sam build && sam deploy --config-env dev
```

#### Frontend Deployment:
```bash
# Commit and push changes (if using AWS Amplify)
git add frontend/src/lib/token-utils.ts
git commit -m "Fix production authentication token type"
git push origin main
```

### 4. Verification Steps

1. **Check debug endpoints:**
   ```bash
   # Should show correct token type for environment
   curl "https://your-domain.com/api/debug-session"
   ```

2. **Test authentication flow:**
   - Sign in to production application
   - Navigate to protected pages (e.g., fund details)
   - Verify no 401 errors in browser console

3. **Monitor backend logs:**
   - Should see "Request already authorized by API Gateway" instead of token validation errors

## Key Technical Insights

### Token Types Explained

- **ID Token**: Contains identity information about the user (email, name, etc.)
  - Used for: User authentication and identity verification
  - Required by: Standard Cognito User Pool JWT authorizers

- **Access Token**: Contains authorization scopes and permissions
  - Used for: API resource access authorization
  - Required by: Custom resource servers and scoped APIs

### API Gateway Authorization Flow

1. **Request arrives** with Bearer token in Authorization header
2. **API Gateway** validates token against Cognito User Pool
3. **Valid requests** get authorizer context with user claims
4. **Lambda function** receives event with `requestContext.authorizer.claims`
5. **No re-validation needed** - trust API Gateway's work

### Environment Differences

| Environment | Token Type | Validation Method | Use Case |
|-------------|------------|-------------------|----------|
| Development | Access Token | Direct Cognito API calls | Local testing flexibility |
| Production | ID Token | API Gateway JWT authorizer | Standard production pattern |

## Prevention Tips

1. **Always test** authentication in production-like environments
2. **Use environment-aware** token selection logic
3. **Trust API Gateway** authorization when available
4. **Monitor logs** for authentication patterns
5. **Document token flows** for team understanding

## Related Files

- `frontend/src/lib/token-utils.ts` - Token selection utility
- `src/shared/security/session_manager.py` - Backend session validation
- `template.yaml` - API Gateway authorizer configuration
- `frontend/src/lib/auth.ts` - NextAuth configuration

## Troubleshooting Commands

```bash
# Check CloudFormation stack status
aws cloudformation describe-stacks --stack-name fundflow-dev --query "Stacks[0].StackStatus"

# View recent Lambda logs
aws logs filter-log-events --log-group-name "/aws/lambda/fundflow-dev-FundsAPIFunction" --start-time $(date -d '1 hour ago' +%s)000

# Test API Gateway directly
curl -H "Authorization: Bearer YOUR_TOKEN" "https://your-api-gateway.amazonaws.com/dev/funds"
```

---

**Note**: This fix ensures compatibility between local development (access tokens) and production (ID tokens) while eliminating redundant token validation in Lambda functions.