# Python Environment Setup for FundFlow

## Overview

This document explains how the Python environment has been configured for the FundFlow project to enable proper testing in Cursor.

## Environment Details

- **Conda Environment**: `ff_env`
- **Python Version**: 3.13.4
- **Python Path**: `/Users/<USER>/miniconda3/envs/ff_env/bin/python`
- **Package Manager**: pip (from conda environment)

## Fixed Issues

### 1. Python Path Priority

**Problem**: pyenv was taking precedence over conda environment
**Solution**: Created `setup_test_env.sh` script that:

- Unsets `PYENV_ROOT` and `PYENV_VERSION`
- Prioritizes conda Python in PATH
- Sets proper `PYTHONPATH` for imports

### 2. Import Path Resolution

**Problem**: Tests couldn't import from `src/` directory
**Solution**: Added `src/` directory to Python path via:

- `PYTHONPATH` environment variable
- `conftest.py` configuration
- `.vscode/settings.json` for Cursor

### 3. AWS Mocking Setup

**Problem**: Tests failed due to missing AWS credentials and incompatible moto imports
**Solution**:

- Updated `conftest.py` to use moto 5.x unified `mock_aws` decorator
- Added proper AWS environment variables for testing
- Created comprehensive fixtures for AWS services

### 4. Package Dependencies

**Problem**: Missing test and AWS packages
**Solution**: Installed all required packages from `requirements.txt`

## Usage Instructions

### In Terminal

```bash
# Source the environment setup script
source setup_test_env.sh

# Run specific test
pytest tests/test_funds_api.py::TestFundsAPIHandler::test_handler_routes_get_funds_list -v

# Run all tests in a file
pytest tests/test_funds_api.py -v

# Run all tests
pytest tests/ -v
```

### In Cursor

The environment is now properly configured for Cursor with:

- Python interpreter set to conda environment
- Test discovery configured for pytest
- Import paths properly configured
- AWS environment variables set for testing

## Files Created/Modified

1. **`pytest.ini`** - Pytest configuration with AWS environment variables
2. **`conftest.py`** - Global test fixtures and AWS mocking setup
3. **`setup_test_env.sh`** - Environment setup script
4. **`.vscode/settings.json`** - Cursor/VS Code configuration
5. **`PYTHON_ENV_SETUP.md`** - This documentation

## Test Configuration

### Pytest Configuration (`pytest.ini`)

- Test discovery paths: `tests/`
- AWS environment variables for testing
- Warning filters to reduce noise
- Markers for different test types

### Global Fixtures (`conftest.py`)

- **AWS Mocking**: All AWS services mocked with moto
- **JWT Manager**: Mocked authentication
- **Session Manager**: Mocked session validation
- **Sample Data**: Reusable test data fixtures

### Cursor Settings (`.vscode/settings.json`)

- Python interpreter path
- Test framework configuration (pytest)
- Import path resolution
- Linting and formatting setup

## Verification

The environment is working correctly if:

- `pytest tests/test_funds_api.py::TestFundsAPIHandler::test_handler_routes_get_funds_list -v` passes
- Imports like `from src.functions.api.funds import handler` work
- Tests can be discovered and run from Cursor's test panel

## Troubleshooting

### If imports still fail:

```bash
# Check Python path
python -c "import sys; print('Python:', sys.executable); print('Paths:'); [print(p) for p in sys.path[:5]]"

# Verify environment
source setup_test_env.sh
echo $PYTHONPATH
which python
```

### If tests fail with AWS errors:

- Ensure AWS environment variables are set (handled by conftest.py)
- Check that moto is properly installed: `pip show moto`

### If Cursor doesn't recognize the environment:

1. Restart Cursor
2. Use Command Palette → "Python: Select Interpreter"
3. Choose `/Users/<USER>/miniconda3/envs/ff_env/bin/python`

## Next Steps

1. **Update failing tests**: Fix the 2 failing tests related to AWS Lambda Powertools metrics namespace
2. **Add more test fixtures**: Expand conftest.py with additional shared fixtures as needed
3. **Code formatting**: Run `black` and `flake8` to ensure code quality
4. **Update Pydantic**: Consider migrating from V1 to V2 validators to reduce warnings

## Success Indicators

✅ Tests can be run from terminal with pytest
✅ Tests can be discovered and run from Cursor
✅ Import errors resolved
✅ AWS services properly mocked
✅ Environment consistently uses conda Python (not pyenv)
