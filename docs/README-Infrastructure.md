# FundFlow AWS Infrastructure

This document describes the AWS serverless infrastructure setup for the FundFlow application using AWS SAM (Serverless Application Model).

## Architecture Overview

The FundFlow application uses a modern serverless architecture with the following AWS services:

- **AWS Lambda**: Serverless compute for API endpoints
- **Amazon DynamoDB**: NoSQL database for funds, users, and reports
- **Amazon API Gateway**: REST API management and routing
- **Amazon S3**: Static asset hosting and report storage
- **Amazon CloudFront**: Content delivery network (CDN)
- **Amazon Cognito**: User authentication and authorization
- **AWS Lambda Powertools**: Observability and best practices

## Project Structure

```
├── template.yaml                 # Main SAM template
├── samconfig.toml                # SAM configuration for environments
├── src/
│   ├── functions/               # Lambda function code
│   ├── layers/
│   │   └── powertools/         # Lambda Powertools layer
│   └── shared/                 # Shared utilities and models
├── scripts/
│   ├── deploy.sh               # Enhanced deployment script with safety features
│   ├── validate-deployment.sh  # Pre-deployment validation script
│   ├── compare-environments.sh # Environment configuration comparison
│   ├── check-environment.sh    # Development environment verification
│   ├── setup-aws-profiles.sh   # AWS profile configuration helper
│   └── local-test.sh           # Local testing script
└── README-Infrastructure.md    # This file
```

## Prerequisites

1. **AWS CLI** configured with appropriate credentials
2. **SAM CLI** installed ([Installation Guide](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html))
3. **Docker** installed and running (for local testing)
4. **Python 3.11** (for Lambda functions)

## Environment Setup

The infrastructure supports three environments:

### Development (dev)

- Minimal resources for cost optimization
- Reduced retention periods
- Basic monitoring

### Staging (staging)

- Production-like configuration
- Extended testing capabilities
- Full monitoring enabled

### Production (prod)

- High availability configuration
- Extended retention periods
- Point-in-time recovery enabled
- Enhanced monitoring and alerting

## Deployment

### Quick Deployment

```bash
# Deploy to development environment
./scripts/deploy.sh -e dev

# Deploy to staging environment
./scripts/deploy.sh -e staging

# Deploy to production environment with specific AWS profile
./scripts/deploy.sh -e prod -p production
```

### Manual Deployment

```bash
# Validate template
sam validate

# Build application
sam build

# Deploy to specific environment
sam deploy --config-env dev
```

### Enhanced Deployment Features

The deployment script now includes enhanced safety features:

- **Production Safety**: Requires explicit confirmation for production deployments
- **Account Verification**: Validates AWS account for production deployments
- **Dry Run Mode**: Preview changes without deploying using `--dry-run`
- **Deployment Validation**: Pre-deployment checks for common issues

### Deployment Script Options

```bash
Usage: ./scripts/deploy.sh [OPTIONS]

Options:
  -e, --environment    Target environment (dev, staging, prod) [default: dev]
  -r, --region         AWS region [default: us-east-1]
  -p, --profile        AWS profile to use
  -f, --use-param-file Use parameter file instead of samconfig.toml overrides
  -v, --validate-only  Only validate the template, don't deploy
  -b, --build-only     Only build, don't deploy
  -d, --dry-run        Preview changes without deploying (requires changeset)
  -h, --help           Show this help message
```

### Pre-Deployment Validation

Before deploying, especially to production, run the validation script:

```bash
# Validate deployment readiness
./scripts/validate-deployment.sh -e prod

# Validate and attempt to fix common issues
./scripts/validate-deployment.sh -e staging --fix
```

### Environment Configuration Comparison

Compare configurations across environments:

```bash
# View side-by-side parameter comparison
./scripts/compare-environments.sh
```

### Environment-Specific Configuration

The infrastructure supports two methods for environment-specific configuration:

#### Method 1: SAM Configuration (Default)

Parameters are defined in `samconfig.toml` with environment-specific overrides:

```bash
# Deploy using samconfig.toml
./scripts/deploy.sh -e dev
./scripts/deploy.sh -e staging
./scripts/deploy.sh -e prod
```

#### Method 2: Parameter Files

Use JSON parameter files in the `parameters/` directory:

```bash
# Deploy using parameter files
./scripts/deploy.sh -e dev -f
./scripts/deploy.sh -e staging -f
./scripts/deploy.sh -e prod -f
```

See `parameters/README.md` for detailed parameter documentation.

## Recommended Deployment Workflow

### For Development

```bash
# 1. Validate environment setup
./scripts/check-environment.sh

# 2. Validate deployment readiness
./scripts/validate-deployment.sh -e dev

# 3. Deploy to development
./scripts/deploy.sh -e dev
```

### For Staging

```bash
# 1. Validate deployment readiness
./scripts/validate-deployment.sh -e staging

# 2. Preview changes (optional)
./scripts/deploy.sh -e staging --dry-run

# 3. Deploy to staging
./scripts/deploy.sh -e staging
```

### For Production

```bash
# 1. Compare environment configurations
./scripts/compare-environments.sh

# 2. Validate production deployment readiness
./scripts/validate-deployment.sh -e prod

# 3. Preview production changes
./scripts/deploy.sh -e prod --dry-run

# 4. Deploy to production (with safety prompts)
./scripts/deploy.sh -e prod
```

## Local Development

### Start Local API Gateway

```bash
# Start local API on port 3001
./scripts/local-test.sh api

# Start on custom port
./scripts/local-test.sh api -p 8080
```

### Test Lambda Functions Locally

```bash
# Build the application
./scripts/local-test.sh build

# Invoke a function
./scripts/local-test.sh invoke -f FunctionName

# Invoke with event file
./scripts/local-test.sh invoke -f FunctionName -e events/sample-event.json
```

### Local Testing Script Commands

```bash
Usage: ./scripts/local-test.sh [COMMAND] [OPTIONS]

Commands:
  api           Start SAM local API Gateway
  lambda        Start SAM local Lambda service
  invoke        Invoke a specific Lambda function
  build         Build the SAM application
  validate      Validate the SAM template
```

## Infrastructure Components

### DynamoDB Tables

#### Fund Table

- **Primary Key**: `fund_id` (String)
- **GSI**: `fund_type_index` - Query funds by type
- **GSI**: `status_index` - Query funds by status
- **Features**: Point-in-time recovery (prod), encryption at rest

#### User Table

- **Primary Key**: `user_id` (String)
- **GSI**: `email_index` - Unique email lookup
- **GSI**: `role_index` - Query users by role
- **Features**: Point-in-time recovery (prod), encryption at rest

#### Report Table

- **Primary Key**: `report_id` (String)
- **GSI**: `user_reports_index` - Query reports by user
- **GSI**: `report_type_index` - Query reports by type
- **Features**: Point-in-time recovery (prod), encryption at rest

### S3 Buckets

#### Static Assets Bucket

- **Purpose**: Frontend static files (React/Next.js build)
- **Features**: CloudFront integration, encryption at rest
- **Access**: Through CloudFront only (OAI configured)

#### Reports Bucket

- **Purpose**: Generated reports storage
- **Features**: Lifecycle policies, encryption at rest
- **Retention**: 7 years (prod), 90 days (dev/staging)

### API Gateway

- **Type**: REST API
- **Authentication**: AWS Cognito User Pool
- **CORS**: Configured for web frontend
- **Features**: Request validation, custom error responses

### CloudFront Distribution

- **Purpose**: Static asset CDN and web hosting
- **Features**: Custom domain support, SSL/TLS termination
- **Caching**: Optimized for static content
- **Error Handling**: SPA-friendly (404 → index.html)

### Cognito User Pool

- **Authentication**: Email-based login
- **Password Policy**: Strong requirements enforced
- **Features**: Email verification, user attributes
- **Integration**: JWT tokens for API access

## Configuration Parameters

The following parameters can be configured during deployment:

| Parameter             | Description              | Default | Dev   | Staging                  | Prod             |
| --------------------- | ------------------------ | ------- | ----- | ------------------------ | ---------------- |
| Environment           | Deployment environment   | dev     | dev   | staging                  | prod             |
| DomainName            | Custom domain name       | ""      | ""    | staging-api.fundflow.com | api.fundflow.com |
| CertificateArn        | ACM certificate ARN      | ""      | ""    | ""                       | arn:aws:acm:...  |
| LogLevel              | Lambda logging level     | INFO    | DEBUG | INFO                     | WARNING          |
| LambdaMemorySize      | Lambda memory (MB)       | 512     | 512   | 1024                     | 1024             |
| LambdaTimeout         | Lambda timeout (seconds) | 30      | 30    | 30                       | 60               |
| ApiThrottleBurstLimit | API burst limit          | 200     | 100   | 200                      | 500              |
| ApiThrottleRateLimit  | API rate limit (req/sec) | 100     | 50    | 100                      | 250              |

**Note:** CertificateArn is required if DomainName is provided

## Stack Outputs

After deployment, the following outputs are available:

- `ApiGatewayUrl`: API Gateway endpoint URL
- `CloudFrontUrl`: CloudFront distribution URL
- `FundTableName`: DynamoDB Fund table name
- `UserTableName`: DynamoDB User table name
- `ReportTableName`: DynamoDB Report table name
- `StaticAssetsBucketName`: S3 static assets bucket name
- `ReportsBucketName`: S3 reports bucket name
- `UserPoolId`: Cognito User Pool ID
- `UserPoolClientId`: Cognito User Pool Client ID

## Monitoring and Observability

### Lambda Powertools

All Lambda functions include AWS Lambda Powertools for:

- Structured logging
- Metrics collection
- Distributed tracing
- Event parsing and validation

### CloudWatch Integration

- Function logs automatically forwarded to CloudWatch
- Custom metrics namespace: `fundflow`
- Environment-specific log groups
- Configurable log levels

## Security

### Authentication & Authorization

- AWS Cognito for user management
- JWT tokens for API authentication
- Role-based access control

### Data Protection

- Encryption at rest for all DynamoDB tables
- S3 bucket encryption
- HTTPS enforced (CloudFront + API Gateway)
- Private S3 access through CloudFront OAI

### Network Security

- API Gateway with CORS configuration
- CloudFront security headers
- No direct public access to S3 buckets

## Cost Optimization

### Development Environment

- Pay-per-request DynamoDB billing
- Minimal CloudFront price class
- Reduced retention periods
- No point-in-time recovery

### Production Environment

- Optimized for performance and availability
- Extended retention for compliance
- Enhanced monitoring and backup

## Troubleshooting

### Common Issues

1. **SAM CLI not found**

   ```bash
   # Install SAM CLI
   pip install aws-sam-cli
   ```

2. **AWS credentials not configured**

   ```bash
   # Configure AWS CLI
   aws configure
   ```

3. **Docker not running**

   ```bash
   # Start Docker (varies by OS)
   docker --version
   ```

4. **Template validation errors**
   ```bash
   # Validate template
   sam validate --lint
   ```

### Deployment Failures

1. Check AWS credentials and permissions
2. Verify SAM template syntax
3. Check CloudFormation events in AWS Console
4. Review deployment logs

### Local Testing Issues

1. Ensure Docker is running
2. Check port availability
3. Verify Lambda function syntax
4. Review local invocation logs

## Next Steps

After successful infrastructure deployment:

1. **Backend Development**: Create Lambda functions for API endpoints
2. **Frontend Setup**: Configure Next.js application with Cognito
3. **Data Models**: Implement Python models for DynamoDB entities
4. **API Integration**: Connect frontend to deployed API Gateway
5. **Testing**: Set up integration and end-to-end tests

## Additional Resources

- [AWS SAM Documentation](https://docs.aws.amazon.com/serverless-application-model/)
- [AWS Lambda Powertools](https://awslabs.github.io/aws-lambda-powertools-python/)
- [DynamoDB Best Practices](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/best-practices.html)
- [API Gateway Documentation](https://docs.aws.amazon.com/apigateway/)
- [CloudFront Documentation](https://docs.aws.amazon.com/cloudfront/)
