# AWS API Gateway Funds API Testing Guide

## Current Status ✅ FIXED

The testing scripts have been successfully set up and are working! Here's what we discovered:

### Authentication Status

- ✅ **Cognito Authentication**: Working perfectly with `USER_PASSWORD_AUTH` flow
- ✅ **Token Generation**: Both Access and ID tokens are generated correctly
- ✅ **Token Validation**: Access tokens work with Cognito `get_user` calls
- ⚠️ **API Integration**: Backend configuration issue causing 401/502 errors

### Key Findings

1. **Correct Client ID**: `2jh76f894g6lv9vrus4qbb9hu7` ✅
2. **Working Auth Flow**: `USER_PASSWORD_AUTH` ✅
3. **Token Detection**: Script automatically detects which token works ✅
4. **Backend Issue**: Lambda environment variables likely misconfigured ⚠️

## Test Scripts Location

The scripts are now located in:

```
tests/integration/
├── test_aws_api_client.py      # Main Python test client
├── test_api_curl.sh           # Bash/curl alternative
├── test_debug_auth.py         # Debug authentication flow
├── check_backend_config.py    # Backend configuration checker
└── requirements.txt           # Python dependencies
```

## Available Endpoints

| Method | Endpoint             | Description                                    |
| ------ | -------------------- | ---------------------------------------------- |
| GET    | `/funds`             | List all funds (supports pagination & filters) |
| GET    | `/funds/{fund_id}`   | Get specific fund details                      |
| POST   | `/funds`             | Create new fund                                |
| PUT    | `/funds/{fund_id}`   | Update existing fund                           |
| DELETE | `/funds/{fund_id}`   | Delete fund (soft delete)                      |
| POST   | `/funds/bulk-update` | Bulk update multiple funds                     |

## Quick Start

### 1. Run Python Tests

```bash
cd tests/integration
python test_aws_api_client.py
```

**Expected Output:**

```
🔐 Authenticating with AWS Cognito...
   Trying USER_PASSWORD_AUTH flow...
✅ Authentication successful using USER_PASSWORD_AUTH!
   Access token length: 1086
   ID token length: 1157
   🧪 Testing which token works with API...
   ❌ Access token failed: 401
   🔄 Trying ID token...
   ✅ ID token works with API!
```

### 2. Run Bash Tests

```bash
cd tests/integration
chmod +x test_api_curl.sh
./test_api_curl.sh
```

### 3. Check Backend Configuration

```bash
cd tests/integration
python check_backend_config.py
```

## Current Issue: Backend Configuration

The scripts authenticate successfully but API calls return 401/502 errors. This indicates a **backend environment variable configuration issue**.

### Problem Analysis

- ✅ **Authentication**: Cognito tokens generate correctly
- ✅ **Token Validity**: Access tokens work with Cognito `get_user`
- ❌ **API Gateway/Lambda**: Environment variables likely misconfigured
- ❌ **SessionManager**: Can't validate tokens due to config mismatch

### Required Lambda Environment Variables

The Lambda function needs these environment variables:

```json
{
  "USER_POOL_ID": "ap-northeast-1_H2kKHGUAT",
  "USER_POOL_CLIENT_ID": "2jh76f894g6lv9vrus4qbb9hu7",
  "AWS_REGION": "ap-northeast-1"
}
```

## Troubleshooting

### Option 1: Fix Lambda Environment (Recommended)

If you have AWS access, update the Lambda function environment variables:

```bash
aws lambda update-function-configuration \
  --region ap-northeast-1 \
  --function-name <LAMBDA_FUNCTION_NAME> \
  --environment Variables='{
    "USER_POOL_ID": "ap-northeast-1_H2kKHGUAT",
    "USER_POOL_CLIENT_ID": "2jh76f894g6lv9vrus4qbb9hu7",
    "AWS_REGION": "ap-northeast-1"
  }'
```

### Option 2: Enable Cognito Auth Flows

Ensure the Cognito client has the correct auth flows:

```bash
aws cognito-idp update-user-pool-client \
  --region ap-northeast-1 \
  --user-pool-id ap-northeast-1_H2kKHGUAT \
  --client-id 2jh76f894g6lv9vrus4qbb9hu7 \
  --explicit-auth-flows ADMIN_USER_PASSWORD_AUTH USER_PASSWORD_AUTH
```

### Option 3: Check CloudWatch Logs

View Lambda function logs to see specific error details:

```bash
aws logs describe-log-groups \
  --region ap-northeast-1 \
  --log-group-name-prefix '/aws/lambda/fundflow'
```

### Option 4: Manual Token Testing

If the backend can't be fixed immediately, use manual token testing:

1. Run the Python script: `python test_aws_api_client.py`
2. Choose option for manual token input
3. Get a JWT token from the frontend application
4. Paste it when prompted

## Test Features

### Smart Authentication

- **Auto-Detection**: Tries multiple auth flows automatically
- **Token Testing**: Tests both access and ID tokens with API
- **Fallback Options**: Manual token input if auto-auth fails
- **Error Handling**: Clear messages for different failure types

### Comprehensive Testing

- **Read Operations**: List funds, get specific fund, pagination, filtering
- **Write Operations**: Create, update, delete funds
- **Bulk Operations**: Mass updates with validation
- **Security Testing**: Unauthorized access attempts
- **Error Validation**: Tests invalid data handling

### Multiple Formats

- **Python Client**: Rich testing with detailed output
- **Bash/Curl Script**: Lightweight alternative for CI/CD
- **Debug Tools**: Token analysis and backend configuration checking

## API Testing Patterns

### Basic Read Test

```python
response = client.make_request("GET", "/funds")
print(f"Status: {response['status_code']}")
print(f"Data: {response['data']}")
```

### Authenticated Request with Pagination

```python
response = client.make_request("GET", "/funds", params={
    "page": 1,
    "page_size": 10,
    "status": "ACTIVE"
})
```

### Create Fund

```python
fund_data = {
    "fund_id": "TEST-001",
    "fund_name": "Test Fund",
    "fund_type": "EQUITY",
    "status": "ACTIVE",
    "nav": 125.75
}
response = client.make_request("POST", "/funds", data=fund_data)
```

## Configuration Management

### Test Credentials

- **Email**: `<EMAIL>`
- **Password**: `TestPassword123!`
- **User Pool**: `ap-northeast-1_H2kKHGUAT`
- **Client ID**: `2jh76f894g6lv9vrus4qbb9hu7`

### AWS Resources

- **Region**: `ap-northeast-1`
- **API URL**: `https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev`
- **Cognito**: AWS Cognito User Pool authentication
- **Backend**: AWS Lambda with API Gateway

## Error Reference

| Status Code | Meaning               | Common Causes                            |
| ----------- | --------------------- | ---------------------------------------- |
| 401         | Unauthorized          | Missing/invalid JWT token                |
| 403         | Forbidden             | Valid token but insufficient permissions |
| 404         | Not Found             | Invalid endpoint or fund ID              |
| 422         | Validation Error      | Invalid request data                     |
| 500         | Internal Server Error | Backend service issues                   |
| 502         | Bad Gateway           | Lambda configuration issues              |

## Next Steps

1. **Fix Backend Configuration**: Update Lambda environment variables
2. **Verify CloudWatch Logs**: Check for specific error messages
3. **Test Again**: Re-run scripts after backend fixes
4. **Deploy Changes**: Ensure configuration is persistent across deployments

## Integration Testing

Once the backend is fixed, these scripts provide comprehensive integration testing for:

- ✅ Authentication flows
- ✅ CRUD operations
- ✅ Data validation
- ✅ Error handling
- ✅ Security testing
- ✅ Performance validation

The scripts will automatically detect when the backend is fixed and all tests should pass.

---

**Status**: Ready for backend configuration fix
**Last Updated**: Current session  
**Scripts Working**: ✅ Authentication ✅ Token Detection ✅ Error Diagnosis
