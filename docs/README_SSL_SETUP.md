# SSL Certificate Setup for FundFlow

## 🚀 Quick Start

To resolve `UNABLE_TO_GET_ISSUER_CERT_LOCALLY` errors in your Node.js application, follow these steps:

### 1. Run the SSL Setup Script

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies if not already done
npm install

# Run the SSL setup script
npm run setup:ssl

# Validate the SSL configuration
npm run validate:ssl
```

### 2. Start Development with SSL Configuration

```bash
# Option 1: Use SSL-configured development server
npm run dev:ssl

# Option 2: Use the regular development server (SSL disabled)
npm run dev

# Option 3: Use secure development server
npm run dev:secure
```

## 📋 What the Setup Does

The SSL setup script automatically:

1. **Downloads CA Certificates**: Gets the latest Mozilla CA certificate bundle
2. **Creates Certificate Directory**: Sets up `./certs/` directory
3. **Validates Certificates**: Ensures the downloaded bundle is valid
4. **Configures Environment**: Creates SSL configuration files
5. **Updates Scripts**: Adds SSL-aware npm scripts

## 🔧 Manual Setup (if automatic setup fails)

### Step 1: Create Certificate Directory

```bash
mkdir -p frontend/certs
```

### Step 2: Download CA Bundle

```bash
# Download Mozilla's CA certificate bundle
curl -o frontend/certs/cacert.pem https://curl.se/ca/cacert.pem

# Verify the download
ls -la frontend/certs/cacert.pem
```

### Step 3: Update Environment Variables

Add to your `frontend/.env.local`:

```env
# SSL Configuration
SSL_CA_BUNDLE_PATH=./certs/cacert.pem
SSL_USE_SYSTEM_CA=true
SSL_TIMEOUT=10000

# For development only - uncomment to disable SSL verification (NOT RECOMMENDED)
# NODE_TLS_REJECT_UNAUTHORIZED=0
```

### Step 4: Use SSL Configuration in Your Code

```typescript
// Import SSL configuration
import { configureGlobalSSL, secureFetch } from '@/lib/ssl-config';

// Configure SSL globally (do this once at app startup)
configureGlobalSSL();

// Use secure fetch for HTTPS requests
const response = await secureFetch('https://api.example.com/data', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-token',
  },
});
```

## 🔍 Testing Your SSL Configuration

### Test SSL Connectivity

```bash
# Validate SSL configuration
npm run validate:ssl

# Test specific endpoints manually
node -e "
const https = require('https');
const fs = require('fs');

const options = {
  hostname: 'b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com',
  port: 443,
  path: '/dev/funds',
  method: 'HEAD',
  ca: fs.readFileSync('./certs/cacert.pem', 'utf8')
};

https.request(options, (res) => {
  console.log('✅ SSL connection successful:', res.statusCode);
}).on('error', (err) => {
  console.error('❌ SSL connection failed:', err.message);
}).end();
"
```

### Test in Browser

1. Start your development server: `npm run dev:ssl`
2. Open browser to `http://localhost:3000`
3. Check browser console for SSL-related messages
4. Test API calls to AWS services

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### 1. "UNABLE_TO_GET_ISSUER_CERT_LOCALLY"

**Solution**: Run the SSL setup script
```bash
npm run setup:ssl
```

#### 2. "Certificate has expired"

**Solution**: Update CA certificates
```bash
# Re-download fresh certificates
rm -f frontend/certs/cacert.pem
npm run setup:ssl
```

#### 3. "Connection timeout"

**Solution**: Increase timeout or check network
```env
# In .env.local, increase timeout
SSL_TIMEOUT=30000
```

#### 4. Corporate Network Issues

**Solution**: Contact IT for corporate CA certificates
```bash
# Add corporate CA to bundle
cat corporate-ca.pem >> frontend/certs/cacert.pem
```

### Debug Commands

```bash
# Check Node.js and OpenSSL versions
node -e "console.log(process.versions)"

# Test basic connectivity
curl -I https://aws.amazon.com

# Check certificate details
openssl x509 -in frontend/certs/cacert.pem -text -noout | head -20

# Test with verbose output
NODE_DEBUG=tls npm run dev:ssl
```

## 📁 File Structure

After setup, your project will have:

```
frontend/
├── certs/
│   ├── cacert.pem              # CA certificate bundle
│   └── ssl-config.env          # SSL configuration
├── scripts/
│   ├── setup-ssl-certificates.js  # SSL setup script
│   └── validate-ssl.js            # SSL validation script
├── src/lib/
│   └── ssl-config.ts              # SSL configuration module
├── docs/
│   └── SSL_CONFIGURATION.md       # Detailed documentation
├── .env.local                     # Environment variables
└── package.json                   # Updated with SSL scripts
```

## 🔒 Security Notes

1. **Never disable SSL verification in production**
2. **Keep CA certificates updated regularly**
3. **Use environment variables for sensitive configuration**
4. **Monitor SSL certificate expiration**
5. **Log SSL errors for debugging**

## 📚 Additional Resources

- [SSL Configuration Documentation](./docs/SSL_CONFIGURATION.md)
- [Node.js TLS Documentation](https://nodejs.org/api/tls.html)
- [Mozilla CA Certificate Store](https://curl.se/docs/caextract.html)

## 🆘 Getting Help

If you continue to experience SSL issues:

1. **Check the logs**: Look for specific error codes in console
2. **Run validation**: Use `npm run validate:ssl` for diagnostics
3. **Test connectivity**: Try `curl -I https://aws.amazon.com`
4. **Corporate environment**: Contact your IT department
5. **Update dependencies**: Ensure Node.js and npm are current

## 🎯 Next Steps

After SSL setup is complete:

1. **Test your application**: Ensure all AWS API calls work
2. **Update CI/CD**: Add SSL setup to deployment scripts
3. **Monitor certificates**: Set up alerts for certificate expiration
4. **Document for team**: Share this setup with other developers
