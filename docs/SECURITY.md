# FundFlow Security Documentation

This document outlines the security measures implemented in the FundFlow application to protect against common threats and ensure data integrity.

## Table of Contents

1. [Authentication & Authorization](#authentication--authorization)
2. [Session Management](#session-management)
3. [Rate Limiting](#rate-limiting)
4. [CSRF Protection](#csrf-protection)
5. [Input Validation & Sanitization](#input-validation--sanitization)
6. [Security Headers](#security-headers)
7. [Deployment Security](#deployment-security)
8. [Monitoring & Alerting](#monitoring--alerting)
9. [Incident Response](#incident-response)

## Authentication & Authorization

### AWS Cognito Integration

FundFlow uses AWS Cognito User Pools for authentication and authorization:

- **User Pool**: Centralized user directory with built-in security features
- **User Pool Client**: Application configuration for secure authentication
- **JWT Tokens**: Stateless authentication using JSON Web Tokens
- **API Gateway Authorizer**: Automatic token validation for protected endpoints

### Password Policy

Cognito enforces the following password requirements:

- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character
- Cannot be a commonly used password

### Multi-Factor Authentication (MFA)

- Optional MFA via SMS or TOTP
- Can be enabled per user or enforced globally
- Configurable in AWS Cognito console

## Session Management

### Session Validation

The `SessionManager` class provides comprehensive session validation:

```python
from src.shared.security.session_manager import SessionManager

session_manager = SessionManager()
result = session_manager.validate_session(event)

if result['valid']:
    user_info = result['user_info']
    session_info = result['session_info']
    # Process authenticated request
else:
    # Handle invalid/expired session
```

### Session Features

- **Automatic Token Validation**: Validates JWT tokens with AWS Cognito
- **Session Timeout Detection**: Configurable session timeouts (default: 1 hour)
- **Refresh Notifications**: Warns when session needs refreshing (15 minutes before expiry)
- **Secure Logout**: Revokes tokens and invalidates sessions
- **Activity Tracking**: Logs session activity for audit purposes

### Configuration

Set environment variables to configure session behavior:

```env
SESSION_TIMEOUT_SECONDS=3600      # 1 hour default
REFRESH_THRESHOLD_SECONDS=900     # 15 minutes before expiry
USER_POOL_ID=your-user-pool-id
USER_POOL_CLIENT_ID=your-client-id
```

## Rate Limiting

### Protection Against Abuse

The `RateLimiter` class prevents brute force attacks and API abuse:

- **Per-IP Rate Limiting**: Tracks requests by client IP address
- **Method-Specific Limits**: Different limits for different HTTP methods
- **Sliding Window**: 60-second sliding window for rate calculations
- **Automatic Cleanup**: Removes old entries to prevent memory leaks

### Rate Limits by HTTP Method

| Method | Requests per Minute | Use Case       |
| ------ | ------------------- | -------------- |
| GET    | 60                  | Data retrieval |
| POST   | 10                  | Data creation  |
| PUT    | 10                  | Data updates   |
| DELETE | 5                   | Data deletion  |

### Customization

Modify limits in `src/shared/security/session_manager.py`:

```python
self.max_requests_per_window = {
    'POST': 15,    # Increase POST limit
    'PUT': 15,     # Increase PUT limit
    'DELETE': 3,   # Decrease DELETE limit
    'GET': 100     # Increase GET limit
}
```

## CSRF Protection

### Cross-Site Request Forgery Prevention

The `CSRFProtection` class protects against CSRF attacks:

- **Token Generation**: Creates cryptographically secure CSRF tokens
- **Token Validation**: Validates tokens on state-changing operations
- **Time-Based Expiry**: Tokens expire after 1 hour
- **Signature Verification**: HMAC signatures prevent token tampering

### Implementation

CSRF tokens are required for POST, PUT, and DELETE requests:

```javascript
// Client-side: Include CSRF token in headers
headers: {
    'X-CSRF-Token': csrfToken,
    'Authorization': `Bearer ${accessToken}`
}
```

### Exemptions

The following endpoints bypass CSRF protection:

- `/auth/*` - Authentication endpoints
- Public read-only endpoints

### Configuration

Set the CSRF secret key:

```env
CSRF_SECRET_KEY=your-secret-key-change-in-production
```

## Input Validation & Sanitization

### XSS Prevention

The `SecurityEnforcer` class sanitizes all input data:

- **Recursive Sanitization**: Processes nested objects and arrays
- **Script Tag Removal**: Removes `<script>` tags and JavaScript
- **Event Handler Blocking**: Prevents `onclick`, `onerror`, etc.
- **URL Scheme Filtering**: Blocks `javascript:` and `data:` URLs

### Dangerous Content Detection

The following patterns are detected and removed:

- `<script>` tags
- `javascript:` URLs
- HTML event handlers (`onclick`, `onload`, etc.)
- `<img>` tags with `onerror` handlers
- Other XSS vectors

### Validation Models

Pydantic models provide additional validation:

```python
from pydantic import EmailStr, validator

class UserRequest(BaseModel):
    email: EmailStr
    password: str

    @validator('password')
    def password_strength(cls, v):
        if len(v) < 8:
            raise ValueError('Password too short')
        return v
```

## Security Headers

### HTTP Security Headers

The application includes security headers in all responses:

```python
headers = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'",
    'Referrer-Policy': 'strict-origin-when-cross-origin'
}
```

### CORS Configuration

Cross-Origin Resource Sharing is configured securely:

```python
cors_headers = {
    'Access-Control-Allow-Origin': 'https://yourdomain.com',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type,Authorization,X-CSRF-Token',
    'Access-Control-Max-Age': '86400'
}
```

## Deployment Security

### Environment Variables

Secure configuration management:

```yaml
# CloudFormation template.yaml
Environment:
  Variables:
    # ✅ Safe to include in template
    SESSION_TIMEOUT_SECONDS: "3600"
    LOG_LEVEL: "INFO"

    # ❌ Never include in template - use Parameter Store or Secrets Manager
    # DATABASE_PASSWORD: "secret"
    # API_KEYS: "sensitive"
```

### AWS IAM Policies

Minimal IAM permissions for Lambda functions:

```yaml
Policies:
  - Statement:
      - Effect: Allow
        Action:
          - cognito-idp:GetUser
          - cognito-idp:GlobalSignOut
          - cognito-idp:ForgotPassword
          - cognito-idp:ConfirmForgotPassword
          - cognito-idp:ChangePassword
        Resource: !GetAtt UserPool.Arn
      - Effect: Allow
        Action:
          - logs:CreateLogGroup
          - logs:CreateLogStream
          - logs:PutLogEvents
        Resource: !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:*"
```

### Encryption

- **Data in Transit**: All communications use HTTPS/TLS
- **Data at Rest**: DynamoDB encryption enabled
- **Lambda Environment**: Environment variables encrypted with KMS
- **S3 Buckets**: Server-side encryption enabled

### Network Security

- **VPC Configuration**: Lambda functions in private subnets (if needed)
- **Security Groups**: Minimal port access
- **NACLs**: Network-level traffic filtering
- **WAF**: Web Application Firewall for additional protection

## Monitoring & Alerting

### CloudWatch Metrics

Security-related metrics are tracked:

```python
# Rate limiting violations
metrics.add_metric(name="RateLimitViolations", unit=MetricUnit.Count, value=1)

# CSRF violations
metrics.add_metric(name="CSRFViolations", unit=MetricUnit.Count, value=1)

# Session expiration events
metrics.add_metric(name="ExpiredSessions", unit=MetricUnit.Count, value=1)

# Successful logouts
metrics.add_metric(name="SuccessfulLogouts", unit=MetricUnit.Count, value=1)
```

### Alerting Setup

Create CloudWatch alarms for security events:

```yaml
SecurityAlarm:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: !Sub "${AWS::StackName}-SecurityViolations"
    MetricName: RateLimitViolations
    Namespace: FundFlow
    Statistic: Sum
    Period: 300
    Threshold: 10
    ComparisonOperator: GreaterThanThreshold
    AlarmActions:
      - !Ref SecurityNotificationTopic
```

### Log Analysis

Security events are logged with structured data:

```python
logger.warning("Rate limit exceeded", extra={
    "client_ip": client_ip,
    "method": http_method,
    "endpoint": path,
    "user_agent": user_agent,
    "timestamp": timestamp
})
```

## Incident Response

### Security Event Types

Monitor for these security events:

1. **Authentication Failures**

   - Multiple failed login attempts
   - Invalid token usage
   - Session hijacking attempts

2. **Rate Limiting Violations**

   - Excessive API requests
   - Brute force attack patterns
   - Bot traffic

3. **Input Validation Failures**

   - XSS attempt detection
   - SQL injection attempts
   - Malformed requests

4. **Authorization Failures**
   - Privilege escalation attempts
   - Access to unauthorized resources
   - Token manipulation

### Response Procedures

1. **Immediate Response**

   - Block suspicious IP addresses
   - Revoke compromised tokens
   - Scale up rate limiting

2. **Investigation**

   - Analyze CloudWatch logs
   - Check AWS CloudTrail
   - Review user behavior patterns

3. **Mitigation**

   - Deploy security patches
   - Update security rules
   - Implement additional monitoring

4. **Recovery**
   - Restore affected services
   - Validate system integrity
   - Communicate with stakeholders

## Testing Security Features

### Unit Tests

Run security tests:

```bash
# Test all security components
pytest tests/test_security.py -v

# Test specific security feature
pytest tests/test_security.py::TestRateLimiter -v
```

### Integration Tests

Test the complete security stack:

```bash
# Test authentication flow
pytest tests/test_auth_api.py::TestAuthSecurity -v

# Test session management
pytest tests/test_security.py::TestSessionManager -v
```

### Penetration Testing

Regular security assessments should include:

- **OWASP Top 10** vulnerability testing
- **Rate limiting** effectiveness
- **Input validation** bypass attempts
- **Session management** security
- **CSRF protection** validation

## Best Practices

### Development

1. **Secure Coding**

   - Always validate input data
   - Use parameterized queries
   - Implement proper error handling
   - Follow principle of least privilege

2. **Testing**

   - Write security-focused tests
   - Include negative test cases
   - Test edge cases and boundary conditions
   - Perform regular security reviews

3. **Code Reviews**
   - Review security-sensitive code changes
   - Check for credential exposure
   - Validate input handling
   - Verify authorization logic

### Operations

1. **Monitoring**

   - Set up comprehensive alerting
   - Monitor security metrics
   - Review logs regularly
   - Track failed authentication attempts

2. **Updates**

   - Keep dependencies updated
   - Apply security patches promptly
   - Review security advisories
   - Update security configurations

3. **Backup & Recovery**
   - Regular security audits
   - Incident response planning
   - Data backup procedures
   - Disaster recovery testing

## Compliance Considerations

### Data Protection

- **GDPR**: User data protection and privacy rights
- **CCPA**: California consumer privacy compliance
- **HIPAA**: Healthcare data protection (if applicable)
- **SOX**: Financial data controls (if applicable)

### Security Standards

- **OWASP**: Web application security guidelines
- **NIST**: Cybersecurity framework
- **ISO 27001**: Information security management
- **AWS Security**: Cloud security best practices

---

## Support

For security questions or to report vulnerabilities:

- **Security Team**: <EMAIL>
- **Emergency**: +1-XXX-XXX-XXXX
- **Documentation**: Internal security wiki
- **Training**: Security awareness program

**Last Updated**: [Current Date]  
**Version**: 1.0  
**Next Review**: [Date + 6 months]
