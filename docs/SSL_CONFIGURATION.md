# SSL Configuration Guide for FundFlow

This guide helps you resolve `UNABLE_TO_GET_ISSUER_CERT_LOCALLY` errors and properly configure SSL certificates for your Node.js application.

## Quick Start

### 1. Automatic Setup (Recommended)

```bash
# Navigate to frontend directory
cd frontend

# Run the SSL setup script
npm run setup:ssl

# Validate the configuration
npm run validate:ssl

# Start development with SSL configuration
npm run dev:ssl
```

### 2. Manual Setup

If the automatic setup doesn't work, follow these manual steps:

1. **Download CA Certificates**:
   ```bash
   mkdir -p certs
   curl -o certs/cacert.pem https://curl.se/ca/cacert.pem
   ```

2. **Update Environment Variables**:
   Add to your `.env.local`:
   ```env
   SSL_CA_BUNDLE_PATH=./certs/cacert.pem
   SSL_USE_SYSTEM_CA=true
   SSL_TIMEOUT=10000
   ```

3. **Use SSL Configuration in Code**:
   ```typescript
   import { configureGlobalSSL, secureFetch } from './lib/ssl-config';
   
   // Configure SSL globally
   configureGlobalSSL();
   
   // Use secure fetch for HTTPS requests
   const response = await secureFetch('https://api.example.com/data');
   ```

## Understanding the Error

The `UNABLE_TO_GET_ISSUER_CERT_LOCALLY` error occurs when:

1. **Missing Certificate Chain**: The server's certificate chain is incomplete
2. **Corporate Firewall**: Your organization intercepts SSL traffic
3. **Outdated CA Certificates**: System CA certificates are outdated
4. **Self-Signed Certificates**: The server uses self-signed certificates
5. **Proxy Issues**: Network proxy interferes with SSL verification

## Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SSL_CA_BUNDLE_PATH` | Path to custom CA certificate bundle | `./certs/cacert.pem` |
| `SSL_USE_SYSTEM_CA` | Use system CA certificates | `true` |
| `SSL_TIMEOUT` | SSL connection timeout (ms) | `10000` |
| `NODE_TLS_REJECT_UNAUTHORIZED` | Reject unauthorized certificates | `1` (enabled) |

### Development vs Production

**Development Mode**:
- More lenient SSL verification
- Custom CA bundle support
- Detailed error logging
- Option to disable verification (not recommended)

**Production Mode**:
- Strict SSL verification
- Always reject unauthorized certificates
- Minimal error exposure
- Performance optimized

## Troubleshooting

### Common Errors and Solutions

#### 1. UNABLE_TO_GET_ISSUER_CERT_LOCALLY

**Symptoms**: SSL requests fail with certificate verification errors

**Solutions**:
```bash
# Option 1: Run SSL setup
npm run setup:ssl

# Option 2: Update CA certificates
npm run setup:ssl --force

# Option 3: Validate current setup
npm run validate:ssl

# Option 4: Use development mode (temporary)
npm run dev
```

#### 2. CERT_HAS_EXPIRED

**Symptoms**: Certificate expiration errors

**Solutions**:
```bash
# Update CA certificate bundle
npm run setup:ssl

# Check system date/time
date

# Validate specific endpoints
npm run validate:ssl
```

#### 3. TIMEOUT Errors

**Symptoms**: SSL connections timeout

**Solutions**:
```env
# Increase timeout in .env.local
SSL_TIMEOUT=30000
```

```bash
# Check network connectivity
curl -I https://aws.amazon.com

# Test with different endpoints
npm run validate:ssl
```

#### 4. Corporate Environment Issues

**Symptoms**: SSL errors in corporate networks

**Solutions**:
1. **Contact IT Department**: Get corporate CA certificates
2. **Proxy Configuration**: Configure proxy settings
3. **Custom CA Bundle**: Install corporate certificates

```bash
# Add corporate CA to bundle
cat corporate-ca.pem >> certs/cacert.pem

# Validate with corporate endpoints
npm run validate:ssl
```

### Debugging Commands

```bash
# Check Node.js SSL configuration
node -e "console.log(process.versions)"

# Test specific endpoint
node -e "
const https = require('https');
https.get('https://aws.amazon.com', (res) => {
  console.log('Status:', res.statusCode);
  console.log('Certificate:', res.socket.getPeerCertificate().subject);
}).on('error', console.error);
"

# Validate CA bundle
openssl x509 -in certs/cacert.pem -text -noout | head -20
```

## Advanced Configuration

### Custom CA Certificates

If you need to add custom CA certificates:

1. **Create Custom Bundle**:
   ```bash
   # Combine system and custom CAs
   cat /etc/ssl/certs/ca-certificates.crt > certs/custom-ca-bundle.pem
   cat your-custom-ca.pem >> certs/custom-ca-bundle.pem
   ```

2. **Update Configuration**:
   ```env
   SSL_CA_BUNDLE_PATH=./certs/custom-ca-bundle.pem
   ```

### Programmatic Configuration

```typescript
import { createSecureHttpsAgent, secureFetch } from './lib/ssl-config';

// Create custom HTTPS agent
const agent = createSecureHttpsAgent();

// Use with fetch
const response = await fetch('https://api.example.com', {
  agent: agent, // Node.js only
});

// Or use secure fetch wrapper
const response = await secureFetch('https://api.example.com');
```

## Security Best Practices

1. **Never Disable SSL Verification in Production**
2. **Keep CA Certificates Updated**
3. **Use Specific CA Bundles When Possible**
4. **Monitor Certificate Expiration**
5. **Log SSL Errors for Debugging**

## Scripts Reference

| Script | Purpose |
|--------|---------|
| `npm run setup:ssl` | Download and configure CA certificates |
| `npm run validate:ssl` | Test SSL configuration |
| `npm run dev:ssl` | Start development with SSL config |
| `npm run dev` | Start development (SSL disabled) |

## Getting Help

If you continue to experience SSL issues:

1. **Check the logs**: Look for specific error codes
2. **Run validation**: Use `npm run validate:ssl`
3. **Test connectivity**: Try `curl -I https://aws.amazon.com`
4. **Corporate environment**: Contact your IT department
5. **Update dependencies**: Ensure Node.js and npm are current

## Related Files

- `lib/ssl-config.ts` - SSL configuration module
- `scripts/setup-ssl-certificates.js` - SSL setup script
- `scripts/validate-ssl.js` - SSL validation script
- `next.config.ts` - Next.js SSL configuration
- `.env.local` - Environment variables
