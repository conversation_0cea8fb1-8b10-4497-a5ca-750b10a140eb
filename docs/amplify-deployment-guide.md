# AWS Amplify Deployment Guide for FundFlow Frontend

## Prerequisites

1. AWS Account with appropriate permissions
2. Backend API already deployed (currently at `https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev`)
3. AWS Cognito User Pool configured (ID: `ap-northeast-1_H2kKHGUAT`)

## Step 1: Connect GitHub Repository to AWS Amplify

1. Go to AWS Amplify Console (https://console.aws.amazon.com/amplify/)
2. Click "New app" → "Host web app"
3. Select "GitHub" as source provider
4. Authorize AWS Amplify to access your GitHub account
5. Select your repository and branch (e.g., `main` or `dev`)

## Step 2: Configure Build Settings

AWS Amplify will detect the Next.js app. Ensure these settings:

1. **App build specification**: Use the `amplify.yml` file we created
2. **Build settings**:
   - Base directory: `frontend`
   - Build command: `npm run build`
   - Build output directory: `.next`

## Step 3: Configure Environment Variables

In the Amplify Console, go to "Environment variables" and add:

```
NEXT_PUBLIC_API_BASE_URL=https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev
NEXT_PUBLIC_AWS_REGION=ap-northeast-1
NEXT_PUBLIC_USE_AWS_API=true
NEXTAUTH_SECRET=<generate-a-secure-secret>
NEXTAUTH_URL=https://<your-amplify-domain>
COGNITO_CLIENT_ID=2jh76f894g6lv9vrus4qbb9hu7
COGNITO_ISSUER=https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_H2kKHGUAT
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_DEBUG=false
NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=false
NEXT_PUBLIC_ENABLE_MOCK_LOGIN=false
NODE_ENV=production
```

## Step 4: Update Cognito Callback URLs

After deployment, you'll get an Amplify domain (e.g., `https://main.d1234567890.amplifyapp.com`).

Update your Cognito User Pool App Client:

1. Go to AWS Cognito Console
2. Select your User Pool (`ap-northeast-1_H2kKHGUAT`)
3. Go to "App integration" → "App clients"
4. Add these URLs:
   - Callback URL: `https://<your-amplify-domain>/api/auth/callback/cognito`
   - Sign out URL: `https://<your-amplify-domain>`

## Step 5: Deploy

1. Click "Save and deploy" in Amplify Console
2. Monitor the build progress
3. Once deployed, test the application

## Step 6: Custom Domain (Optional)

1. In Amplify Console, go to "Domain management"
2. Add your custom domain
3. Follow DNS configuration instructions
4. Update environment variables:
   - `NEXTAUTH_URL=https://your-custom-domain.com`
5. Update Cognito callback URLs with your custom domain

## Post-Deployment Checklist

- [ ] Test authentication flow (sign in/sign out)
- [ ] Verify API calls to backend are working
- [ ] Check that funds list loads correctly
- [ ] Test creating/editing funds
- [ ] Verify PDF upload functionality
- [ ] Check responsive design on mobile devices

## Troubleshooting

### Authentication Issues
- Ensure `NEXTAUTH_URL` matches your Amplify domain exactly
- Verify Cognito callback URLs are correctly configured
- Check browser console for any CORS errors

### API Connection Issues
- Verify `NEXT_PUBLIC_API_BASE_URL` is correct
- Check API Gateway CORS configuration
- Ensure Lambda functions have proper permissions

### Build Failures
- Check build logs in Amplify Console
- Ensure all dependencies are in `package.json`
- Verify Node.js version compatibility

## Monitoring

1. Enable CloudWatch logs in Amplify
2. Set up alarms for:
   - Build failures
   - High error rates
   - Performance issues

## CI/CD Pipeline

Amplify automatically builds and deploys on every push to the connected branch. To set up staging environments:

1. Create a new branch (e.g., `staging`)
2. In Amplify, add a new environment connected to this branch
3. Use different environment variables for staging vs production