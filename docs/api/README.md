# FundFlow API Gateway Configuration

This document describes the API Gateway setup for the FundFlow application.

## Overview

The FundFlow API is built using AWS SAM (Serverless Application Model) and provides a RESTful interface for fund management operations. The API is secured using AWS Cognito User Pools and follows REST conventions.

## API Structure

### Base URL

- **Development**: `https://api.fundflow.dev.com/v1`
- **Production**: `https://api.fundflow.com/v1`

### Authentication

All endpoints (except `/health`) require JWT authentication via AWS Cognito User Pools.

**Authorization Header Format:**

```
Authorization: Bearer <JWT_TOKEN>
```

### CORS Configuration

The API Gateway is configured with CORS to allow cross-origin requests:

- **Allowed Origins**: `*` (configurable per environment)
- **Allowed Methods**: `GET, POST, PUT, DELETE, OPTIONS`
- **Allowed Headers**: `Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token`

## Endpoints

### Health Check

- **GET** `/health` - Check API health status (no authentication required)

### Fund Management

- **GET** `/funds` - List funds with optional filtering and pagination
- **POST** `/funds` - Create a new fund (requires admin or fund_manager role)
- **GET** `/funds/{fundId}` - Get specific fund details
- **PUT** `/funds/{fundId}` - Update fund (requires admin or fund_manager role)
- **DELETE** `/funds/{fundId}` - Soft delete fund (requires admin role)

## Lambda Functions

### HealthCheckFunction

- **Handler**: `functions.api.health.handler`
- **Memory**: 512 MB
- **Timeout**: 30 seconds
- **Purpose**: Provides health status and basic connectivity checks

### FundsAPIFunction

- **Handler**: `functions.api.funds.handler`
- **Memory**: 1024 MB
- **Timeout**: 30 seconds
- **Purpose**: Handles all fund-related CRUD operations

## Request/Response Format

### Standard Response Structure

```json
{
  "data": {}, // Response data
  "message": "Optional message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Response Structure

```json
{
  "error": "ERROR_CODE",
  "message": "Human-readable error message",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "details": {} // Optional additional error details
}
```

### Pagination Response Structure

```json
{
  "data": {
    "data": [], // Array of items
    "pagination": {
      "count": 20,
      "has_more": true,
      "last_key": "pagination_token"
    }
  }
}
```

## Error Handling

### HTTP Status Codes

- **200**: Success
- **201**: Created
- **204**: No Content
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **409**: Conflict
- **422**: Validation Error
- **500**: Internal Server Error

### Error Types

- `BAD_REQUEST`: Invalid request parameters
- `UNAUTHORIZED`: Missing or invalid authentication
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `CONFLICT`: Resource already exists
- `VALIDATION_ERROR`: Request validation failed
- `INTERNAL_SERVER_ERROR`: Unexpected server error

## Security Features

### Cognito Integration

- User authentication via AWS Cognito User Pools
- JWT token validation on protected endpoints
- Role-based access control (RBAC)

### User Roles

- **admin**: Full access to all operations
- **fund_manager**: Can create and update funds
- **analyst**: Read-only access to funds
- **viewer**: Limited read access

### Rate Limiting

API Gateway includes built-in throttling and rate limiting:

- **Burst limit**: 5000 requests
- **Rate limit**: 2000 requests per second

## Monitoring and Observability

### AWS Lambda Powertools

All Lambda functions use AWS Lambda Powertools for:

- **Structured logging**: JSON-formatted logs with correlation IDs
- **Metrics**: Custom CloudWatch metrics for business KPIs
- **Tracing**: AWS X-Ray integration for request tracing

### Custom Metrics

- `HealthCheckRequests`: Number of health check requests
- `FundsListRequests`: Number of fund list requests
- `FundsCreated`: Number of funds created
- `FundsUpdated`: Number of funds updated
- `FundsDeleted`: Number of funds deleted
- `FundsAPIErrors`: Number of API errors

### Log Structure

```json
{
  "timestamp": "2024-01-01T00:00:00.000Z",
  "level": "INFO",
  "service": "fundflow-funds-api",
  "message": "Fund created successfully",
  "fund_id": "fund_123",
  "user_id": "user_456",
  "request_id": "req_789"
}
```

## Development and Testing

### Local Testing

Use the provided scripts for local development:

```bash
# Start local API Gateway
./scripts/local-test.sh

# Deploy to development environment
./scripts/deploy.sh dev
```

### OpenAPI Specification

The complete API specification is available in `docs/api/openapi.yaml` and can be used with tools like:

- Swagger UI
- Postman
- Insomnia
- API testing frameworks

## Environment Variables

### Lambda Function Environment Variables

- `ENVIRONMENT`: Deployment environment (dev/staging/prod)
- `FUND_TABLE`: DynamoDB Fund table name
- `USER_TABLE`: DynamoDB User table name
- `LOG_LEVEL`: Logging level (DEBUG/INFO/WARN/ERROR)
- `POWERTOOLS_SERVICE_NAME`: Service name for observability
- `POWERTOOLS_METRICS_NAMESPACE`: CloudWatch metrics namespace

## Deployment

The API Gateway and Lambda functions are deployed using AWS SAM:

```bash
# Build the application
sam build

# Deploy to specific environment
sam deploy --config-env dev
```

## Performance Considerations

### Cold Start Optimization

- Lambda functions use provisioned concurrency in production
- Shared code is organized in layers to reduce deployment package size
- Connection pooling for DynamoDB clients

### Caching

- API Gateway response caching enabled for read operations
- CloudFront caching for static content
- DynamoDB DAX for microsecond latency (production only)

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check JWT token validity and Cognito configuration
2. **403 Forbidden**: Verify user roles and permissions
3. **500 Internal Server Error**: Check CloudWatch logs for detailed error information
4. **Timeout**: Increase Lambda timeout or optimize database queries

### Debugging

- Enable X-Ray tracing for request flow analysis
- Use CloudWatch Insights for log analysis
- Monitor API Gateway metrics in CloudWatch dashboard
