# Fund Management API Documentation

## Overview

The Fund Management API provides CRUD operations for fund entities with JWT authentication. It allows users to create, read, update, and delete fund records with comprehensive validation and error handling.

## Base URL

```
https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev/api/funds
```

## Authentication

All endpoints require a valid session token for authentication. The session should be established through the authentication system and validated for each request.

**Authentication Method:** Session-based authentication

- Session validation is performed automatically for all endpoints
- Invalid or expired sessions will return a 401 Unauthorized response

## Common Response Format

All API responses follow a consistent format:

```json
{
  "message": "Response message",
  "data": {
    /* Response data */
  },
  "error": "Error type (only present on errors)",
  "details": {
    /* Additional error details (only present on validation errors) */
  }
}
```

## Endpoints

### 1. List Funds

Retrieves a list of funds with optional filters and pagination.

- **Method:** `GET`
- **Endpoint:** `/funds`
- **Query Parameters:**
  - `page` (integer, optional): Page number for pagination (default: 1)
  - `page_size` (integer, optional): Number of items per page (default: 10, max: 100)
  - `status` (string, optional): Filter by fund status (e.g., "ACTIVE", "CLOSED")
  - `fund_type` (string, optional): Filter by fund type (e.g., "EQUITY", "BOND")
  - `search` (string, optional): Search term for fund names or descriptions

**Example Request:**

```http
GET /funds?page=1&page_size=20&status=ACTIVE&search=growth
```

**Responses:**

- **200 OK**

  ```json
  {
    "message": "Retrieved 15 funds",
    "data": {
      "funds": [
        {
          "fund_id": "FUND-001",
          "name": "Growth Fund",
          "fund_type": "EQUITY",
          "status": "ACTIVE",
          "created_at": "2023-01-01T00:00:00Z",
          "updated_at": "2023-06-01T00:00:00Z"
        }
      ],
      "pagination": {
        "page": 1,
        "page_size": 20,
        "total_count": 15,
        "has_more": false
      },
      "filters_applied": {
        "status": "ACTIVE",
        "fund_type": null,
        "search": "growth"
      }
    }
  }
  ```

- **401 Unauthorized**

  ```json
  {
    "error": "Unauthorized",
    "message": "Invalid or expired session"
  }
  ```

- **400 Bad Request**

  ```json
  {
    "error": "Validation Error",
    "message": "Invalid query parameters",
    "details": [
      {
        "field": "page_size",
        "message": "Must be between 1 and 100"
      }
    ]
  }
  ```

- **500 Internal Server Error**
  ```json
  {
    "error": "INTERNAL_SERVER_ERROR",
    "message": "Failed to retrieve funds"
  }
  ```

### 2. Get Specific Fund

Retrieves detailed information about a specific fund.

- **Method:** `GET`
- **Endpoint:** `/funds/{fund_id}`
- **Path Parameters:**
  - `fund_id` (string, required): The unique identifier of the fund

**Example Request:**

```http
GET /funds/FUND-001
```

**Responses:**

- **200 OK**

  ```json
  {
    "message": "Fund retrieved successfully",
    "data": {
      "fund": {
        "fund_id": "FUND-001",
        "name": "Growth Fund",
        "description": "A diversified growth-oriented fund",
        "fund_type": "EQUITY",
        "status": "ACTIVE",
        "inception_date": "2023-01-01",
        "total_assets": 1000000.0,
        "expense_ratio": 0.75,
        "minimum_investment": 1000.0,
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-06-01T00:00:00Z"
      }
    }
  }
  ```

- **401 Unauthorized**

  ```json
  {
    "error": "Unauthorized",
    "message": "Invalid or expired session"
  }
  ```

- **404 Not Found**

  ```json
  {
    "error": "Not Found",
    "message": "Fund FUND-001 not found"
  }
  ```

- **500 Internal Server Error**
  ```json
  {
    "error": "INTERNAL_SERVER_ERROR",
    "message": "Failed to retrieve fund"
  }
  ```

### 3. Create Fund

Creates a new fund with comprehensive validation.

- **Method:** `POST`
- **Endpoint:** `/funds`
- **Content-Type:** `application/json`

**Request Body Schema:**

```json
{
  "fund_id": "string (required, unique)",
  "name": "string (required)",
  "description": "string (optional)",
  "fund_type": "string (required, enum: EQUITY, BOND, MIXED, etc.)",
  "status": "string (optional, default: ACTIVE)",
  "inception_date": "string (date format, required)",
  "total_assets": "number (optional, default: 0)",
  "expense_ratio": "number (optional, 0-100)",
  "minimum_investment": "number (optional, default: 0)",
  "risk_level": "string (optional, enum: LOW, MEDIUM, HIGH)",
  "currency": "string (optional, default: USD)"
}
```

**Example Request:**

```http
POST /funds
Content-Type: application/json

{
  "fund_id": "FUND-002",
  "name": "Conservative Bond Fund",
  "description": "A low-risk bond fund for conservative investors",
  "fund_type": "BOND",
  "inception_date": "2023-07-01",
  "total_assets": 500000.00,
  "expense_ratio": 0.50,
  "minimum_investment": 500.00,
  "risk_level": "LOW"
}
```

**Responses:**

- **201 Created**

  ```json
  {
    "message": "Fund created successfully",
    "data": {
      "fund": {
        "fund_id": "FUND-002",
        "name": "Conservative Bond Fund",
        "description": "A low-risk bond fund for conservative investors",
        "fund_type": "BOND",
        "status": "ACTIVE",
        "inception_date": "2023-07-01",
        "total_assets": 500000.0,
        "expense_ratio": 0.5,
        "minimum_investment": 500.0,
        "risk_level": "LOW",
        "currency": "USD",
        "created_at": "2023-07-01T10:00:00Z",
        "updated_at": "2023-07-01T10:00:00Z"
      },
      "validation_warnings": [
        "Consider setting a higher minimum investment for this fund type"
      ]
    }
  }
  ```

- **400 Bad Request**

  ```json
  {
    "error": "Validation Error",
    "message": "Fund FUND-002 already exists"
  }
  ```

- **400 Bad Request (Validation Errors)**

  ```json
  {
    "error": "Validation Error",
    "message": "Invalid fund data",
    "details": [
      {
        "field": "fund_id",
        "message": "Fund ID is required"
      },
      {
        "field": "expense_ratio",
        "message": "Must be between 0 and 100"
      }
    ]
  }
  ```

- **401 Unauthorized**

  ```json
  {
    "error": "Unauthorized",
    "message": "Invalid or expired session"
  }
  ```

- **500 Internal Server Error**
  ```json
  {
    "error": "INTERNAL_SERVER_ERROR",
    "message": "Failed to create fund"
  }
  ```

### 4. Update Fund

Updates an existing fund with partial data and validation.

- **Method:** `PUT`
- **Endpoint:** `/funds/{fund_id}`
- **Path Parameters:**
  - `fund_id` (string, required): The unique identifier of the fund to update
- **Content-Type:** `application/json`

**Request Body Schema:**
All fields are optional for updates. Only provided fields will be updated.

```json
{
  "name": "string (optional)",
  "description": "string (optional)",
  "fund_type": "string (optional)",
  "status": "string (optional)",
  "total_assets": "number (optional)",
  "expense_ratio": "number (optional)",
  "minimum_investment": "number (optional)",
  "risk_level": "string (optional)",
  "currency": "string (optional)"
}
```

**Example Request:**

```http
PUT /funds/FUND-001
Content-Type: application/json

{
  "total_assets": 1250000.00,
  "expense_ratio": 0.70,
  "description": "Updated: A diversified growth-oriented fund with strong performance"
}
```

**Responses:**

- **200 OK**

  ```json
  {
    "message": "Fund updated successfully",
    "data": {
      "fund": {
        "fund_id": "FUND-001",
        "name": "Growth Fund",
        "description": "Updated: A diversified growth-oriented fund with strong performance",
        "fund_type": "EQUITY",
        "status": "ACTIVE",
        "total_assets": 1250000.0,
        "expense_ratio": 0.7,
        "updated_at": "2023-07-01T15:30:00Z"
      },
      "validation_warnings": []
    }
  }
  ```

- **400 Bad Request**

  ```json
  {
    "error": "Validation Error",
    "message": "No valid update data provided"
  }
  ```

- **401 Unauthorized**

  ```json
  {
    "error": "Unauthorized",
    "message": "Invalid or expired session"
  }
  ```

- **404 Not Found**

  ```json
  {
    "error": "Not Found",
    "message": "Fund FUND-001 not found"
  }
  ```

- **500 Internal Server Error**
  ```json
  {
    "error": "INTERNAL_SERVER_ERROR",
    "message": "Failed to update fund"
  }
  ```

### 5. Delete Fund

Performs a soft delete on a fund by changing its status to CLOSED.

- **Method:** `DELETE`
- **Endpoint:** `/funds/{fund_id}`
- **Path Parameters:**
  - `fund_id` (string, required): The unique identifier of the fund to delete

**Example Request:**

```http
DELETE /funds/FUND-001
```

**Responses:**

- **200 OK**

  ```json
  {
    "message": "Fund deleted successfully",
    "data": {
      "fund_id": "FUND-001",
      "deleted": true
    }
  }
  ```

- **401 Unauthorized**

  ```json
  {
    "error": "Unauthorized",
    "message": "Invalid or expired session"
  }
  ```

- **404 Not Found**

  ```json
  {
    "error": "Not Found",
    "message": "Fund FUND-001 not found"
  }
  ```

- **500 Internal Server Error**
  ```json
  {
    "error": "INTERNAL_SERVER_ERROR",
    "message": "Failed to delete fund"
  }
  ```

### 6. Bulk Update Funds

Updates multiple funds in a single operation with comprehensive validation.

- **Method:** `POST`
- **Endpoint:** `/funds/bulk-update`
- **Content-Type:** `application/json`

**Request Body Schema:**

```json
{
  "updates": [
    {
      "fund_id": "string (required)",
      "data": {
        "name": "string (optional)",
        "description": "string (optional)",
        "total_assets": "number (optional)",
        "expense_ratio": "number (optional)",
        "minimum_investment": "number (optional)",
        "risk_level": "string (optional)"
      }
    }
  ]
}
```

**Limitations:**

- Maximum 100 funds per bulk update request
- Each update is validated individually
- Failed updates don't affect successful ones

**Example Request:**

```http
POST /funds/bulk-update
Content-Type: application/json

{
  "updates": [
    {
      "fund_id": "FUND-001",
      "data": {
        "total_assets": 1300000.00,
        "expense_ratio": 0.65
      }
    },
    {
      "fund_id": "FUND-002",
      "data": {
        "minimum_investment": 750.00,
        "description": "Updated bond fund description"
      }
    }
  ]
}
```

**Responses:**

- **200 OK**

  ```json
  {
    "message": "Bulk update completed: 2 successful, 0 failed",
    "data": {
      "summary": {
        "total_requested": 2,
        "successful": 2,
        "failed": 0
      },
      "results": [
        {
          "fund_id": "FUND-001",
          "success": true,
          "warnings": null
        },
        {
          "fund_id": "FUND-002",
          "success": true,
          "warnings": ["Consider reviewing minimum investment amount"]
        }
      ]
    }
  }
  ```

- **200 OK (Partial Success)**

  ```json
  {
    "message": "Bulk update completed: 1 successful, 1 failed",
    "data": {
      "summary": {
        "total_requested": 2,
        "successful": 1,
        "failed": 1
      },
      "results": [
        {
          "fund_id": "FUND-001",
          "success": true,
          "warnings": null
        },
        {
          "fund_id": "FUND-999",
          "success": false,
          "error": "Fund not found"
        }
      ]
    }
  }
  ```

- **400 Bad Request**

  ```json
  {
    "error": "Validation Error",
    "message": "Updates array cannot be empty"
  }
  ```

- **400 Bad Request (Limit Exceeded)**

  ```json
  {
    "error": "Validation Error",
    "message": "Bulk update limited to 100 funds per request"
  }
  ```

- **401 Unauthorized**

  ```json
  {
    "error": "Unauthorized",
    "message": "Invalid or expired session"
  }
  ```

- **500 Internal Server Error**
  ```json
  {
    "error": "INTERNAL_SERVER_ERROR",
    "message": "Failed to process bulk fund updates"
  }
  ```

## Data Models

### Fund Object

```json
{
  "fund_id": "string (unique identifier)",
  "name": "string (fund name)",
  "description": "string (optional description)",
  "fund_type": "string (EQUITY, BOND, MIXED, etc.)",
  "status": "string (ACTIVE, CLOSED, SUSPENDED, etc.)",
  "inception_date": "string (ISO date format)",
  "total_assets": "number (total fund assets)",
  "expense_ratio": "number (0-100, percentage)",
  "minimum_investment": "number (minimum investment amount)",
  "risk_level": "string (LOW, MEDIUM, HIGH)",
  "currency": "string (currency code, default: USD)",
  "created_at": "string (ISO datetime)",
  "updated_at": "string (ISO datetime)"
}
```

### Pagination Object

```json
{
  "page": "integer (current page number)",
  "page_size": "integer (items per page)",
  "total_count": "integer (total number of items)",
  "has_more": "boolean (whether more pages exist)"
}
```

## Error Handling

### Error Response Format

```json
{
  "error": "Error type",
  "message": "Human-readable error message",
  "details": "Additional error details (validation errors only)"
}
```

### Common HTTP Status Codes

- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request data or validation errors
- **401 Unauthorized**: Authentication required or session expired
- **404 Not Found**: Requested resource not found
- **405 Method Not Allowed**: HTTP method not supported for endpoint
- **500 Internal Server Error**: Unexpected server error

### Validation Error Format

```json
{
  "error": "Validation Error",
  "message": "Invalid fund data",
  "details": [
    {
      "field": "field_name",
      "message": "Specific validation error message"
    }
  ]
}
```

## Rate Limiting

- Bulk operations are limited to 100 items per request
- Standard rate limiting applies per AWS API Gateway configuration

## Monitoring and Metrics

The API includes comprehensive logging and metrics collection:

- Request/response logging
- Error tracking
- Performance metrics
- Business metrics (funds created, updated, deleted)

## Notes

- All datetime fields are in ISO 8601 format with UTC timezone
- Monetary amounts are represented as numbers with appropriate precision
- Fund deletion is implemented as soft delete (status change to CLOSED)
- The API includes comprehensive server-side validation for all operations
- Session validation is performed for all endpoints automatically
