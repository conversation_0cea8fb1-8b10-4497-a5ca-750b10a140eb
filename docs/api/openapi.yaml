openapi: 3.0.3
info:
  title: FundFlow API
  description: RESTful API for the FundFlow fund management platform
  version: 1.0.0
  contact:
    name: FundFlow API Support

servers:
  - url: https://api.fundflow.dev.com/v1
    description: Development server
  - url: https://api.fundflow.com/v1
    description: Production server

security:
  - CognitoAuth: []

paths:
  /health:
    get:
      summary: Health check endpoint
      tags:
        - Health
      security: []
      responses:
        "200":
          description: API is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  timestamp:
                    type: string
                    format: date-time

  /funds:
    get:
      summary: List funds
      tags:
        - Funds
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: status
          in: query
          schema:
            $ref: "#/components/schemas/FundStatus"
        - name: type
          in: query
          schema:
            $ref: "#/components/schemas/FundType"
      responses:
        "200":
          description: Successfully retrieved funds
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FundListResponse"

    post:
      summary: Create a new fund
      tags:
        - Funds
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FundCreateRequest"
      responses:
        "201":
          description: Fund created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FundResponse"

  /funds/{fundId}:
    get:
      summary: Get fund by ID
      tags:
        - Funds
      parameters:
        - name: fundId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Successfully retrieved fund
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FundResponse"

components:
  securitySchemes:
    CognitoAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: AWS Cognito JWT token

  schemas:
    FundType:
      type: string
      enum:
        - equity
        - bond
        - mixed
        - money_market
        - alternative
        - index
        - etf

    FundStatus:
      type: string
      enum:
        - active
        - inactive
        - suspended
        - liquidating
        - closed

    FundResponse:
      type: object
      properties:
        fund_id:
          type: string
        name:
          type: string
        fund_type:
          $ref: "#/components/schemas/FundType"
        status:
          $ref: "#/components/schemas/FundStatus"
        nav:
          type: number
        currency:
          type: string
        fund_manager:
          type: string
        created_at:
          type: string
          format: date-time

    FundCreateRequest:
      type: object
      required:
        - name
        - fund_type
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 200
        fund_type:
          $ref: "#/components/schemas/FundType"
        nav:
          type: number
          minimum: 0
        fund_manager:
          type: string

    FundListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/FundResponse"
        pagination:
          type: object
          properties:
            count:
              type: integer
            hasMore:
              type: boolean

tags:
  - name: Health
    description: Health check operations
  - name: Funds
    description: Fund management operations
