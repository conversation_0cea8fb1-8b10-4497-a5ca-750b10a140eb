# FundFlow AWS Infrastructure Settings - 2025-06-21

This document contains all AWS infrastructure settings and configurations for the FundFlow application that can be used to recreate the environment when needed.

## Table of Contents

1. [Overview](#overview)
2. [CloudFormation Stack Configuration](#cloudformation-stack-configuration)
3. [SAM Configuration](#sam-configuration)
4. [Environment-Specific Parameters](#environment-specific-parameters)
5. [AWS Services Configuration](#aws-services-configuration)
6. [Current Deployment Status](#current-deployment-status)
7. [Deployment Commands](#deployment-commands)
8. [Environment Variables](#environment-variables)
9. [Security Configuration](#security-configuration)
10. [Monitoring and Logging](#monitoring-and-logging)

## Overview

The FundFlow application uses AWS Serverless Application Model (SAM) for infrastructure as code, deploying a complete serverless architecture across multiple environments.

### Architecture Components

- **AWS Lambda**: Serverless compute for API endpoints
- **Amazon API Gateway**: REST API management and routing
- **Amazon DynamoDB**: NoSQL database for funds, users, and reports
- **Amazon Cognito**: User authentication and authorization
- **Amazon S3**: Static asset hosting and report storage
- **Amazon CloudFront**: Content delivery network (CDN)
- **AWS Lambda Powertools**: Observability and best practices

### Regions and Environments

- **Primary Region**: us-east-1 (default), ap-northeast-1 (current deployment)
- **Environments**: dev, staging, prod
- **Current Active**: dev environment in ap-northeast-1

## CloudFormation Stack Configuration

### Stack Names

```yaml
# Default Environment
stack_name: "fundflow"

# Environment-Specific Stacks
dev: "fundflow-dev"
staging: "fundflow-staging"
prod: "fundflow-prod"
```

### Template Information

- **Template**: `template.yaml`
- **Format Version**: "2010-09-09"
- **Transform**: AWS::Serverless-2016-10-31
- **Description**: "FundFlow - Serverless application for fund management platform"

### Global Function Settings

```yaml
Globals:
  Function:
    Timeout: 30
    MemorySize: 512
    Runtime: python3.13
    Environment:
      Variables:
        POWERTOOLS_SERVICE_NAME: fundflow
        POWERTOOLS_METRICS_NAMESPACE: fundflow
        LOG_LEVEL: INFO
        ENVIRONMENT: !Ref Environment
        COGNITO_USER_POOL_ID: !Ref ExistingUserPoolId
        COGNITO_APP_CLIENT_ID: !Ref ExistingUserPoolClientId
    Layers:
      - !Ref PowertoolsLayer
```

## SAM Configuration

### samconfig.toml Settings

#### Default Configuration

```toml
version = 0.1

[default]
[default.global.parameters]
stack_name = "fundflow"

[default.build.parameters]
cached = true
parallel = true

[default.validate.parameters]
lint = true

[default.deploy.parameters]
capabilities = "CAPABILITY_IAM"
confirm_changeset = true
resolve_s3 = true
s3_prefix = "fundflow"
region = "us-east-1"
image_repositories = []

[default.package.parameters]
resolve_s3 = true

[default.sync.parameters]
watch = true

[default.local_start_api.parameters]
warm_containers = "EAGER"

[default.local_start_lambda.parameters]
warm_containers = "EAGER"
```

#### Development Environment

```toml
[dev]
[dev.deploy.parameters]
stack_name = "fundflow-dev"
profile = "fundflow-dev"
parameter_overrides = [
    "ParameterKey=Environment,ParameterValue=dev",
    "ParameterKey=LogLevel,ParameterValue=DEBUG",
    "ParameterKey=LambdaMemorySize,ParameterValue=512",
    "ParameterKey=LambdaTimeout,ParameterValue=30",
    "ParameterKey=ApiThrottleBurstLimit,ParameterValue=100",
    "ParameterKey=ApiThrottleRateLimit,ParameterValue=50",
    "ParameterKey=DynamoDBBillingMode,ParameterValue=PAY_PER_REQUEST"
]
capabilities = "CAPABILITY_IAM"
confirm_changeset = true
```

#### Staging Environment

```toml
[staging]
[staging.deploy.parameters]
stack_name = "fundflow-staging"
profile = "fundflow-staging"
parameter_overrides = [
    "ParameterKey=Environment,ParameterValue=staging",
    "ParameterKey=LogLevel,ParameterValue=INFO",
    "ParameterKey=LambdaMemorySize,ParameterValue=1024",
    "ParameterKey=LambdaTimeout,ParameterValue=30",
    "ParameterKey=ApiThrottleBurstLimit,ParameterValue=200",
    "ParameterKey=ApiThrottleRateLimit,ParameterValue=100",
    "ParameterKey=DomainName,ParameterValue=staging-api.fundflow.com",
    "ParameterKey=DynamoDBBillingMode,ParameterValue=PAY_PER_REQUEST"
]
capabilities = "CAPABILITY_IAM"
confirm_changeset = true
```

#### Production Environment

```toml
[prod]
[prod.deploy.parameters]
stack_name = "fundflow-prod"
profile = "fundflow-prod"
parameter_overrides = [
    "ParameterKey=Environment,ParameterValue=prod",
    "ParameterKey=LogLevel,ParameterValue=WARNING",
    "ParameterKey=LambdaMemorySize,ParameterValue=1024",
    "ParameterKey=LambdaTimeout,ParameterValue=60",
    "ParameterKey=ApiThrottleBurstLimit,ParameterValue=500",
    "ParameterKey=ApiThrottleRateLimit,ParameterValue=250",
    "ParameterKey=DomainName,ParameterValue=api.fundflow.com",
    "ParameterKey=CertificateArn,ParameterValue=arn:aws:acm:us-east-1:123456789012:certificate/example-certificate-id",
    "ParameterKey=DynamoDBBillingMode,ParameterValue=PROVISIONED",
    "ParameterKey=DynamoDBReadCapacity,ParameterValue=50",
    "ParameterKey=DynamoDBWriteCapacity,ParameterValue=25"
]
capabilities = "CAPABILITY_IAM"
confirm_changeset = true
```

## Environment-Specific Parameters

### Development Environment (dev.json)

```json
{
  "Parameters": {
    "Environment": "dev",
    "DomainName": "",
    "CertificateArn": "",
    "LogLevel": "DEBUG",
    "LambdaMemorySize": "512",
    "LambdaTimeout": "30",
    "ApiThrottleBurstLimit": "100",
    "ApiThrottleRateLimit": "50",

    "DynamoDBBillingMode": "PAY_PER_REQUEST",
    "DynamoDBReadCapacity": "5",
    "DynamoDBWriteCapacity": "5",
    "BackupRetentionDays": "7",

    "LogRetentionDays": "7",
    "EnableDetailedMonitoring": "false",
    "AlarmNotificationEmail": "",

    "ApiCachingEnabled": "false",
    "ApiCacheTTL": "300",
    "RequestValidationMode": "basic",

    "CloudFrontPriceClass": "PriceClass_100",
    "CloudFrontCompressionEnabled": "true",
    "CloudFrontDefaultTTL": "86400",
    "CloudFrontMaxTTL": "31536000",

    "EnableWAF": "false",
    "SecurityHeadersEnabled": "true",
    "IPWhitelistEnabled": "false"
  }
}
```

### Staging Environment (staging.json)

```json
{
  "Parameters": {
    "Environment": "staging",
    "DomainName": "staging-api.fundflow.com",
    "CertificateArn": "",
    "LogLevel": "INFO",
    "LambdaMemorySize": "1024",
    "LambdaTimeout": "30",
    "ApiThrottleBurstLimit": "200",
    "ApiThrottleRateLimit": "100",

    "DynamoDBBillingMode": "PAY_PER_REQUEST",
    "DynamoDBReadCapacity": "10",
    "DynamoDBWriteCapacity": "10",
    "BackupRetentionDays": "30",

    "LogRetentionDays": "30",
    "EnableDetailedMonitoring": "true",
    "AlarmNotificationEmail": "<EMAIL>",

    "ApiCachingEnabled": "true",
    "ApiCacheTTL": "300",
    "RequestValidationMode": "full",

    "CloudFrontPriceClass": "PriceClass_100",
    "CloudFrontCompressionEnabled": "true",
    "CloudFrontDefaultTTL": "86400",
    "CloudFrontMaxTTL": "31536000",

    "EnableWAF": "true",
    "SecurityHeadersEnabled": "true",
    "IPWhitelistEnabled": "false"
  }
}
```

### Production Environment (prod.json)

```json
{
  "Parameters": {
    "Environment": "prod",
    "DomainName": "api.fundflow.com",
    "CertificateArn": "arn:aws:acm:us-east-1:123456789012:certificate/example-certificate-id",
    "LogLevel": "WARNING",
    "LambdaMemorySize": "1024",
    "LambdaTimeout": "60",
    "ApiThrottleBurstLimit": "500",
    "ApiThrottleRateLimit": "250",

    "DynamoDBBillingMode": "PROVISIONED",
    "DynamoDBReadCapacity": "50",
    "DynamoDBWriteCapacity": "25",
    "BackupRetentionDays": "2555",

    "LogRetentionDays": "365",
    "EnableDetailedMonitoring": "true",
    "AlarmNotificationEmail": "<EMAIL>",

    "ApiCachingEnabled": "true",
    "ApiCacheTTL": "600",
    "RequestValidationMode": "full",

    "CloudFrontPriceClass": "PriceClass_All",
    "CloudFrontCompressionEnabled": "true",
    "CloudFrontDefaultTTL": "86400",
    "CloudFrontMaxTTL": "31536000",

    "EnableWAF": "true",
    "SecurityHeadersEnabled": "true",
    "IPWhitelistEnabled": "true"
  }
}
```

## AWS Services Configuration

### Amazon Cognito

#### User Pool Configuration

```yaml
UserPool:
  Type: AWS::Cognito::UserPool
  Properties:
    UserPoolName: !Sub "${AWS::StackName}-${CognitoUserPoolName}"
    AliasAttributes:
      - email
      - preferred_username
    AutoVerifiedAttributes:
      - email
    Policies:
      PasswordPolicy:
        MinimumLength: 8
        RequireUppercase: true
        RequireLowercase: true
        RequireNumbers: true
        RequireSymbols: false
    Schema:
      - Name: email
        AttributeDataType: String
        Required: true
        Mutable: false
      - Name: given_name
        AttributeDataType: String
        Required: false
        Mutable: true
      - Name: family_name
        AttributeDataType: String
        Required: false
        Mutable: true
    UsernameConfiguration:
      CaseSensitive: false
```

#### User Pool Client Configuration

```yaml
UserPoolClient:
  Type: AWS::Cognito::UserPoolClient
  Properties:
    UserPoolId: !Ref UserPool
    ClientName: !Sub "${AWS::StackName}-${CognitoClientName}"
    GenerateSecret: false
    ExplicitAuthFlows:
      - ALLOW_USER_SRP_AUTH
      - ALLOW_REFRESH_TOKEN_AUTH
      - ALLOW_USER_PASSWORD_AUTH
    TokenValidityUnits:
      AccessToken: hours
      IdToken: hours
      RefreshToken: days
    AccessTokenValidity: 1
    IdTokenValidity: 1
    RefreshTokenValidity: 30
    PreventUserExistenceErrors: ENABLED
```

#### Current Cognito Settings (Active Deployment)

- **User Pool ID**: `ap-northeast-1_H2kKHGUAT`
- **Client ID**: `2jh76f894g6lv9vrus4qbb9hu7`
- **Region**: `ap-northeast-1`
- **Test User**: `<EMAIL>` / `TestPassword123!`

### Amazon API Gateway

#### API Gateway Configuration

```yaml
ApiGateway:
  Type: AWS::Serverless::Api
  Properties:
    Name: !Sub "${AWS::StackName}-api"
    StageName: !Ref Environment
    Cors:
      AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
      AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
      AllowOrigin: "'*'"
      MaxAge: "'600'"
    MethodSettings:
      - ResourcePath: "/*"
        HttpMethod: "*"
        ThrottlingBurstLimit: !Ref ApiThrottleBurstLimit
        ThrottlingRateLimit: !Ref ApiThrottleRateLimit
        LoggingLevel: !If [IsProduction, INFO, ERROR]
        DataTraceEnabled: !If [IsProduction, false, true]
        MetricsEnabled: !Ref EnableDetailedMonitoring
        CachingEnabled: !Ref ApiCachingEnabled
        CacheTtlInSeconds: !Ref ApiCacheTTL
    Auth:
      DefaultAuthorizer: CognitoAuthorizer
      Authorizers:
        CognitoAuthorizer:
          UserPoolArn: !If
            - CreateNewCognito
            - !GetAtt UserPool.Arn
            - !Sub "arn:aws:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/${ExistingUserPoolId}"
          AuthorizationScopes:
            - "aws.cognito.signin.user.admin"
```

#### Current API Gateway Settings (Active Deployment)

- **API Gateway URL**: `https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev`
- **Stage**: `dev`
- **Region**: `ap-northeast-1`
- **Authentication**: AWS Cognito User Pool

### AWS Lambda Functions

#### Health Check Function

```yaml
HealthCheckFunction:
  Type: AWS::Serverless::Function
  Properties:
    CodeUri: src/
    Handler: functions.api.health.handler
    Runtime: python3.13
    MemorySize: !Ref LambdaMemorySize
    Timeout: !Ref LambdaTimeout
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        API_VERSION: "1.0.0"
        LOG_LEVEL: !Ref LogLevel
        POWERTOOLS_SERVICE_NAME: fundflow-health
        POWERTOOLS_METRICS_NAMESPACE: FundFlow
    Events:
      HealthAPI:
        Type: Api
        Properties:
          RestApiId: !Ref ApiGateway
          Path: /health
          Method: get
```

#### Funds API Function

```yaml
FundsAPIFunction:
  Type: AWS::Serverless::Function
  Properties:
    CodeUri: src/
    Handler: functions.api.funds.handler
    Runtime: python3.13
    MemorySize: !Ref LambdaMemorySize
    Timeout: !Ref LambdaTimeout
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        FUND_TABLE: !Ref FundTable
        USER_TABLE: !Ref UserTable
        USER_POOL_ID:
          !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
        USER_POOL_CLIENT_ID:
          !If [
            CreateNewCognito,
            !Ref UserPoolClient,
            !Ref ExistingUserPoolClientId,
          ]
        LOG_LEVEL: !Ref LogLevel
        POWERTOOLS_SERVICE_NAME: fundflow-funds-api
        POWERTOOLS_METRICS_NAMESPACE: FundFlow
```

#### Users API Function

```yaml
UsersAPIFunction:
  Type: AWS::Serverless::Function
  Properties:
    CodeUri: src/
    Handler: functions.api.users.handler
    Runtime: python3.13
    MemorySize: !Ref LambdaMemorySize
    Timeout: !Ref LambdaTimeout
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        USER_TABLE: !Ref UserTable
        USER_POOL_ID:
          !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
        LOG_LEVEL: !Ref LogLevel
        POWERTOOLS_SERVICE_NAME: fundflow-users-api
        POWERTOOLS_METRICS_NAMESPACE: FundFlow
```

#### Reports API Function

```yaml
ReportsAPIFunction:
  Type: AWS::Serverless::Function
  Properties:
    CodeUri: src/
    Handler: functions.api.reports.handler
    Runtime: python3.13
    MemorySize: !Ref LambdaMemorySize
    Timeout: !Ref LambdaTimeout
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        REPORT_TABLE: !Ref ReportTable
        REPORTS_BUCKET: !Ref ReportsBucket
        USER_POOL_ID:
          !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
        LOG_LEVEL: !Ref LogLevel
        POWERTOOLS_SERVICE_NAME: fundflow-reports-api
        POWERTOOLS_METRICS_NAMESPACE: FundFlow
```

#### Auth API Function

```yaml
AuthAPIFunction:
  Type: AWS::Serverless::Function
  Properties:
    CodeUri: src/
    Handler: functions.api.auth.handler
    Runtime: python3.13
    MemorySize: !Ref LambdaMemorySize
    Timeout: !Ref LambdaTimeout
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        USER_POOL_ID:
          !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
        USER_POOL_CLIENT_ID:
          !If [
            CreateNewCognito,
            !Ref UserPoolClient,
            !Ref ExistingUserPoolClientId,
          ]
        LOG_LEVEL: !Ref LogLevel
        POWERTOOLS_SERVICE_NAME: fundflow-auth-api
        POWERTOOLS_METRICS_NAMESPACE: FundFlow
        SESSION_TIMEOUT_SECONDS: "3600"
        REFRESH_THRESHOLD_SECONDS: "900"
        CSRF_SECRET_KEY: !Sub "${AWS::StackName}-csrf-secret-${Environment}"
```

### Amazon DynamoDB

#### Fund Table Configuration

```yaml
FundTable:
  Type: AWS::DynamoDB::Table
  Properties:
    TableName: !Sub "${AWS::StackName}-funds"
    BillingMode: !Ref DynamoDBBillingMode
    ProvisionedThroughput: !If
      - IsProvisionedBilling
      - ReadCapacityUnits: !Ref DynamoDBReadCapacity
        WriteCapacityUnits: !Ref DynamoDBWriteCapacity
      - !Ref "AWS::NoValue"
    AttributeDefinitions:
      - AttributeName: fund_id
        AttributeType: S
      - AttributeName: created_at
        AttributeType: S
      - AttributeName: fund_type
        AttributeType: S
      - AttributeName: status
        AttributeType: S
    KeySchema:
      - AttributeName: fund_id
        KeyType: HASH
    GlobalSecondaryIndexes:
      - IndexName: fund_type_index
        KeySchema:
          - AttributeName: fund_type
            KeyType: HASH
          - AttributeName: created_at
            KeyType: RANGE
        Projection:
          ProjectionType: ALL
        ProvisionedThroughput: !If
          - IsProvisionedBilling
          - ReadCapacityUnits: !Ref DynamoDBReadCapacity
            WriteCapacityUnits: !Ref DynamoDBWriteCapacity
          - !Ref "AWS::NoValue"
      - IndexName: status_index
        KeySchema:
          - AttributeName: status
            KeyType: HASH
          - AttributeName: created_at
            KeyType: RANGE
        Projection:
          ProjectionType: ALL
        ProvisionedThroughput: !If
          - IsProvisionedBilling
          - ReadCapacityUnits: !Ref DynamoDBReadCapacity
            WriteCapacityUnits: !Ref DynamoDBWriteCapacity
          - !Ref "AWS::NoValue"
    PointInTimeRecoverySpecification:
      PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
```

#### User Table Configuration

```yaml
UserTable:
  Type: AWS::DynamoDB::Table
  Properties:
    TableName: !Sub "${AWS::StackName}-users"
    BillingMode: !Ref DynamoDBBillingMode
    ProvisionedThroughput: !If
      - IsProvisionedBilling
      - ReadCapacityUnits: !Ref DynamoDBReadCapacity
        WriteCapacityUnits: !Ref DynamoDBWriteCapacity
      - !Ref "AWS::NoValue"
    AttributeDefinitions:
      - AttributeName: user_id
        AttributeType: S
      - AttributeName: email
        AttributeType: S
      - AttributeName: role
        AttributeType: S
      - AttributeName: created_at
        AttributeType: S
    KeySchema:
      - AttributeName: user_id
        KeyType: HASH
    GlobalSecondaryIndexes:
      - IndexName: email_index
        KeySchema:
          - AttributeName: email
            KeyType: HASH
        Projection:
          ProjectionType: ALL
        ProvisionedThroughput: !If
          - IsProvisionedBilling
          - ReadCapacityUnits: !Ref DynamoDBReadCapacity
            WriteCapacityUnits: !Ref DynamoDBWriteCapacity
          - !Ref "AWS::NoValue"
      - IndexName: role_index
        KeySchema:
          - AttributeName: role
            KeyType: HASH
          - AttributeName: created_at
            KeyType: RANGE
        Projection:
          ProjectionType: ALL
        ProvisionedThroughput: !If
          - IsProvisionedBilling
          - ReadCapacityUnits: !Ref DynamoDBReadCapacity
            WriteCapacityUnits: !Ref DynamoDBWriteCapacity
          - !Ref "AWS::NoValue"
    PointInTimeRecoverySpecification:
      PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
```

#### Report Table Configuration

```yaml
ReportTable:
  Type: AWS::DynamoDB::Table
  Properties:
    TableName: !Sub "${AWS::StackName}-reports"
    BillingMode: !Ref DynamoDBBillingMode
    ProvisionedThroughput: !If
      - IsProvisionedBilling
      - ReadCapacityUnits: !Ref DynamoDBReadCapacity
        WriteCapacityUnits: !Ref DynamoDBWriteCapacity
      - !Ref "AWS::NoValue"
    AttributeDefinitions:
      - AttributeName: report_id
        AttributeType: S
      - AttributeName: user_id
        AttributeType: S
      - AttributeName: created_at
        AttributeType: S
      - AttributeName: report_type
        AttributeType: S
    KeySchema:
      - AttributeName: report_id
        KeyType: HASH
    GlobalSecondaryIndexes:
      - IndexName: user_reports_index
        KeySchema:
          - AttributeName: user_id
            KeyType: HASH
          - AttributeName: created_at
            KeyType: RANGE
        Projection:
          ProjectionType: ALL
        ProvisionedThroughput: !If
          - IsProvisionedBilling
          - ReadCapacityUnits: !Ref DynamoDBReadCapacity
            WriteCapacityUnits: !Ref DynamoDBWriteCapacity
          - !Ref "AWS::NoValue"
      - IndexName: type_reports_index
        KeySchema:
          - AttributeName: report_type
            KeyType: HASH
          - AttributeName: created_at
            KeyType: RANGE
        Projection:
          ProjectionType: ALL
        ProvisionedThroughput: !If
          - IsProvisionedBilling
          - ReadCapacityUnits: !Ref DynamoDBReadCapacity
            WriteCapacityUnits: !Ref DynamoDBWriteCapacity
          - !Ref "AWS::NoValue"
    PointInTimeRecoverySpecification:
      PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
```

#### Current DynamoDB Settings (Active Deployment)

- **Fund Table**: `fundflow-dev-funds`
- **User Table**: `fundflow-dev-users`
- **Report Table**: `fundflow-dev-reports`
- **Billing Mode**: PAY_PER_REQUEST (dev environment)
- **Region**: `ap-northeast-1`

### Amazon S3

#### Static Assets Bucket Configuration

```yaml
StaticAssetsBucket:
  Type: AWS::S3::Bucket
  Properties:
    BucketName: !Sub "${AWS::StackName}-static-assets-${AWS::AccountId}"
    PublicAccessBlockConfiguration:
      BlockPublicAcls: true
      BlockPublicPolicy: true
      IgnorePublicAcls: true
      RestrictPublicBuckets: true
    BucketEncryption:
      ServerSideEncryptionConfiguration:
        - ServerSideEncryptionByDefault:
            SSEAlgorithm: AES256
    VersioningConfiguration:
      Status: !If [IsProduction, Enabled, Suspended]
    Tags:
      - Key: Environment
        Value: !Ref Environment
      - Key: Application
        Value: FundFlow
```

#### Reports Bucket Configuration

```yaml
ReportsBucket:
  Type: AWS::S3::Bucket
  Properties:
    BucketName: !Sub "${AWS::StackName}-reports-${AWS::AccountId}"
    PublicAccessBlockConfiguration:
      BlockPublicAcls: true
      BlockPublicPolicy: true
      IgnorePublicAcls: true
      RestrictPublicBuckets: true
    BucketEncryption:
      ServerSideEncryptionConfiguration:
        - ServerSideEncryptionByDefault:
            SSEAlgorithm: AES256
    VersioningConfiguration:
      Status: !If [IsProduction, Enabled, Suspended]
    LifecycleConfiguration:
      Rules:
        - Id: DeleteOldReports
          Status: Enabled
          ExpirationInDays: !Ref BackupRetentionDays
    Tags:
      - Key: Environment
        Value: !Ref Environment
      - Key: Application
        Value: FundFlow
```

### Amazon CloudFront

#### CloudFront Distribution Configuration

```yaml
CloudFrontDistribution:
  Type: AWS::CloudFront::Distribution
  Properties:
    DistributionConfig:
      Comment: !Sub "FundFlow ${Environment} CloudFront Distribution"
      DefaultCacheBehavior:
        TargetOriginId: S3Origin
        ViewerProtocolPolicy: redirect-to-https
        CachePolicyId: 658327ea-f89d-4fab-a63d-7e88639e58f6 # Managed-CachingOptimized
        OriginRequestPolicyId: 88a5eaf4-2fd4-4709-b370-b4c650ea3fcf # Managed-CORS-S3Origin
        DefaultTTL: !Ref CloudFrontDefaultTTL
        MaxTTL: !Ref CloudFrontMaxTTL
        Compress: !Ref CloudFrontCompressionEnabled
      Origins:
        - Id: S3Origin
          DomainName: !GetAtt StaticAssetsBucket.RegionalDomainName
          S3OriginConfig:
            OriginAccessIdentity: !Sub "origin-access-identity/cloudfront/${CloudFrontOriginAccessIdentity}"
      Aliases: !If
        - HasCustomDomain
        - [!Ref DomainName]
        - !Ref "AWS::NoValue"
      ViewerCertificate: !If
        - HasCustomDomain
        - AcmCertificateArn: !Ref CertificateArn
          SslSupportMethod: sni-only
          MinimumProtocolVersion: TLSv1.2_2021
        - CloudFrontDefaultCertificate: true
      Enabled: true
      DefaultRootObject: index.html
      CustomErrorResponses:
        - ErrorCode: 404
          ResponseCode: 200
          ResponsePagePath: /index.html
      PriceClass: !Ref CloudFrontPriceClass
```

#### CloudFront Origin Access Identity

```yaml
CloudFrontOriginAccessIdentity:
  Type: AWS::CloudFront::CloudFrontOriginAccessIdentity
  Properties:
    CloudFrontOriginAccessIdentityConfig:
      Comment: !Sub "FundFlow ${Environment} OAI"
```

## Current Deployment Status

### Active Environment: Development (ap-northeast-1)

#### Infrastructure Resources

- **Stack Name**: `fundflow-dev`
- **Region**: `ap-northeast-1`
- **Deployment Date**: 2025-06-21

#### API Gateway

- **URL**: `https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev`
- **Stage**: `dev`
- **Authentication**: Enabled with Cognito

#### Cognito User Pool

- **User Pool ID**: `ap-northeast-1_H2kKHGUAT`
- **Client ID**: `2jh76f894g6lv9vrus4qbb9hu7`
- **Test User**: `<EMAIL>` / `TestPassword123!`

#### DynamoDB Tables

- **Fund Table**: `fundflow-dev-funds` (PAY_PER_REQUEST)
- **User Table**: `fundflow-dev-users` (PAY_PER_REQUEST)
- **Report Table**: `fundflow-dev-reports` (PAY_PER_REQUEST)

#### Lambda Functions

- **Health Check**: `fundflow-dev-HealthCheckFunction-*`
- **Funds API**: `fundflow-dev-FundsAPIFunction-*`
- **Users API**: `fundflow-dev-UsersAPIFunction-*`
- **Reports API**: `fundflow-dev-ReportsAPIFunction-*`
- **Auth API**: `fundflow-dev-AuthAPIFunction-*`

#### S3 Buckets

- **Static Assets**: `fundflow-dev-static-assets-*`
- **Reports**: `fundflow-dev-reports-*`

#### CloudFront Distribution

- **Distribution ID**: (Generated during deployment)
- **Domain**: (CloudFront generated domain)

### Environment Variables (Active Deployment)

```json
{
  "Variables": {
    "POWERTOOLS_SERVICE_NAME": "fundflow-funds-api",
    "POWERTOOLS_METRICS_NAMESPACE": "FundFlow",
    "ENVIRONMENT": "dev",
    "USER_TABLE": "fundflow-dev-users",
    "LOG_LEVEL": "DEBUG",
    "FUND_TABLE": "fundflow-dev-funds",
    "USER_POOL_ID": "ap-northeast-1_H2kKHGUAT",
    "USER_POOL_CLIENT_ID": "2jh76f894g6lv9vrus4qbb9hu7",
    "AWS_REGION": "ap-northeast-1"
  }
}
```

## Deployment Commands

### Prerequisites

```bash
# Install AWS CLI
aws --version

# Install SAM CLI
sam --version

# Configure AWS credentials
aws configure --profile fundflow-dev
aws configure --profile fundflow-staging
aws configure --profile fundflow-prod
```

### Build and Deploy Commands

#### Development Environment

```bash
# Build the application
sam build

# Deploy to development
sam deploy --config-env dev

# Alternative: Deploy with parameter file
sam deploy --parameter-overrides file://parameters/dev.json \
  --stack-name fundflow-dev \
  --region ap-northeast-1 \
  --capabilities CAPABILITY_IAM \
  --profile fundflow-dev
```

#### Staging Environment

```bash
# Deploy to staging
sam deploy --config-env staging

# Alternative: Deploy with parameter file
sam deploy --parameter-overrides file://parameters/staging.json \
  --stack-name fundflow-staging \
  --region us-east-1 \
  --capabilities CAPABILITY_IAM \
  --profile fundflow-staging
```

#### Production Environment

```bash
# Deploy to production
sam deploy --config-env prod

# Alternative: Deploy with parameter file
sam deploy --parameter-overrides file://parameters/prod.json \
  --stack-name fundflow-prod \
  --region us-east-1 \
  --capabilities CAPABILITY_IAM \
  --profile fundflow-prod
```

#### Using Deployment Script

```bash
# Make script executable
chmod +x scripts/deploy.sh

# Deploy to specific environment
./scripts/deploy.sh -e dev
./scripts/deploy.sh -e staging
./scripts/deploy.sh -e prod

# Deploy with parameter file
./scripts/deploy.sh -e prod -f

# Dry run (create changeset only)
./scripts/deploy.sh -e prod -d
```

### Local Development Commands

```bash
# Start API locally
sam local start-api --warm-containers EAGER

# Start Lambda functions locally
sam local start-lambda --warm-containers EAGER

# Invoke specific function
sam local invoke FundsAPIFunction --event events/test-event.json

# Sync changes during development
sam sync --watch --stack-name fundflow-dev
```

### Validation and Testing

```bash
# Validate template
sam validate

# Validate with parameters
sam validate --parameter-overrides file://parameters/prod.json

# Run unit tests
python -m pytest tests/

# Run integration tests
python -m pytest tests/integration/
```

## Environment Variables

### Frontend Environment Variables (frontend/.env.local)

```bash
# Application Environment
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# AWS API Configuration
NEXT_PUBLIC_API_BASE_URL=https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev
NEXT_PUBLIC_AWS_REGION=ap-northeast-1
NEXT_PUBLIC_USE_AWS_API=true

# Cognito Configuration
COGNITO_CLIENT_ID=2jh76f894g6lv9vrus4qbb9hu7
COGNITO_ISSUER=https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_H2kKHGUAT

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Local API Configuration (fallback)
API_SECRET_KEY=your-secret-key-here
```

### Lambda Function Environment Variables

```yaml
# Common Environment Variables for all Lambda functions
Environment:
  Variables:
    # Application Settings
    ENVIRONMENT: !Ref Environment
    LOG_LEVEL: !Ref LogLevel

    # AWS Powertools Configuration
    POWERTOOLS_SERVICE_NAME: fundflow
    POWERTOOLS_METRICS_NAMESPACE: FundFlow

    # Cognito Configuration
    USER_POOL_ID: !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
    USER_POOL_CLIENT_ID:
      !If [CreateNewCognito, !Ref UserPoolClient, !Ref ExistingUserPoolClientId]

    # DynamoDB Tables
    FUND_TABLE: !Ref FundTable
    USER_TABLE: !Ref UserTable
    REPORT_TABLE: !Ref ReportTable

    # S3 Buckets
    STATIC_ASSETS_BUCKET: !Ref StaticAssetsBucket
    REPORTS_BUCKET: !Ref ReportsBucket

    # API Configuration
    API_VERSION: "1.0.0"

    # Security Settings
    SESSION_TIMEOUT_SECONDS: "3600"
    REFRESH_THRESHOLD_SECONDS: "900"
    CSRF_SECRET_KEY: !Sub "${AWS::StackName}-csrf-secret-${Environment}"
```

### Testing Environment Variables

```bash
# Test Environment Setup
export AWS_ACCESS_KEY_ID="testing"
export AWS_SECRET_ACCESS_KEY="testing"
export AWS_SECURITY_TOKEN="testing"
export AWS_SESSION_TOKEN="testing"
export AWS_DEFAULT_REGION="ap-northeast-1"
export AWS_REGION="ap-northeast-1"

# Project-specific test variables
export DYNAMODB_TABLE="fund-management-dev"
export USER_POOL_ID="us-east-1_test123"
export USER_POOL_CLIENT_ID="test_client_id"
```

## Security Configuration

### IAM Roles and Policies

#### Lambda Execution Role

```yaml
# Automatically created by SAM with these policies:
Policies:
  - DynamoDBCrudPolicy:
      TableName: !Ref FundTable
  - DynamoDBCrudPolicy:
      TableName: !Ref UserTable
  - DynamoDBCrudPolicy:
      TableName: !Ref ReportTable
  - S3ReadPolicy:
      BucketName: !Ref StaticAssetsBucket
  - S3CrudPolicy:
      BucketName: !Ref ReportsBucket
  - Statement:
      - Effect: Allow
        Action:
          - cognito-idp:AdminGetUser
          - cognito-idp:AdminCreateUser
          - cognito-idp:AdminUpdateUserAttributes
          - cognito-idp:ListUsers
          - cognito-idp:ForgotPassword
          - cognito-idp:ConfirmForgotPassword
          - cognito-idp:ChangePassword
        Resource: !Sub "arn:aws:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/${ExistingUserPoolId}"
```

#### API Gateway Resource Policy

```yaml
# CORS Configuration
Cors:
  AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
  AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
  AllowOrigin: "'*'"
  MaxAge: "'600'"

# Authentication Configuration
Auth:
  DefaultAuthorizer: CognitoAuthorizer
  Authorizers:
    CognitoAuthorizer:
      UserPoolArn:
        !If [
          CreateNewCognito,
          !GetAtt UserPool.Arn,
          !Sub "arn:aws:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/${ExistingUserPoolId}",
        ]
      AuthorizationScopes:
        - "aws.cognito.signin.user.admin"
```

#### S3 Bucket Policies

```yaml
# Static Assets Bucket - Private with CloudFront access only
PublicAccessBlockConfiguration:
  BlockPublicAcls: true
  BlockPublicPolicy: true
  IgnorePublicAcls: true
  RestrictPublicBuckets: true

# Reports Bucket - Private with Lambda access only
BucketEncryption:
  ServerSideEncryptionConfiguration:
    - ServerSideEncryptionByDefault:
        SSEAlgorithm: AES256
```

### Cognito Security Settings

#### Password Policy

```yaml
Policies:
  PasswordPolicy:
    MinimumLength: 8
    RequireUppercase: true
    RequireLowercase: true
    RequireNumbers: true
    RequireSymbols: false
```

#### Token Configuration

```yaml
TokenValidityUnits:
  AccessToken: hours
  IdToken: hours
  RefreshToken: days
AccessTokenValidity: 1
IdTokenValidity: 1
RefreshTokenValidity: 30
PreventUserExistenceErrors: ENABLED
```

### Environment-Specific Security

#### Development

- **WAF**: Disabled
- **IP Restrictions**: None
- **Detailed Monitoring**: Disabled
- **Log Level**: DEBUG

#### Staging

- **WAF**: Enabled
- **IP Restrictions**: None
- **Detailed Monitoring**: Enabled
- **Log Level**: INFO

#### Production

- **WAF**: Enabled
- **IP Restrictions**: Enabled
- **Detailed Monitoring**: Enabled
- **Log Level**: WARNING

## Monitoring and Logging

### CloudWatch Configuration

#### Log Groups

```yaml
# Automatically created for each Lambda function:
- /aws/lambda/fundflow-{env}-HealthCheckFunction-*
- /aws/lambda/fundflow-{env}-FundsAPIFunction-*
- /aws/lambda/fundflow-{env}-UsersAPIFunction-*
- /aws/lambda/fundflow-{env}-ReportsAPIFunction-*
- /aws/lambda/fundflow-{env}-AuthAPIFunction-*

# API Gateway logs:
- /aws/apigateway/fundflow-{env}-api
```

#### Log Retention

- **Development**: 7 days
- **Staging**: 30 days
- **Production**: 365 days

#### Metrics and Alarms

```yaml
# Lambda Metrics
- Duration
- Errors
- Throttles
- ConcurrentExecutions

# API Gateway Metrics
- Count
- Latency
- 4XXError
- 5XXError

# DynamoDB Metrics
- ConsumedReadCapacityUnits
- ConsumedWriteCapacityUnits
- ThrottledRequests
```

### AWS Powertools Integration

#### Structured Logging

```python
from aws_lambda_powertools import Logger

logger = Logger(service="fundflow")

@logger.inject_lambda_context
def handler(event, context):
    logger.info("Processing request", extra={"request_id": context.aws_request_id})
```

#### Metrics

```python
from aws_lambda_powertools import Metrics
from aws_lambda_powertools.metrics import MetricUnit

metrics = Metrics(namespace="FundFlow", service="funds-api")

@metrics.log_metrics
def handler(event, context):
    metrics.add_metric(name="FundCreated", unit=MetricUnit.Count, value=1)
```

#### Tracing

```python
from aws_lambda_powertools import Tracer

tracer = Tracer(service="fundflow")

@tracer.capture_lambda_handler
def handler(event, context):
    # Function logic here
    pass
```

### Alarm Configuration (Production)

#### Lambda Function Alarms

```yaml
# Error Rate Alarm
ErrorRateAlarm:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: !Sub "${AWS::StackName}-lambda-error-rate"
    MetricName: Errors
    Namespace: AWS/Lambda
    Statistic: Sum
    Period: 300
    EvaluationPeriods: 2
    Threshold: 5
    ComparisonOperator: GreaterThanThreshold
    AlarmActions:
      - !Ref SNSAlarmTopic

# Duration Alarm
DurationAlarm:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: !Sub "${AWS::StackName}-lambda-duration"
    MetricName: Duration
    Namespace: AWS/Lambda
    Statistic: Average
    Period: 300
    EvaluationPeriods: 2
    Threshold: 10000 # 10 seconds
    ComparisonOperator: GreaterThanThreshold
    AlarmActions:
      - !Ref SNSAlarmTopic
```

#### API Gateway Alarms

```yaml
# 5XX Error Alarm
APIGateway5XXAlarm:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: !Sub "${AWS::StackName}-api-5xx-errors"
    MetricName: 5XXError
    Namespace: AWS/ApiGateway
    Statistic: Sum
    Period: 300
    EvaluationPeriods: 2
    Threshold: 10
    ComparisonOperator: GreaterThanThreshold
    AlarmActions:
      - !Ref SNSAlarmTopic
```

## CloudFormation Outputs

### Stack Outputs

```yaml
Outputs:
  ApiGatewayUrl:
    Description: "API Gateway endpoint URL"
    Value: !Sub "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Environment}"
    Export:
      Name: !Sub "${AWS::StackName}-ApiUrl"

  CloudFrontUrl:
    Description: "CloudFront distribution URL"
    Value: !Sub "https://${CloudFrontDistribution.DomainName}"
    Export:
      Name: !Sub "${AWS::StackName}-CloudFrontUrl"

  FundTableName:
    Description: "DynamoDB Fund table name"
    Value: !Ref FundTable
    Export:
      Name: !Sub "${AWS::StackName}-FundTable"

  UserTableName:
    Description: "DynamoDB User table name"
    Value: !Ref UserTable
    Export:
      Name: !Sub "${AWS::StackName}-UserTable"

  ReportTableName:
    Description: "DynamoDB Report table name"
    Value: !Ref ReportTable
    Export:
      Name: !Sub "${AWS::StackName}-ReportTable"

  StaticAssetsBucketName:
    Description: "S3 bucket for static assets"
    Value: !Ref StaticAssetsBucket
    Export:
      Name: !Sub "${AWS::StackName}-StaticAssetsBucket"

  ReportsBucketName:
    Description: "S3 bucket for reports"
    Value: !Ref ReportsBucket
    Export:
      Name: !Sub "${AWS::StackName}-ReportsBucket"

  UserPoolId:
    Description: "Cognito User Pool ID"
    Value: !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
    Export:
      Name: !Sub "${AWS::StackName}-UserPoolId"

  UserPoolClientId:
    Description: "Cognito User Pool Client ID"
    Value:
      !If [CreateNewCognito, !Ref UserPoolClient, !Ref ExistingUserPoolClientId]
    Export:
      Name: !Sub "${AWS::StackName}-UserPoolClientId"
```

## Recreation Instructions

### Complete Environment Recreation

1. **Prerequisites Setup**

   ```bash
   # Install required tools
   pip install aws-sam-cli
   npm install -g aws-cdk

   # Configure AWS credentials
   aws configure --profile fundflow-dev
   ```

2. **Clone and Setup**

   ```bash
   # Clone repository
   git clone <repository-url>
   cd FundFlow

   # Install dependencies
   pip install -r requirements.txt
   cd frontend && npm install
   ```

3. **Deploy Infrastructure**

   ```bash
   # Build SAM application
   sam build

   # Deploy to development
   sam deploy --config-env dev

   # Deploy to staging
   sam deploy --config-env staging

   # Deploy to production
   sam deploy --config-env prod
   ```

4. **Configure Frontend**

   ```bash
   # Copy environment template
   cp frontend/env.example frontend/.env.local

   # Update with actual values from CloudFormation outputs
   # NEXT_PUBLIC_API_BASE_URL=<API Gateway URL>
   # COGNITO_CLIENT_ID=<User Pool Client ID>
   # COGNITO_ISSUER=<User Pool Issuer URL>
   ```

5. **Test Deployment**

   ```bash
   # Test API endpoints
   curl https://<api-gateway-url>/health

   # Start frontend
   cd frontend && npm run dev
   ```

### Disaster Recovery

1. **Backup Current Settings**

   ```bash
   # Export CloudFormation template
   aws cloudformation get-template --stack-name fundflow-prod > backup-template.json

   # Export stack parameters
   aws cloudformation describe-stacks --stack-name fundflow-prod > backup-parameters.json
   ```

2. **Data Backup**

   ```bash
   # Enable DynamoDB point-in-time recovery (production)
   aws dynamodb put-backup-policy --table-name fundflow-prod-funds --backup-policy-enabled

   # Create manual backup
   aws dynamodb create-backup --table-name fundflow-prod-funds --backup-name fundflow-prod-funds-manual-backup
   ```

3. **Recovery Process**
   ```bash
   # Restore from this documentation
   # Use the configurations in this file to recreate the infrastructure
   # Deploy using the exact parameters documented above
   ```

---

**Document Created**: 2025-06-21
**Last Updated**: 2025-06-21
**Version**: 1.0
**Environment**: Development (ap-northeast-1)

This document contains all necessary information to recreate the FundFlow AWS infrastructure. Keep this document updated when making infrastructure changes.
