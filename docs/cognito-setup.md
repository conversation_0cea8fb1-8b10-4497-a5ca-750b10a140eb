# NextAuth.js + AWS Cognito Integration Guide (2024)

## 1. AWS Cognito Configuration

### A. Create a User Pool

- Go to AWS Cognito → User Pools → Create user pool.
- Choose "Manual setup" or "Quick create" as needed.

### B. Create an App Client

1. Go to **App integration** → **App clients** → **Create app client**.
2. **Application type:**
   - Select **Single-page application** (for public clients, e.g., Next.js frontend).
3. **App client name:**
   - e.g., `my-nextjs-client`
4. **Client secret:**
   - Should be **not generated** (blank) for public clients.
5. **Allowed callback URLs:**
   - e.g., `https://your-ngrok-url.ngrok-free.app/api/auth/callback/cognito`
6. **Allowed sign-out URLs:**
   - e.g., `https://your-ngrok-url.ngrok-free.app`
7. **OAuth 2.0 grant types:**
   - Check **Authorization code grant**.
8. **OpenID Connect scopes:**
   - Check **openid**, **profile**, **email**.
9. **Identity providers:**
   - Check **Cognito User Pool**.
10. **Save** the app client.

### C. Cognito Domain

- Go to **App integration** → **Domain**.
- Note your domain, e.g., `https://ap-northeast-1h2kkhguat.auth.ap-northeast-1.amazoncognito.com`.

---

## 2. NextAuth.js Configuration

### A. .env.local Example

```env
# .env.local
NEXTAUTH_URL=https://your-ngrok-url.ngrok-free.app
COGNITO_CLIENT_ID=your-cognito-client-id
COGNITO_CLIENT_SECRET=        # Leave blank for public client
COGNITO_ISSUER=https://cognito-idp.ap-northeast-1.amazonaws.com/your-user-pool-id
NEXTAUTH_SECRET=your-random-nextauth-secret
```

### B. next-auth Provider Setup (TypeScript)

```typescript
import NextAuth from "next-auth";
import CognitoProvider from "next-auth/providers/cognito";
import https from "https";

const createHttpsAgent = () => {
  if (process.env.NODE_ENV === "development") {
    return new https.Agent({ rejectUnauthorized: false, timeout: 10000 });
  }
  return new https.Agent({ timeout: 10000 });
};

const cognitoProviderOptions = {
  clientId: process.env.COGNITO_CLIENT_ID!,
  clientSecret: process.env.COGNITO_CLIENT_SECRET || "",
  client: {
    token_endpoint_auth_method: "none" as const, // <-- CRUCIAL: disables client_secret_basic
  },
  issuer: process.env.COGNITO_ISSUER!,
  checks: ["pkce", "state"] as ("pkce" | "state")[],
  authorization: {
    params: {
      scope: "openid profile email",
    },
  },
  httpOptions: {
    timeout: 10000,
    agent: createHttpsAgent(),
  },
};

// Remove clientSecret for public clients
if (!process.env.COGNITO_CLIENT_SECRET) {
  delete (cognitoProviderOptions as { clientSecret?: string }).clientSecret;
}

export const authOptions = {
  providers: [CognitoProvider(cognitoProviderOptions)],
  // ... your callbacks, events, etc.
};

export default NextAuth(authOptions);
```

---

## 3. Key Points & Troubleshooting

- **ALWAYS** use `client: { token_endpoint_auth_method: "none" }` for public clients (no secret).
- If you see `invalid_client` or `client_secret_basic client authentication method requires a client_secret`, it means Cognito expects a secret—double-check your app client type and this setting.
- Use **pinggy.link**, **ngrok**, or a public URL for local development; Cognito cannot call back to `localhost` from the cloud.
- Restart your Next.js server after any `.env.local` changes.
- Use a **new incognito window** for each test to avoid session/cookie issues.

---

## 4. Deployment Checklist

- Update callback/sign-out URLs to your production domain.
- Set `NEXTAUTH_URL` to your production URL.
- Use a strong, unique `NEXTAUTH_SECRET` in production.
- For production, use a real HTTPS domain (not ngrok).

---

## 5. References

- [NextAuth.js Cognito Provider Docs](https://next-auth.js.org/providers/cognito)
- [AWS Cognito User Pool App Client Docs](https://docs.aws.amazon.com/cognito/latest/developerguide/user-pool-settings-client-apps.html)
- [NextAuth.js Troubleshooting](https://next-auth.js.org/warnings#debug_enabled)

---

**Copy this file into your project's README or internal docs for future deployments!**
