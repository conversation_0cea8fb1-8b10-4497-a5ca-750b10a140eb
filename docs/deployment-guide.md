# FundFlow Deployment Guide

This guide covers the complete deployment pipeline for the FundFlow serverless application, including both automated CI/CD and manual deployment processes.

## Overview

The FundFlow deployment pipeline supports three environments:

- **Development (dev)**: For feature development and testing
- **Staging**: For integration testing and pre-production validation
- **Production (prod)**: Live environment serving users

## Deployment Architecture

### Automated CI/CD Pipeline (GitHub Actions)

The CI/CD pipeline automatically handles deployments based on Git branch strategy:

| Branch        | Target Environment | Trigger           | Approval Required          |
| ------------- | ------------------ | ----------------- | -------------------------- |
| `develop`     | Staging            | Push              | No                         |
| `release/*`   | Staging            | Push              | No                         |
| `main`        | Production         | Push              | Yes (GitHub Environment)   |
| Pull Requests | Development        | PR to main        | No                         |
| Manual        | Any                | Workflow Dispatch | Yes (Environment-specific) |

### Branch Strategy

```
main (prod) ← release/v1.0.0 (staging) ← develop (staging) ← feature/* (dev)
```

## Prerequisites

### Local Development Setup

1. **Required Tools**:

   ```bash
   # Install AWS CLI
   curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
   unzip awscliv2.zip && sudo ./aws/install

   # Install AWS SAM CLI
   pip install aws-sam-cli

   # Install Docker (optional for local testing)
   # See: https://docs.docker.com/get-docker/
   ```

2. **AWS Profile Configuration**:

   ```bash
   # Run the setup script
   ./scripts/setup-aws-profiles.sh

   # Or configure manually
   aws configure --profile fundflow-dev
   aws configure --profile fundflow-staging
   aws configure --profile fundflow-prod
   ```

3. **Environment Check**:
   ```bash
   # Verify setup
   ./scripts/check-environment.sh
   ```

### GitHub Actions Setup

Configure the following secrets in GitHub repository settings:

#### Environment-Specific Secrets

**Development Environment**:

- `AWS_ACCESS_KEY_ID_DEV`
- `AWS_SECRET_ACCESS_KEY_DEV`

**Staging Environment**:

- `AWS_ACCESS_KEY_ID_STAGING`
- `AWS_SECRET_ACCESS_KEY_STAGING`

**Production Environment**:

- `AWS_ACCESS_KEY_ID_PROD`
- `AWS_SECRET_ACCESS_KEY_PROD`

#### GitHub Environment Protection

1. Go to Repository Settings → Environments
2. Create environments: `development`, `staging`, `production`
3. Configure protection rules:
   - **Production**: Require reviewers, restrict to `main` branch
   - **Staging**: Restrict to `develop` and `release/*` branches
   - **Development**: No restrictions

## Deployment Methods

### 1. Automated Deployment (Recommended)

#### Via Git Workflow

```bash
# Deploy to staging
git checkout develop
git push origin develop

# Deploy to production
git checkout main
git merge develop
git push origin main
```

#### Via Manual Trigger

1. Go to GitHub Actions → Deploy FundFlow Infrastructure
2. Click "Run workflow"
3. Select environment and options
4. Click "Run workflow"

### 2. Manual Deployment

#### Quick Deployment

```bash
# Deploy to development
./scripts/deploy.sh -e dev

# Deploy to staging
./scripts/deploy.sh -e staging

# Deploy to production (with confirmation)
./scripts/deploy.sh -e prod
```

#### With Validation

```bash
# Pre-deployment validation
./scripts/validate-deployment.sh -e prod

# Preview changes (dry run)
./scripts/deploy.sh -e prod -d

# Deploy after review
./scripts/deploy.sh -e prod
```

#### Using Parameter Files

```bash
# Deploy with parameter file override
./scripts/deploy.sh -e prod -f
```

## Deployment Scripts Reference

### Core Scripts

| Script                   | Purpose                      | Usage                                       |
| ------------------------ | ---------------------------- | ------------------------------------------- |
| `deploy.sh`              | Main deployment script       | `./scripts/deploy.sh -e <env>`              |
| `validate-deployment.sh` | Pre-deployment validation    | `./scripts/validate-deployment.sh -e <env>` |
| `check-environment.sh`   | Environment setup validation | `./scripts/check-environment.sh`            |
| `local-test.sh`          | Local development testing    | `./scripts/local-test.sh api`               |

### Deployment Script Options

```bash
./scripts/deploy.sh [OPTIONS]

Options:
  -e, --environment    Target environment (dev, staging, prod)
  -r, --region         AWS region [default: us-east-1]
  -p, --profile        AWS profile to use
  -f, --use-param-file Use parameter file instead of samconfig
  -v, --validate-only  Only validate template, don't deploy
  -b, --build-only     Only build, don't deploy
  -d, --dry-run        Preview changes without deploying
  -h, --help           Show help message
```

### Example Commands

```bash
# Standard deployments
./scripts/deploy.sh -e dev
./scripts/deploy.sh -e staging -p fundflow-staging
./scripts/deploy.sh -e prod -p fundflow-prod

# Validation and preview
./scripts/deploy.sh -e prod -v  # Validate only
./scripts/deploy.sh -e prod -d  # Dry run
./scripts/deploy.sh -e prod -b  # Build only

# Using parameter files
./scripts/deploy.sh -e prod -f
```

## Configuration Management

### Environment Parameters

Environment-specific configurations are managed through:

1. **SAM Config** (`samconfig.toml`): Default deployment parameters
2. **Parameter Files** (`parameters/*.json`): Environment-specific overrides

#### Parameter File Structure

```json
{
  "Parameters": {
    "Environment": "prod",
    "DomainName": "api.fundflow.com",
    "CertificateArn": "arn:aws:acm:us-east-1:123456789012:certificate/...",
    "LogLevel": "WARNING",
    "LambdaMemorySize": "1024",
    "LambdaTimeout": "60",
    "ApiThrottleBurstLimit": "500",
    "ApiThrottleRateLimit": "250"
  }
}
```

### Environment-Specific Settings

| Parameter             | Dev   | Staging | Prod    |
| --------------------- | ----- | ------- | ------- |
| LogLevel              | DEBUG | INFO    | WARNING |
| LambdaMemorySize      | 512   | 1024    | 1024    |
| LambdaTimeout         | 30    | 30      | 60      |
| ApiThrottleBurstLimit | 100   | 200     | 500     |
| ApiThrottleRateLimit  | 50    | 100     | 250     |

## Safety Features

### Production Safeguards

1. **Confirmation Prompts**: Production deployments require explicit confirmation
2. **Account Verification**: Validates correct AWS account before deployment
3. **Dry Run Support**: Preview changes before applying
4. **Change Sets**: CloudFormation change sets for review
5. **Rollback Capability**: Automatic rollback on deployment failure

### Validation Checks

The validation script checks:

- ✅ Required tools installed (SAM CLI, AWS CLI)
- ✅ AWS credentials configured
- ✅ SAM template validity
- ✅ Parameter file existence and structure
- ✅ Environment-specific configuration
- ✅ Stack status and dependencies
- ✅ Production-specific requirements

## Monitoring and Verification

### Post-Deployment Checks

```bash
# Get stack outputs
aws cloudformation describe-stacks \
  --stack-name fundflow-prod \
  --region us-east-1 \
  --query 'Stacks[0].Outputs' \
  --output table

# Test API health
curl https://api-gateway-url/health

# View stack events
aws cloudformation describe-stack-events \
  --stack-name fundflow-prod \
  --region us-east-1
```

### Stack Outputs

Each deployment provides these outputs:

- `ApiGatewayUrl`: API Gateway endpoint URL
- `CloudFrontUrl`: CloudFront distribution URL
- `StaticAssetsBucketName`: S3 bucket for static assets
- `ReportsBucketName`: S3 bucket for reports
- `UserPoolId`: Cognito User Pool ID
- `UserPoolClientId`: Cognito User Pool Client ID

## Troubleshooting

### Common Issues

#### Docker Not Available

```bash
# Error: Running AWS SAM projects locally requires Docker
# Solution: Install and start Docker Desktop
brew install --cask docker
open /Applications/Docker.app
```

#### Invalid AWS Credentials

```bash
# Error: AWS CLI is not configured
# Solution: Configure AWS credentials
aws configure --profile fundflow-dev
```

#### Template Validation Errors

```bash
# Check template syntax
sam validate --region us-east-1

# Common fixes:
# - Check YAML indentation
# - Verify resource names and references
# - Ensure parameter values are valid
```

#### Stack in Failed State

```bash
# Check stack status
aws cloudformation describe-stacks \
  --stack-name fundflow-dev \
  --query 'Stacks[0].StackStatus'

# If ROLLBACK_COMPLETE or CREATE_FAILED:
aws cloudformation delete-stack --stack-name fundflow-dev
```

### Rollback Procedures

#### Automatic Rollback

SAM automatically rolls back failed deployments to the previous working state.

#### Manual Rollback

```bash
# List stack events to identify issue
aws cloudformation describe-stack-events \
  --stack-name fundflow-prod

# Revert to previous version (if available)
# Deploy previous working template
git checkout <previous-working-commit>
./scripts/deploy.sh -e prod
```

### Getting Help

1. **Check validation first**: `./scripts/validate-deployment.sh -e <env>`
2. **Review stack events**: AWS CloudFormation console
3. **Check logs**: CloudWatch logs for Lambda functions
4. **Test locally**: `./scripts/local-test.sh api`

## Best Practices

### Development Workflow

1. **Feature Development**:

   ```bash
   git checkout -b feature/new-feature
   # Make changes
   ./scripts/local-test.sh validate
   ./scripts/deploy.sh -e dev -d  # Preview
   ./scripts/deploy.sh -e dev     # Deploy
   ```

2. **Integration Testing**:

   ```bash
   git checkout develop
   git merge feature/new-feature
   git push origin develop  # Auto-deploy to staging
   ```

3. **Production Release**:
   ```bash
   git checkout main
   git merge develop
   git push origin main  # Auto-deploy to production
   ```

### Security Considerations

- Use least-privilege IAM policies for deployment credentials
- Store sensitive configuration in AWS Systems Manager Parameter Store
- Enable CloudTrail for deployment auditing
- Review CloudFormation change sets before production deployment
- Use separate AWS accounts for different environments

### Performance Optimization

- Monitor CloudWatch metrics post-deployment
- Adjust Lambda memory/timeout based on usage patterns
- Review API Gateway throttling settings
- Optimize CloudFront cache behaviors
- Use DynamoDB on-demand billing for variable workloads

## Next Steps

After successful deployment:

1. **Configure Monitoring**: Set up CloudWatch alarms and dashboards
2. **Implement Logging**: Configure centralized logging with AWS X-Ray
3. **Security Hardening**: Review and tighten IAM policies
4. **Performance Tuning**: Monitor and optimize resource configurations
5. **Backup Strategy**: Implement automated backup procedures
6. **Disaster Recovery**: Plan and test recovery procedures
