# AWS SAM Development Environment Setup

This guide will help you set up the complete development environment for the FundFlow AWS Serverless application.

## Prerequisites

### Required Tools

1. **AWS CLI** (✅ Already installed: v2.27.7)

   - Used for AWS account interactions and resource management
   - Documentation: https://docs.aws.amazon.com/cli/latest/userguide/

2. **AWS SAM CLI** (✅ Already installed: v1.140.0)

   - Used for building, testing, and deploying serverless applications
   - Documentation: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html

3. **Docker Desktop** (⚠️ Installation pending)
   - Required for `sam local` commands (local testing)
   - Download from: https://www.docker.com/products/docker-desktop/

### Python Environment

The project includes a Python virtual environment:

- Virtual environment: `ff_env`
- Status: ✅ Already activated

## AWS Profile Configuration

### Environment-Specific Profiles

The project is configured to use separate AWS profiles for each environment:

| Environment | Profile Name       | Purpose                 |
| ----------- | ------------------ | ----------------------- |
| Development | `fundflow-dev`     | Development and testing |
| Staging     | `fundflow-staging` | Pre-production testing  |
| Production  | `fundflow-prod`    | Production deployment   |

### Setup AWS Profiles

Run the automated setup script:

```bash
./scripts/setup-aws-profiles.sh
```

This script will:

1. Prompt for AWS credentials for each environment
2. Configure AWS profiles with proper naming
3. Test connectivity for each profile
4. Validate the setup

### Manual Profile Setup (Alternative)

If you prefer manual setup:

```bash
# Development environment
aws configure --profile fundflow-dev
# Enter: Access Key ID, Secret Access Key, Region (us-east-1), Output (json)

# Staging environment
aws configure --profile fundflow-staging
# Enter: Access Key ID, Secret Access Key, Region (us-east-1), Output (json)

# Production environment
aws configure --profile fundflow-prod
# Enter: Access Key ID, Secret Access Key, Region (us-east-1), Output (json)
```

### Verify Profile Configuration

```bash
# List all profiles
aws configure list-profiles

# Test each profile
aws sts get-caller-identity --profile fundflow-dev
aws sts get-caller-identity --profile fundflow-staging
aws sts get-caller-identity --profile fundflow-prod
```

## Docker Desktop Setup

### Install Docker Desktop

**Option 1: Direct Download**

1. Download from: https://www.docker.com/products/docker-desktop/
2. Install the .dmg file
3. Start Docker Desktop

**Option 2: Homebrew** (if permission issues are resolved)

```bash
brew install --cask docker
```

### Verify Docker Installation

```bash
docker --version
docker run hello-world
```

### Why Docker is Needed

Docker is required for:

- `sam local start-api` - Local API Gateway simulation
- `sam local start-lambda` - Local Lambda function testing
- `sam local invoke` - Individual function testing

## Validation & Testing

### Template Validation

```bash
# Validate SAM template
sam validate

# Validate with environment-specific parameters
./scripts/deploy.sh -e dev -v
```

### Local Testing (requires Docker)

```bash
# Start local API Gateway
sam local start-api --env-vars env.json

# Test individual function
sam local invoke HealthCheckFunction --event events/health-check.json
```

### Environment Testing

```bash
# Test deployment to dev environment (dry-run)
./scripts/deploy.sh -e dev -v

# Test with parameter files
./scripts/deploy.sh -e dev -f -v
```

## Development Workflow

1. **Start Development Session**

   ```bash
   # Activate virtual environment (if not already active)
   source ff_env/bin/activate

   # Validate template
   sam validate
   ```

2. **Local Development**

   ```bash
   # Start local API (requires Docker)
   sam local start-api

   # In another terminal, test endpoints
   curl http://localhost:3000/health
   ```

3. **Deploy Changes**

   ```bash
   # Deploy to dev environment
   ./scripts/deploy.sh -e dev

   # Deploy to staging (with confirmation)
   ./scripts/deploy.sh -e staging
   ```

## Environment Variables

Create a `.env` file in the project root with any local configuration:

```bash
# Example .env file
AWS_DEFAULT_REGION=us-east-1
LOG_LEVEL=DEBUG
```

## Troubleshooting

### Common Issues

1. **AWS Profile Not Found**

   ```bash
   # Error: The config profile (fundflow-dev) could not be found
   # Solution: Run ./scripts/setup-aws-profiles.sh
   ```

2. **Docker Not Available**

   ```bash
   # Error: Cannot connect to the Docker daemon
   # Solution: Start Docker Desktop application
   ```

3. **SAM Template Validation Errors**
   ```bash
   # Run validation with detailed output
   sam validate --lint
   ```

### Verification Checklist

- [ ] AWS CLI installed and working
- [ ] SAM CLI installed and working
- [ ] Docker Desktop installed and running
- [ ] AWS profiles configured for all environments
- [ ] SAM template validates successfully
- [ ] Can run local API (if Docker is available)
- [ ] Can deploy to dev environment

## Next Steps

Once your environment is set up:

1. Review the [Infrastructure Documentation](../README-Infrastructure.md)
2. Explore the [Parameter Configuration](../parameters/README.md)
3. Run your first deployment: `./scripts/deploy.sh -e dev`
4. Test the deployed endpoints

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Ensure AWS credentials are valid
4. Consult the AWS SAM documentation
