# Enhanced DynamoDB Schema for Market Data

## Overview

This document outlines the enhanced DynamoDB schema design to support comprehensive market data, valuation metrics, technical indicators, and analytical data for the FundFlow application.

## Table Structure

### Primary Table: `fundflow-{env}-funds`

**Primary Key:**
- Partition Key (PK): `fund_id` (String)
- Sort Key (SK): `data_type#timestamp` (String)

**Global Secondary Indexes:**

1. **GSI1: Type-Date Index**
   - PK: `data_type` (String)
   - SK: `timestamp` (String)
   - Purpose: Query all records of a specific type across funds

2. **GSI2: Fund-Type Index**
   - PK: `fund_id` (String)
   - SK: `data_type` (String)
   - Purpose: Get latest data of each type for a fund

3. **GSI3: Source-Quality Index**
   - PK: `data_source` (String)
   - SK: `data_quality#timestamp` (String)
   - Purpose: Query data by source and quality

## Data Types and Access Patterns

### 1. Fund Basic Information
```
PK: FUND#{fund_id}
SK: INFO#latest
data_type: INFO
```

### 2. Price Data
```
PK: FUND#{fund_id}
SK: PRICE#{timestamp}
data_type: PRICE
```

### 3. Valuation Metrics
```
PK: FUND#{fund_id}
SK: VALUATION#{date}
data_type: VALUATION
```

### 4. Technical Indicators
```
PK: FUND#{fund_id}
SK: TECHNICAL#{timestamp}
data_type: TECHNICAL
```

### 5. Risk Analytics
```
PK: FUND#{fund_id}
SK: RISK#{date}
data_type: RISK
```

### 6. Market Data Input Records
```
PK: FUND#{fund_id}
SK: INPUT#{timestamp}#{user_id}
data_type: INPUT
```

### 7. Benchmark Data
```
PK: BENCHMARK#{benchmark_id}
SK: DATA#{timestamp}
data_type: BENCHMARK
```

## Item Structures

### Price Data Item
```json
{
  "PK": "FUND#fund-123",
  "SK": "PRICE#2024-01-15T10:30:00Z",
  "data_type": "PRICE",
  "fund_id": "fund-123",
  "timestamp": "2024-01-15T10:30:00Z",
  "nav": {
    "value": "125.45",
    "currency": "USD",
    "source": "fund_company",
    "quality": "excellent",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "market_price": {
    "value": "125.50",
    "currency": "USD",
    "source": "yahoo_finance",
    "quality": "good",
    "timestamp": "2024-01-15T10:29:45Z"
  },
  "volume": 150000,
  "bid_ask_spread": "0.05",
  "market_cap": "5000000000",
  "ttl": 1705392600
}
```

### Valuation Metrics Item
```json
{
  "PK": "FUND#fund-123",
  "SK": "VALUATION#2024-01-15",
  "data_type": "VALUATION",
  "fund_id": "fund-123",
  "as_of_date": "2024-01-15",
  "price_to_book": "2.45",
  "price_to_earnings": "18.5",
  "price_to_sales": "3.2",
  "enterprise_value": "6000000000",
  "return_on_equity": "15.2",
  "debt_to_equity": "0.45",
  "dividend_yield": "2.8",
  "ttl": 1705392600
}
```

### Technical Indicators Item
```json
{
  "PK": "FUND#fund-123",
  "SK": "TECHNICAL#2024-01-15T16:00:00Z",
  "data_type": "TECHNICAL",
  "fund_id": "fund-123",
  "timestamp": "2024-01-15T16:00:00Z",
  "sma_20": "124.80",
  "sma_50": "123.45",
  "sma_200": "120.30",
  "rsi_14": "65.4",
  "macd_line": "0.85",
  "macd_signal": "0.72",
  "bollinger_upper": "127.50",
  "bollinger_lower": "122.10",
  "support_level": "121.00",
  "resistance_level": "128.00",
  "ttl": 1705392600
}
```

### Risk Analytics Item
```json
{
  "PK": "FUND#fund-123",
  "SK": "RISK#2024-01-15",
  "data_type": "RISK",
  "fund_id": "fund-123",
  "as_of_date": "2024-01-15",
  "var_1d_95": "-2.5",
  "var_1d_99": "-3.8",
  "cvar_1d_95": "-3.2",
  "cvar_1d_99": "-4.5",
  "sharpe_ratio": "1.25",
  "sortino_ratio": "1.45",
  "max_drawdown": "-15.2",
  "volatility": "18.5",
  "beta": "1.05",
  "correlation": "0.85",
  "ttl": 1705392600
}
```

### Market Data Input Item
```json
{
  "PK": "FUND#fund-123",
  "SK": "INPUT#2024-01-15T14:30:00Z#user-456",
  "data_type": "INPUT",
  "fund_id": "fund-123",
  "input_timestamp": "2024-01-15T14:30:00Z",
  "data_timestamp": "2024-01-15T14:00:00Z",
  "input_by": "user-456",
  "nav": "125.45",
  "market_price": "125.50",
  "volume": 150000,
  "price_to_book": "2.45",
  "volatility": "18.5",
  "notes": "Manual input from fund company report",
  "validated": true,
  "validation_notes": "Verified against official NAV",
  "ttl": 1705392600
}
```

## Query Patterns

### 1. Get Latest Fund Data
```
Query: PK = "FUND#fund-123" AND SK begins_with "INFO#"
Limit: 1
SortOrder: Descending
```

### 2. Get Price History
```
Query: PK = "FUND#fund-123" AND SK between "PRICE#2024-01-01" and "PRICE#2024-01-31"
```

### 3. Get Latest Market Data Summary
```
BatchGetItem:
- PK = "FUND#fund-123", SK = "PRICE#latest"
- PK = "FUND#fund-123", SK = "VALUATION#latest"
- PK = "FUND#fund-123", SK = "TECHNICAL#latest"
- PK = "FUND#fund-123", SK = "RISK#latest"
```

### 4. Get All Funds with Recent Price Updates
```
GSI1 Query: PK = "PRICE" AND SK > "2024-01-15T00:00:00Z"
```

### 5. Get Market Data Inputs by User
```
Query: PK = "FUND#fund-123" AND SK begins_with "INPUT#"
FilterExpression: input_by = "user-456"
```

## Data Lifecycle Management

### TTL (Time To Live) Strategy
- **Price Data**: 90 days for intraday, 1 year for daily
- **Technical Indicators**: 30 days for intraday, 6 months for daily
- **Valuation Metrics**: 2 years
- **Risk Analytics**: 2 years
- **Market Data Inputs**: 1 year
- **Fund Info**: No TTL (permanent)

### Data Archival
- Older data automatically archived to S3 via DynamoDB Streams
- Point-in-time recovery enabled for 35 days
- Backup strategy: Daily backups retained for 30 days

## Performance Considerations

### Read Patterns
- Use eventually consistent reads for non-critical data
- Implement caching layer (Redis/ElastiCache) for frequently accessed data
- Batch operations for multiple fund queries

### Write Patterns
- Use batch writes for bulk data imports
- Implement write sharding for high-frequency updates
- Use conditional writes to prevent data conflicts

### Cost Optimization
- Use on-demand billing for variable workloads
- Implement data compression for large items
- Archive old data to reduce storage costs

## Security and Compliance

### Access Control
- IAM roles with least privilege access
- VPC endpoints for secure access
- Encryption at rest and in transit

### Data Quality
- Validation rules for all numeric fields
- Source tracking for audit trails
- Data quality scoring system

### Monitoring
- CloudWatch metrics for read/write capacity
- Alarms for throttling and errors
- Custom metrics for data freshness
