# Fund Data Models

This document describes the new fund data models implemented for the FundFlow application. These models follow a DynamoDB single-table design pattern and are built using Pydantic for data validation.

## Overview

The fund data models are designed to store detailed fund information, statistics, returns, and exposure data in a single DynamoDB table using a composite key structure (PK/SK). This design optimizes for various query patterns while maintaining data consistency.

## Models

### BaseItem

Base class for all fund data entities.

```python
class BaseItem(BaseModel):
    PK: str          # Partition key
    SK: str          # Sort key
    entity_type: str # Entity type identifier
```

### FundInfo

Stores basic fund information and characteristics.

**Access Pattern:**

- PK: `FUND#{fund_id}`
- SK: `INFO#{as_of_date}`
- entity_type: `INFO`

**Key Fields:**

- `fund_name`: Fund name
- `summary`: Fund description
- `structure`: Fund structure (e.g., UCITS, SICAV)
- `base_currency`: Three-letter currency code
- `fund_managers`: List of fund manager names
- `share_classes`: Available share classes
- `management_fee`: Management fees by share class
- `performance_fee_pct`: Performance fee percentage
- `status`: Fund status (active, closed, suspended)

### FundStats

Stores fund statistical data and performance metrics.

**Access Pattern:**

- PK: `FUND#{fund_id}`
- SK: `STATS#{as_of_date}`
- entity_type: `STATS`

**Key Fields:**

- `aum_usd_bn`: Assets under management in USD billions
- `sharpe_ratio`: Sharpe ratio
- `beta`: Beta coefficient
- `correlation`: Correlation coefficient
- `volatility_pct`: Volatility percentage
- `inception_date`: Fund inception date

### MonthlyReturn

Stores monthly return data for funds.

**Access Pattern:**

- PK: `FUND#{fund_id}`
- SK: `RETURN#{year}#{month:02d}`
- entity_type: `RETURN`

**Key Fields:**

- `year`: Return year
- `month`: Return month (1-12)
- `net_return_pct`: Net return percentage

### CalendarYearReturn

Stores annual return data with benchmark comparisons.

**Access Pattern:**

- PK: `FUND#{fund_id}`
- SK: `CAL_YEAR_RET#{year}`
- entity_type: `CAL_YEAR_RET`

**Key Fields:**

- `year`: Calendar year
- `net_return_pct`: Net return percentage for the year
- `benchmarks`: Dictionary of benchmark returns for comparison

### ExposureSnapshot

Stores fund exposure data at a point in time.

**Access Pattern:**

- PK: `FUND#{fund_id}`
- SK: `EXPOSURE#{as_of_date}`
- entity_type: `EXPOSURE`

**Key Fields:**

- `long_pct`: Long exposure percentage
- `short_pct`: Short exposure percentage (negative values)
- `net_public_pct`: Net public equity exposure
- `net_private_pct`: Net private equity exposure
- `gross_pct`: Gross exposure percentage
- `positive_months`: Number of positive return months
- `negative_months`: Number of negative return months

### MarketCapExposure

Stores market capitalization exposure breakdown.

**Access Pattern:**

- PK: `FUND#{fund_id}`
- SK: `MCAP_EXP#{as_of_date}`
- entity_type: `MCAP_EXP`

**Key Fields:**

- `buckets`: Dictionary containing market cap exposure buckets (e.g., large_cap, mid_cap, small_cap)

## Usage Examples

### Creating Fund Information

```python
from datetime import date
from decimal import Decimal
from src.shared.models.fund_data import FundInfo, generate_fund_info_pk, generate_fund_info_sk

fund_info = FundInfo(
    PK=generate_fund_info_pk("my-fund-123"),
    SK=generate_fund_info_sk(date(2024, 1, 1)),
    entity_type="INFO",
    as_of=date(2024, 1, 1),
    fund_name="Growth Equity Fund",
    summary="A diversified growth equity fund",
    structure="UCITS",
    base_currency="USD",
    fund_managers=["John Doe", "Jane Smith"],
    share_classes=["A", "B", "C"],
    subscription_freq="Daily",
    redemption_freq="Daily",
    notice_period_days=1,
    management_fee={"A": Decimal("1.5"), "B": Decimal("1.0")},
    performance_fee_pct=Decimal("20.0"),
    status="active"
)
```

### Creating Monthly Returns

```python
from src.shared.models.fund_data import MonthlyReturn, generate_fund_info_pk, generate_monthly_return_sk

monthly_return = MonthlyReturn(
    PK=generate_fund_info_pk("my-fund-123"),
    SK=generate_monthly_return_sk(2024, 1),
    entity_type="RETURN",
    year=2024,
    month=1,
    net_return_pct=2.5
)
```

### DynamoDB Conversion

The models include utilities for converting to/from DynamoDB format:

```python
from src.shared.models.fund_data import FundDataDynamoDBItem

# Convert to DynamoDB item format
dynamodb_item = FundDataDynamoDBItem.to_dynamodb_item(fund_info)

# Convert from DynamoDB item format
fund_info_restored = FundDataDynamoDBItem.from_dynamodb_item(dynamodb_item, FundInfo)
```

## Key Generation Helpers

The module includes helper functions for generating consistent partition and sort keys:

- `generate_fund_info_pk(fund_id)`: Creates partition key for fund data
- `generate_fund_info_sk(as_of_date)`: Creates sort key for fund info
- `generate_fund_stats_sk(as_of_date)`: Creates sort key for fund statistics
- `generate_monthly_return_sk(year, month)`: Creates sort key for monthly returns
- `generate_calendar_year_return_sk(year)`: Creates sort key for calendar year returns
- `generate_exposure_sk(as_of_date)`: Creates sort key for exposure snapshots
- `generate_market_cap_exposure_sk(as_of_date)`: Creates sort key for market cap exposure

## Validation

All models include comprehensive validation:

- Date ranges for years and months
- Reasonable bounds for financial metrics
- Currency code format validation
- Management fee percentage limits
- Exposure percentage constraints

## Testing

Comprehensive tests are available in `tests/test_fund_data_models.py` covering:

- Model instantiation and validation
- Error handling for invalid data
- DynamoDB conversion utilities
- Key generation functions

Run tests with:

```bash
python -m pytest tests/test_fund_data_models.py -v
```

## Integration

The models are exported from `src.shared.models` and can be imported as:

```python
from src.shared.models import (
    FundInfo,
    FundStats,
    MonthlyReturn,
    CalendarYearReturn,
    ExposureSnapshot,
    MarketCapExposure,
    FundDataDynamoDBItem
)
```
