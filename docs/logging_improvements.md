# Logging Improvements for PDF Extractor Lambda

## Problem Identified

The CloudWatch logs were showing random strings instead of proper readable logging. This was caused by several issues:

1. **Binary Data Logging**: The Lambda function was configured with `log_event=True` in the AWS Lambda Powertools decorator, which logs the entire event including base64-encoded PDF content
2. **Raw Body Logging**: Error handlers were logging raw request body content which contains binary data
3. **Large Response Logging**: AI response content was being logged in full, creating cluttered logs

## Root Cause

The main culprit was this line in `src/functions/api/fund_extractor.py`:

```python
@logger.inject_lambda_context(log_event=True)  # This was logging binary data!
```

When `log_event=True` is set, AWS Lambda Powertools automatically logs the entire Lambda event, including the request body. For PDF uploads, this body contains base64-encoded binary data that appears as random strings in CloudWatch.

## Fixes Applied

### 1. Disabled Event Logging
```python
# Before
@logger.inject_lambda_context(log_event=True)

# After  
@logger.inject_lambda_context(log_event=False)  # Disable event logging to avoid binary data in logs
```

### 2. Improved Error Logging
Instead of logging raw body content:
```python
# Before
logger.error(f"Event body preview: {str(event.get('body', ''))[:200]}...")

# After
body_length = len(event.get('body', ''))
logger.error(f"Event body length: {body_length} characters")
```

### 3. Structured Logging with Context
Added structured logging with relevant context without binary data:
```python
logger.info("PDF extraction request received", extra={
    "event_keys": list(event.keys()),
    "content_type": event.get('headers', {}).get('content-type', 'Not found'),
    "is_base64_encoded": event.get('isBase64Encoded', False),
    "body_length": len(event.get('body', '')),
    "method": method,
    "path": path
})
```

### 4. Enhanced Error Context
All error handlers now include structured context:
```python
logger.error("AI API error during PDF extraction", extra={
    "error_type": "RequestException",
    "error_message": str(e),
    "user_id": user_context.get('user_id')
})
```

### 5. Limited AI Response Logging
```python
# Before
logger.error(f"AI Response content: {content}")

# After
content_preview = content[:500] + "..." if len(content) > 500 else content
logger.error(f"AI Response content preview: {content_preview}")
```

## Benefits

1. **Readable Logs**: CloudWatch logs now show structured, meaningful information instead of random strings
2. **Better Debugging**: Each log entry includes relevant context (user_id, error_type, etc.)
3. **Performance**: Reduced log volume by not logging large binary data
4. **Security**: Sensitive binary data is no longer exposed in logs

## Deployment

To deploy these changes:

```bash
# Build and deploy the updated Lambda function
sam build
sam deploy --stack-name fundflow-dev --region ap-northeast-1
```

## Monitoring

After deployment, CloudWatch logs should show:

### Successful Request
```json
{
    "level": "INFO",
    "location": "handler:350",
    "message": "Starting PDF extraction process",
    "timestamp": "2025-07-02 08:00:00,000+0000",
    "service": "fundflow-pdf-extractor",
    "pdf_size_chars": 123456,
    "user_id": "user-123"
}
```

### Error Case
```json
{
    "level": "ERROR", 
    "location": "handler:403",
    "message": "AI API error during PDF extraction",
    "timestamp": "2025-07-02 08:00:00,000+0000",
    "service": "fundflow-pdf-extractor",
    "error_type": "RequestException",
    "error_message": "Connection timeout",
    "user_id": "user-123"
}
```

## Additional Recommendations

1. **Log Retention**: Consider setting appropriate log retention periods in CloudWatch to manage costs
2. **Log Aggregation**: Consider using AWS CloudWatch Insights for better log analysis
3. **Alerting**: Set up CloudWatch alarms for error rates and specific error types
4. **Monitoring Dashboard**: Create a dashboard to monitor PDF extraction success/failure rates

## Files Modified

- `src/functions/api/fund_extractor.py` - Main Lambda handler
- `src/functions/api/pdf_fund_extractor.py` - PDF extraction class
- `docs/logging_improvements.md` - This documentation
