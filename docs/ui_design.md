# FundFlow UI Design System

## Overview
This document outlines the comprehensive design system for the FundFlow financial management application, ensuring consistency across all screens and components.

## 🎨 Color Scheme

### Primary Colors
- **Primary Blue**: `#2563eb` (blue-600) / `#3b82f6` (blue-500 dark)
- **Primary Blue Hover**: `#1d4ed8` (blue-700) / `#2563eb` (blue-600 dark)
- **Primary Blue Light**: `#dbeafe` (blue-100) / `#1e3a8a` (blue-900 dark)

### Semantic Colors
- **Success**: Green variants for positive financial indicators
- **Warning**: Yellow variants for alerts and warnings  
- **Error**: Red variants for negative values and errors
- **Info**: Blue variants for informational content

### Grayscale Palette
```css
--color-gray-50: #f9fafb;   /* Lightest background */
--color-gray-100: #f3f4f6;  /* Light background */
--color-gray-200: #e5e7eb;  /* Border light */
--color-gray-300: #d1d5db;  /* Border */
--color-gray-400: #9ca3af;  /* Placeholder text */
--color-gray-500: #6b7280;  /* Secondary text */
--color-gray-600: #4b5563;  /* Primary text light */
--color-gray-700: #374151;  /* Primary text */
--color-gray-800: #1f2937;  /* Dark background */
--color-gray-900: #111827;  /* Darkest background */
--color-white: #ffffff;     /* Pure white */
```

### Dark Mode Colors
```css
:root {
  --background-rgb: 255 255 255;
  --foreground-rgb: 0 0 0;
  --card-rgb: 240 240 240;
  --card-border-rgb: 224 224 224;
}

.dark {
  --background-rgb: 20 20 20;
  --foreground-rgb: 255 255 255;
  --card-rgb: 30 30 30;
  --card-border-rgb: 50 50 50;
}
```

## 🔤 Typography

### Font Family
- **Primary**: Lato (Google Fonts)
- **Weights**: 300 (Light), 400 (Regular), 700 (Bold), 900 (Black)
- **Fallback**: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif

### Font Configuration
```css
font-family: var(--font-lato), "Lato", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
```

### Typography Scale
- **Headings**: 
  - H1: `text-3xl font-bold` (30px, bold)
  - H2: `text-2xl font-bold` (24px, bold)
  - H3: `text-xl font-bold` (20px, bold)
  - Card Title: `text-lg font-semibold` (18px, semibold)
- **Body Text**: `text-base` (16px, regular)
- **Small Text**: `text-sm` (14px, regular)
- **Labels**: `text-sm font-medium` (14px, medium)

## 🧩 Component Design System

### Button Component
**Variants:**
- `primary`: Blue background, white text
- `secondary`: Gray background, white text
- `outline`: White/transparent background, colored border
- `ghost`: Transparent background, colored text
- `destructive`: Red background, white text

**Sizes:**
- `sm`: `px-3 py-2 text-sm` (Small)
- `md`: `px-4 py-2 text-base` (Medium - default)
- `lg`: `px-6 py-3 text-lg` (Large)

**States:**
- Default: Base styling
- Hover: Darker background
- Focus: Ring outline (`focus:ring-2 focus:ring-offset-2`)
- Disabled: `opacity-50 cursor-not-allowed`
- Loading: Spinner icon with disabled state

### Card Component
**Structure:**
- Base: `bg-white dark:bg-gray-800 rounded-lg`
- Border: `border border-gray-200 dark:border-gray-700`
- Shadow: `shadow-sm` (default), `shadow-md`, `shadow-lg`
- Hover: `hover:shadow-md transition-shadow duration-200`

**Sub-components:**
- `Card.Header`: `mb-4`
- `Card.Title`: `text-lg font-semibold text-gray-900 dark:text-gray-100`
- `Card.Description`: `text-sm text-gray-600 dark:text-gray-400`
- `Card.Content`: Main content area
- `Card.Footer`: `mt-4 pt-4 border-t border-gray-200 dark:border-gray-700`

### Form Elements

**Input Fields:**
```css
w-full px-3 py-2 border rounded-md shadow-sm 
focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 
transition-colors bg-white dark:bg-gray-800 
text-gray-900 dark:text-gray-100
```

**States:**
- Default: `border-gray-300 dark:border-gray-600`
- Error: `border-red-300 dark:border-red-600 focus:ring-red-500`
- Success: `border-green-300 dark:border-green-600 focus:ring-green-500`

**Labels:**
```css
block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1
```

**Error Messages:**
```css
mt-1 text-sm text-red-600 dark:text-red-400 flex items-center
```

## 🎯 Layout Patterns

### Page Layout
```css
min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors
```

### Container
```css
max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6
```

### Navigation
- Background: `bg-white dark:bg-gray-900`
- Border: `border-b border-gray-200 dark:border-gray-700`
- Height: `h-16`
- Shadow: `shadow-lg`

### Grid Layouts
- Single column: `grid-cols-1`
- Two columns: `md:grid-cols-2`
- Three columns: `lg:grid-cols-3`
- Gap: `gap-4` or `gap-6`

## 🔄 Interactive States

### Loading States
**Spinner Animation:**
```css
animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600
```

**Skeleton Loading:**
```css
bg-gray-200 dark:bg-gray-700 rounded-md animate-pulse
```

### Hover Effects
- Cards: `hover:shadow-md transition-shadow duration-200`
- Buttons: Background color darkens
- Links: `hover:text-gray-900 dark:hover:text-gray-100`

### Focus States
- Ring: `focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`
- Outline: `focus:outline-none`

## 🌙 Dark Mode Implementation

### Theme Toggle
- Button variant: Simple toggle with sun/moon icons
- Dropdown variant: Light/Dark/System options
- Smooth transitions: `transition-colors duration-200`

### Color Transitions
All color-changing elements include:
```css
transition-colors duration-200
```

## 🎨 Icon System

### Icon Library
- **Material-UI Icons** (@mui/icons-material)
- Common icons: Home, Dashboard, AccountBalance, Receipt, Settings, Menu, Close

### Icon Usage
- Navigation: `w-5 h-5` (20px)
- Logo: `text-2xl` (24px)
- Form validation: `w-4 h-4` (16px)

## 📱 Responsive Design

### Breakpoints
- `sm`: 640px and up
- `md`: 768px and up  
- `lg`: 1024px and up
- `xl`: 1280px and up

### Mobile Navigation
- Hidden on desktop: `hidden md:flex`
- Mobile menu: Hamburger icon with slide-out menu
- Touch-friendly sizing: Minimum 44px touch targets

## 🎭 Animation & Transitions

### Standard Transitions
- Duration: `duration-200` (200ms)
- Easing: `ease-in-out`
- Properties: `transition-colors`, `transition-shadow`, `transition-transform`

### Loading Animations
- Spin: `animate-spin`
- Pulse: `animate-pulse`
- Scale on hover: `hover:scale-110`

## 📋 Usage Guidelines

### Consistency Rules
1. Always use the defined color palette
2. Maintain consistent spacing using Tailwind's spacing scale
3. Use semantic color meanings (red for errors, green for success)
4. Ensure proper contrast ratios for accessibility
5. Include dark mode variants for all components
6. Use consistent border radius: `rounded-lg` for cards, `rounded-md` for inputs

### Component Composition
1. Use compound components (Card.Header, Card.Content, etc.)
2. Leverage the base component props interface
3. Include proper TypeScript typing
4. Support all standard HTML attributes via spread props

This design system ensures visual consistency and provides a solid foundation for creating new screens that seamlessly integrate with the existing FundFlow application.
