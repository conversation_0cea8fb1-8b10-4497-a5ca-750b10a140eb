# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
/sample/

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
!.env.example
.env.local
.env.local.*
.env.development
.env.test
.env.production
.env.staging

# SSL certificates and keys
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx

# Sensitive configuration files
config/secrets.json
config/credentials.json

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
