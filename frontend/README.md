# FundFlow Frontend

A modern financial management application built with Next.js 15, TypeScript, and Tailwind CSS.

## 🚀 Tech Stack

- **Framework**: Next.js 15.3.3 with App Router
- **Language**: TypeScript 5
- **Styling**: Tailwind CSS v4
- **State Management**: Redux Toolkit
- **Development**: ESLint + Prettier
- **Package Manager**: npm

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── dashboard/          # Dashboard overview page
│   ├── transactions/       # Transaction management
│   ├── budgets/           # Budget planning
│   ├── reports/           # Financial reports
│   ├── settings/          # User settings
│   ├── layout.tsx         # Root layout with navigation
│   └── page.tsx           # Landing page
├── components/
│   ├── ui/                # Reusable UI components
│   │   ├── Button.tsx     # Styled button component
│   │   ├── Card.tsx       # Card compound component
│   │   └── index.ts       # Component exports
│   └── features/          # Feature-specific components
│       └── Navigation.tsx # Main navigation component
├── store/                 # Redux store configuration
│   ├── store.ts          # Store setup
│   ├── hooks.ts          # Typed Redux hooks
│   ├── Provider.tsx      # Redux Provider wrapper
│   └── slices/           # Redux Toolkit slices
├── types/                # TypeScript type definitions
│   └── index.ts          # Financial domain types
├── utils/                # Utility functions
│   └── index.ts          # Helper functions
├── lib/                  # Constants and configurations
│   └── constants.ts      # App constants
└── hooks/                # Custom React hooks
```

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+ 
- npm 9+

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd FundFlow/frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp env.example .env.local
   # Update the environment variables in .env.local
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📜 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server |
| `npm run build` | Build production application |
| `npm run start` | Start production server |
| `npm run lint` | Run ESLint |
| `npm run lint:fix` | Fix ESLint errors automatically |
| `npm run format` | Format code with Prettier |
| `npm run format:check` | Check code formatting |
| `npm run type-check` | Run TypeScript type checking |
| `npm run clean` | Clean build artifacts |
| `npm run dev:clean` | Clean and start development |
| `npm run precommit` | Run all checks (lint, format, type-check) |

## 🧩 Key Features

### State Management
- **Redux Toolkit** for predictable state management
- Typed hooks (`useAppDispatch`, `useAppSelector`)
- Organized slice structure for different features

### UI Components
- **Reusable components** with consistent styling
- **Compound components** (Card with Header, Content, Footer)
- **Responsive design** with Tailwind CSS utilities
- **Accessible** button variants and states

### Financial Features
- Transaction management and categorization
- Budget planning and tracking
- Financial reporting and analytics
- User settings and preferences

### Type Safety
- **Full TypeScript** integration
- **Financial domain types** (User, Transaction, Budget, Category)
- **API response types** for consistent data handling

## 🎨 Design System

### Colors
Following Tailwind's semantic color system with financial app theming:
- Primary: Blue variants for main actions
- Secondary: Gray variants for secondary elements
- Success: Green for positive financial indicators
- Warning: Yellow for alerts and warnings
- Error: Red for negative values and errors

### Components
- **Button**: Multiple variants (primary, secondary, outline, ghost, destructive)
- **Card**: Flexible container with optional header, content, and footer
- **Navigation**: Responsive navigation with financial app routes

## 📊 Financial Data Types

```typescript
interface Transaction {
  id: string;
  amount: number;
  description: string;
  category: string;
  date: string;
  type: 'income' | 'expense';
  userId: string;
}

interface Budget {
  id: string;
  name: string;
  amount: number;
  spent: number;
  category: string;
  period: 'monthly' | 'yearly';
  userId: string;
}
```

## 🔧 Development Guidelines

### Code Quality
- **ESLint** enforces coding standards
- **Prettier** ensures consistent formatting
- **TypeScript** provides type safety
- **Import organization** with automatic sorting

### Best Practices
- Use TypeScript interfaces for all data structures
- Implement proper error boundaries
- Follow component composition patterns
- Maintain consistent naming conventions
- Write self-documenting code with clear variable names

### Testing Strategy
- Unit tests for utility functions
- Component tests for UI elements
- Integration tests for user workflows
- End-to-end tests for critical paths

## 🚀 Deployment

### AWS Amplify Deployment

The frontend is configured for deployment on AWS Amplify. See [Amplify Deployment Guide](../docs/amplify-deployment-guide.md) for detailed instructions.

**Quick Steps:**
1. Connect GitHub repository to AWS Amplify
2. Use the provided `amplify.yml` configuration
3. Set environment variables in Amplify Console
4. Update Cognito callback URLs with your Amplify domain
5. Deploy and test

### Build for Production
```bash
npm run build
npm run start
```

### Environment Variables
Set the following in your production environment:
- `NODE_ENV=production`
- `NEXT_PUBLIC_API_BASE_URL=https://your-api-gateway-url`
- `NEXT_PUBLIC_AWS_REGION=ap-northeast-1`
- `NEXTAUTH_SECRET=<generate-secure-secret>`
- `NEXTAUTH_URL=https://your-amplify-domain`
- `COGNITO_CLIENT_ID=your-cognito-client-id`
- `COGNITO_ISSUER=your-cognito-issuer-url`

**Generate NEXTAUTH_SECRET:**
```bash
node scripts/generate-nextauth-secret.js
```

## 🤝 Contributing

1. Follow the established file structure
2. Use TypeScript for all new code
3. Run `npm run precommit` before committing
4. Follow the component patterns established in `/components/ui`
5. Update this README when adding new features

## 🎭 Mock Development Mode

For local development without AWS dependencies, enable mock mode:

```env
# Enable mock data fallback
NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=true

# Enable mock login (bypasses Cognito)
NEXT_PUBLIC_ENABLE_MOCK_LOGIN=true
```

**Mock Features:**
- **Mock Authentication**: Sign in with any email/password
- **Mock API Data**: 50+ generated funds with realistic data
- **Complete Local Testing**: No AWS or backend dependencies
- **Development Speed**: Instant authentication and data loading

See [MOCK_LOGIN_SETUP.md](../MOCK_LOGIN_SETUP.md) for detailed setup instructions.

## 📝 Next Steps

- [x] Add authentication with NextAuth.js
- [x] Implement API integration
- [x] Add mock development mode
- [ ] Add comprehensive testing suite
- [ ] Set up Storybook for component documentation
- [ ] Add internationalization (i18n)
- [ ] Implement PWA features
- [ ] Add performance monitoring

---

Built with ❤️ for better financial management.
