# FundFlow Internationalization (i18n) Implementation

This document describes the internationalization implementation for the FundFlow application, supporting English, Simplified Chinese, and Traditional Chinese.

## 🌍 Supported Languages

- **English (en)** - Default language 🇺🇸
- **Simplified Chinese (zh-CN)** - 简体中文 🇨🇳  
- **Traditional Chinese (zh-TW)** - 繁體中文 🇹🇼

## 📁 File Structure

```
frontend/src/i18n/
├── config.ts                 # Language configuration and constants
├── provider.tsx               # React context provider for language management
└── messages/
    ├── en.json               # English translations
    ├── zh-CN.json            # Simplified Chinese translations
    └── zh-TW.json            # Traditional Chinese translations
```

## 🛠 Implementation Details

### 1. Language Provider

The `LanguageProvider` component manages the current locale state and provides translation functions throughout the application.

**Key Features:**
- Automatic message loading for the selected locale
- Fallback to English if translation loading fails
- Persistent language preference in localStorage
- Parameter interpolation support (e.g., `{name}` replacements)

### 2. Translation Functions

```typescript
// Basic usage
const { t } = useTranslation();
const title = t('dashboard.title'); // "Dashboard" | "仪表板" | "儀表板"

// With parameters
const welcome = t('home.welcomeBack', { name: '<PERSON>' });
// "Welcome back, <PERSON>!" | "欢迎回来，John！" | "歡迎回來，John！"
```

### 3. Language Switcher

The `LanguageSwitcher` component provides a dropdown interface for users to change languages.

**Props:**
- `size?: 'sm' | 'md' | 'lg'` - Component size
- `showFlag?: boolean` - Show country flags (default: true)
- `showText?: boolean` - Show language names (default: true)
- `className?: string` - Additional CSS classes

## 🔧 Usage

### 1. Setup in Layout

The `LanguageProvider` is already integrated in the root layout:

```typescript
// src/app/layout.tsx
<LanguageProvider>
  <SessionProvider>
    <ReduxProvider>
      <ThemeProvider>
        {/* App content */}
      </ThemeProvider>
    </ReduxProvider>
  </SessionProvider>
</LanguageProvider>
```

### 2. Using Translations in Components

```typescript
'use client';

import { useTranslation } from '@/i18n/provider';

export default function MyComponent() {
  const { t } = useTranslation();

  return (
    <div>
      <h1>{t('common.title')}</h1>
      <p>{t('common.description')}</p>
    </div>
  );
}
```

### 3. Adding the Language Switcher

```typescript
import LanguageSwitcher from '@/components/ui/LanguageSwitcher';

// Basic usage
<LanguageSwitcher />

// Customized
<LanguageSwitcher 
  size="sm" 
  showText={false} 
  className="ml-2" 
/>
```

## 📝 Translation Key Structure

### Common Keys
```json
{
  "common": {
    "loading": "Loading...",
    "save": "Save",
    "cancel": "Cancel",
    "edit": "Edit"
  }
}
```

### Navigation Keys
```json
{
  "navigation": {
    "home": "HOME",
    "dashboard": "DASHBOARD",
    "funds": "FUNDS"
  }
}
```

### Feature-Specific Keys
```json
{
  "funds": {
    "title": "Funds",
    "createFund": "Create New Fund",
    "validation": {
      "nameRequired": "Fund name is required"
    }
  }
}
```

## 🔄 Adding New Translations

### 1. Add to All Language Files

When adding new translation keys, ensure they are added to all three language files:

```json
// en.json
{
  "newFeature": {
    "title": "New Feature",
    "description": "This is a new feature"
  }
}

// zh-CN.json
{
  "newFeature": {
    "title": "新功能",
    "description": "这是一个新功能"
  }
}

// zh-TW.json
{
  "newFeature": {
    "title": "新功能",
    "description": "這是一個新功能"
  }
}
```

### 2. Update Components

```typescript
// Before
<h1>New Feature</h1>
<p>This is a new feature</p>

// After
<h1>{t('newFeature.title')}</h1>
<p>{t('newFeature.description')}</p>
```

## 🎯 Updated Components

The following components have been updated to support internationalization:

### Core Components
- **Navigation** - Menu items and labels
- **LanguageSwitcher** - Complete language switching interface
- **Home Page** - Welcome messages, features, call-to-action
- **Dashboard** - Page titles and common labels

### Example Updates

**Navigation Component:**
```typescript
const navItems = [
  { href: '/', label: t('navigation.home'), icon: Home },
  { href: '/dashboard', label: t('navigation.dashboard'), icon: Dashboard },
  { href: '/funds', label: t('navigation.funds'), icon: AccountBalance },
];
```

**Home Page:**
```typescript
<h1>{t('home.title')}</h1>
<p>{t('home.subtitle')}</p>
<h3>{t('home.features.realTimeDashboard.title')}</h3>
```

## 🚀 Next Steps

To complete the internationalization implementation:

### 1. Update Remaining Components
- **Funds Pages** (`/funds`, `/funds/[id]`, `/funds/[id]/edit`)
- **Portfolios Page** (`/portfolios`)
- **Auth Pages** (`/auth/signin`, `/auth/error`)
- **Form Components** (FundEditForm, MarketDataInput)

### 2. Add More Translation Keys
- Form validation messages
- Error messages
- Success notifications
- Help text and tooltips

### 3. Example Component Update Pattern

```typescript
'use client';

import { useTranslation } from '@/i18n/provider';

export default function ComponentToUpdate() {
  const { t } = useTranslation();

  return (
    <div>
      {/* Replace hardcoded text */}
      <h1>{t('component.title')}</h1>
      <button>{t('common.save')}</button>
      
      {/* Handle validation messages */}
      {error && <p>{t('validation.required')}</p>}
      
      {/* Dynamic content with parameters */}
      <p>{t('component.welcome', { name: user.name })}</p>
    </div>
  );
}
```

## 🔍 Testing

### Language Switching
1. Start the development server: `npm run dev`
2. Navigate to any page
3. Click the language switcher in the navigation bar
4. Verify that text changes to the selected language
5. Refresh the page to confirm language preference is persisted

### Missing Translations
- Watch the browser console for warnings about missing translation keys
- Missing keys will display the key name as fallback (e.g., "navigation.home")

## 💡 Best Practices

1. **Consistent Key Naming**: Use descriptive, hierarchical key names
2. **Parameter Usage**: Use parameters for dynamic content: `t('message', { name: 'John' })`
3. **Fallback Strategy**: Always ensure English translations exist as fallback
4. **Context Grouping**: Group related translations by feature/component
5. **Pluralization**: For complex pluralization, consider using key variants

## 🐛 Troubleshooting

### Translation Not Showing
- Ensure the component uses `'use client'` directive
- Check that `useTranslation` hook is imported and used
- Verify the translation key exists in all language files

### Build Warnings
- Translation warnings during build are expected for SSG (Static Site Generation)
- These warnings do not affect runtime functionality
- The warnings disappear once the client loads and messages are available

---

The internationalization system is now ready for use across the FundFlow application. Continue updating components following the patterns shown above to provide a fully localized experience for users. 