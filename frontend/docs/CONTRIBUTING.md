# Contributing to FundFlow Frontend

Thank you for your interest in contributing to FundFlow! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Development Setup

1. **Fork and clone the repository**
   ```bash
   git clone https://github.com/your-username/FundFlow.git
   cd FundFlow/frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment**
   ```bash
   cp env.example .env.local
   # Update variables as needed
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Run setup script (optional)**
   ```bash
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   ```

## 🏗️ Development Workflow

### Before You Start
- Check existing [Issues](../../issues) and [Pull Requests](../../pulls)
- Create an issue for new features or significant changes
- Follow the established coding patterns

### Making Changes

1. **Create a branch**
   ```bash
   git checkout -b feature/your-feature-name
   # or
   git checkout -b fix/your-bug-fix
   ```

2. **Make your changes**
   - Follow TypeScript best practices
   - Use existing component patterns
   - Maintain consistent styling with Tailwind CSS
   - Add proper type definitions

3. **Test your changes**
   ```bash
   npm run precommit  # Runs lint, format, and type-check
   npm run build      # Ensure build succeeds
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   # Follow conventional commits format
   ```

5. **Push and create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## 📋 Code Standards

### TypeScript
- Use strict TypeScript configuration
- Define interfaces for all data structures
- Avoid `any` types (use `unknown` if necessary)
- Export types from `src/types/index.ts`

### React Components
- Use functional components with hooks
- Implement proper prop types
- Use compound components for complex UI elements
- Follow the component structure in `src/components/`

### Styling
- Use Tailwind CSS utility classes
- Follow the established design system
- Use semantic class names when needed
- Maintain responsive design principles

### State Management
- Use Redux Toolkit for global state
- Create typed hooks for store access
- Organize slices by feature domain
- Keep components focused and testable

## 🎨 Component Guidelines

### UI Components (`src/components/ui/`)
- Should be reusable and generic
- Must include proper TypeScript props
- Should handle all common variants
- Must be exported from `index.ts`

Example structure:
```typescript
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  // ... other props
}

export const Button: React.FC<ButtonProps> = ({ ... }) => {
  // Component implementation
};
```

### Feature Components (`src/components/features/`)
- Should be specific to application features
- Can use business logic and domain types
- May connect to Redux store
- Should remain modular and testable

## 📁 File Organization

### Adding New Features
1. Create types in `src/types/index.ts`
2. Add utilities to `src/utils/index.ts`
3. Create components in appropriate directories
4. Add Redux slice if needed
5. Create page routes in `src/app/`

### Naming Conventions
- **Files**: PascalCase for components, camelCase for utilities
- **Directories**: lowercase with hyphens
- **Constants**: UPPER_SNAKE_CASE
- **Functions**: camelCase
- **Interfaces**: PascalCase with descriptive names

## 🧪 Testing Guidelines

### Unit Tests
- Test utility functions thoroughly
- Test component logic and edge cases
- Mock external dependencies
- Aim for meaningful test coverage

### Component Tests
- Test user interactions
- Verify prop handling
- Check accessibility features
- Test error states

### Integration Tests
- Test feature workflows
- Verify state management
- Test API integration points
- Check routing behavior

## 🔍 Code Review Process

### Before Submitting PR
- [ ] Run `npm run precommit` successfully
- [ ] Build completes without errors
- [ ] No console errors or warnings
- [ ] Code follows established patterns
- [ ] Types are properly defined
- [ ] Documentation is updated if needed

### PR Requirements
- Clear description of changes
- Reference related issues
- Include screenshots for UI changes
- Update tests if applicable
- Update documentation if needed

## 🚫 Common Issues

### Import Errors
- Use absolute imports with `@/` prefix
- Organize imports according to ESLint rules
- Check TypeScript path mapping

### Styling Issues
- Verify Tailwind class names
- Check responsive breakpoints
- Ensure accessibility compliance
- Test dark mode compatibility (if implemented)

### State Management
- Use typed hooks (`useAppDispatch`, `useAppSelector`)
- Follow Redux Toolkit patterns
- Avoid direct store access in components
- Keep actions and reducers focused

## 📖 Resources

### Documentation
- [Next.js Documentation](https://nextjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Redux Toolkit Documentation](https://redux-toolkit.js.org/)

### Tools
- [React DevTools](https://react.dev/learn/react-developer-tools)
- [Redux DevTools](https://github.com/reduxjs/redux-devtools)
- [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss)

## 💬 Getting Help

- Check existing [Issues](../../issues)
- Join our development discussions
- Read the [README](./README.md) for setup instructions
- Review existing code for patterns and examples

## 🎯 Contribution Types

We welcome various types of contributions:

- 🐛 **Bug fixes**
- ✨ **New features**
- 📚 **Documentation improvements**
- 🎨 **UI/UX enhancements**
- ⚡ **Performance optimizations**
- 🧪 **Test additions**
- 🔧 **Build process improvements**

Thank you for contributing to FundFlow! 🙏 