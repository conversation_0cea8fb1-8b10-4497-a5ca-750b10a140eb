# Dark Mode Implementation

This document describes the comprehensive dark mode implementation for the FundFlow frontend application.

## Overview

The dark mode system provides:
- **Manual toggle** between light and dark themes
- **System preference detection** that automatically follows the user's OS setting
- **Persistent theme selection** stored in localStorage
- **Smooth transitions** between theme changes
- **Comprehensive color system** that works across all components

## Architecture

### 1. Redux State Management (`src/store/slices/themeSlice.ts`)

The theme state is managed through Redux Toolkit with the following structure:

```typescript
interface ThemeState {
  mode: 'light' | 'dark' | 'system';
  isDark: boolean;
}
```

**Actions:**
- `setTheme(mode)` - Set specific theme mode
- `toggleTheme()` - Toggle between light/dark (ignores system)
- `updateSystemPreference()` - Update when system preference changes

### 2. Theme Hook (`src/hooks/useTheme.ts`)

Custom hook that provides:
- Current theme state
- Theme switching functions
- Automatic DOM class management
- System preference monitoring

### 3. Theme Components

#### ThemeToggle (`src/components/ui/ThemeToggle.tsx`)
- **Button variant**: Simple toggle between light/dark
- **Dropdown variant**: Full control (light/dark/system)
- Responsive sizing (sm/md/lg)
- Accessible with proper ARIA labels

#### ThemeProvider (`src/components/providers/ThemeProvider.tsx`)
- Initializes theme on app startup
- Manages DOM class application
- Wraps the entire application

## CSS System

### 1. CSS Custom Properties (`src/app/globals.css`)

Comprehensive color system with semantic naming:

```css
:root {
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --primary: #2563eb;
  /* ... more colors */
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #1a1a1a;
  --primary: #3b82f6;
  /* ... dark variants */
}
```

### 2. Tailwind Configuration (`tailwind.config.js`)

- **Dark mode**: Class-based (`darkMode: 'class'`)
- **Custom colors**: Mapped to CSS variables
- **Semantic naming**: Consistent with design system

## Usage

### Basic Theme Toggle

```tsx
import { ThemeToggle } from '@/components/ui';

// Simple button toggle
<ThemeToggle variant="button" size="md" />

// Dropdown with all options
<ThemeToggle variant="dropdown" size="lg" />
```

### Using Theme in Components

```tsx
import { useTheme } from '@/hooks/useTheme';

function MyComponent() {
  const { mode, isDark, setTheme, toggleTheme } = useTheme();
  
  return (
    <div className="bg-white dark:bg-gray-800">
      <p className="text-gray-900 dark:text-gray-100">
        Current theme: {mode} ({isDark ? 'Dark' : 'Light'})
      </p>
      <button onClick={toggleTheme}>Toggle Theme</button>
    </div>
  );
}
```

### Component Styling

All UI components support dark mode automatically:

```tsx
// Buttons
<Button variant="primary">Primary Button</Button>
<Button variant="outline">Outline Button</Button>

// Cards
<Card>
  <Card.Header>
    <Card.Title>Card Title</Card.Title>
    <Card.Description>Card description</Card.Description>
  </Card.Header>
  <Card.Content>
    Content automatically adapts to theme
  </Card.Content>
</Card>
```

## Color System

### Semantic Colors

| Color | Light | Dark | Usage |
|-------|-------|------|-------|
| `background` | #ffffff | #0a0a0a | Page background |
| `foreground` | #171717 | #ededed | Primary text |
| `card` | #ffffff | #1a1a1a | Card backgrounds |
| `primary` | #2563eb | #3b82f6 | Primary actions |
| `secondary` | #f1f5f9 | #1e293b | Secondary elements |
| `muted` | #f1f5f9 | #1e293b | Muted backgrounds |
| `border` | #e2e8f0 | #334155 | Borders and dividers |

### Using Colors in Tailwind

```tsx
// Background colors
<div className="bg-background text-foreground">
<div className="bg-card text-card-foreground">
<div className="bg-primary text-primary-foreground">

// Traditional approach also works
<div className="bg-white dark:bg-gray-800">
<div className="text-gray-900 dark:text-gray-100">
```

## Features

### 1. System Preference Detection
- Automatically detects OS dark mode preference
- Updates when system preference changes
- Respects user's explicit choice over system

### 2. Persistence
- Theme choice saved to localStorage
- Restored on page reload
- Works across browser sessions

### 3. Smooth Transitions
- CSS transitions on theme changes
- Prevents jarring color switches
- Configurable transition duration

### 4. Accessibility
- Proper ARIA labels on toggle buttons
- Keyboard navigation support
- High contrast ratios in both themes

## Testing

Visit `/theme-test` to see a comprehensive test page with:
- All button variants
- Card components
- Form elements
- Color palette
- Theme toggle controls

## Integration Points

The dark mode system is integrated into:

1. **Navigation** - Theme toggle in header
2. **Layout** - Body classes and transitions
3. **All UI Components** - Automatic dark mode support
4. **Landing Page** - Full dark mode styling
5. **Redux Store** - Centralized state management

## Browser Support

- **Modern browsers**: Full support with CSS custom properties
- **Fallback**: Graceful degradation for older browsers
- **System preference**: Supported in all modern browsers

## Performance

- **Minimal overhead**: CSS variables provide efficient theming
- **No flash**: Theme applied before first paint
- **Optimized transitions**: Hardware-accelerated where possible
