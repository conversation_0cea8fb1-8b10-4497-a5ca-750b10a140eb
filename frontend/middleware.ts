import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { auth } from '@/lib/auth'

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/funds',
  '/portfolios',
  '/transactions',
  '/budgets',
  '/reports',
  '/settings',
  '/api/protected',
]

// Define public routes that should redirect authenticated users
const publicRoutes = [
  '/auth/signin',
  '/auth/signup',
]

export default async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Special handling for sign-out and logout completion routes - always allow access and clear any cached session
  if (pathname === '/auth/signout' || pathname === '/auth/logout-complete') {
    const response = NextResponse.next()

    // Clear NextAuth cookies
    response.cookies.delete('next-auth.session-token')
    response.cookies.delete('next-auth.callback-url')
    response.cookies.delete('next-auth.csrf-token')
    response.cookies.delete('__Secure-next-auth.session-token')
    response.cookies.delete('__Host-next-auth.csrf-token')

    // Also clear any chunked session cookies
    for (let i = 0; i < 10; i++) {
      response.cookies.delete(`next-auth.session-token.${i}`)
      response.cookies.delete(`__Secure-next-auth.session-token.${i}`)
    }

    return response
  }

  // Get session using NextAuth.js
  const session = await auth()

  // Check if the route is protected
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  )

  // Check if the route is public (should redirect authenticated users)
  const isPublicRoute = publicRoutes.some(route =>
    pathname.startsWith(route)
  )

  // If user is not authenticated and trying to access protected route
  if (isProtectedRoute && !session) {
    const signInUrl = new URL('/auth/signin', request.url)
    signInUrl.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(signInUrl)
  }

  // If user is authenticated and trying to access public auth routes
  if (isPublicRoute && session) {
    return NextResponse.redirect(new URL('/funds', request.url))
  }

  // For API routes, return 401 if not authenticated
  if (pathname.startsWith('/api/protected') && !session) {
    return new NextResponse(
      JSON.stringify({ error: 'Authentication required' }),
      {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
} 