import type { NextConfig } from "next";
import path from "path";
import fs from "fs";

/**
 * Configure SSL settings for Next.js (development only)
 */
function configureSSLSettings() {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // Only configure SSL for development
  if (!isDevelopment) {
    return { isDevelopment: false };
  }

  // Load SSL configuration from environment or config file
  const sslConfig = {
    caBundlePath: process.env.SSL_CA_BUNDLE_PATH,
    useSystemCA: process.env.SSL_USE_SYSTEM_CA !== 'false',
    rejectUnauthorized: process.env.NODE_TLS_REJECT_UNAUTHORIZED !== '0',
    isDevelopment: true,
  };

  // Try to load CA bundle path from ssl-config.env if not in environment
  if (!sslConfig.caBundlePath) {
    const envConfigPath = path.join(__dirname, 'certs', 'ssl-config.env');
    if (fs.existsSync(envConfigPath)) {
      try {
        const envContent = fs.readFileSync(envConfigPath, 'utf8');
        const match = envContent.match(/SSL_CA_BUNDLE_PATH=(.+)/);
        if (match) {
          sslConfig.caBundlePath = match[1].trim();
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.warn('⚠️ Failed to load SSL config from file:', errorMessage);
      }
    }
  }

  // Configure global SSL settings for development
  console.log('🔧 Configuring SSL for development environment');

  if (!sslConfig.rejectUnauthorized) {
    console.warn('⚠️ SSL certificate verification is DISABLED in development');
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
  }

  // Set CA bundle path if available
  if (sslConfig.caBundlePath && fs.existsSync(sslConfig.caBundlePath)) {
    console.log(`✅ Using custom CA bundle: ${sslConfig.caBundlePath}`);
    process.env.SSL_CA_BUNDLE_PATH = sslConfig.caBundlePath;
  }

  return sslConfig;
}

// Configure SSL settings when Next.js loads
const sslConfig = configureSSLSettings();

const nextConfig: NextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },

  // Handle SSL certificate issues and AWS SDK
  serverExternalPackages: [
    '@aws-sdk/client-cognito-idp',
    '@aws-sdk/client-dynamodb',
    'aws-sdk'
  ],

  // Configure experimental features for better SSL handling
  experimental: {
    // Other experimental features can be added here if needed
  },

  // Custom webpack configuration for SSL handling
  webpack: (config, { isServer }) => {
    if (isServer) {
      // Configure Node.js built-ins for server-side SSL
      config.resolve.fallback = {
        ...config.resolve.fallback,
        "tls": false,
        "net": false,
        "fs": false,
      };
    }

    return config;
  },

  // Environment variables to expose to both client and server
  env: {
    SSL_CONFIGURED: sslConfig?.caBundlePath ? 'true' : 'false',
    SSL_DEVELOPMENT_MODE: sslConfig?.isDevelopment ? 'true' : 'false',
    // NextAuth environment variables
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    COGNITO_CLIENT_ID: process.env.COGNITO_CLIENT_ID,
    COGNITO_ISSUER: process.env.COGNITO_ISSUER,
  },
};

export default nextConfig;
