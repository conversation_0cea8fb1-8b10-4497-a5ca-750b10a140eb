{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_TLS_REJECT_UNAUTHORIZED=0 HTTPS=true next dev", "dev:secure": "cross-env SSL_CA_BUNDLE_PATH=./certs/cacert.pem next dev", "dev:ssl": "cross-env SSL_CA_BUNDLE_PATH=./certs/cacert.pem SSL_USE_SYSTEM_CA=true next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "clean": "rm -rf .next && rm -rf out && rm -rf dist", "dev:clean": "npm run clean && npm run dev", "build:analyze": "ANALYZE=true npm run build", "test": "echo \"Error: no test specified\" && exit 1", "precommit": "npm run lint:fix && npm run format && npm run type-check", "setup:ssl": "node scripts/setup-ssl-certificates.js", "validate:ssl": "node scripts/validate-ssl.js", "postinstall": "npm run setup:ssl"}, "dependencies": {"@auth/core": "^0.34.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "autoprefixer": "^10.4.21", "aws-sdk": "^2.1692.0", "chart.js": "^4.4.9", "next": "15.3.3", "next-auth": "^4.24.11", "next-intl": "^4.3.1", "next-themes": "^0.4.6", "postcss": "^8.4.49", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-redux": "^9.2.0", "tailwindcss": "^4", "typescript": "^5", "zod": "^3.25.64"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-redux": "^7.1.34", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.3"}}