#!/usr/bin/env node

/**
 * SSL Certificate Setup Script
 * 
 * This script downloads and configures CA certificates to resolve
 * UNABLE_TO_GET_ISSUER_CERT_LOCALLY errors in Node.js applications.
 */

const https = require('https');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Configuration
const CONFIG = {
  // Mozilla's CA certificate bundle (most trusted source)
  CA_BUNDLE_URL: 'https://curl.se/ca/cacert.pem',
  
  // Local paths for certificates
  CERT_DIR: path.join(__dirname, '..', 'certs'),
  CA_BUNDLE_FILE: 'cacert.pem',
  
  // Backup URLs in case primary fails
  BACKUP_URLS: [
    'https://raw.githubusercontent.com/bagder/ca-bundle/master/ca-bundle.crt',
    'https://curl.haxx.se/ca/cacert.pem'
  ],
  
  // Expected minimum size for CA bundle (to detect incomplete downloads)
  MIN_BUNDLE_SIZE: 200000, // ~200KB
};

/**
 * Create certificates directory if it doesn't exist
 */
function ensureCertDirectory() {
  if (!fs.existsSync(CONFIG.CERT_DIR)) {
    fs.mkdirSync(CONFIG.CERT_DIR, { recursive: true });
    console.log(`✅ Created certificates directory: ${CONFIG.CERT_DIR}`);
  }
}

/**
 * Download file from URL with retry logic
 */
function downloadFile(url, retryCount = 0) {
  return new Promise((resolve, reject) => {
    console.log(`📥 Downloading CA bundle from: ${url}`);
    
    const request = https.get(url, {
      timeout: 30000,
      headers: {
        'User-Agent': 'FundFlow-SSL-Setup/1.0'
      }
    }, (response) => {
      if (response.statusCode === 200) {
        let data = '';
        
        response.on('data', (chunk) => {
          data += chunk;
        });
        
        response.on('end', () => {
          if (data.length < CONFIG.MIN_BUNDLE_SIZE) {
            reject(new Error(`Downloaded file too small: ${data.length} bytes`));
            return;
          }
          
          console.log(`✅ Downloaded ${data.length} bytes`);
          resolve(data);
        });
      } else if (response.statusCode >= 300 && response.statusCode < 400 && response.headers.location) {
        // Handle redirects
        console.log(`🔄 Redirecting to: ${response.headers.location}`);
        downloadFile(response.headers.location, retryCount).then(resolve).catch(reject);
      } else {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
      }
    });
    
    request.on('timeout', () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
    
    request.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Download CA bundle with fallback URLs
 */
async function downloadCABundle() {
  const urls = [CONFIG.CA_BUNDLE_URL, ...CONFIG.BACKUP_URLS];
  
  for (let i = 0; i < urls.length; i++) {
    try {
      const data = await downloadFile(urls[i]);
      return data;
    } catch (error) {
      console.warn(`⚠️ Failed to download from ${urls[i]}: ${error.message}`);
      
      if (i === urls.length - 1) {
        throw new Error('All download URLs failed');
      }
      
      console.log(`🔄 Trying next URL...`);
    }
  }
}

/**
 * Validate CA bundle content
 */
function validateCABundle(content) {
  console.log('🔍 Validating CA bundle...');
  
  // Check if content looks like a PEM certificate bundle
  if (!content.includes('-----BEGIN CERTIFICATE-----')) {
    throw new Error('Invalid CA bundle: No certificates found');
  }
  
  // Count certificates
  const certCount = (content.match(/-----BEGIN CERTIFICATE-----/g) || []).length;
  console.log(`✅ Found ${certCount} certificates in bundle`);
  
  if (certCount < 100) {
    throw new Error(`Suspicious CA bundle: Only ${certCount} certificates found`);
  }
  
  // Generate checksum for integrity verification
  const hash = crypto.createHash('sha256').update(content).digest('hex');
  console.log(`✅ CA bundle SHA256: ${hash}`);
  
  return { certCount, hash };
}

/**
 * Save CA bundle to file
 */
function saveCABundle(content) {
  const filePath = path.join(CONFIG.CERT_DIR, CONFIG.CA_BUNDLE_FILE);
  
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ CA bundle saved to: ${filePath}`);
    
    // Set appropriate file permissions (readable by owner and group)
    fs.chmodSync(filePath, 0o644);
    
    return filePath;
  } catch (error) {
    throw new Error(`Failed to save CA bundle: ${error.message}`);
  }
}

/**
 * Create environment configuration file
 */
function createEnvConfig(caBundlePath, metadata) {
  const envConfigPath = path.join(CONFIG.CERT_DIR, 'ssl-config.env');
  
  const envContent = `# SSL Configuration for FundFlow
# Generated on ${new Date().toISOString()}

# Path to CA certificate bundle
SSL_CA_BUNDLE_PATH=${caBundlePath}

# Use system CA certificates in addition to custom bundle
SSL_USE_SYSTEM_CA=true

# SSL connection timeout (milliseconds)
SSL_TIMEOUT=10000

# Certificate bundle metadata
SSL_CA_BUNDLE_CERT_COUNT=${metadata.certCount}
SSL_CA_BUNDLE_HASH=${metadata.hash}
SSL_CA_BUNDLE_UPDATED=${new Date().toISOString()}

# For development only - set to '0' to disable SSL verification
# NODE_TLS_REJECT_UNAUTHORIZED=1
`;
  
  fs.writeFileSync(envConfigPath, envContent, 'utf8');
  console.log(`✅ SSL configuration saved to: ${envConfigPath}`);
  
  return envConfigPath;
}

/**
 * Update package.json scripts with SSL configuration
 */
function updatePackageJsonScripts() {
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.warn('⚠️ package.json not found, skipping script updates');
    return;
  }
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Update scripts to use SSL configuration
    if (packageJson.scripts) {
      // Add SSL-aware development script
      packageJson.scripts['dev:ssl'] = 'cross-env SSL_CA_BUNDLE_PATH=./certs/cacert.pem next dev';
      
      // Add SSL validation script
      packageJson.scripts['validate:ssl'] = 'node scripts/validate-ssl.js';
      
      console.log('✅ Updated package.json scripts with SSL configuration');
    }
    
    // Don't write back to avoid conflicts - just log the suggestions
    console.log('💡 Suggested package.json script updates:');
    console.log('   "dev:ssl": "cross-env SSL_CA_BUNDLE_PATH=./certs/cacert.pem next dev"');
    console.log('   "validate:ssl": "node scripts/validate-ssl.js"');
    
  } catch (error) {
    console.warn(`⚠️ Failed to update package.json: ${error.message}`);
  }
}

/**
 * Main setup function
 */
async function setupSSLCertificates() {
  console.log('🚀 Setting up SSL certificates for FundFlow...\n');
  
  try {
    // Step 1: Ensure certificate directory exists
    ensureCertDirectory();
    
    // Step 2: Download CA bundle
    console.log('📥 Downloading CA certificate bundle...');
    const caBundleContent = await downloadCABundle();
    
    // Step 3: Validate the bundle
    const metadata = validateCABundle(caBundleContent);
    
    // Step 4: Save the bundle
    const caBundlePath = saveCABundle(caBundleContent);
    
    // Step 5: Create environment configuration
    const envConfigPath = createEnvConfig(caBundlePath, metadata);
    
    // Step 6: Update package.json scripts
    updatePackageJsonScripts();
    
    // Step 7: Provide usage instructions
    console.log('\n🎉 SSL certificate setup completed successfully!\n');
    console.log('📋 Next steps:');
    console.log('1. Add the following to your .env.local file:');
    console.log(`   SSL_CA_BUNDLE_PATH=${caBundlePath}`);
    console.log('   SSL_USE_SYSTEM_CA=true');
    console.log('   SSL_TIMEOUT=10000\n');
    console.log('2. Import and use the SSL configuration in your app:');
    console.log('   import { configureGlobalSSL } from "./lib/ssl-config";');
    console.log('   configureGlobalSSL();\n');
    console.log('3. Use secureFetch() instead of fetch() for HTTPS requests\n');
    console.log('4. Run validation: npm run validate:ssl\n');
    
  } catch (error) {
    console.error('❌ SSL certificate setup failed:', error.message);
    console.error('\n💡 Troubleshooting:');
    console.error('1. Check your internet connection');
    console.error('2. Verify firewall/proxy settings');
    console.error('3. Try running with NODE_TLS_REJECT_UNAUTHORIZED=0 temporarily');
    console.error('4. Contact your system administrator if in a corporate environment');
    process.exit(1);
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  setupSSLCertificates();
}

module.exports = { setupSSLCertificates };
