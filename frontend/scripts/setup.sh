#!/bin/bash

# FundFlow Frontend Development Setup Script
echo "🚀 Setting up FundFlow Frontend Development Environment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version $NODE_VERSION is too old. Please upgrade to Node.js 18+ and try again."
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm and try again."
    exit 1
fi

echo "✅ npm $(npm -v) detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Create environment file if it doesn't exist
if [ ! -f .env.local ]; then
    echo "🔧 Creating environment configuration..."
    cp env.example .env.local
    echo "✅ Environment file created (.env.local)"
    echo "📝 Please update the environment variables in .env.local"
else
    echo "✅ Environment file already exists"
fi

# Run type check
echo "🔍 Running type check..."
npm run type-check

if [ $? -ne 0 ]; then
    echo "❌ Type check failed"
    exit 1
fi

echo "✅ Type check passed"

# Run linting
echo "🔍 Running ESLint..."
npm run lint

if [ $? -ne 0 ]; then
    echo "⚠️  Linting issues found. Running auto-fix..."
    npm run lint:fix
fi

echo "✅ Linting completed"

# Check formatting
echo "🎨 Checking code formatting..."
npm run format:check

if [ $? -ne 0 ]; then
    echo "⚠️  Formatting issues found. Running auto-format..."
    npm run format
fi

echo "✅ Code formatting completed"

# Try to build the project
echo "🏗️  Testing build..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful"

# Clean up build artifacts
npm run clean

echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "   1. Update environment variables in .env.local"
echo "   2. Run 'npm run dev' to start the development server"
echo "   3. Open http://localhost:3000 in your browser"
echo ""
echo "🛠️  Useful commands:"
echo "   npm run dev          - Start development server"
echo "   npm run build        - Build for production"
echo "   npm run lint:fix     - Fix linting issues"
echo "   npm run format       - Format code"
echo "   npm run type-check   - Check TypeScript types"
echo "   npm run precommit    - Run all checks" 