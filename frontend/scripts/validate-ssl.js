#!/usr/bin/env node

/**
 * SSL Configuration Validation Script
 * 
 * This script validates SSL configuration and tests connectivity
 * to AWS services to ensure certificates are working properly.
 */

const https = require('https');
const fs = require('fs');
const path = require('path');
const { URL } = require('url');

// Test endpoints
const TEST_ENDPOINTS = [
  'https://cognito-idp.ap-northeast-1.amazonaws.com',
  'https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com',
  'https://aws.amazon.com',
  'https://google.com', // Control test
];

/**
 * Load SSL configuration
 */
function loadSSLConfig() {
  const config = {
    caBundlePath: process.env.SSL_CA_BUNDLE_PATH,
    useSystemCA: process.env.SSL_USE_SYSTEM_CA !== 'false',
    rejectUnauthorized: process.env.NODE_TLS_REJECT_UNAUTHORIZED !== '0',
    timeout: parseInt(process.env.SSL_TIMEOUT || '10000'),
  };
  
  // Try to load from ssl-config.env file
  const envConfigPath = path.join(__dirname, '..', 'certs', 'ssl-config.env');
  if (fs.existsSync(envConfigPath)) {
    const envContent = fs.readFileSync(envConfigPath, 'utf8');
    const envLines = envContent.split('\n');
    
    for (const line of envLines) {
      if (line.startsWith('SSL_CA_BUNDLE_PATH=')) {
        config.caBundlePath = line.split('=')[1];
      }
    }
  }
  
  return config;
}

/**
 * Test SSL connection to a specific endpoint
 */
function testSSLConnection(url, config) {
  return new Promise((resolve) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname || '/',
      method: 'HEAD',
      timeout: config.timeout,
      rejectUnauthorized: config.rejectUnauthorized,
    };
    
    // Add custom CA if available
    if (config.caBundlePath && fs.existsSync(config.caBundlePath)) {
      try {
        const caCert = fs.readFileSync(config.caBundlePath, 'utf8');
        options.ca = caCert;
      } catch (error) {
        console.warn(`⚠️ Failed to load CA bundle: ${error.message}`);
      }
    }
    
    const startTime = Date.now();
    
    const request = https.request(options, (response) => {
      const duration = Date.now() - startTime;
      
      resolve({
        url,
        success: true,
        statusCode: response.statusCode,
        duration,
        certificate: response.socket.getPeerCertificate(),
        protocol: response.socket.getProtocol(),
      });
    });
    
    request.on('error', (error) => {
      const duration = Date.now() - startTime;
      
      resolve({
        url,
        success: false,
        error: error.message,
        duration,
        code: error.code,
      });
    });
    
    request.on('timeout', () => {
      request.destroy();
      const duration = Date.now() - startTime;
      
      resolve({
        url,
        success: false,
        error: 'Request timeout',
        duration,
        code: 'TIMEOUT',
      });
    });
    
    request.end();
  });
}

/**
 * Validate CA bundle file
 */
function validateCABundle(filePath) {
  if (!fs.existsSync(filePath)) {
    return {
      valid: false,
      error: 'CA bundle file not found',
    };
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const certCount = (content.match(/-----BEGIN CERTIFICATE-----/g) || []).length;
    const fileSize = fs.statSync(filePath).size;
    const lastModified = fs.statSync(filePath).mtime;
    
    return {
      valid: true,
      certCount,
      fileSize,
      lastModified,
      path: filePath,
    };
  } catch (error) {
    return {
      valid: false,
      error: error.message,
    };
  }
}

/**
 * Check Node.js SSL configuration
 */
function checkNodeSSLConfig() {
  const nodeVersion = process.version;
  const opensslVersion = process.versions.openssl;
  const tlsRejectUnauthorized = process.env.NODE_TLS_REJECT_UNAUTHORIZED;
  
  return {
    nodeVersion,
    opensslVersion,
    tlsRejectUnauthorized,
    platform: process.platform,
    arch: process.arch,
  };
}

/**
 * Main validation function
 */
async function validateSSL() {
  console.log('🔍 Validating SSL configuration...\n');
  
  // Load configuration
  const config = loadSSLConfig();
  
  console.log('📋 SSL Configuration:');
  console.log(`  CA Bundle Path: ${config.caBundlePath || 'Not specified'}`);
  console.log(`  Use System CA: ${config.useSystemCA}`);
  console.log(`  Reject Unauthorized: ${config.rejectUnauthorized}`);
  console.log(`  Timeout: ${config.timeout}ms\n`);
  
  // Check Node.js configuration
  const nodeConfig = checkNodeSSLConfig();
  console.log('🔧 Node.js Configuration:');
  console.log(`  Node.js Version: ${nodeConfig.nodeVersion}`);
  console.log(`  OpenSSL Version: ${nodeConfig.opensslVersion}`);
  console.log(`  Platform: ${nodeConfig.platform} (${nodeConfig.arch})`);
  console.log(`  TLS Reject Unauthorized: ${nodeConfig.tlsRejectUnauthorized || 'default (1)'}\n`);
  
  // Validate CA bundle if specified
  if (config.caBundlePath) {
    console.log('📜 Validating CA Bundle:');
    const bundleValidation = validateCABundle(config.caBundlePath);
    
    if (bundleValidation.valid) {
      console.log(`  ✅ CA bundle is valid`);
      console.log(`  📊 Certificate count: ${bundleValidation.certCount}`);
      console.log(`  📏 File size: ${(bundleValidation.fileSize / 1024).toFixed(1)} KB`);
      console.log(`  📅 Last modified: ${bundleValidation.lastModified.toISOString()}`);
    } else {
      console.log(`  ❌ CA bundle validation failed: ${bundleValidation.error}`);
    }
    console.log();
  }
  
  // Test SSL connections
  console.log('🌐 Testing SSL connections...\n');
  
  const results = [];
  for (const endpoint of TEST_ENDPOINTS) {
    console.log(`Testing ${endpoint}...`);
    const result = await testSSLConnection(endpoint, config);
    results.push(result);
    
    if (result.success) {
      console.log(`  ✅ Success (${result.duration}ms) - Status: ${result.statusCode}`);
      if (result.certificate && result.certificate.subject) {
        console.log(`  📜 Certificate: ${result.certificate.subject.CN || 'Unknown'}`);
        console.log(`  🔒 Protocol: ${result.protocol}`);
      }
    } else {
      console.log(`  ❌ Failed (${result.duration}ms) - ${result.error}`);
      if (result.code) {
        console.log(`  🔍 Error code: ${result.code}`);
      }
    }
    console.log();
  }
  
  // Summary
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log('📊 Summary:');
  console.log(`  Successful connections: ${successCount}/${totalCount}`);
  
  if (successCount === totalCount) {
    console.log('  🎉 All SSL connections successful!');
  } else {
    console.log('  ⚠️ Some SSL connections failed');
    
    const failedResults = results.filter(r => !r.success);
    const commonErrors = {};
    
    failedResults.forEach(result => {
      const errorKey = result.code || result.error;
      commonErrors[errorKey] = (commonErrors[errorKey] || 0) + 1;
    });
    
    console.log('\n🔍 Common errors:');
    Object.entries(commonErrors).forEach(([error, count]) => {
      console.log(`  - ${error}: ${count} occurrence(s)`);
    });
    
    console.log('\n💡 Troubleshooting suggestions:');
    
    if (commonErrors['UNABLE_TO_GET_ISSUER_CERT_LOCALLY']) {
      console.log('  - Run: node scripts/setup-ssl-certificates.js');
      console.log('  - Add SSL_CA_BUNDLE_PATH to your .env.local file');
    }
    
    if (commonErrors['CERT_HAS_EXPIRED']) {
      console.log('  - Update your CA certificate bundle');
      console.log('  - Check system date and time');
    }
    
    if (commonErrors['TIMEOUT']) {
      console.log('  - Check internet connection');
      console.log('  - Increase SSL_TIMEOUT value');
      console.log('  - Check firewall/proxy settings');
    }
  }
  
  return successCount === totalCount;
}

// Run validation if this script is executed directly
if (require.main === module) {
  validateSSL().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  });
}

module.exports = { validateSSL };
