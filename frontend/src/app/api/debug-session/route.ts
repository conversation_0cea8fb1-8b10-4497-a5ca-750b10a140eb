import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debug session endpoint called');

    // Get the session on the server side
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({
        hasSession: false,
        message: 'No session found',
        debugInfo: {
          authOptionsAvailable: !!authOptions,
          requestHeaders: Object.fromEntries(request.headers.entries()),
          cookies: request.cookies.getAll().map(c => ({ name: c.name, hasValue: !!c.value }))
        }
      });
    }

    // Check token expiration
    const isExpired = session.expiresAt && Date.now() >= session.expiresAt * 1000;
    const tokenPreview = session.accessToken ? 
      `${session.accessToken.substring(0, 20)}...${session.accessToken.substring(session.accessToken.length - 10)}` : 
      'No token';

    // Test token format (JWT should have 3 parts separated by dots)
    const tokenParts = session.accessToken ? session.accessToken.split('.') : [];
    const isValidJWTFormat = tokenParts.length === 3;
    
    // Try to decode JWT header to verify it's a proper token
    let jwtHeader = null;
    if (isValidJWTFormat) {
      try {
        jwtHeader = JSON.parse(atob(tokenParts[0]));
      } catch (e) {
        // JWT header parsing failed
      }
    }

    // Test ID token format as well
    const idTokenParts = session.idToken ? session.idToken.split('.') : [];
    const isValidIdJWTFormat = idTokenParts.length === 3;
    let idJwtHeader = null;
    if (isValidIdJWTFormat) {
      try {
        idJwtHeader = JSON.parse(atob(idTokenParts[0]));
      } catch (e) {
        // ID JWT header parsing failed
      }
    }

    const idTokenPreview = session.idToken ? 
      `${session.idToken.substring(0, 20)}...${session.idToken.substring(session.idToken.length - 10)}` : 
      'No ID token';

    return NextResponse.json({
      hasSession: true,
      hasAccessToken: !!session.accessToken,
      hasIdToken: !!session.idToken,
      accessToken: {
        preview: tokenPreview,
        length: session.accessToken?.length || 0,
        parts: tokenParts.length,
        isValidJWTFormat,
        jwtHeader
      },
      idToken: {
        preview: idTokenPreview,
        length: session.idToken?.length || 0,
        parts: idTokenParts.length,
        isValidJWTFormat: isValidIdJWTFormat,
        jwtHeader: idJwtHeader
      },
      environment: process.env.NODE_ENV,
      recommendedTokenType: process.env.NODE_ENV === 'production' ? 'ID Token' : 'Access Token',
      expiresAt: session.expiresAt,
      isExpired,
      timeUntilExpiry: session.expiresAt ? (session.expiresAt * 1000 - Date.now()) / 1000 : null,
      timeUntilExpiryMinutes: session.expiresAt ? Math.round((session.expiresAt * 1000 - Date.now()) / 60000) : null,
      provider: session.provider,
      mfaEnabled: session.mfaEnabled,
      user: {
        email: session.user?.email,
        name: session.user?.name,
        id: session.user?.id,
      },
      currentTime: Date.now(),
      currentTimeISO: new Date().toISOString(),
      expiryTimeISO: session.expiresAt ? new Date(session.expiresAt * 1000).toISOString() : null,
      sessionKeys: Object.keys(session),
      debugInfo: {
        authOptionsAvailable: !!authOptions,
        requestHeaders: Object.fromEntries(request.headers.entries()),
        cookies: request.cookies.getAll().map(c => ({ name: c.name, hasValue: !!c.value }))
      }
    });

  } catch (error) {
    console.error('❌ Debug session error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get session',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      },
      { status: 500 }
    );
  }
}