import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getApiToken } from '@/lib/token-utils';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Token decode endpoint called');

    // Get the session on the server side
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({
        error: 'No session found',
        hasSession: false,
      }, { status: 401 });
    }

    // Get both tokens for analysis
    const accessToken = session.accessToken;
    const idToken = session.idToken;
    const apiToken = getApiToken(session);

    // Decode tokens without verification to inspect contents
    const decodeToken = (token: string | undefined) => {
      if (!token) return null;
      try {
        // Decode JWT without verification
        const parts = token.split('.');
        if (parts.length !== 3) return null;
        
        const header = JSON.parse(atob(parts[0]));
        const payload = JSON.parse(atob(parts[1]));
        
        return { header, payload };
      } catch (error) {
        return { error: error instanceof Error ? error.message : 'Unknown error' };
      }
    };

    const accessTokenDecoded = decodeToken(accessToken);
    const idTokenDecoded = decodeToken(idToken);
    const apiTokenDecoded = decodeToken(apiToken);

    return NextResponse.json({
      session: {
        provider: session.provider,
        userEmail: session.user?.email,
        hasAccessToken: !!session.accessToken,
        hasIdToken: !!session.idToken,
        expiresAt: session.expiresAt,
      },
      tokens: {
        accessToken: {
          present: !!accessToken,
          length: accessToken?.length || 0,
          preview: accessToken ? `${accessToken.substring(0, 30)}...${accessToken.substring(accessToken.length - 10)}` : null,
          decoded: accessTokenDecoded,
        },
        idToken: {
          present: !!idToken,
          length: idToken?.length || 0,
          preview: idToken ? `${idToken.substring(0, 30)}...${idToken.substring(idToken.length - 10)}` : null,
          decoded: idTokenDecoded,
        },
        apiToken: {
          present: !!apiToken,
          length: apiToken?.length || 0,
          preview: apiToken ? `${apiToken.substring(0, 30)}...${apiToken.substring(apiToken.length - 10)}` : null,
          isAccessToken: apiToken === accessToken,
          isIdToken: apiToken === idToken,
          decoded: apiTokenDecoded,
        },
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        expectedTokenType: process.env.NODE_ENV === 'production' ? 'ID Token' : 'Access Token',
      }
    });

  } catch (error) {
    console.error('❌ Token decode error:', error);
    return NextResponse.json(
      { 
        error: 'Token decode failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
