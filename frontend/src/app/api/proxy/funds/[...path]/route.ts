import { NextRequest, NextResponse } from 'next/server';

import { getServerSession } from 'next-auth';

import { authOptions } from '@/app/api/auth/[...nextauth]/route';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev';

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Determine which token to use based on the API endpoint
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
    const isAwsApi = apiBaseUrl.includes('amazonaws.com');
    const token = isAwsApi ? session?.idToken : session?.accessToken;
    
    if (!token) {
      return NextResponse.json(
        { error: `Unauthorized - No ${isAwsApi ? 'ID' : 'access'} token` },
        { 
          status: 401,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          }
        }
      );
    }

    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (session.expiresAt && currentTime >= session.expiresAt) {
      return NextResponse.json(
        { error: 'Unauthorized - Token expired' },
        { 
          status: 401,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          }
        }
      );
    }

    // Construct the full path
    const path = params.path.join('/');
    const queryString = request.nextUrl.search;
    const apiUrl = `${API_BASE_URL}/funds/${path}${queryString}`;

    console.log('Proxying request to:', apiUrl);

    // Make the request to the actual API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    // Return the response with CORS headers
    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  } catch (error) {
    console.error('Proxy error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);
    
    // Determine which token to use based on the API endpoint
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
    const isAwsApi = apiBaseUrl.includes('amazonaws.com');
    const token = isAwsApi ? session?.idToken : session?.accessToken;
    
    console.log('POST /api/proxy/funds - Session check:', {
      hasSession: !!session,
      hasAccessToken: !!session?.accessToken,
      hasIdToken: !!session?.idToken,
      expiresAt: session?.expiresAt,
      currentTime: Math.floor(Date.now() / 1000),
      isAwsApi,
      usingToken: isAwsApi ? 'idToken' : 'accessToken',
      tokenPresent: !!token
    });

    if (!token) {
      return NextResponse.json(
        { error: `Unauthorized - No ${isAwsApi ? 'ID' : 'access'} token` },
        { 
          status: 401,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          }
        }
      );
    }

    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (session.expiresAt && currentTime >= session.expiresAt) {
      return NextResponse.json(
        { error: 'Unauthorized - Token expired' },
        { 
          status: 401,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          }
        }
      );
    }

    // Construct the full path
    const path = params.path.join('/');
    const queryString = request.nextUrl.search;
    const apiUrl = `${API_BASE_URL}/funds/${path}${queryString}`;

    console.log('Proxying POST request to:', apiUrl);

    // Check if this is a file upload request (extract-pdf endpoint)
    const contentType = request.headers.get('content-type');
    const isFileUpload = path === 'extract-pdf' || (contentType && contentType.includes('multipart/form-data'));

    let body: string | FormData;
    let requestHeaders: Record<string, string>;

    if (isFileUpload) {
      // Handle FormData for file uploads
      body = await request.formData();
      requestHeaders = {
        'Authorization': `Bearer ${token}`,
        // Don't set Content-Type for FormData - let fetch set it with boundary
      };
    } else {
      // Handle JSON requests
      const jsonBody = await request.json();
      body = JSON.stringify(jsonBody);
      requestHeaders = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      };
    }

    // Make the request to the actual API
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: requestHeaders,
      body: body,
    });

    const data = await response.json();

    // Return the response with CORS headers
    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  } catch (error) {
    console.error('Proxy error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Determine which token to use based on the API endpoint
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
    const isAwsApi = apiBaseUrl.includes('amazonaws.com');
    const token = isAwsApi ? session?.idToken : session?.accessToken;
    
    if (!token) {
      return NextResponse.json(
        { error: `Unauthorized - No ${isAwsApi ? 'ID' : 'access'} token` },
        { 
          status: 401,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          }
        }
      );
    }

    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (session.expiresAt && currentTime >= session.expiresAt) {
      return NextResponse.json(
        { error: 'Unauthorized - Token expired' },
        { 
          status: 401,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          }
        }
      );
    }

    // Construct the full path
    const path = params.path.join('/');
    const queryString = request.nextUrl.search;
    const apiUrl = `${API_BASE_URL}/funds/${path}${queryString}`;

    console.log('Proxying DELETE request to:', apiUrl);

    // Make the request to the actual API
    const response = await fetch(apiUrl, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    // Return the response with CORS headers
    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  } catch (error) {
    console.error('Proxy error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}