import { NextRequest, NextResponse } from 'next/server';

import { getServerSession } from 'next-auth';

import { authOptions } from '@/lib/auth';
import { secureFetch } from '@/lib/ssl-config';
import { getApiToken, getTokenPreview } from '@/lib/token-utils';

export async function GET(request: NextRequest) {
  try {
    console.log('🔧 Proxy API: Starting funds request');
    console.log('🔧 Proxy API: Request URL:', request.url);
    console.log('🔧 Proxy API: Request headers:', Object.fromEntries(request.headers.entries()));

    // Get the session on the server side
    const session = await getServerSession(authOptions);
    console.log('🔐 Proxy API: Session check:', session ? 'Session found' : 'No session');
    
    const apiToken = getApiToken(session);
    
    if (session) {
      console.log('🔐 Proxy API: Session details:', {
        hasAccessToken: !!session.accessToken,
        hasIdToken: !!session.idToken,
        hasUser: !!session.user,
        userEmail: session.user?.email,
        provider: session.provider,
        environment: process.env.NODE_ENV,
        expiresAt: session.expiresAt,
        isExpired: session.expiresAt ? Date.now() >= session.expiresAt * 1000 : false,
        timeUntilExpiry: session.expiresAt ? Math.round((session.expiresAt * 1000 - Date.now()) / 60000) : null,
        tokenLength: apiToken?.length || 0,
        tokenPreview: getTokenPreview(apiToken),
        usingTokenType: process.env.NODE_ENV === 'production' ? 'ID Token' : 'Access Token'
      });
    }

    if (!apiToken) {
      console.error('❌ Proxy API: No access token found');
      return NextResponse.json(
        { error: 'Authentication required', message: 'No access token found in session. Please sign in again.' },
        { status: 401 }
      );
    }

    // Check if token is expired
    if (session.expiresAt && Date.now() >= session.expiresAt * 1000) {
      console.error('❌ Proxy API: Access token has expired');
      return NextResponse.json(
        {
          error: 'Token expired',
          message: 'Access token has expired. Please sign in again.',
          requiresSignIn: true
        },
        { status: 401 }
      );
    }

    console.log('✅ Proxy API: Access token found, making request to AWS API Gateway');

    // Extract query parameters from the request
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();

    // Make the request to AWS API Gateway from the server side
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev';
    const apiUrl = `${apiBaseUrl}/funds${queryString ? `?${queryString}` : ''}`;
    
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${apiToken}`,
    };
    
    console.log('📋 Proxy API: Request headers:', {
      'Content-Type': headers['Content-Type'],
      'Accept': headers['Accept'],
      'Authorization': `Bearer ${getTokenPreview(apiToken)}`,
      'TokenType': process.env.NODE_ENV === 'production' ? 'ID Token' : 'Access Token'
    });
    
    const response = await secureFetch(apiUrl, {
      method: 'GET',
      headers,
    });
    
    console.log('📡 Proxy API: Response status:', response.status, response.statusText);
    console.log('📡 Proxy API: Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Proxy API: AWS API request failed:', response.status, response.statusText);
      console.error('❌ Proxy API: Error response body:', errorText);
      console.error('❌ Proxy API: Request details:', {
        apiUrl,
        method: 'GET',
        headers: {
          'Content-Type': headers['Content-Type'],
          'Accept': headers['Accept'],
          'Authorization': `Bearer ${getTokenPreview(apiToken)}`,
          'TokenType': process.env.NODE_ENV === 'production' ? 'ID Token' : 'Access Token'
        }
      });

      // Handle specific error cases
      if (response.status === 401) {
        let errorMessage = 'Authentication failed';
        try {
          const errorData = JSON.parse(errorText);
          if (errorData.message === 'The incoming token has expired') {
            errorMessage = 'Access token has expired. Please refresh the page or sign in again.';
          }
        } catch (e) {
          // Use default message if parsing fails
        }

        return NextResponse.json(
          {
            error: 'Authentication failed',
            message: errorMessage,
            status: response.status,
            statusText: response.statusText,
          },
          { status: 401 }
        );
      }

      return NextResponse.json(
        {
          error: 'AWS API request failed',
          status: response.status,
          statusText: response.statusText,
          message: errorText
        },
        { status: response.status }
      );
    }
    
    const data = await response.json();
    console.log('✅ Proxy API: Successfully received data from AWS API');
    console.log('📦 Proxy API: Data preview:', {
      message: data.message,
      dataLength: data.data?.length || 0,
      firstItem: data.data?.[0] ? Object.keys(data.data[0]) : 'No data'
    });
    
    // Return the data with CORS headers
    return NextResponse.json(data, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
    
  } catch (error) {
    console.error('❌ Proxy API: Unexpected error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        message: error instanceof Error ? error.message : 'Unknown error',
        details: error instanceof Error ? error.stack : 'No stack trace'
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
