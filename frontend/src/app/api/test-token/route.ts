import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getApiToken } from '@/lib/token-utils';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Token test endpoint called');

    // Get the session on the server side
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({
        error: 'No session found',
        hasSession: false,
      }, { status: 401 });
    }

    // Get the appropriate token for the current environment
    const apiToken = getApiToken(session);

    if (!apiToken) {
      return NextResponse.json({
        error: 'No API token found',
        hasSession: !!session,
        hasAccessToken: !!session.accessToken,
        hasIdToken: !!session.idToken,
        environment: process.env.NODE_ENV,
      }, { status: 401 });
    }

    // Test the token against AWS API Gateway
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev';
    const testUrl = `${apiBaseUrl}/funds?page_size=1`;
    
    console.log('🧪 Testing token against:', testUrl);
    console.log('🧪 Token preview:', `${apiToken.substring(0, 50)}...`);
    console.log('🧪 Environment:', process.env.NODE_ENV);
    console.log('🧪 Using token type:', process.env.NODE_ENV === 'production' ? 'ID Token' : 'Access Token');

    const response = await fetch(testUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${apiToken}`,
      },
    });

    const responseText = await response.text();
    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }

    console.log('🧪 API Gateway response:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      body: responseData
    });

    return NextResponse.json({
      tokenTest: {
        success: response.ok,
        status: response.status,
        statusText: response.statusText,
        responseHeaders: Object.fromEntries(response.headers.entries()),
        responseBody: responseData,
        requestUrl: testUrl,
        tokenLength: apiToken.length,
        tokenPreview: `${apiToken.substring(0, 50)}...`,
        tokenType: process.env.NODE_ENV === 'production' ? 'ID Token' : 'Access Token',
        isExpired: session.expiresAt ? Date.now() >= session.expiresAt * 1000 : false,
        expiresAt: session.expiresAt,
        timeUntilExpiry: session.expiresAt ? Math.round((session.expiresAt * 1000 - Date.now()) / 60000) : null,
      },
      session: {
        provider: session.provider,
        userEmail: session.user?.email,
        hasAccessToken: !!session.accessToken,
        hasIdToken: !!session.idToken,
        hasRefreshToken: !!session.refreshToken,
      }
    });

  } catch (error) {
    console.error('❌ Token test error:', error);
    return NextResponse.json(
      { 
        error: 'Token test failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      },
      { status: 500 }
    );
  }
}