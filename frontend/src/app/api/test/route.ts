import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    message: 'Local API is working!',
    timestamp: new Date().toISOString(),
    success: true,
  });
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
} 