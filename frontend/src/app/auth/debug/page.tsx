'use client'

import { useSession } from 'next-auth/react'

import Card from '@/components/ui/Card'

export default function AuthDebug() {
  const { data: session, status } = useSession()
  
  // Detect if we're running on ngrok
  const currentUrl = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'
  const isNgrok = currentUrl.includes('ngrok-free.app') || currentUrl.includes('ngrok.io')

  const cognitoConfig = {
    currentUrl,
    isNgrok,
    environment: process.env.NODE_ENV,
    expectedCallback: `${currentUrl}/api/auth/callback/cognito`,
    expectedSignOut: currentUrl,
  }

  return (
    <div className="max-w-4xl mx-auto py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Authentication Debug</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <Card.Header>
            <Card.Title>Session Status</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              <div>
                <span className="font-medium">Status:</span> 
                <span className={`ml-2 px-2 py-1 rounded text-sm ${
                  status === 'authenticated' ? 'bg-green-100 text-green-800' :
                  status === 'unauthenticated' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {status}
                </span>
              </div>
              
              {session && (
                <>
                  <div>
                    <span className="font-medium">User:</span> {session.user?.email || 'No email'}
                  </div>
                  <div>
                    <span className="font-medium">Provider:</span> {session.provider || 'Not set'}
                  </div>
                  <div>
                    <span className="font-medium">MFA Enabled:</span> {session.mfaEnabled ? 'Yes' : 'No'}
                  </div>
                </>
              )}
            </div>
          </Card.Content>
        </Card>

        <Card>
          <Card.Header>
            <Card.Title>Current Configuration</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3 text-sm">
              <div>
                <span className="font-medium">Environment:</span> {cognitoConfig.environment}
              </div>
              <div>
                <span className="font-medium">Current URL:</span> 
                <code className="block mt-1 p-2 bg-gray-100 rounded text-xs">
                  {cognitoConfig.currentUrl}
                </code>
              </div>
              <div>
                <span className="font-medium">Using ngrok:</span> 
                <span className={`ml-2 px-2 py-1 rounded text-xs ${
                  isNgrok ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {isNgrok ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <span className="font-medium">Callback URL:</span>
                <code className="block mt-1 p-2 bg-gray-100 rounded text-xs">
                  {cognitoConfig.expectedCallback}
                </code>
              </div>
              <div>
                <span className="font-medium">Sign Out URL:</span>
                <code className="block mt-1 p-2 bg-gray-100 rounded text-xs">
                  {cognitoConfig.expectedSignOut}
                </code>
              </div>
            </div>
          </Card.Content>
        </Card>

        {!isNgrok && (
          <Card className="lg:col-span-2 border-amber-200 bg-amber-50">
            <Card.Header>
              <Card.Title className="text-amber-900">⚠️ Local Development Setup Required</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="space-y-4">
                <p className="text-sm text-amber-800">
                  You're running on localhost, which AWS Cognito cannot reach for callbacks. 
                  You need to expose your local server to the internet using ngrok.
                </p>
                
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-medium text-amber-900 mb-2">Setup Steps:</h4>
                  <ol className="list-decimal list-inside space-y-2 text-sm text-amber-800">
                    <li>Install ngrok: <code className="bg-gray-100 px-1 rounded">brew install ngrok/ngrok/ngrok</code></li>
                    <li>Start your Next.js server: <code className="bg-gray-100 px-1 rounded">npm run dev</code></li>
                    <li>In a new terminal, run: <code className="bg-gray-100 px-1 rounded">ngrok http 3000</code></li>
                    <li>Copy the HTTPS URL (e.g., https://abc123.ngrok-free.app)</li>
                    <li>Update your .env.local file with: <code className="bg-gray-100 px-1 rounded">NEXTAUTH_URL=https://your-ngrok-url.ngrok-free.app</code></li>
                    <li>Add the ngrok URLs to your AWS Cognito configuration</li>
                    <li>Restart your Next.js server</li>
                  </ol>
                </div>
              </div>
            </Card.Content>
          </Card>
        )}

        <Card className="lg:col-span-2">
          <Card.Header>
            <Card.Title>AWS Cognito Configuration</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg transition-colors">
                <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Required Callback URLs in Cognito</h3>
                <div className="space-y-2 text-sm text-blue-800 dark:text-blue-200">
                  <div>
                    <span className="font-medium">Localhost:</span>
                    <code className="block mt-1 p-2 bg-white dark:bg-gray-800 rounded border text-xs text-gray-900 dark:text-gray-100">
                      http://localhost:3000/api/auth/callback/cognito
                    </code>
                  </div>
                  {isNgrok && (
                    <div>
                      <span className="font-medium">ngrok (Current):</span>
                      <code className="block mt-1 p-2 bg-white dark:bg-gray-800 rounded border text-xs text-gray-900 dark:text-gray-100">
                        {cognitoConfig.expectedCallback}
                      </code>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg transition-colors">
                <h3 className="font-medium text-green-900 dark:text-green-100 mb-2">Required Sign Out URLs in Cognito</h3>
                <div className="space-y-2 text-sm text-green-800 dark:text-green-200">
                  <div>
                    <span className="font-medium">Localhost:</span>
                    <code className="block mt-1 p-2 bg-white dark:bg-gray-800 rounded border text-xs text-gray-900 dark:text-gray-100">
                      http://localhost:3000
                    </code>
                  </div>
                  {isNgrok && (
                    <div>
                      <span className="font-medium">ngrok (Current):</span>
                      <code className="block mt-1 p-2 bg-white dark:bg-gray-800 rounded border text-xs text-gray-900 dark:text-gray-100">
                        {cognitoConfig.expectedSignOut}
                      </code>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors">
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">OAuth Configuration Checklist</h3>
                <ul className="space-y-1 text-sm text-gray-800 dark:text-gray-200">
                  <li>✓ OAuth 2.0 scopes: openid, profile, email</li>
                  <li>✓ OAuth 2.0 grant types: Authorization code grant</li>
                  <li>✓ Identity providers: Cognito User Pool</li>
                  <li>✓ App client type: Public client (for development)</li>
                </ul>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  )
} 