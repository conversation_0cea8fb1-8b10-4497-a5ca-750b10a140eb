'use client'

import { useEffect, useState } from 'react'

import { useRouter } from 'next/navigation'

import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'

export default function LogoutCompletePage() {
  const router = useRouter()
  const [countdown, setCountdown] = useState(5)

  useEffect(() => {
    // Clear any remaining client-side data
    if (typeof window !== 'undefined') {
      console.log('🧹 Final cleanup after Cognito logout...')
      
      // Clear all storage
      localStorage.clear()
      sessionStorage.clear()
      
      // Clear any remaining cookies
      document.cookie.split(";").forEach((cookie) => {
        const name = cookie.split("=")[0].trim()
        if (name.includes('next-auth') || 
            name.includes('auth') || 
            name.includes('token') ||
            name.includes('cognito')) {
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`
        }
      })
      
      console.log('✅ Final cleanup completed')
    }

    // Countdown timer
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          router.push('/')
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [router])

  const handleGoHome = () => {
    router.push('/')
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <Card.Header>
          <h1 className="text-2xl font-bold text-center text-green-600">
            Logout Successful
          </h1>
        </Card.Header>
        <Card.Content>
          <div className="text-center space-y-4">
            <div className="text-6xl">✅</div>
            <p className="text-gray-600">
              You have been successfully logged out from all sessions.
            </p>
            <p className="text-sm text-gray-500">
              Your email, user ID, and all session data have been cleared.
            </p>
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-700">
                Redirecting to home page in <strong>{countdown}</strong> seconds...
              </p>
            </div>
          </div>
        </Card.Content>
        <Card.Footer>
          <Button 
            onClick={handleGoHome}
            className="w-full"
          >
            Go to Home Page Now
          </Button>
        </Card.Footer>
      </Card>
    </div>
  )
}
