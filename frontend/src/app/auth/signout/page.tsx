'use client'

import { useState } from 'react'

import { useRouter } from 'next/navigation'

import { useSession } from 'next-auth/react'

import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'
import { performCompleteSignOut } from '@/lib/auth-utils'

export default function SignOut() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handleSignOut = async () => {
    setLoading(true)
    try {
      await performCompleteSignOut('/')
    } catch (error) {
      console.error('Sign out error:', error)
      setLoading(false)
    }
  }

  const handleCancel = () => {
    router.back()
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8 transition-colors">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-gray-100">
            Sign out
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Are you sure you want to sign out of your account?
          </p>
        </div>

        <Card className="mt-8">
          <Card.Content>
            {session?.user && (
              <div className="mb-6 text-center">
                <div className="flex items-center justify-center mb-4">
                  {session.user.image && (
                    <img 
                      src={session.user.image} 
                      alt={session.user.name || 'User'} 
                      className="w-16 h-16 rounded-full"
                    />
                  )}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Signed in as{' '}
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {session.user.name || session.user.email}
                  </span>
                </p>
              </div>
            )}

            <div className="space-y-3">
              <Button
                fullWidth
                variant="destructive"
                size="lg"
                loading={loading}
                onClick={handleSignOut}
              >
                Yes, sign me out
              </Button>
              
              <Button
                fullWidth
                variant="outline"
                size="lg"
                onClick={handleCancel}
                disabled={loading}
              >
                Cancel
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  )
} 