'use client'

import { useState } from 'react'

import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'

export default function AuthTest() {
  const [testResult, setTestResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testCognitoConnection = async () => {
    setLoading(true)
    setTestResult('Testing...')
    
    try {
      // Test the Cognito well-known endpoint
      const wellKnownUrl = 'https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_H2kKHGUAT/.well-known/openid-configuration'
      const response = await fetch(wellKnownUrl)
      const data = await response.json()
      
      setTestResult(`✅ Cognito endpoint accessible\n📋 Issuer: ${data.issuer}\n🔗 Authorization endpoint: ${data.authorization_endpoint}`)
    } catch (error) {
      setTestResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const directCognitoTest = () => {
    // Build the direct Cognito URL
    const params = new URLSearchParams({
      client_id: '2jh76f894g6lv9vrus4qbb9hu7',
      response_type: 'code',
      scope: 'openid profile email',
      redirect_uri: 'https://0fca-58-176-137-190.ngrok-free.app/api/auth/callback/cognito',
      state: 'test-state-12345'
    })
    
    const cognitoUrl = `https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_H2kKHGUAT/authorize?${params.toString()}`
    
    // Open in new tab
    window.open(cognitoUrl, '_blank')
  }

  return (
    <div className="max-w-4xl mx-auto py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Cognito Authentication Test</h1>
      
      <div className="grid grid-cols-1 gap-6">
        <Card>
          <Card.Header>
            <Card.Title>Connection Test</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Test if we can reach the Cognito endpoints.
              </p>
              
              <Button 
                onClick={testCognitoConnection}
                loading={loading}
                variant="primary"
              >
                Test Cognito Connection
              </Button>
              
              {testResult && (
                <pre className="bg-gray-100 p-3 rounded text-sm whitespace-pre-wrap">
                  {testResult}
                </pre>
              )}
            </div>
          </Card.Content>
        </Card>

        <Card>
          <Card.Header>
            <Card.Title>Direct OAuth Test</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Test the OAuth flow directly with Cognito (bypasses NextAuth).
              </p>
              
              <div className="bg-blue-50 p-4 rounded">
                <h4 className="font-medium text-blue-900 mb-2">Direct Cognito URL:</h4>
                <code className="text-xs break-all">
                  https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_H2kKHGUAT/authorize?client_id=2jh76f894g6lv9vrus4qbb9hu7&response_type=code&scope=openid%20profile%20email&redirect_uri=https://0fca-58-176-137-190.ngrok-free.app/api/auth/callback/cognito&state=test-state-12345
                </code>
              </div>
              
              <Button 
                onClick={directCognitoTest}
                variant="outline"
              >
                Test Direct OAuth Flow
              </Button>
              
              <div className="text-sm text-amber-600">
                ⚠️ This will open a new tab and try to authenticate directly with Cognito.
                If it works, the issue is with NextAuth configuration.
                If it fails, the issue is with Cognito configuration.
              </div>
            </div>
          </Card.Content>
        </Card>

        <Card>
          <Card.Header>
            <Card.Title>Configuration Summary</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3 text-sm">
              <div>
                <span className="font-medium">Client ID:</span>
                <code className="ml-2 bg-gray-100 px-1 rounded">2jh76f894g6lv9vrus4qbb9hu7</code>
              </div>
              <div>
                <span className="font-medium">Redirect URI:</span>
                <code className="ml-2 bg-gray-100 px-1 rounded text-xs">
                  https://0fca-58-176-137-190.ngrok-free.app/api/auth/callback/cognito
                </code>
              </div>
              <div>
                <span className="font-medium">Scopes:</span>
                <code className="ml-2 bg-gray-100 px-1 rounded">openid profile email</code>
              </div>
              <div>
                <span className="font-medium">User Pool:</span>
                <code className="ml-2 bg-gray-100 px-1 rounded">ap-northeast-1_H2kKHGUAT</code>
              </div>
            </div>
          </Card.Content>
        </Card>

        <Card className="border-red-200 bg-red-50">
          <Card.Header>
            <Card.Title className="text-red-900">Common Issues Checklist</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-2 text-sm text-red-800">
              <h4 className="font-medium">In AWS Cognito, verify:</h4>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>App client type is "Public client" (for development)</li>
                <li>Exact callback URL: <code>https://0fca-58-176-137-190.ngrok-free.app/api/auth/callback/cognito</code></li>
                <li>OAuth scopes enabled: openid, profile, email</li>
                <li>Grant type: Authorization code grant</li>
                <li>Identity provider: Cognito User Pool (checked)</li>
                <li><strong>App client does NOT require client secret</strong></li>
                <li>Hosted UI domain configured (if using Hosted UI)</li>
              </ul>
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  )
} 