'use client';

import { useEffect, useState } from 'react';

export default function CSSDebugPage() {
  const [htmlClasses, setHtmlClasses] = useState('');
  const [computedStyles, setComputedStyles] = useState<any>({});

  useEffect(() => {
    const updateInfo = () => {
      setHtmlClasses(document.documentElement.className);
      
      // Test element to check computed styles
      const testDiv = document.createElement('div');
      testDiv.className = 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100';
      document.body.appendChild(testDiv);
      
      const styles = window.getComputedStyle(testDiv);
      setComputedStyles({
        backgroundColor: styles.backgroundColor,
        color: styles.color,
      });
      
      document.body.removeChild(testDiv);
    };

    updateInfo();
    
    // Watch for class changes
    const observer = new MutationObserver(updateInfo);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });
    
    return () => observer.disconnect();
  }, []);

  const toggleDark = () => {
    document.documentElement.classList.toggle('dark');
  };

  return (
    <div className="p-8 min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">CSS Debug Page</h1>
        
        <div className="space-y-6">
          {/* Manual Toggle */}
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Manual Toggle Test</h2>
            <button
              onClick={toggleDark}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600"
            >
              Toggle Dark Class
            </button>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              HTML classes: <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{htmlClasses || 'none'}</code>
            </p>
          </div>

          {/* Color Test Grid */}
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Color Test Grid</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded">
                <div className="text-gray-900 dark:text-gray-100 font-semibold">Primary Text</div>
                <div className="text-gray-600 dark:text-gray-400 text-sm">Secondary Text</div>
              </div>
              
              <div className="p-4 bg-blue-100 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded">
                <div className="text-blue-900 dark:text-blue-100 font-semibold">Blue Theme</div>
                <div className="text-blue-700 dark:text-blue-300 text-sm">Blue Secondary</div>
              </div>
              
              <div className="p-4 bg-green-100 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded">
                <div className="text-green-900 dark:text-green-100 font-semibold">Green Theme</div>
                <div className="text-green-700 dark:text-green-300 text-sm">Green Secondary</div>
              </div>
              
              <div className="p-4 bg-red-100 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded">
                <div className="text-red-900 dark:text-red-100 font-semibold">Red Theme</div>
                <div className="text-red-700 dark:text-red-300 text-sm">Red Secondary</div>
              </div>
            </div>
          </div>

          {/* Computed Styles */}
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Computed Styles Test</h2>
            <div className="space-y-2 text-sm">
              <div>Background Color: <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{computedStyles.backgroundColor}</code></div>
              <div>Text Color: <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{computedStyles.color}</code></div>
            </div>
          </div>

          {/* CSS Variables Test */}
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">CSS Variables Test</h2>
            <div className="space-y-2">
              <div 
                className="p-4 rounded border"
                style={{ 
                  backgroundColor: 'hsl(var(--background))', 
                  color: 'hsl(var(--foreground))',
                  borderColor: 'hsl(var(--border))'
                }}
              >
                Using CSS Variables: background and foreground
              </div>
            </div>
          </div>

          {/* Tailwind Classes Test */}
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Tailwind Classes Test</h2>
            <div className="space-y-2">
              <div className="p-2 bg-background text-foreground border border-border rounded">
                Using Tailwind custom colors
              </div>
              <div className="p-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700 rounded">
                Using explicit dark: classes
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 p-4 rounded-lg">
            <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Debug Instructions:</h3>
            <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
              <li>1. Click "Toggle Dark Class" to manually add/remove the dark class</li>
              <li>2. Watch the color changes in the grid above</li>
              <li>3. Check the computed styles to see if CSS is being applied</li>
              <li>4. If colors don't change, there's a Tailwind compilation issue</li>
              <li>5. If they do change, the issue is with the theme toggle logic</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
