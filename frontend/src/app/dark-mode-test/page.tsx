'use client';

import { useEffect, useState } from 'react';

import { Card } from '@/components/ui';
import { useTheme } from '@/hooks/useTheme';

export default function DarkModeTestPage() {
  const { mode, isDark, isInitialized, toggleTheme } = useTheme();
  const [htmlClasses, setHtmlClasses] = useState('');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    const updateClasses = () => {
      if (typeof document !== 'undefined') {
        setHtmlClasses(document.documentElement.className);
      }
    };
    
    updateClasses();
    
    // Watch for class changes
    const observer = new MutationObserver(updateClasses);
    if (typeof document !== 'undefined') {
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['class']
      });
    }
    
    return () => observer.disconnect();
  }, []);

  if (!mounted || !isInitialized) {
    return <div>Loading...</div>;
  }

  return (
    <div className="p-8 min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Dark Mode Debug Test
        </h1>

        {/* Debug Info */}
        <div className="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <h3 className="font-bold text-yellow-800 dark:text-yellow-200 mb-2">Debug Information</h3>
          <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
            <div>Theme Mode: <strong>{mode}</strong></div>
            <div>Is Dark: <strong>{isDark ? 'true' : 'false'}</strong></div>
            <div>HTML Classes: <strong>{htmlClasses || 'none'}</strong></div>
            <div>Has 'dark' class: <strong>{htmlClasses.includes('dark') ? 'YES' : 'NO'}</strong></div>
          </div>
        </div>

        {/* Theme Toggle */}
        <div className="flex gap-4">
          <button
            onClick={toggleTheme}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 transition-colors"
          >
            Toggle Theme
          </button>
          
          <button
            onClick={() => document.documentElement.classList.toggle('dark')}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
          >
            Manual Toggle (Direct DOM)
          </button>
        </div>

        {/* Card Tests */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Card Component Tests</h2>
          
          {/* Test Card 1 */}
          <Card>
            <Card.Header>
              <Card.Title>Test Card 1</Card.Title>
            </Card.Header>
            <Card.Content>
              <p className="text-gray-700 dark:text-gray-300">
                This card should change background and text colors in dark mode.
              </p>
              <p className="text-gray-700 dark:text-gray-300 mt-2">
                Background should be white in light mode, gray-800 in dark mode.
              </p>
            </Card.Content>
          </Card>

          {/* Test Card 2 with explicit classes */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Manual Card (Same Classes)
              </h3>
            </div>
            <div>
              <p className="text-gray-700 dark:text-gray-300">
                This is a manual div with the exact same classes as the Card component.
              </p>
              <p className="text-gray-700 dark:text-gray-300 mt-2">
                If this works but the Card component doesn't, there's a component issue.
              </p>
            </div>
          </div>

          {/* Test with inline styles for comparison */}
          <div style={{
            backgroundColor: isDark ? '#1f2937' : 'white',
            color: isDark ? '#d1d5db' : '#374151',
            padding: '1.5rem',
            borderRadius: '0.5rem',
            border: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
          }}>
            <h3 style={{
              fontSize: '1.125rem',
              fontWeight: '600',
              color: isDark ? '#f3f4f6' : '#111827',
              marginBottom: '1rem'
            }}>
              Inline Styles Card (JS-controlled)
            </h3>
            <p>
              This card uses inline styles controlled by JavaScript isDark state.
              If this works correctly, the issue is with Tailwind dark mode classes.
            </p>
          </div>
        </div>

        {/* Raw HTML test */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Raw Element Tests</h2>
          
          <div className="bg-red-500 dark:bg-blue-500 p-4 rounded text-white">
            Simple color test: Red in light mode, Blue in dark mode
          </div>
          
          <div className="text-gray-900 dark:text-gray-100 p-4 border border-gray-300 dark:border-gray-600 rounded">
            Text and border test: Should change colors in dark mode
          </div>
        </div>
      </div>
    </div>
  );
}
