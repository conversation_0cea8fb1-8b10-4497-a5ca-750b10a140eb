'use client';

import { useEffect, useState } from 'react';

import { SessionInfo } from '@/components/auth';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { useTranslation } from '@/i18n/provider';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { fetchFunds } from '@/store/slices/fundsSlice';
import { Fund } from '@/types';
import { formatCurrency } from '@/utils';

export default function DashboardPage() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { funds, loading, error } = useAppSelector(state => state.funds);
  const [dashboardData, setDashboardData] = useState({
    totalFunds: 0,
    totalAUM: 0,
    avgChange: 0,
    topPerformers: [] as Fund[],
    bottomPerformers: [] as Fund[],
    fundsByType: {} as Record<string, number>,
    fundsByRisk: {} as Record<string, number>
  });

  useEffect(() => {
    if (funds.length === 0) {
      dispatch(fetchFunds(100));
    }
  }, [dispatch, funds.length]);

  useEffect(() => {
    if (funds.length > 0) {
      const totalFunds = funds.length;
      const totalAUM = funds.reduce((sum, fund) => sum + fund.aum, 0);
      const avgChange = funds.reduce((sum, fund) => sum + fund.changePercent, 0) / funds.length;
      
      const sortedByPerformance = [...funds].sort((a, b) => b.changePercent - a.changePercent);
      const topPerformers = sortedByPerformance.slice(0, 3);
      const bottomPerformers = sortedByPerformance.slice(-3).reverse();
      
      const fundsByType = funds.reduce((acc, fund) => {
        acc[fund.type] = (acc[fund.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      const fundsByRisk = funds.reduce((acc, fund) => {
        acc[fund.riskLevel] = (acc[fund.riskLevel] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      setDashboardData({
        totalFunds,
        totalAUM,
        avgChange,
        topPerformers,
        bottomPerformers,
        fundsByType,
        fundsByRisk
      });
    }
  }, [funds]);

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{t('dashboard.title')}</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">{t('dashboard.subtitle')}</p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">{t('common.loading')}</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{t('dashboard.title')}</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">{t('dashboard.subtitle')}</p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-red-500">{t('common.error')}: {error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{t('dashboard.title')}</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">{t('dashboard.subtitle')}</p>
      </div>

      {/* Fund Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card hover>
          <Card.Header>
            <Card.Title>Total Funds</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="text-2xl font-bold text-blue-600">
              {dashboardData.totalFunds}
            </div>
            <p className="text-sm text-gray-500 mt-1">Active investment funds</p>
          </Card.Content>
        </Card>

        <Card hover>
          <Card.Header>
            <Card.Title>Total AUM</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(dashboardData.totalAUM)}
            </div>
            <p className="text-sm text-gray-500 mt-1">Assets under management</p>
          </Card.Content>
        </Card>

        <Card hover>
          <Card.Header>
            <Card.Title>Average Change</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className={`text-2xl font-bold ${dashboardData.avgChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatPercentage(dashboardData.avgChange)}
            </div>
            <p className="text-sm text-gray-500 mt-1">Market performance today</p>
          </Card.Content>
        </Card>

        <Card hover>
          <Card.Header>
            <Card.Title>Fund Categories</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="text-2xl font-bold text-purple-600">
              {Object.keys(dashboardData.fundsByType).length}
            </div>
            <p className="text-sm text-gray-500 mt-1">Different fund types</p>
          </Card.Content>
        </Card>
      </div>

      {/* Top & Bottom Performers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <Card.Header>
            <Card.Title>Top Performers</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-4">
              {dashboardData.topPerformers.map((fund) => (
                <div key={fund.id} className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">{fund.name}</h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{fund.symbol}</p>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(fund.nav)}</div>
                    <div className="text-sm text-green-600">{formatPercentage(fund.changePercent)}</div>
                  </div>
                </div>
              ))}
            </div>
          </Card.Content>
        </Card>

        <Card>
          <Card.Header>
            <Card.Title>Bottom Performers</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-4">
              {dashboardData.bottomPerformers.map((fund) => (
                <div key={fund.id} className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">{fund.name}</h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{fund.symbol}</p>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(fund.nav)}</div>
                    <div className="text-sm text-red-600">{formatPercentage(fund.changePercent)}</div>
                  </div>
                </div>
              ))}
            </div>
          </Card.Content>
        </Card>
      </div>

      {/* Fund Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <Card.Header>
            <Card.Title>Fund Types</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {Object.entries(dashboardData.fundsByType).map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <div className="capitalize text-gray-900 dark:text-gray-100">{type.replace('_', ' ')}</div>
                  <div className="text-sm">
                    <span className="font-medium text-gray-900 dark:text-gray-100">{count}</span>
                    <span className="text-gray-500 dark:text-gray-400 ml-1">
                      ({((count / dashboardData.totalFunds) * 100).toFixed(1)}%)
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </Card.Content>
        </Card>

        <Card>
          <Card.Header>
            <Card.Title>Risk Levels</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {Object.entries(dashboardData.fundsByRisk).map(([risk, count]) => (
                <div key={risk} className="flex items-center justify-between">
                  <div className="capitalize text-gray-900 dark:text-gray-100">{risk.replace('_', ' ')}</div>
                  <div className="text-sm">
                    <span className="font-medium text-gray-900 dark:text-gray-100">{count}</span>
                    <span className="text-gray-500 dark:text-gray-400 ml-1">
                      ({((count / dashboardData.totalFunds) * 100).toFixed(1)}%)
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </Card.Content>
        </Card>
      </div>

      {/* Session Information and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <Card.Header>
              <Card.Title>Quick Actions</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button fullWidth>View Funds</Button>
                <Button variant="outline" fullWidth>Add Fund</Button>
                <Button variant="outline" fullWidth>View Reports</Button>
                <Button variant="outline" fullWidth>Settings</Button>
              </div>
            </Card.Content>
          </Card>
        </div>
        
        <div>
          <SessionInfo compact showActions={false} />
        </div>
      </div>
    </div>
  );
} 