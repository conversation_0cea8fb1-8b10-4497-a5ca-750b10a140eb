'use client';

import { Card } from '@/components/ui';

export default function DebugCardPage() {
  return (
    <div className="container mx-auto p-8 space-y-6">
      <h1 className="text-3xl font-bold text-gray-900">Debug Card Styling</h1>
      
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <h3 className="font-bold text-yellow-800 mb-2">Debug Instructions</h3>
        <p className="text-yellow-700 text-sm">
          Open browser console to see the CSS classes being applied to Card components.
          Compare with the expected classes shown below.
        </p>
      </div>

      {/* Test Card 1: Basic Card */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Test 1: Basic Card</h2>
        <Card>
          <Card.Header>
            <Card.Title>Basic Card Title</Card.Title>
          </Card.Header>
          <Card.Content>
            <p>This is basic card content. The header should have mb-4 class.</p>
          </Card.Content>
        </Card>
        <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
          Expected Card classes: bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700<br/>
          Expected Header classes: mb-4<br/>
          Expected Title classes: text-lg font-semibold text-gray-900 dark:text-gray-100
        </div>
      </div>

      {/* Test Card 2: Card with Custom Classes */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Test 2: Card with Custom Classes</h2>
        <Card className="border-2 border-blue-200">
          <Card.Header className="bg-blue-50">
            <Card.Title className="text-blue-900">Custom Styled Card</Card.Title>
          </Card.Header>
          <Card.Content>
            <p>This card has custom classes applied to test class merging.</p>
          </Card.Content>
        </Card>
        <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
          Expected Card classes: bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 border-2 border-blue-200<br/>
          Expected Header classes: mb-4 bg-blue-50<br/>
          Expected Title classes: text-lg font-semibold text-gray-900 dark:text-gray-100 text-blue-900
        </div>
      </div>

      {/* Test Card 3: Different Shadow */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Test 3: Large Shadow Card</h2>
        <Card shadow="lg" hover>
          <Card.Header>
            <Card.Title>Large Shadow Card</Card.Title>
          </Card.Header>
          <Card.Content>
            <p>This card should have a large shadow and hover effect.</p>
          </Card.Content>
        </Card>
        <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
          Expected Card classes: bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200
        </div>
      </div>

      {/* Raw HTML Comparison */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Test 4: Raw HTML for Comparison</h2>
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Raw HTML Card Title</h3>
          </div>
          <div>
            <p>This is raw HTML with the same classes. It should look identical to the React Card above.</p>
          </div>
        </div>
        <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
          This is pure HTML/CSS - if this looks different from the React cards above, there's a React rendering issue.
        </div>
      </div>
    </div>
  );
}
