'use client';

import { useState, useEffect } from 'react';

import { useParams, useRouter } from 'next/navigation';

import HoldingsEditor from '@/components/funds/HoldingsEditor';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { useTranslation } from '@/i18n/provider';
import { fundApi } from '@/lib/api';
import { Fund } from '@/types';

export default function HoldingsManagementPage() {
  const params = useParams();
  const router = useRouter();
  const fundId = params.id as string;
  const { t } = useTranslation();
  
  const [fund, setFund] = useState<Fund | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const fetchFund = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fundApi.getFundById(fundId);
        
        if (response.data) {
          setFund(response.data);
          setError(null);
        } else {
          setFund(null);
          setError(response.message || 'Failed to fetch fund details');
        }
      } catch (err) {
        setFund(null);
        setError('Failed to fetch fund details');
        console.error('Error fetching fund:', err);
      } finally {
        setLoading(false);
      }
    };

    if (fundId) {
      fetchFund();
    }
  }, [fundId]);

  const handleSubmit = async (holdingsData: any) => {
    try {
      setSubmitting(true);
      setError(null);
      setSuccess(null);

      console.log('Submitting holdings data:', holdingsData);
      
      // Update fund with new holdings data
      const response = await fundApi.updateFund(fundId, {
        holdings: holdingsData
      });
      
      if (response.success) {
        setSuccess('Holdings updated successfully');
        
        // Redirect back to fund details after a delay
        setTimeout(() => {
          router.push(`/funds/${fundId}`);
        }, 2000);
      } else {
        setError(response.message || 'Failed to update holdings');
      }
    } catch (err) {
      setError('Failed to update holdings');
      console.error('Error updating holdings:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push(`/funds/${fundId}`);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading fund details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error && !fund) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Card.Content>
            <div className="text-center py-8">
              <div className="text-red-600 dark:text-red-400 text-lg font-medium mb-4">
                Error loading fund
              </div>
              <p className="text-gray-600 dark:text-gray-400 mb-6">{error}</p>
              <Button onClick={() => router.push('/funds')}>
                Back to Funds
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  if (!fund) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Card.Content>
            <div className="text-center py-8">
              <div className="text-gray-600 dark:text-gray-400 text-lg font-medium mb-4">
                Fund not found
              </div>
              <Button onClick={() => router.push('/funds')}>
                Back to Funds
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Holdings Management</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage holdings and allocations for {fund.name}
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => router.push(`/funds/${fundId}`)}
          >
            Back to Fund Details
          </Button>
        </div>
      </div>

      {/* Success Message */}
      {success && (
        <div className="mb-6">
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800 dark:text-green-200">
                  {success}
                </p>
                <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                  Redirecting to fund details...
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && fund && (
        <div className="mb-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800 dark:text-red-200">
                  {error}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Holdings Editor */}
      <HoldingsEditor
        fund={fund}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        disabled={submitting}
      />

      {/* Loading Overlay */}
      {submitting && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm mx-4">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>
              <div>
                <p className="font-medium text-gray-900 dark:text-gray-100">Updating Holdings</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Please wait...</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}