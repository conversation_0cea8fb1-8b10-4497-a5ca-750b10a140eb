'use client';

import { useState, useEffect } from 'react';

import { useParams, useRouter } from 'next/navigation';

import MarketDataInputForm from '@/components/funds/MarketDataInput';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { useTranslation } from '@/i18n/provider';
import { fundApi } from '@/lib/api';
import { Fund, MarketDataInput } from '@/types';

export default function MarketDataInputPage() {
  const params = useParams();
  const router = useRouter();
  const fundId = params.id as string;
  const { t } = useTranslation();
  
  const [fund, setFund] = useState<Fund | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const fetchFund = async () => {
      try {
        setLoading(true);
        setError(null); // Clear errors at the start of the request
        console.log('🔍 Fetching fund details for:', fundId);

        const response = await fundApi.getFundById(fundId);
        console.log('📡 API response:', response);

        // Check if we have data (success can be undefined from backend)
        if (response.data) {
          setFund(response.data);
          setError(null); // Clear any previous errors
          console.log('✅ Fund loaded successfully:', response.data.name);
        } else {
          setFund(null);
          setError(response.message || 'Failed to fetch fund details');
          console.error('❌ API returned error:', response.message);
        }
      } catch (err) {
        setFund(null);
        setError('Failed to fetch fund details');
        console.error('❌ Exception during fund fetch:', err);
      } finally {
        setLoading(false);
      }
    };

    if (fundId) {
      fetchFund();
    }
  }, [fundId]);

  const handleSubmit = async (marketData: Partial<MarketDataInput>) => {
    try {
      setSubmitting(true);
      setError(null);
      setSuccess(null);

      console.log('Submitting market data:', marketData);
      
      const response = await fundApi.submitMarketDataInput(fundId, marketData);
      
      if (response.success) {
        setSuccess(response.message || t('funds.marketDataSubmittedSuccessfully'));
        
        // Redirect back to fund details after a delay
        setTimeout(() => {
          router.push(`/funds/${fundId}`);
        }, 2000);
      } else {
        setError(response.message || 'Failed to submit market data');
      }
    } catch (err) {
      setError('Failed to submit market data');
      console.error('Error submitting market data:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push(`/funds/${fundId}`);
  };

  // Debug logging
  console.log('🔍 MarketDataInputPage state:', {
    loading,
    error,
    fund: fund ? { id: fund.id, name: fund.name } : null,
    success,
    fundId
  });

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">{t('funds.loadingFunds')}</p>
          </div>
        </div>
      </div>
    );
  }

  if (error && !fund) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Card.Content>
            <div className="text-center py-8">
              <div className="text-red-600 dark:text-red-400 text-lg font-medium mb-4">
                {t('funds.errorLoadingFunds')}
              </div>
              <p className="text-gray-600 dark:text-gray-400 mb-6">{error}</p>
              <Button onClick={() => router.push('/funds')}>
                {t('funds.backToFunds')}
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  if (!fund) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Card.Content>
            <div className="text-center py-8">
              <div className="text-gray-600 dark:text-gray-400 text-lg font-medium mb-4">
                {t('funds.fundNotFound')}
              </div>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {t('funds.fundNotFoundMessage')}
              </p>
              <Button onClick={() => router.push('/funds')}>
                {t('funds.backToFunds')}
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{t('funds.marketDataInputTitle')}</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              {t('funds.submitMarketDataFor', { name: fund.name, symbol: fund.symbol || fund.id })}
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => router.push(`/funds/${fundId}`)}
          >
            {t('funds.backToFundDetails')}
          </Button>
        </div>
      </div>

      {/* Success Message */}
      {success && (
        <div className="mb-6">
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800 dark:text-green-200">
                  {success}
                </p>
                <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                  {t('funds.redirectingToFundDetails')}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Message - Only show if there's an error and we have fund data (for form submission errors) */}
      {error && fund && (
        <div className="mb-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800 dark:text-red-200">
                  {error}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Market Data Input Form */}
      <MarketDataInputForm
        fund={fund}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        className={submitting ? 'opacity-50 pointer-events-none' : ''}
      />

      {/* Loading Overlay */}
      {submitting && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm mx-4">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>
              <div>
                <p className="font-medium text-gray-900 dark:text-gray-100">{t('funds.submittingMarketData')}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">{t('funds.pleaseWait')}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
