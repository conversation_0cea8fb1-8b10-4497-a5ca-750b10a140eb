'use client';

import { useState, useCallback } from 'react';

import {
  CloudUpload,
  Info,
  TipsAndUpdates,
  Description,
} from '@mui/icons-material';

import { Card, Button, FileUpload, UploadStatus } from '@/components/ui';
import { UploadFile } from '@/components/ui/FileUpload';
import { UploadResult } from '@/components/ui/UploadStatus';
import { useTranslation } from '@/i18n/provider';
import { fundApi } from '@/lib/api';


export default function BulkUploadPage() {
  const { t } = useTranslation();
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const handleFilesSelected = useCallback((newFiles: UploadFile[]) => {
    setFiles(prevFiles => [...prevFiles, ...newFiles]);
  }, []);

  const handleFileRemove = useCallback((fileId: string) => {
    setFiles(prevFiles => prevFiles.filter(file => file.id !== fileId));
  }, []);

  const updateFileStatus = useCallback((fileId: string, status: UploadFile['status'], progress?: number, error?: string) => {
    setFiles(prevFiles => 
      prevFiles.map(file => 
        file.id === fileId 
          ? { ...file, status, progress: progress ?? file.progress, error }
          : file
      )
    );
  }, []);

  const handleUpload = useCallback(async (filesToUpload: UploadFile[]) => {
    if (filesToUpload.length === 0) {return;}

    setIsUploading(true);

    try {
      // Update file status to uploading
      filesToUpload.forEach(file => {
        updateFileStatus(file.id, 'uploading', 0);
      });

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        filesToUpload.forEach(file => {
          const currentFile = files.find(f => f.id === file.id);
          if (currentFile && currentFile.status === 'uploading' && currentFile.progress < 90) {
            updateFileStatus(file.id, 'uploading', currentFile.progress + Math.random() * 20);
          }
        });
      }, 500);

      // Call the API
      const response = await fundApi.bulkUploadPDFs(filesToUpload.map(f => f.file));

      clearInterval(progressInterval);

      if (response.success && response.data) {
        // Update file status to success
        filesToUpload.forEach(file => {
          updateFileStatus(file.id, 'success', 100);
        });

        // Add results to the results list
        setUploadResults(prevResults => [...prevResults, ...response.data]);
      } else {
        // Handle API error
        filesToUpload.forEach(file => {
          updateFileStatus(file.id, 'error', 0, 'Upload failed');
        });
      }
    } catch (error) {
      console.error('Upload failed:', error);
      
      // Update file status to error
      filesToUpload.forEach(file => {
        updateFileStatus(file.id, 'error', 0, error instanceof Error ? error.message : 'Upload failed');
      });
    } finally {
      setIsUploading(false);
    }
  }, [files, updateFileStatus]);

  const handleRetry = useCallback(async (resultId: string) => {
    const result = uploadResults.find(r => r.id === resultId);
    if (!result) {return;}

    // Find the original file and retry upload
    const originalFile = files.find(f => f.file.name === result.fileName);
    if (originalFile) {
      await handleUpload([originalFile]);
    }
  }, [uploadResults, files, handleUpload]);

  const handleClearResults = useCallback(() => {
    setUploadResults([]);
    setFiles(prevFiles => prevFiles.filter(file => file.status === 'pending'));
  }, []);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Bulk Fund Upload
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Upload multiple PDF files to extract and import fund data automatically
          </p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <Info className="w-4 h-4" />
          <span>PDF files only</span>
        </div>
      </div>

      {/* Instructions Card */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <TipsAndUpdates className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <Card.Title>How to Use Bulk Upload</Card.Title>
          </div>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <span className="text-sm font-bold text-blue-600 dark:text-blue-400">1</span>
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100">Select PDF Files</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Choose fund documents in PDF format. Maximum 10 files, 10MB each.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <span className="text-sm font-bold text-blue-600 dark:text-blue-400">2</span>
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100">Upload & Process</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Files will be processed using AI to extract fund information automatically.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <span className="text-sm font-bold text-blue-600 dark:text-blue-400">3</span>
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100">Review & Edit</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Review extracted data and click "Edit" to make any necessary adjustments.
                </p>
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* File Upload Section */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <CloudUpload className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <Card.Title>Upload PDF Files</Card.Title>
          </div>
          <Card.Description>
            Select or drag and drop PDF files containing fund information
          </Card.Description>
        </Card.Header>
        <Card.Content>
          <FileUpload
            files={files}
            onFilesSelected={handleFilesSelected}
            onFileRemove={handleFileRemove}
            onUpload={handleUpload}
            maxFiles={10}
            maxFileSize={10}
            acceptedFileTypes={['.pdf']}
            disabled={isUploading}
          />
        </Card.Content>
      </Card>

      {/* Upload Results */}
      {uploadResults.length > 0 && (
        <UploadStatus
          results={uploadResults}
          onRetry={handleRetry}
          onClear={handleClearResults}
        />
      )}

      {/* Tips Section */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <Description className="w-5 h-5 text-green-600 dark:text-green-400" />
            <Card.Title>Tips for Best Results</Card.Title>
          </div>
        </Card.Header>
        <Card.Content>
          <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-start space-x-2">
              <span className="text-green-600 dark:text-green-400">•</span>
              <span>Use high-quality PDF files with clear, readable text</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-green-600 dark:text-green-400">•</span>
              <span>Ensure PDFs contain structured fund data (factsheets, reports, etc.)</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-green-600 dark:text-green-400">•</span>
              <span>Avoid password-protected or image-only PDF files</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-green-600 dark:text-green-400">•</span>
              <span>Review extracted data carefully and make corrections as needed</span>
            </div>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
}
