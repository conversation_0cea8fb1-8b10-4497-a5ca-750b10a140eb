'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { CloudUpload, Compare } from '@mui/icons-material';

import FundComparison from '@/components/funds/FundComparison';
import FundFilters from '@/components/funds/FundFilters';
import FundListTable from '@/components/funds/FundListTable';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { useTranslation } from '@/i18n/provider';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { fetchFunds } from '@/store/slices/fundsSlice';


export default function FundsPage() {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { filteredFunds, loading, error, lastUpdated } = useAppSelector(state => state.funds);
  const { t } = useTranslation();
  const [selectedFunds, setSelectedFunds] = useState<string[]>([]);
  const [showComparison, setShowComparison] = useState(false);

  useEffect(() => {
    // Fetch funds on component mount
    console.log('Funds page mounted, fetching funds...');
    dispatch(fetchFunds(100)).catch((error) => {
      console.error('Error fetching funds on mount:', error);
    });
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(fetchFunds(100));
  };

  const handleFundSelection = (fundId: string, isSelected: boolean) => {
    if (isSelected) {
      if (selectedFunds.length < 3) {
        setSelectedFunds([...selectedFunds, fundId]);
      }
    } else {
      setSelectedFunds(selectedFunds.filter(id => id !== fundId));
    }
  };

  const handleCompare = () => {
    if (selectedFunds.length > 0) {
      setShowComparison(true);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{t('funds.manager')}</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            {t('funds.exploreAnalyze')}
          </p>
          {lastUpdated && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {t('funds.lastUpdated', { date: new Date(lastUpdated).toLocaleString() })}
            </p>
          )}
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            loading={loading}
            disabled={loading}
          >
            {loading ? t('funds.refreshing') : t('funds.refreshData')}
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push('/funds/bulk-upload')}
            className="flex items-center space-x-2"
          >
            <CloudUpload className="w-4 h-4" />
            <span>{t('funds.bulkUpload')}</span>
          </Button>
          <Button
            variant="outline"
            onClick={handleCompare}
            disabled={selectedFunds.length === 0}
            className="flex items-center space-x-2"
            title={selectedFunds.length === 0 ? t('funds.selectFunds') : t('funds.compareSelected')}
          >
            <Compare className="w-4 h-4" />
            <span>{t('funds.compare')} {selectedFunds.length > 0 && `(${selectedFunds.length})`}</span>
          </Button>
          <Button onClick={() => router.push('/funds/new')}>{t('funds.addFund')}</Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <Card.Content>
            <div className="flex items-center">
              <div className="text-red-600 mr-3">⚠️</div>
              <div>
                <h3 className="text-sm font-medium text-red-800">{t('funds.errorLoadingFunds')}</h3>
                <p className="text-sm text-red-600 mt-1">{error}</p>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                className="ml-auto"
                onClick={handleRefresh}
              >
                {t('funds.retry')}
              </Button>
            </div>
          </Card.Content>
        </Card>
      )}

      {/* Filters */}
      <FundFilters />

      {/* Fund List Table */}
      <Card>
        <Card.Header>
          <div className="flex justify-between items-center">
            <Card.Title>
              {t('funds.fundPortfolio')} ({t('funds.fundsCount', { count: filteredFunds.length })})
            </Card.Title>
            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              {loading && (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  {t('common.loading')}
                </div>
              )}
            </div>
          </div>
        </Card.Header>
        <Card.Content className="p-0">
          <FundListTable
            selectedFunds={selectedFunds}
            onFundSelection={handleFundSelection}
            maxSelection={3}
          />
        </Card.Content>
      </Card>

      {/* Fund Comparison Modal */}
      {showComparison && selectedFunds.length > 0 && (
        <FundComparison
          funds={filteredFunds.filter(fund => selectedFunds.includes(fund.id))}
          onClose={() => setShowComparison(false)}
        />
      )}
    </div>
  );
}