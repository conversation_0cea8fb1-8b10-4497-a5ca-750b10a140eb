'use client';

import { useEffect, useState } from 'react';

import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { fetchPortfolios, clearError } from '@/store/slices/portfoliosSlice';

import PortfolioCreateModal from '../../components/portfolios/PortfolioCreateModal';
import PortfolioFilters from '../../components/portfolios/PortfolioFilters';
import PortfolioListTable from '../../components/portfolios/PortfolioListTable';

export default function PortfoliosPage() {
  const dispatch = useAppDispatch();
  const { filteredPortfolios, loading, error, lastUpdated } = useAppSelector(state => state.portfolios);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    // Fetch portfolios on component mount only
    dispatch(fetchPortfolios());
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(fetchPortfolios());
  };

  const handleCreatePortfolio = () => {
    window.location.href = '/portfolios/new';
  };

  const handleCloseCreateModal = () => {
    setShowCreateModal(false);
  };

  const handleCreateSuccess = () => {
    setShowCreateModal(false);
    // Refresh portfolios to show the new one
    dispatch(fetchPortfolios());
  };

  const handleClearError = () => {
    dispatch(clearError());
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Portfolio Manager</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Manage your investment portfolios with comprehensive tracking and analytics.
          </p>
          {lastUpdated && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Last updated: {new Date(lastUpdated).toLocaleString()}
            </p>
          )}
          {process.env.NEXT_PUBLIC_ENABLE_MOCK_MODE === 'true' && (
            <p className="text-sm text-blue-600 dark:text-blue-400 mt-1 font-medium">
              🎭 Mock Mode: Using simulated portfolio data for development
            </p>
          )}
        </div>
        <div className="flex space-x-3">
          <Button 
            variant="outline" 
            onClick={handleRefresh}
            loading={loading}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh Data'}
          </Button>
          <Button onClick={handleCreatePortfolio}>
            Create Portfolio
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <Card.Content>
            <div className="flex items-center">
              <div className="text-red-600 mr-3">⚠️</div>
              <div>
                <h3 className="text-sm font-medium text-red-800">Error Loading Portfolios</h3>
                <p className="text-sm text-red-600 mt-1">{error}</p>
              </div>
              <div className="ml-auto flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleClearError}
                >
                  Dismiss
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleRefresh}
                >
                  Retry
                </Button>
              </div>
            </div>
          </Card.Content>
        </Card>
      )}

      {/* Filters */}
      <PortfolioFilters />

      {/* Portfolio List Table */}
      <Card padding="sm">
        <Card.Header>
          <div className="flex justify-between items-center">
            <Card.Title>
              Your Portfolios ({filteredPortfolios.length} portfolios)
            </Card.Title>
            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              {loading && (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 dark:border-blue-400 mr-2"></div>
                  Loading...
                </div>
              )}
              <span>
                Total Value: ${filteredPortfolios.reduce((sum, p) => sum + p.totalValue, 0).toLocaleString()}
              </span>
            </div>
          </div>
        </Card.Header>
        <Card.Content className="p-0">
          <PortfolioListTable />
        </Card.Content>
      </Card>

      {/* Create Portfolio Modal */}
      {showCreateModal && (
        <PortfolioCreateModal
          isOpen={showCreateModal}
          onClose={handleCloseCreateModal}
          onSuccess={handleCreateSuccess}
        />
      )}
    </div>
  );
}
