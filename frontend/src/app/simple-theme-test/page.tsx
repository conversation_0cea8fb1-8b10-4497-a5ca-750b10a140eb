'use client';

import { useTheme } from '@/hooks/useTheme';

export default function SimpleThemeTest() {
  const { mode, isDark, isInitialized, setTheme, toggleTheme } = useTheme();

  if (!isInitialized) {
    return <div>Loading...</div>;
  }

  return (
    <div className="p-8 min-h-screen bg-white dark:bg-gray-900 transition-colors">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-gray-900 dark:text-white">
          Simple Theme Test
        </h1>
        
        <div className="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg mb-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
            Current State
          </h2>
          <div className="space-y-2 text-gray-700 dark:text-gray-300">
            <p>Mode: <strong>{mode}</strong></p>
            <p>Is Dark: <strong>{isDark ? 'Yes' : 'No'}</strong></p>
            <p>Initialized: <strong>{isInitialized ? 'Yes' : 'No'}</strong></p>
            <p>HTML has dark class: <strong>
              {typeof document !== 'undefined' && document.documentElement.classList.contains('dark') ? 'Yes' : 'No'}
            </strong></p>
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
            Theme Controls
          </h2>
          
          <div className="flex flex-wrap gap-4">
            <button
              onClick={toggleTheme}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600"
            >
              Toggle Theme
            </button>
            
            <button
              onClick={() => setTheme('light')}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            >
              Set Light
            </button>
            
            <button
              onClick={() => setTheme('dark')}
              className="px-4 py-2 bg-gray-800 text-white rounded hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-500"
            >
              Set Dark
            </button>
            
            <button
              onClick={() => setTheme('system')}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 dark:bg-purple-700 dark:hover:bg-purple-600"
            >
              Set System
            </button>
          </div>
        </div>

        <div className="mt-8 p-4 border border-gray-300 dark:border-gray-600 rounded-lg">
          <h3 className="text-lg font-semibold mb-2 text-gray-800 dark:text-gray-200">
            Visual Test Elements
          </h3>
          <div className="space-y-2">
            <p className="text-gray-600 dark:text-gray-400">
              This text should change color based on theme
            </p>
            <div className="w-full h-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded"></div>
            <div className="grid grid-cols-3 gap-2">
              <div className="h-12 bg-red-500 rounded"></div>
              <div className="h-12 bg-green-500 rounded"></div>
              <div className="h-12 bg-blue-500 rounded"></div>
            </div>
          </div>
        </div>

        <div className="mt-6 text-sm text-gray-500 dark:text-gray-400">
          <p>Open browser console to see debug logs when toggling theme.</p>
          <p className="mt-2">
            <strong>Debug:</strong> Check if you can see this text change color when toggling.
          </p>
          <div className="mt-2 p-2 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded">
            This should be red background in light mode, dark red in dark mode
          </div>
          <div className="mt-2 p-2 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded">
            This should be green background in light mode, dark green in dark mode
          </div>
        </div>
      </div>
    </div>
  );
}
