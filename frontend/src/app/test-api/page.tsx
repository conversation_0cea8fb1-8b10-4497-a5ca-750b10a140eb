'use client';

import { useState } from 'react';

export default function TestAPIPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testBasicFetch = async () => {
    setLoading(true);
    setResult('');
    
    try {
      console.log('🧪 Testing basic fetch to API Gateway...');
      
      const response = await fetch('https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev/funds', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });
      
      console.log('✅ Response received:', response);
      const text = await response.text();
      console.log('📄 Response text:', text);
      
      setResult(`Status: ${response.status}\nResponse: ${text}`);
      
    } catch (error) {
      console.error('❌ Fetch error:', error);
      setResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const testWithCORS = async () => {
    setLoading(true);
    setResult('');
    
    try {
      console.log('🧪 Testing CORS preflight...');
      
      const response = await fetch('https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev/funds', {
        method: 'OPTIONS',
        headers: {
          'Origin': 'http://localhost:3000',
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type,Authorization',
        },
      });
      
      console.log('✅ CORS Response received:', response);
      const text = await response.text();
      console.log('📄 CORS Response text:', text);
      
      setResult(`CORS Status: ${response.status}\nResponse: ${text}`);
      
    } catch (error) {
      console.error('❌ CORS error:', error);
      setResult(`CORS Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const testLocalAPI = async () => {
    setLoading(true);
    setResult('');
    
    try {
      console.log('🧪 Testing local API...');
      
      const response = await fetch('/api/test', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      console.log('✅ Local Response received:', response);
      const text = await response.text();
      console.log('📄 Local Response text:', text);
      
      setResult(`Local API Status: ${response.status}\nResponse: ${text}`);
      
    } catch (error) {
      console.error('❌ Local API error:', error);
      setResult(`Local API Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">API Connection Test</h1>
      
      <div className="space-y-4 mb-8">
        <button
          onClick={testBasicFetch}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test AWS API Gateway'}
        </button>
        
        <button
          onClick={testWithCORS}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50 ml-2"
        >
          {loading ? 'Testing...' : 'Test CORS Preflight'}
        </button>
        
        <button
          onClick={testLocalAPI}
          disabled={loading}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50 ml-2"
        >
          {loading ? 'Testing...' : 'Test Local API'}
        </button>
      </div>
      
      {result && (
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold mb-2">Result:</h2>
          <pre className="whitespace-pre-wrap text-sm">{result}</pre>
        </div>
      )}
      
      <div className="mt-8 text-sm text-gray-600">
        <p>This page tests different API endpoints to help debug connection issues.</p>
        <p>Check the browser console for detailed logs.</p>
      </div>
    </div>
  );
} 