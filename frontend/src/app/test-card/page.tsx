'use client';

import { <PERSON>, But<PERSON> } from '@/components/ui';

export default function TestCardPage() {
  return (
    <div className="container mx-auto p-8 space-y-6">
      <h1 className="text-3xl font-bold text-gray-900">Card Component Test</h1>

      {/* Test Card with Header and Title */}
      <Card>
        <Card.Header>
          <Card.Title>Test Card Title</Card.Title>
        </Card.Header>
        <Card.Content>
          <p>This is a test card to verify the styling is working correctly.</p>
          <p className="mt-2">The header should have proper spacing and the title should be styled.</p>
        </Card.Content>
      </Card>

      {/* Test Card with Header, Title, and Description */}
      <Card>
        <Card.Header>
          <Card.Title>Card with Description</Card.Title>
          <Card.Description>
            This card includes both a title and description in the header.
          </Card.Description>
        </Card.Header>
        <Card.Content>
          <p>Content goes here. This should be properly spaced from the header.</p>
        </Card.Content>
      </Card>

      {/* Test Card with different variants */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card shadow="sm">
          <Card.Header>
            <Card.Title>Small Shadow</Card.Title>
          </Card.Header>
          <Card.Content>
            <p>This card has a small shadow.</p>
          </Card.Content>
        </Card>

        <Card shadow="lg" hover>
          <Card.Header>
            <Card.Title>Large Shadow with Hover</Card.Title>
          </Card.Header>
          <Card.Content>
            <p>This card has a large shadow and hover effect.</p>
          </Card.Content>
        </Card>
      </div>

      {/* Test Card with Footer */}
      <Card>
        <Card.Header>
          <Card.Title>Card with Footer</Card.Title>
        </Card.Header>
        <Card.Content>
          <p>This card includes a footer section.</p>
        </Card.Content>
        <Card.Footer>
          <Button variant="primary" size="sm">Action</Button>
          <Button variant="outline" size="sm">Cancel</Button>
        </Card.Footer>
      </Card>

      {/* Debug: Raw HTML structure */}
      <div className="bg-gray-100 p-4 rounded">
        <h3 className="font-bold mb-2">Debug: Expected HTML Structure</h3>
        <pre className="text-sm">
{`<div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
  <div class="mb-4">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Title</h3>
  </div>
  <div>
    Content
  </div>
</div>`}
        </pre>
      </div>
    </div>
  );
}
