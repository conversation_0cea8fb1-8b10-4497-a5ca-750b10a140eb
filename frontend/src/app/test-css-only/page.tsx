'use client';

import '../test-css.css';

export default function TestCSSOnlyPage() {
  return (
    <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>
      <h1 style={{ fontSize: '2rem', fontWeight: 'bold', marginBottom: '2rem' }}>
        CSS-Only Test Page
      </h1>
      
      <div style={{ 
        backgroundColor: '#fef3c7', 
        border: '1px solid #f59e0b', 
        borderRadius: '0.5rem', 
        padding: '1rem', 
        marginBottom: '2rem' 
      }}>
        <h3 style={{ fontWeight: 'bold', color: '#92400e', marginBottom: '0.5rem' }}>
          CSS Compilation Test
        </h3>
        <p style={{ color: '#b45309', fontSize: '0.875rem' }}>
          This page tests if CSS files are being compiled and loaded properly.
        </p>
      </div>

      {/* Test Card using custom CSS */}
      <div className="test-card">
        <div className="test-card-header">
          <h3 className="test-card-title">Custom CSS Card</h3>
        </div>
        <div className="test-card-content">
          <p>This card uses custom CSS classes defined in test-css.css</p>
          <p>If you can see proper styling here, CSS compilation is working.</p>
        </div>
      </div>

      {/* Test Card using inline styles */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '0.5rem',
        padding: '1.5rem',
        boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        border: '1px solid #e5e7eb',
        marginBottom: '1rem'
      }}>
        <div style={{
          marginBottom: '1rem',
          paddingBottom: '0.5rem',
          borderBottom: '1px solid #f3f4f6'
        }}>
          <h3 style={{
            fontSize: '1.125rem',
            fontWeight: '600',
            color: '#111827',
            margin: '0'
          }}>
            Inline Styles Card
          </h3>
        </div>
        <div style={{ color: '#374151', lineHeight: '1.5' }}>
          <p>This card uses inline styles and should always work.</p>
          <p>Compare this with the custom CSS card above.</p>
        </div>
      </div>

      <button 
        onClick={() => document.documentElement.classList.toggle('dark')}
        style={{
          padding: '0.5rem 1rem',
          backgroundColor: '#2563eb',
          color: 'white',
          border: 'none',
          borderRadius: '0.25rem',
          cursor: 'pointer'
        }}
      >
        Toggle Dark Mode
      </button>
    </div>
  );
}
