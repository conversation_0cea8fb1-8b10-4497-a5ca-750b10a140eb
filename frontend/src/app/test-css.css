/* Test CSS file to verify CSS compilation */

.test-card {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  margin-bottom: 1rem;
}

.test-card-header {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f3f4f6;
}

.test-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.test-card-content {
  color: #374151;
  line-height: 1.5;
}

/* Dark mode styles */
.dark .test-card {
  background-color: #1f2937;
  border-color: #4b5563;
}

.dark .test-card-header {
  border-bottom-color: #374151;
}

.dark .test-card-title {
  color: #f3f4f6;
}

.dark .test-card-content {
  color: #d1d5db;
}
