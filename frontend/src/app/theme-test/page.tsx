'use client';

import ThemeDebug from '@/components/debug/ThemeDebug';
import { <PERSON>, Button, ThemeToggle } from '@/components/ui';
import { useTheme } from '@/hooks/useTheme';

export default function ThemeTestPage() {
  const { mode, isDark, isInitialized } = useTheme();

  if (!isInitialized) {
    return (
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 mx-auto mb-4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-48 mx-auto mb-6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          Dark Mode Test Page
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Current theme: <span className="font-semibold">{mode}</span>
          {' '}({isDark ? 'Dark' : 'Light'} mode active)
        </p>

        <div className="flex justify-center space-x-4 mb-8">
          <ThemeToggle variant="button" size="md" />
          <ThemeToggle variant="dropdown" size="md" />
        </div>

        {/* Debug info */}
        <div className="text-sm text-gray-500 dark:text-gray-400 mb-8">
          <p>HTML class: {typeof document !== 'undefined' ? (document.documentElement.classList.contains('dark') ? 'dark' : 'light') : 'unknown'}</p>
          <p>Initialized: {isInitialized ? 'Yes' : 'No'}</p>
        </div>
      </div>

      {/* Button Variants */}
      <Card>
        <Card.Header>
          <Card.Title>Button Variants</Card.Title>
          <Card.Description>
            Test all button variants in both light and dark modes
          </Card.Description>
        </Card.Header>
        <Card.Content>
          <div className="flex flex-wrap gap-4">
            <Button variant="primary">Primary</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="destructive">Destructive</Button>
          </div>
        </Card.Content>
      </Card>

      {/* Card Variants */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <Card.Header>
            <Card.Title>Default Card</Card.Title>
            <Card.Description>
              This is a default card with standard styling
            </Card.Description>
          </Card.Header>
          <Card.Content>
            <p className="text-gray-700 dark:text-gray-300">
              Card content goes here. This text should adapt to the current theme.
            </p>
          </Card.Content>
          <Card.Footer>
            <Button variant="primary" size="sm">Action</Button>
          </Card.Footer>
        </Card>

        <Card hover>
          <Card.Header>
            <Card.Title>Hover Card</Card.Title>
            <Card.Description>
              This card has hover effects enabled
            </Card.Description>
          </Card.Header>
          <Card.Content>
            <p className="text-gray-700 dark:text-gray-300">
              Hover over this card to see the shadow effect.
            </p>
          </Card.Content>
        </Card>
      </div>

      {/* Color Palette Test */}
      <Card>
        <Card.Header>
          <Card.Title>Color Palette</Card.Title>
          <Card.Description>
            Test the color system in both themes
          </Card.Description>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-4 bg-blue-600 text-white dark:bg-blue-700 rounded-md text-center">
              Primary
            </div>
            <div className="p-4 bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-100 rounded-md text-center">
              Secondary
            </div>
            <div className="p-4 bg-gray-50 text-gray-600 dark:bg-gray-900 dark:text-gray-400 rounded-md text-center">
              Muted
            </div>
            <div className="p-4 bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-100 rounded-md text-center">
              Accent
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* Form Elements */}
      <Card>
        <Card.Header>
          <Card.Title>Form Elements</Card.Title>
          <Card.Description>
            Test form elements in both themes
          </Card.Description>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Text Input
              </label>
              <input
                type="text"
                placeholder="Enter some text..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Select
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option>Option 1</option>
                <option>Option 2</option>
                <option>Option 3</option>
              </select>
            </div>
          </div>
        </Card.Content>
      </Card>

      <ThemeDebug />
    </div>
  );
}
