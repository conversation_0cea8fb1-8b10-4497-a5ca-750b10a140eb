'use client'

import { signIn, useSession } from 'next-auth/react'

import { Dropdown } from '@/components/ui'
import Button from '@/components/ui/Button'
import { performCompleteSignOut, performFreshSignIn } from '@/lib/auth-utils'
import { BaseComponentProps } from '@/types'

import MFAStatus from './MFAStatus'

interface AuthButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  showUserInfo?: boolean
  redirectTo?: string
}

const AuthButton: React.FC<AuthButtonProps> = ({ 
  className = '',
  variant = 'primary',
  size = 'md',
  showUserInfo = false,
  redirectTo = '/funds'
}) => {
  const { data: session, status } = useSession()

  if (status === 'loading') {
    return (
      <Button 
        variant={variant} 
        size={size} 
        className={className}
        loading
        disabled
        aria-label="Loading authentication status"
      >
        Loading...
      </Button>
    )
  }

  if (session) {
    if (showUserInfo && session.user) {
      return (
        <Dropdown
          className={className}
          trigger={
            <div className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100">
              {session.user.image && (
                <img
                  src={session.user.image}
                  alt={`Profile picture of ${session.user.name || session.user.email || 'User'}`}
                  className="w-8 h-8 rounded-full"
                />
              )}
              <span className="hidden sm:inline">
                {session.user.name || session.user.email}
              </span>
              <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          }
        >
          <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              {session.user.image && (
                <img
                  src={session.user.image}
                  alt={`Profile picture of ${session.user.name || session.user.email || 'User'}`}
                  className="w-8 h-8 rounded-full"
                />
              )}
              <div>
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {session.user.name || session.user.email}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {session.user.email}
                </div>
              </div>
            </div>
          </div>
          <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
            <MFAStatus showDetails={false} />
          </div>
          <Dropdown.Item
            onClick={() => performCompleteSignOut('/')}
          >
            Sign Out
          </Dropdown.Item>
        </Dropdown>
      )
    }
    
    return (
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => performCompleteSignOut('/')}
        aria-label={`Sign out ${session.user?.name || session.user?.email || 'current user'}`}
      >
        Sign Out
      </Button>
    )
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={() => performFreshSignIn(redirectTo)}
      aria-label="Sign in to access your account"
    >
      Sign In
    </Button>
  )
}

export default AuthButton 