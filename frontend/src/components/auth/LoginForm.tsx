'use client'

import { useState } from 'react'

import { signIn } from 'next-auth/react'

import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'
import { performFreshSignIn } from '@/lib/auth-utils'
import { BaseComponentProps } from '@/types'

interface LoginFormProps extends BaseComponentProps {
  callbackUrl?: string
  error?: string | null
}

const LoginForm: React.FC<LoginFormProps> = ({ 
  className = '',
  callbackUrl = '/funds',
  error = null
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [formError, setFormError] = useState<string | null>(error)


  const handleCognitoSignIn = async () => {
    setIsLoading(true)
    setFormError(null)

    try {
      await performFreshSignIn('cognito', callbackUrl)
    } catch (error) {
      console.error('Cognito sign in error:', error)
      setFormError('Authentication failed. Please try again.')
      setIsLoading(false)
    }
  }

  return (
    <div className={`w-full max-w-md mx-auto ${className}`}>
      <Card className="shadow-lg">
        <Card.Header className="text-center">
          <div className="mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 dark:from-blue-700 dark:to-blue-800 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
              <svg
                className="w-8 h-8 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Welcome to FundFlow
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
              Sign in to your account to continue
            </p>
          </div>
        </Card.Header>

        <Card.Content>
          {/* Error Display */}
          {formError && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <div className="flex items-center">
                <svg 
                  className="w-4 h-4 text-red-600 dark:text-red-400 mr-2" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
                  />
                </svg>
                <span className="text-sm text-red-600 dark:text-red-400">
                  {formError}
                </span>
              </div>
            </div>
          )}

          {/* Primary Cognito Sign In */}
          <div className="space-y-4">
            <Button
              fullWidth
              size="lg"
              loading={isLoading}
              onClick={handleCognitoSignIn}
              className="justify-center"
              aria-label="Sign in with AWS Cognito"
            >
              <svg 
                className="w-5 h-5 mr-2" 
                viewBox="0 0 24 24" 
                fill="currentColor"
                aria-hidden="true"
              >
                <path d="M13.5 2c-5.621 0-10.211 4.443-10.495 10h3.009c.275-3.264 2.997-5.824 6.486-5.824 3.59 0 6.5 2.91 6.5 6.5s-2.91 6.5-6.5 6.5c-1.832 0-3.49-.76-4.676-1.976L6.5 18.524c1.5 1.52 3.616 2.476 6 2.476 4.418 0 8-3.582 8-8s-3.582-8-8-8z"/>
              </svg>
              Sign in with Cognito
            </Button>


            {/* Additional Options */}
            <div className="mt-6">
              <div className="text-center">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Don't have an account?{' '}
                  <a
                    href="/auth/signup"
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                  >
                    Sign up here
                  </a>
                </p>
              </div>
            </div>
          </div>
        </Card.Content>

        <Card.Footer className="text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            By signing in, you agree to our{' '}
            <a
              href="/terms"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 underline"
            >
              Terms of Service
            </a>{' '}
            and{' '}
            <a
              href="/privacy"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 underline"
            >
              Privacy Policy
            </a>
          </p>
        </Card.Footer>
      </Card>
    </div>
  )
}

export default LoginForm
