'use client'

import { Session } from 'next-auth'
import { SessionProvider as NextAuthSessionProvider } from 'next-auth/react'

import { BaseComponentProps } from '@/types'

interface SessionProviderProps extends BaseComponentProps {
  session?: Session | null
}

const SessionProvider: React.FC<SessionProviderProps> = ({ 
  children, 
  session 
}) => {
  return (
    <NextAuthSessionProvider session={session}>
      {children}
    </NextAuthSessionProvider>
  )
}

export default SessionProvider 