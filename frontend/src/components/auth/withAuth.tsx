'use client'

import { useEffect, ComponentType } from 'react'

import { useRouter } from 'next/navigation'

import { useSession } from 'next-auth/react'

import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'

interface WithAuthOptions {
  redirectTo?: string
  loadingComponent?: ComponentType
  unauthorizedComponent?: ComponentType
}

export function withAuth<P extends object>(
  WrappedComponent: ComponentType<P>,
  options: WithAuthOptions = {}
) {
  const {
    redirectTo = '/auth/signin',
    loadingComponent: LoadingComponent,
    unauthorizedComponent: UnauthorizedComponent,
  } = options

  const AuthenticatedComponent = (props: P) => {
    const { data: session, status } = useSession()
    const router = useRouter()

    useEffect(() => {
      if (status === 'loading') {return} // Still loading
      
      if (!session) {
        const currentPath = window.location.pathname
        const signInUrl = `${redirectTo}?callbackUrl=${encodeURIComponent(currentPath)}`
        router.push(signInUrl)
      }
    }, [session, status, router])

    // Show loading state
    if (status === 'loading') {
      if (LoadingComponent) {
        return <LoadingComponent />
      }
      
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <Card className="w-full max-w-md">
            <Card.Content>
              <div className="flex items-center justify-center space-x-3" role="status" aria-live="polite">
                <div 
                  className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"
                  aria-hidden="true"
                ></div>
                <span className="text-gray-600">Loading authentication...</span>
              </div>
            </Card.Content>
          </Card>
        </div>
      )
    }

    // Show unauthorized state
    if (!session) {
      if (UnauthorizedComponent) {
        return <UnauthorizedComponent />
      }
      
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <Card className="w-full max-w-md">
            <Card.Header>
              <Card.Title>Authentication Required</Card.Title>
              <Card.Description>
                You need to sign in to access this page.
              </Card.Description>
            </Card.Header>
            <Card.Content>
              <Button 
                fullWidth 
                onClick={() => router.push(redirectTo)}
              >
                Sign In
              </Button>
            </Card.Content>
          </Card>
        </div>
      )
    }

    // User is authenticated, render the protected component
    return <WrappedComponent {...props} />
  }

  AuthenticatedComponent.displayName = `withAuth(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`

  return AuthenticatedComponent
}

export default withAuth 