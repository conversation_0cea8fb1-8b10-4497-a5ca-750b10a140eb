'use client';

import { useEffect, useState } from 'react';

import { useTheme } from '@/hooks/useTheme';

const ThemeDebug = () => {
  const { mode, isDark, isInitialized } = useTheme();
  const [htmlClass, setHtmlClass] = useState<string>('');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    const updateHtmlClass = () => {
      if (typeof document !== 'undefined') {
        setHtmlClass(document.documentElement.className);
      }
    };
    
    updateHtmlClass();
    
    // Watch for class changes
    const observer = new MutationObserver(updateHtmlClass);
    if (typeof document !== 'undefined') {
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['class']
      });
    }
    
    return () => observer.disconnect();
  }, []);

  if (!mounted) {
    return <div>Loading debug info...</div>;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg text-sm font-mono">
      <h3 className="font-bold mb-2 text-gray-900 dark:text-gray-100">Theme Debug</h3>
      <div className="space-y-1 text-gray-600 dark:text-gray-400">
        <div>Mode: <span className="text-blue-600 dark:text-blue-400">{mode}</span></div>
        <div>Is Dark: <span className="text-blue-600 dark:text-blue-400">{isDark ? 'true' : 'false'}</span></div>
        <div>Initialized: <span className="text-blue-600 dark:text-blue-400">{isInitialized ? 'true' : 'false'}</span></div>
        <div>HTML Classes: <span className="text-blue-600 dark:text-blue-400">{htmlClass || 'none'}</span></div>
        <div>System Prefers Dark: <span className="text-blue-600 dark:text-blue-400">
          {typeof window !== 'undefined' && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'true' : 'false'}
        </span></div>
        <div>LocalStorage: <span className="text-blue-600 dark:text-blue-400">
          {typeof window !== 'undefined' ? localStorage.getItem('theme') || 'null' : 'unavailable'}
        </span></div>
      </div>
    </div>
  );
};

export default ThemeDebug;
