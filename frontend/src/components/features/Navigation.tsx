'use client';

import { useState } from 'react';

import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';


import {
  Home,
  Dashboard,
  AccountBalance,
  Receipt,
  AccountBalanceWallet,
  Assessment,
  Settings,
  Menu,
  Close
} from '@mui/icons-material';

import AuthButton from '@/components/auth/AuthButton';
import { ThemeToggle } from '@/components/ui';
import LanguageSwitcher from '@/components/ui/LanguageSwitcher';
import { useTranslation, useLanguage } from '@/i18n/provider';
import { APP_NAME } from '@/lib/constants';

const Navigation = () => {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { t } = useTranslation();
  const { isLoading } = useLanguage();
  
  // Default nav items with fallback labels
  const defaultNavItems = [
    { href: '/', label: 'HOME', icon: Home },
    { href: '/dashboard', label: 'DASHBOARD', icon: Dashboard },
    { href: '/funds', label: 'FUNDS', icon: AccountBalance },
  ];
  
  // Use translated labels when available, fallback to defaults during loading
  const navItems = isLoading ? defaultNavItems : [
    { href: '/', label: t('navigation.home'), icon: Home },
    { href: '/dashboard', label: t('navigation.dashboard'), icon: Dashboard },
    { href: '/funds', label: t('navigation.funds'), icon: AccountBalance },
  ];
  
  return (
    <nav className="bg-white dark:bg-gray-900 shadow-lg border-b border-gray-200 dark:border-gray-700 transition-colors duration-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and Navigation Links */}
          <div className="flex items-center space-x-8">
            <Link href="/" className="flex items-center space-x-2">
              <Image
                src="/logo-transparent.png"
                alt="FundFlow Logo"
                width={32}
                height={32}
                className="w-8 h-8"
              />
              <span className="text-xl font-bold text-gray-900 dark:text-gray-100">{APP_NAME}</span>
            </Link>
            
            {/* Navigation Links */}
            <div className="hidden md:flex items-center space-x-6">
              {navItems.map((item) => {
                const isActive = pathname === item.href;
                const IconComponent = item.icon;
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                        : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800'
                    }`}
                  >
                    <IconComponent className="w-5 h-5" />
                    <span>{item.label}</span>
                  </Link>
                );
              })}
            </div>
          </div>
          
          {/* Authentication, Language Switcher and Theme Toggle */}
          <div className="hidden md:flex items-center space-x-3">
            <LanguageSwitcher size="sm" showText={false} />
            <ThemeToggle size="sm" />
            <AuthButton showUserInfo={true} size="sm" />
          </div>
          
          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-2">
            <LanguageSwitcher size="sm" showText={false} />
            <ThemeToggle size="sm" />
            <AuthButton size="sm" />
            <button
              type="button"
              className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:text-gray-900 dark:focus:text-gray-100 p-2"
              aria-label="Toggle mobile menu"
              aria-expanded={mobileMenuOpen}
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? (
                <Close className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>
      
      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navItems.map((item) => {
              const isActive = pathname === item.href;
              const IconComponent = item.icon;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-3 px-3 py-2 rounded-md text-base font-medium transition-colors ${
                    isActive
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <IconComponent className="w-5 h-5" />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navigation; 