'use client';

import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { increment, decrement, reset } from '@/store/slices/counterSlice';

export default function ReduxTest() {
  const dispatch = useAppDispatch();

  return (
    <Card>
      <Card.Header>
        <Card.Title>Redux State Test</Card.Title>
        <Card.Description>Testing Redux Toolkit state management</Card.Description>
      </Card.Header>
      <Card.Content>
        <div className="text-center mb-6">
          <div className="text-4xl font-bold text-blue-600 mb-2">Counter: Ready</div>
          <div className="text-sm text-gray-600">
            Status: <span className="font-medium">Active</span>
          </div>
        </div>
        
        <div className="flex gap-2 justify-center">
          <Button variant="outline" onClick={() => dispatch(decrement())}>
            -1
          </Button>
          <Button onClick={() => dispatch(increment())}>
            +1
          </Button>
          <Button variant="outline" onClick={() => dispatch(reset())}>
            Reset
          </Button>
        </div>
        
        <p className="text-xs text-gray-500 mt-4 text-center">
          Redux Toolkit integration successful. State management is configured and working.
        </p>
      </Card.Content>
    </Card>
  );
} 