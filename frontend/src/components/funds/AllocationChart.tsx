'use client';

import { useState, useEffect } from 'react';

import { DonutLarge } from '@mui/icons-material';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  ChartOptions,
} from 'chart.js';
import { Pie } from 'react-chartjs-2';

import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { FundDetails } from '@/types';

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

interface AllocationChartProps {
  assetAllocation: FundDetails['analytics']['assetAllocation'];
  geographicAllocation: FundDetails['analytics']['geographicAllocation'];
  sectorAllocation: FundDetails['analytics']['sectorAllocation'];
}

type AllocationView = 'asset' | 'geographic' | 'sector';

export default function AllocationChart({
  assetAllocation,
  geographicAllocation,
  sectorAllocation,
}: AllocationChartProps) {
  const [activeView, setActiveView] = useState<AllocationView>('asset');
  const [isDarkMode, setIsDarkMode] = useState(false);



  // Detect dark mode
  useEffect(() => {
    const checkDarkMode = () => {
      const isDark = document.documentElement.classList.contains('dark') ||
                    window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDarkMode(isDark);
    };

    checkDarkMode();

    // Listen for theme changes
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', checkDarkMode);

    return () => {
      observer.disconnect();
      mediaQuery.removeEventListener('change', checkDarkMode);
    };
  }, []);

  // Color palettes for different chart types
  const assetColors = ['#3B82F6', '#10B981', '#6B7280', '#8B5CF6'];
  const geographicColors = ['#6366F1', '#F59E0B', '#EF4444'];
  const sectorColors = [
    '#3B82F6', '#10B981', '#F59E0B', '#8B5CF6', '#EF4444',
    '#6366F1', '#EC4899', '#6B7280', '#84CC16', '#F97316'
  ];

  // Prepare data based on active view
  const getChartData = () => {
    switch (activeView) {
      case 'asset':
        return {
          labels: ['Stocks', 'Bonds', 'Cash', 'Other'],
          datasets: [{
            data: [
              assetAllocation.stocks,
              assetAllocation.bonds,
              assetAllocation.cash,
              assetAllocation.other,
            ],
            backgroundColor: assetColors,
            borderColor: assetColors.map(color => color),
            borderWidth: 2,
            hoverOffset: 8,
          }],
        };
      
      case 'geographic':
        const regions = Object.keys(geographicAllocation);
        const values = Object.values(geographicAllocation);
        // Expand color palette for dynamic regions
        const expandedGeographicColors = [
          '#6366F1', '#F59E0B', '#EF4444', '#10B981', '#8B5CF6', 
          '#EC4899', '#6B7280', '#84CC16', '#F97316', '#06B6D4'
        ];
        return {
          labels: regions,
          datasets: [{
            data: values,
            backgroundColor: expandedGeographicColors.slice(0, regions.length),
            borderColor: expandedGeographicColors.slice(0, regions.length),
            borderWidth: 2,
            hoverOffset: 8,
          }],
        };
      
      case 'sector':
        return {
          labels: sectorAllocation.map(sector => sector.name),
          datasets: [{
            data: sectorAllocation.map(sector => sector.percentage),
            backgroundColor: sectorColors.slice(0, sectorAllocation.length),
            borderColor: sectorColors.slice(0, sectorAllocation.length),
            borderWidth: 2,
            hoverOffset: 8,
          }],
        };
      
      default:
        return { labels: [], datasets: [] };
    }
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000000) {
      return `$${(amount / 1000000000).toFixed(1)}B`;
    } else if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(1)}K`;
    }
    return `$${amount.toLocaleString()}`;
  };

  const options: ChartOptions<'pie'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          color: isDarkMode ? '#ffffff' : '#374151', // white for dark mode, gray-700 for light mode
          font: {
            size: 12,
            family: 'Inter, system-ui, sans-serif',
          },
          generateLabels: (chart) => {
             const data = chart.data;
             if (data.labels?.length && data.datasets.length) {
               return data.labels.map((label, i) => {
                 const dataset = data.datasets[0];
                 const value = dataset.data[i] as number;
                 const bgColors = dataset.backgroundColor as string[];
                 const borderColors = dataset.borderColor as string[];
                 return {
                   text: `${label}: ${value.toFixed(1)}%`,
                   fillStyle: bgColors[i],
                   strokeStyle: borderColors[i],
                   lineWidth: 2,
                   hidden: false,
                   index: i,
                   fontColor: isDarkMode ? '#ffffff' : '#374151', // Explicit font color for each label
                 };
               });
             }
             return [];
           },
        },
      },
      tooltip: {
        backgroundColor: isDarkMode ? 'rgba(31, 41, 55, 0.95)' : 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: isDarkMode ? 'rgba(75, 85, 99, 0.3)' : 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.parsed;
            
            // Add market value for sector allocation
            if (activeView === 'sector' && context.dataIndex < sectorAllocation.length) {
              const sector = sectorAllocation[context.dataIndex];
              return `${label}: ${value.toFixed(1)}% (${formatCurrency(sector.marketValue)})`;
            }
            
            return `${label}: ${value.toFixed(1)}%`;
          },
        },
      },
    },
    interaction: {
      intersect: false,
    },
  };

  const chartData = getChartData();

  const viewButtons = [
    { key: 'asset' as AllocationView, label: 'Asset Allocation' },
    { key: 'geographic' as AllocationView, label: 'Geographic' },
    { key: 'sector' as AllocationView, label: 'Sector' },
  ];

  return (
    <Card>
      <Card.Header>
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex items-center space-x-2">
            <DonutLarge className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
            <Card.Title>Fund Allocation Analysis</Card.Title>
          </div>
          
          <div 
            className="flex flex-wrap gap-1 justify-center lg:justify-end"
            role="tablist"
            aria-label="Select allocation view"
          >
            {viewButtons.map((button) => (
              <Button
                key={button.key}
                variant={activeView === button.key ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setActiveView(button.key)}
                role="tab"
                aria-selected={activeView === button.key}
                aria-controls={`${button.key}-allocation-content`}
                aria-label={`View ${button.label.toLowerCase()} allocation data`}
                className="min-w-[5rem] transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                {button.label}
              </Button>
            ))}
          </div>
        </div>
      </Card.Header>
      
      <Card.Content>
        <div 
          className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8"
          id={`${activeView}-allocation-content`}
          role="tabpanel"
          aria-labelledby={`${activeView}-allocation-tab`}
        >
          {/* Chart */}
          <div className="order-2 xl:order-1">
            <div 
              className="h-64 sm:h-80 lg:h-96"
              role="img"
              aria-label={`${
                activeView === 'asset' ? 'Asset allocation' : 
                activeView === 'geographic' ? 'Geographic allocation' : 
                'Sector allocation'
              } pie chart`}
            >
              <Pie key={`pie-chart-${isDarkMode ? 'dark' : 'light'}`} data={chartData} options={options} />
            </div>
          </div>
          
          {/* Detailed Breakdown */}
          <div className="order-1 xl:order-2 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2">
              {activeView === 'asset' && 'Asset Breakdown'}
              {activeView === 'geographic' && 'Geographic Breakdown'}
              {activeView === 'sector' && 'Sector Breakdown'}
            </h3>
            
            <div className="space-y-2" role="list" aria-label={`${
              activeView === 'asset' ? 'Asset allocation' :
              activeView === 'geographic' ? 'Geographic allocation' :
              'Sector allocation'
            } breakdown`}>
              {activeView === 'asset' && Object.entries(assetAllocation).map(([key, value]) => (
                <div
                  key={key}
                  className="flex justify-between items-center py-3 px-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                  role="listitem"
                >
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">{key}</span>
                  <span
                    className="font-semibold text-gray-900 dark:text-gray-100"
                    aria-label={`${key}: ${value.toFixed(1)} percent`}
                  >
                    {value.toFixed(1)}%
                  </span>
                </div>
              ))}

              {activeView === 'geographic' && Object.entries(geographicAllocation).map(([key, value]) => (
                <div
                  key={key}
                  className="flex justify-between items-center py-3 px-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                  role="listitem"
                >
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {key}
                  </span>
                  <span
                    className="font-semibold text-gray-900 dark:text-gray-100"
                    aria-label={`${key}: ${value.toFixed(1)} percent`}
                  >
                    {value.toFixed(1)}%
                  </span>
                </div>
              ))}

              {activeView === 'sector' && sectorAllocation.map((sector) => (
                <div
                  key={sector.name}
                  className="flex justify-between items-center py-3 px-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                  role="listitem"
                >
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{sector.name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">{formatCurrency(sector.marketValue)}</div>
                  </div>
                  <div className="text-right ml-4">
                    <div
                      className="font-semibold text-gray-900 dark:text-gray-100"
                      aria-label={`${sector.name}: ${sector.percentage.toFixed(1)} percent allocation`}
                    >
                      {sector.percentage.toFixed(1)}%
                    </div>
                    <div
                      className={`text-xs font-medium mt-1 ${sector.change >= 0 ? 'text-green-600' : 'text-red-600'}`}
                      aria-label={`Change: ${sector.change >= 0 ? 'positive' : 'negative'} ${Math.abs(sector.change).toFixed(1)} percent`}
                    >
                      {sector.change >= 0 ? '+' : ''}{sector.change.toFixed(1)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Card.Content>
    </Card>
  );
} 