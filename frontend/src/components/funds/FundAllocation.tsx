import {
  PieChart,
  Public,
  Business,
  Star
} from '@mui/icons-material';

import Card from '@/components/ui/Card';
import { FundDetails } from '@/types';

interface FundAllocationProps {
  assetAllocation: FundDetails['analytics']['assetAllocation'];
  geographicAllocation: FundDetails['analytics']['geographicAllocation'];
  sectorAllocation: FundDetails['analytics']['sectorAllocation'];
  topHoldings: FundDetails['analytics']['topHoldings'];
}

export default function FundAllocation({
  assetAllocation,
  geographicAllocation,
  sectorAllocation,
  topHoldings,
}: FundAllocationProps) {
  const formatCurrency = (amount: number) => {
    if (amount >= 1000000000) {
      return `$${(amount / 1000000000).toFixed(1)}B`;
    } else if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(1)}K`;
    }
    return `$${amount.toLocaleString()}`;
  };

  // Simple progress bar component
  const ProgressBar = ({ percentage, color = 'bg-blue-500' }: { percentage: number; color?: string }) => (
    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 transition-colors">
      <div
        className={`h-2 rounded-full ${color} transition-all duration-300`}
        style={{ width: `${Math.min(percentage, 100)}%` }}
      />
    </div>
  );

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Asset Allocation */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <PieChart className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <Card.Title>Asset Allocation</Card.Title>
          </div>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-700 dark:text-gray-300">Stocks</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">{assetAllocation.stocks}%</span>
            </div>
            <ProgressBar percentage={assetAllocation.stocks} color="bg-blue-500" />

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-700 dark:text-gray-300">Bonds</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">{assetAllocation.bonds}%</span>
            </div>
            <ProgressBar percentage={assetAllocation.bonds} color="bg-green-500" />

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-700 dark:text-gray-300">Cash</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">{assetAllocation.cash}%</span>
            </div>
            <ProgressBar percentage={assetAllocation.cash} color="bg-gray-500" />

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-700 dark:text-gray-300">Other</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">{assetAllocation.other}%</span>
            </div>
            <ProgressBar percentage={assetAllocation.other} color="bg-purple-500" />
          </div>
        </Card.Content>
      </Card>

      {/* Geographic Allocation */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <Public className="w-5 h-5 text-green-600 dark:text-green-400" />
            <Card.Title>Geographic Allocation</Card.Title>
          </div>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            {Object.entries(geographicAllocation).map(([region, percentage], index) => {
              // Define colors for different regions
              const colors = [
                'bg-indigo-500',
                'bg-orange-500', 
                'bg-red-500',
                'bg-green-500',
                'bg-purple-500',
                'bg-blue-500',
                'bg-yellow-500',
                'bg-pink-500',
              ];
              const color = colors[index % colors.length];
              
              return (
                <div key={region} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-700 dark:text-gray-300">{region}</span>
                    <span className="font-medium text-gray-900 dark:text-gray-100">{percentage.toFixed(1)}%</span>
                  </div>
                  <ProgressBar percentage={percentage} color={color} />
                </div>
              );
            })}
          </div>
        </Card.Content>
      </Card>

      {/* Top Holdings */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <Star className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            <Card.Title>Top Holdings</Card.Title>
          </div>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            {topHoldings.map((holding, index) => (
              <div key={holding.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center text-blue-800 dark:text-blue-200 text-sm font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">{holding.name}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">{holding.symbol} • {holding.sector}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-gray-900 dark:text-gray-100">{holding.percentage}%</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">{formatCurrency(holding.marketValue)}</div>
                </div>
              </div>
            ))}
          </div>
        </Card.Content>
      </Card>

      {/* Sector Allocation */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <Business className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            <Card.Title>Sector Allocation</Card.Title>
          </div>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            {sectorAllocation.map((sector, index) => {
              const colors = [
                'bg-blue-500',
                'bg-green-500',
                'bg-yellow-500',
                'bg-purple-500',
                'bg-red-500',
                'bg-indigo-500',
                'bg-pink-500',
                'bg-gray-500',
              ];
              const color = colors[index % colors.length];
              
              return (
                <div key={sector.name} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-700 dark:text-gray-300">{sector.name}</span>
                    <div className="text-right">
                      <span className="font-medium text-gray-900 dark:text-gray-100">{sector.percentage}%</span>
                      <div className={`text-xs ${sector.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {sector.change >= 0 ? '+' : ''}{sector.change.toFixed(1)}%
                      </div>
                    </div>
                  </div>
                  <ProgressBar percentage={sector.percentage} color={color} />
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Market Value: {formatCurrency(sector.marketValue)}
                  </div>
                </div>
              );
            })}
          </div>
        </Card.Content>
      </Card>
    </div>
  );
} 