import {
  Info,
  Timeline,
  AccountBalance,
  TrendingUp
} from '@mui/icons-material';

import { Card } from '@/components/ui';
import { FundDetails } from '@/types';

interface FundBasicInfoProps {
  fund: FundDetails;
}

export default function FundBasicInfo({ fund }: FundBasicInfoProps) {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    if (amount >= **********) {
      return `$${(amount / **********).toFixed(1)}B`;
    } else if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(1)}K`;
    }
    return `$${amount.toLocaleString()}`;
  };

  const riskLevelColor = {
    low: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
    medium: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
    high: 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
  };

  const fundTypeFormatted = fund.type.replace(/_/g, ' ').toUpperCase();

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Fund Details */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <Info className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <Card.Title>Fund Details</Card.Title>
          </div>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {fund.fundManager && fund.fundManager.trim() && (
                <div>
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Fund Manager</div>
                  <div className="text-gray-900 dark:text-gray-100">{fund.fundManager}</div>
                </div>
              )}
              <div>
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Inception Date</div>
                <div className="text-gray-900 dark:text-gray-100">{formatDate(fund.inceptionDate)}</div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Fund Type</div>
                <div className="text-gray-900 dark:text-gray-100">{fundTypeFormatted}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Category</div>
                <div className="text-gray-900 dark:text-gray-100">
                  {fund.category}
                  {fund.subCategory && (
                    <span className="text-gray-600 dark:text-gray-400"> • {fund.subCategory}</span>
                  )}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Risk Level</div>
                <div>
                  <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full transition-colors ${riskLevelColor[fund.riskLevel]}`}>
                    {fund.riskLevel.toUpperCase()}
                  </span>
                </div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Min Investment</div>
                <div className="text-gray-900 dark:text-gray-100">{formatCurrency(fund.minimumInvestment)}</div>
              </div>
            </div>

            <div>
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</div>
              <div className="text-gray-900 dark:text-gray-100 text-sm leading-relaxed">{fund.description}</div>
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* Performance History */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-400" />
            <Card.Title>Performance History</Card.Title>
          </div>
        </Card.Header>
        <Card.Content>
          <div className="space-y-3">
            {Object.entries(fund.performance).map(([period, value]) => {
              const periodLabel = period.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
              
              return (
                <div key={period} className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                  <div className="text-sm text-gray-700 dark:text-gray-300">{periodLabel}</div>
                  <div className={`font-medium ${value >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {value >= 0 ? '+' : ''}{value.toFixed(2)}%
                  </div>
                </div>
              );
            })}
          </div>
        </Card.Content>
      </Card>

      {/* Fund Documents */}
      {fund.documents && fund.documents.length > 0 && (
        <Card className="lg:col-span-2">
          <Card.Header>
            <Card.Title>Fund Documents</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {fund.documents.map((doc) => (
                <div key={doc.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md dark:hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-2">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{doc.name}</div>
                    <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded transition-colors">
                      {doc.type.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-3">
                    Updated: {formatDate(doc.uploadDate)}
                  </div>
                  <button className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors">
                    Download PDF
                  </button>
                </div>
              ))}
            </div>
          </Card.Content>
        </Card>
      )}
    </div>
  );
} 