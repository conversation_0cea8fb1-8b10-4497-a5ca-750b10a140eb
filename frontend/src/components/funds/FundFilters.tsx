'use client';

import { useState, useCallback, useEffect } from 'react';

import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setFilter, resetFilter } from '@/store/slices/fundsSlice';
import { debounce } from '@/utils';


export default function FundFilters() {
  const dispatch = useAppDispatch();
  const { filter, funds } = useAppSelector(state => state.funds);
  
  // Local state for immediate UI updates
  const [localSearch, setLocalSearch] = useState(filter.search);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Debounced search to avoid excessive Redux updates
  const debouncedSearch = useCallback(
    debounce((...args: unknown[]) => {
      const searchTerm = args[0] as string;
      dispatch(setFilter({ search: searchTerm }));
    }, 300),
    [dispatch]
  );

  // Update local search when Redux filter changes
  useEffect(() => {
    setLocalSearch(filter.search);
  }, [filter.search]);

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalSearch(value);
    debouncedSearch(value);
  };

  // Handle filter changes
  const handleFilterChange = (key: string, value: any) => {
    dispatch(setFilter({ [key]: value }));
  };

  // Reset all filters
  const handleResetFilters = () => {
    setLocalSearch('');
    dispatch(resetFilter());
  };

  // Get unique values for filter options
  const getUniqueStringValues = (key: 'type' | 'category' | 'riskLevel') => {
    return Array.from(new Set(funds.map(fund => fund[key]).filter(Boolean))) as string[];
  };

  const fundTypes = getUniqueStringValues('type');
  const categories = getUniqueStringValues('category');
  const riskLevels = getUniqueStringValues('riskLevel');

  // Check if any filters are active
  const hasActiveFilters = 
    filter.search ||
    filter.type ||
    filter.category ||
    filter.riskLevel ||
    filter.minInvestment !== null ||
    filter.maxInvestment !== null ||
    filter.minRating !== null;

  return (
    <Card>
      <Card.Content>
        <div className="space-y-4">
          {/* Search and Quick Filters Row */}
          <div className="flex flex-wrap gap-4 items-center">
            {/* Search Bar */}
            <div className="flex-1 min-w-80">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-400 dark:text-gray-500">🔍</span>
                </div>
                <input
                  type="text"
                  placeholder="Search funds by name, symbol, category, or manager..."
                  value={localSearch}
                  onChange={handleSearchChange}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {localSearch && (
                  <button
                    onClick={() => {
                      setLocalSearch('');
                      dispatch(setFilter({ search: '' }));
                    }}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    ✕
                  </button>
                )}
              </div>
            </div>

            {/* Quick Filters */}
            <select
              value={filter.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Types</option>
              {fundTypes.map((type) => (
                <option key={type} value={type}>
                  {type?.replace('_', ' ').toUpperCase() || ''}
                </option>
              ))}
            </select>

            <select
              value={filter.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Categories</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            <select
              value={filter.riskLevel}
              onChange={(e) => handleFilterChange('riskLevel', e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Risk Levels</option>
              {riskLevels.map((risk) => (
                <option key={risk} value={risk}>
                  {risk?.toUpperCase() || ''} Risk
                </option>
              ))}
            </select>

            {/* Advanced Filters Toggle */}
            <Button
              variant="outline"
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className="flex items-center"
            >
              Advanced {showAdvancedFilters ? '▼' : '▶'}
            </Button>

            {/* Reset Button */}
            {hasActiveFilters && (
              <Button
                variant="outline"
                onClick={handleResetFilters}
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                Reset All
              </Button>
            )}
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Investment Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Minimum Investment Range
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      placeholder="Min"
                      value={filter.minInvestment || ''}
                      onChange={(e) => handleFilterChange('minInvestment', e.target.value ? Number(e.target.value) : null)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <input
                      type="number"
                      placeholder="Max"
                      value={filter.maxInvestment || ''}
                      onChange={(e) => handleFilterChange('maxInvestment', e.target.value ? Number(e.target.value) : null)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                {/* Minimum Rating */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Minimum Rating
                  </label>
                  <select
                    value={filter.minRating || ''}
                    onChange={(e) => handleFilterChange('minRating', e.target.value ? Number(e.target.value) : null)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Any Rating</option>
                    <option value="1">⭐ 1+ Stars</option>
                    <option value="2">⭐⭐ 2+ Stars</option>
                    <option value="3">⭐⭐⭐ 3+ Stars</option>
                    <option value="4">⭐⭐⭐⭐ 4+ Stars</option>
                    <option value="5">⭐⭐⭐⭐⭐ 5 Stars</option>
                  </select>
                </div>

                {/* Sort Options */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Sort By
                  </label>
                  <div className="flex space-x-2">
                    <select
                      value={filter.sortBy}
                      onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="name">Name</option>
                      <option value="nav">NAV</option>
                      <option value="change">Change</option>
                      <option value="volume">Volume</option>
                      <option value="aum">AUM</option>
                      <option value="expenseRatio">Expense Ratio</option>
                      <option value="rating">Rating</option>
                    </select>
                    <select
                      value={filter.sortOrder}
                      onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="asc">↑ Asc</option>
                      <option value="desc">↓ Desc</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Active Filters Summary */}
          {hasActiveFilters && (
            <div className="flex flex-wrap gap-2 pt-2 border-t border-gray-100 dark:border-gray-700">
              <span className="text-sm text-gray-500 dark:text-gray-400">Active filters:</span>
              {filter.search && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                  Search: "{filter.search}"
                  <button
                    onClick={() => handleFilterChange('search', '')}
                    className="ml-1 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100"
                  >
                    ×
                  </button>
                </span>
              )}
              {filter.type && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                  Type: {filter.type.replace('_', ' ').toUpperCase()}
                  <button
                    onClick={() => handleFilterChange('type', '')}
                    className="ml-1 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100"
                  >
                    ×
                  </button>
                </span>
              )}
              {filter.category && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                  Category: {filter.category}
                  <button
                    onClick={() => handleFilterChange('category', '')}
                    className="ml-1 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100"
                  >
                    ×
                  </button>
                </span>
              )}
              {filter.riskLevel && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                  Risk: {filter.riskLevel.toUpperCase()}
                  <button
                    onClick={() => handleFilterChange('riskLevel', '')}
                    className="ml-1 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100"
                  >
                    ×
                  </button>
                </span>
              )}
              {filter.minRating && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                  Min Rating: {filter.minRating}★
                  <button
                    onClick={() => handleFilterChange('minRating', null)}
                    className="ml-1 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100"
                  >
                    ×
                  </button>
                </span>
              )}
            </div>
          )}
        </div>
      </Card.Content>
    </Card>
  );
} 