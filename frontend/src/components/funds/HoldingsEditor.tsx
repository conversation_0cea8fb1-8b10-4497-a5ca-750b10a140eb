'use client';

import { useState, useEffect } from 'react';

import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import MonthSelector from '@/components/ui/MonthSelector';
import { Fund, FundSnapshotCreate } from '@/types';
import { fundApi } from '@/lib/api';

interface HoldingItem {
  name: string;
  symbol: string;
  percentage: number;
  shares?: number;
  marketValue?: number;
  sector?: string;
}

interface HoldingsData {
  topHoldings: HoldingItem[];
  sectorAllocation: Record<string, number>;
  geographicAllocation: Record<string, number>;
  assetAllocation: Record<string, number>;
  marketCapAllocation: Record<string, number>;
  currencyAllocation: Record<string, number>;
  totalHoldingsCount?: number;
  holdingsConcentration?: number;
}

interface HoldingsEditorProps {
  fund: Fund;
  onSubmit: (data: HoldingsData, month: string) => Promise<void>;
  onCancel: () => void;
  disabled?: boolean;
  selectedMonth?: string;
  onMonthChange?: (month: string) => void;
  availableMonths?: string[];
}

const DEFAULT_SECTORS = [
  'Technology', 'Finance', 'Healthcare', 'Consumer', 'Industrial', 
  'Energy', 'Materials', 'Utilities', 'Real Estate', 'Communication'
];

const DEFAULT_REGIONS = [
  'North America', 'Europe', 'Asia Pacific', 'India', 'China', 
  'Japan', 'Latin America', 'Middle East', 'Africa', 'Others'
];

const DEFAULT_ASSETS = [
  'Equity', 'Debt', 'Cash', 'Commodities', 'Real Estate', 'Others'
];

const DEFAULT_MARKET_CAPS = [
  'Large Cap', 'Mid Cap', 'Small Cap', 'Micro Cap'
];

const DEFAULT_CURRENCIES = [
  'USD', 'EUR', 'GBP', 'JPY', 'INR', 'CNY', 'AUD', 'CAD', 'Others'
];

export default function HoldingsEditor({
  fund,
  onSubmit,
  onCancel,
  disabled = false,
  selectedMonth,
  onMonthChange,
  availableMonths = []
}: HoldingsEditorProps) {
  // Get current month as default
  const getCurrentMonth = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  };

  const [currentMonth, setCurrentMonth] = useState<string>(selectedMonth || getCurrentMonth());
  const [topHoldings, setTopHoldings] = useState<HoldingItem[]>([]);
  const [sectorAllocation, setSectorAllocation] = useState<Record<string, number>>({});
  const [geographicAllocation, setGeographicAllocation] = useState<Record<string, number>>({});
  const [assetAllocation, setAssetAllocation] = useState<Record<string, number>>({});
  const [marketCapAllocation, setMarketCapAllocation] = useState<Record<string, number>>({});
  const [currencyAllocation, setCurrencyAllocation] = useState<Record<string, number>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);

  // Handle month change
  const handleMonthChange = (month: string) => {
    setCurrentMonth(month);
    if (onMonthChange) {
      onMonthChange(month);
    }
    // Load data for the selected month
    loadSnapshotData(month);
  };

  // Load snapshot data for selected month
  const loadSnapshotData = async (month: string) => {
    setLoading(true);
    setDataLoaded(false);

    try {
      // Try to get snapshot data for the selected month
      const snapshotResponse = await fundApi.getFundSnapshot(fund.id, month);

      if (snapshotResponse.success && snapshotResponse.data.holdings) {
        const holdings = snapshotResponse.data.holdings;

        // Load top holdings
        if (holdings.top_holdings) {
          setTopHoldings(holdings.top_holdings.map(h => ({
            name: h.name || '',
            symbol: h.symbol || '',
            percentage: h.percentage || 0,
            shares: h.shares,
            marketValue: h.market_value,
            sector: h.sector
          })));
        } else {
          setTopHoldings([]);
        }

        // Load allocations
        setSectorAllocation(holdings.sector_allocation || {});
        setGeographicAllocation(holdings.geographic_allocation || {});
        setAssetAllocation(holdings.asset_allocation || {});
        setMarketCapAllocation(holdings.market_cap_allocation || {});
        setCurrencyAllocation(holdings.currency_allocation || {});

        setDataLoaded(true);
      } else {
        // No snapshot data for this month, load from fund defaults
        loadDefaultData();
      }
    } catch (error) {
      console.warn('No snapshot data found for month:', month, 'Loading default data');
      // Load default data from fund
      loadDefaultData();
    } finally {
      setLoading(false);
    }
  };

  // Load default data from fund
  const loadDefaultData = () => {
    if (fund.holdings) {
      // Top Holdings
      if (fund.holdings.topHoldings) {
        setTopHoldings(fund.holdings.topHoldings.map(h => ({
          name: h.name || '',
          symbol: h.symbol || '',
          percentage: typeof h.percentage === 'string' ? parseFloat(h.percentage) : h.percentage || 0,
          shares: h.shares,
          marketValue: typeof h.marketValue === 'string' ? parseFloat(h.marketValue) : h.marketValue || 0,
          sector: h.sector
        })));
      } else {
        setTopHoldings([]);
      }

      // Allocations
      const parseAllocation = (allocation: any): Record<string, number> => {
        const result: Record<string, number> = {};
        if (allocation) {
          Object.entries(allocation).forEach(([key, value]) => {
            result[key] = typeof value === 'string' ? parseFloat(value) : (value as number) || 0;
          });
        }
        return result;
      };

      setSectorAllocation(parseAllocation(fund.holdings.sectorAllocation));
      setGeographicAllocation(parseAllocation(fund.holdings.geographicAllocation));
      setAssetAllocation(parseAllocation(fund.holdings.assetAllocation));
      setMarketCapAllocation(parseAllocation(fund.holdings.marketCapAllocation));
      setCurrencyAllocation(parseAllocation(fund.holdings.currencyAllocation));
    } else {
      // Initialize with empty data
      setTopHoldings([]);
      setSectorAllocation({});
      setGeographicAllocation({});
      setAssetAllocation({});
      setMarketCapAllocation({});
      setCurrencyAllocation({});
    }
    setDataLoaded(true);
  };

  // Initialize with data for current month
  useEffect(() => {
    loadSnapshotData(currentMonth);
  }, [fund.id]);

  // Update current month when selectedMonth prop changes
  useEffect(() => {
    if (selectedMonth && selectedMonth !== currentMonth) {
      setCurrentMonth(selectedMonth);
      loadSnapshotData(selectedMonth);
    }
  }, [selectedMonth]);

  const addHolding = () => {
    setTopHoldings([...topHoldings, {
      name: '',
      symbol: '',
      percentage: 0,
      shares: 0,
      marketValue: 0,
      sector: DEFAULT_SECTORS[0]
    }]);
  };

  const removeHolding = (index: number) => {
    setTopHoldings(topHoldings.filter((_, i) => i !== index));
  };

  const updateHolding = (index: number, field: keyof HoldingItem, value: any) => {
    const newHoldings = [...topHoldings];
    newHoldings[index] = {
      ...newHoldings[index],
      [field]: value
    };
    setTopHoldings(newHoldings);
  };

  const updateAllocation = (
    type: 'sector' | 'geographic' | 'asset' | 'marketCap' | 'currency',
    key: string,
    value: number
  ) => {
    switch (type) {
      case 'sector':
        setSectorAllocation({ ...sectorAllocation, [key]: value });
        break;
      case 'geographic':
        setGeographicAllocation({ ...geographicAllocation, [key]: value });
        break;
      case 'asset':
        setAssetAllocation({ ...assetAllocation, [key]: value });
        break;
      case 'marketCap':
        setMarketCapAllocation({ ...marketCapAllocation, [key]: value });
        break;
      case 'currency':
        setCurrencyAllocation({ ...currencyAllocation, [key]: value });
        break;
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate top holdings
    const totalHoldingsPercentage = topHoldings.reduce((sum, h) => sum + h.percentage, 0);
    if (totalHoldingsPercentage > 100) {
      newErrors.topHoldings = 'Total holdings percentage cannot exceed 100%';
    }

    // Validate allocations sum to ~100%
    const validateAllocation = (allocation: Record<string, number>, name: string) => {
      const total = Object.values(allocation).reduce((sum, val) => sum + val, 0);
      if (total > 0 && (total < 95 || total > 105)) {
        newErrors[name] = `${name} allocation should sum to approximately 100% (currently ${total.toFixed(1)}%)`;
      }
    };

    validateAllocation(sectorAllocation, 'Sector');
    validateAllocation(geographicAllocation, 'Geographic');
    validateAllocation(assetAllocation, 'Asset');
    validateAllocation(marketCapAllocation, 'Market Cap');
    validateAllocation(currencyAllocation, 'Currency');

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Calculate holdings concentration (top 10)
      const top10Holdings = topHoldings.slice(0, 10);
      const holdingsConcentration = top10Holdings.reduce((sum, h) => sum + h.percentage, 0);

      const holdingsData: HoldingsData = {
        topHoldings: topHoldings.filter(h => h.name && h.percentage > 0),
        sectorAllocation,
        geographicAllocation,
        assetAllocation,
        marketCapAllocation,
        currencyAllocation,
        totalHoldingsCount: topHoldings.filter(h => h.name).length,
        holdingsConcentration
      };

      // Submit with current month
      await onSubmit(holdingsData, currentMonth);

      // Update available months if needed
      if (onMonthChange && !availableMonths.includes(currentMonth)) {
        onMonthChange(currentMonth);
      }
    } catch (error) {
      console.error('Error submitting holdings data:', error);
      setErrors({ submit: 'Failed to submit holdings data. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Month Selection */}
      <Card>
        <Card.Header>
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">Monthly Holdings Data</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Select a month to view or edit holdings data for {fund.name}
          </p>
        </Card.Header>
        <Card.Content>
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <MonthSelector
              selectedMonth={currentMonth}
              onMonthChange={handleMonthChange}
              availableMonths={availableMonths}
              label="Select Month for Holdings Data *"
              className="max-w-md"
              disabled={loading}
            />
            {loading && (
              <p className="mt-2 text-sm text-blue-600 dark:text-blue-400">
                Loading data for {currentMonth}...
              </p>
            )}
            {dataLoaded && !loading && (
              <p className="mt-2 text-sm text-green-600 dark:text-green-400">
                {availableMonths.includes(currentMonth)
                  ? `Loaded existing data for ${currentMonth}`
                  : `No existing data for ${currentMonth} - showing defaults`}
              </p>
            )}
          </div>
          {errors.submit && (
            <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.submit}</p>
          )}
        </Card.Content>
      </Card>

      {/* Top Holdings */}
      <Card>
        <Card.Header>
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">Top Holdings</h2>
            <Button type="button" size="sm" onClick={addHolding}>
              Add Holding
            </Button>
          </div>
        </Card.Header>
        <Card.Content>
          {topHoldings.length === 0 ? (
            <p className="text-gray-600 dark:text-gray-400 text-center py-4">
              No holdings added yet. Click "Add Holding" to start.
            </p>
          ) : (
            <div className="space-y-4">
              {topHoldings.map((holding, index) => (
                <div key={index} className="grid grid-cols-12 gap-4 items-end">
                  <div className="col-span-3">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Name
                    </label>
                    <input
                      type="text"
                      value={holding.name}
                      onChange={(e) => updateHolding(index, 'name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      placeholder="Company name"
                    />
                  </div>
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Symbol
                    </label>
                    <input
                      type="text"
                      value={holding.symbol}
                      onChange={(e) => updateHolding(index, 'symbol', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      placeholder="TICKER"
                    />
                  </div>
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Weight (%)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={holding.percentage}
                      onChange={(e) => updateHolding(index, 'percentage', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      placeholder="0.00"
                    />
                  </div>
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Sector
                    </label>
                    <select
                      value={holding.sector || DEFAULT_SECTORS[0]}
                      onChange={(e) => updateHolding(index, 'sector', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    >
                      {DEFAULT_SECTORS.map(sector => (
                        <option key={sector} value={sector}>{sector}</option>
                      ))}
                    </select>
                  </div>
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Market Value
                    </label>
                    <input
                      type="number"
                      value={holding.marketValue}
                      onChange={(e) => updateHolding(index, 'marketValue', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      placeholder="0"
                    />
                  </div>
                  <div className="col-span-1">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeHolding(index)}
                      className="w-full"
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
          {errors.topHoldings && (
            <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.topHoldings}</p>
          )}
        </Card.Content>
      </Card>

      {/* Allocations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sector Allocation */}
        <Card>
          <Card.Header>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Sector Allocation</h3>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {DEFAULT_SECTORS.map(sector => (
                <div key={sector} className="flex items-center justify-between">
                  <label className="text-sm text-gray-700 dark:text-gray-300">{sector}</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      step="0.1"
                      value={sectorAllocation[sector] || 0}
                      onChange={(e) => updateAllocation('sector', sector, parseFloat(e.target.value) || 0)}
                      className="w-20 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-400">%</span>
                  </div>
                </div>
              ))}
            </div>
            {errors.Sector && (
              <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.Sector}</p>
            )}
          </Card.Content>
        </Card>

        {/* Geographic Allocation */}
        <Card>
          <Card.Header>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Geographic Allocation</h3>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {DEFAULT_REGIONS.map(region => (
                <div key={region} className="flex items-center justify-between">
                  <label className="text-sm text-gray-700 dark:text-gray-300">{region}</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      step="0.1"
                      value={geographicAllocation[region] || 0}
                      onChange={(e) => updateAllocation('geographic', region, parseFloat(e.target.value) || 0)}
                      className="w-20 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-400">%</span>
                  </div>
                </div>
              ))}
            </div>
            {errors.Geographic && (
              <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.Geographic}</p>
            )}
          </Card.Content>
        </Card>

        {/* Asset Allocation */}
        <Card>
          <Card.Header>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Asset Allocation</h3>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {DEFAULT_ASSETS.map(asset => (
                <div key={asset} className="flex items-center justify-between">
                  <label className="text-sm text-gray-700 dark:text-gray-300">{asset}</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      step="0.1"
                      value={assetAllocation[asset] || 0}
                      onChange={(e) => updateAllocation('asset', asset, parseFloat(e.target.value) || 0)}
                      className="w-20 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-400">%</span>
                  </div>
                </div>
              ))}
            </div>
            {errors.Asset && (
              <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.Asset}</p>
            )}
          </Card.Content>
        </Card>

        {/* Market Cap Allocation */}
        <Card>
          <Card.Header>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Market Cap Allocation</h3>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {DEFAULT_MARKET_CAPS.map(cap => (
                <div key={cap} className="flex items-center justify-between">
                  <label className="text-sm text-gray-700 dark:text-gray-300">{cap}</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      step="0.1"
                      value={marketCapAllocation[cap] || 0}
                      onChange={(e) => updateAllocation('marketCap', cap, parseFloat(e.target.value) || 0)}
                      className="w-20 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-400">%</span>
                  </div>
                </div>
              ))}
            </div>
            {errors['Market Cap'] && (
              <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors['Market Cap']}</p>
            )}
          </Card.Content>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={disabled || loading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={disabled || loading}
        >
          {loading ? 'Saving...' : `Save Holdings for ${currentMonth}`}
        </Button>
      </div>
    </form>
  );
}