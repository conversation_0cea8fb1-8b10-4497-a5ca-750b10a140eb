'use client';

import { useState, useEffect } from 'react';

import { Save, Cancel, TrendingUp, Assessment } from '@mui/icons-material';

import { Card, Button, Input } from '@/components/ui';
import MonthSelector from '@/components/ui/MonthSelector';
import { useTranslation } from '@/i18n/provider';
import { FundDetails, FundSnapshotCreate } from '@/types';
import { fundApi } from '@/lib/api';

interface KPIRiskMetricsFormProps {
  fund: FundDetails;
  onSubmit: (data: any, month: string) => Promise<void>;
  onCancel: () => void;
  disabled?: boolean;
  selectedMonth?: string;
  onMonthChange?: (month: string) => void;
  availableMonths?: string[];
}

export default function KPIRiskMetricsForm({
  fund,
  onSubmit,
  onCancel,
  disabled,
  selectedMonth,
  onMonthChange,
  availableMonths = []
}: KPIRiskMetricsFormProps) {
  const { t } = useTranslation();

  // Get current month as default
  const getCurrentMonth = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  };

  const [currentMonth, setCurrentMonth] = useState<string>(selectedMonth || getCurrentMonth());
  const [loading, setLoading] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [formData, setFormData] = useState({
    // Key Performance Indicators
    kpis: {
      totalReturn: fund.analytics?.kpis?.totalReturn || '',
      annualizedReturn: fund.analytics?.kpis?.annualizedReturn || '',
      volatility: fund.analytics?.kpis?.volatility || '',
      sharpeRatio: fund.analytics?.kpis?.sharpeRatio || '',
      sortinoRatio: fund.analytics?.kpis?.sortinoRatio || '',
      calmarRatio: fund.analytics?.kpis?.calmarRatio || '',
      informationRatio: fund.analytics?.kpis?.informationRatio || '',
      treynorRatio: fund.analytics?.kpis?.treynorRatio || '',
      alpha: fund.analytics?.kpis?.alpha || '',
      beta: fund.analytics?.kpis?.beta || '',
      maxDrawdown: fund.analytics?.kpis?.maxDrawdown || '',
      trackingError: fund.analytics?.kpis?.trackingError || '',
    },
    // Risk Metrics
    riskMetrics: {
      standardDeviation: fund.analytics?.riskMetrics?.standardDeviation || '',
      varRisk: fund.analytics?.riskMetrics?.varRisk || '',
      sortRatio: fund.analytics?.riskMetrics?.sortRatio || '',
      calmarRatio: fund.analytics?.riskMetrics?.calmarRatio || '',
      correlation: fund.analytics?.riskMetrics?.correlation || '',
    }
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Handle month change
  const handleMonthChange = (month: string) => {
    setCurrentMonth(month);
    if (onMonthChange) {
      onMonthChange(month);
    }
    // Load data for the selected month
    loadSnapshotData(month);
  };

  // Load snapshot data for selected month
  const loadSnapshotData = async (month: string) => {
    setLoading(true);
    setDataLoaded(false);

    try {
      // Try to get snapshot data for the selected month
      const snapshotResponse = await fundApi.getFundSnapshot(fund.id, month);

      if (snapshotResponse.success && (snapshotResponse.data.performance_metrics || snapshotResponse.data.risk_analytics)) {
        const performanceMetrics = snapshotResponse.data.performance_metrics || {};
        const riskAnalytics = snapshotResponse.data.risk_analytics || {};

        // Load KPIs
        setFormData(prev => ({
          ...prev,
          kpis: {
            totalReturn: performanceMetrics.total_return?.toString() || '',
            annualizedReturn: performanceMetrics.annualized_return?.toString() || '',
            volatility: performanceMetrics.volatility?.toString() || '',
            sharpeRatio: performanceMetrics.sharpe_ratio?.toString() || '',
            sortinoRatio: performanceMetrics.sortino_ratio?.toString() || '',
            calmarRatio: performanceMetrics.calmar_ratio?.toString() || '',
            informationRatio: performanceMetrics.information_ratio?.toString() || '',
            treynorRatio: performanceMetrics.treynor_ratio?.toString() || '',
            alpha: performanceMetrics.alpha?.toString() || '',
            beta: performanceMetrics.beta?.toString() || '',
            maxDrawdown: performanceMetrics.max_drawdown?.toString() || '',
            trackingError: performanceMetrics.tracking_error?.toString() || '',
          },
          riskMetrics: {
            standardDeviation: riskAnalytics.standard_deviation?.toString() || '',
            varRisk: riskAnalytics.var_risk?.toString() || '',
            sortRatio: riskAnalytics.sort_ratio?.toString() || '',
            calmarRatio: riskAnalytics.calmar_ratio?.toString() || '',
            correlation: riskAnalytics.correlation?.toString() || '',
          }
        }));

        setDataLoaded(true);
      } else {
        // No snapshot data for this month, load from fund defaults
        loadDefaultData();
      }
    } catch (error) {
      console.warn('No snapshot data found for month:', month, 'Loading default data');
      // Load default data from fund
      loadDefaultData();
    } finally {
      setLoading(false);
    }
  };

  // Load default data from fund
  const loadDefaultData = () => {
    setFormData({
      kpis: {
        totalReturn: fund.analytics?.kpis?.totalReturn?.toString() || '',
        annualizedReturn: fund.analytics?.kpis?.annualizedReturn?.toString() || '',
        volatility: fund.analytics?.kpis?.volatility?.toString() || '',
        sharpeRatio: fund.analytics?.kpis?.sharpeRatio?.toString() || '',
        sortinoRatio: fund.analytics?.kpis?.sortinoRatio?.toString() || '',
        calmarRatio: fund.analytics?.kpis?.calmarRatio?.toString() || '',
        informationRatio: fund.analytics?.kpis?.informationRatio?.toString() || '',
        treynorRatio: fund.analytics?.kpis?.treynorRatio?.toString() || '',
        alpha: fund.analytics?.kpis?.alpha?.toString() || '',
        beta: fund.analytics?.kpis?.beta?.toString() || '',
        maxDrawdown: fund.analytics?.kpis?.maxDrawdown?.toString() || '',
        trackingError: fund.analytics?.kpis?.trackingError?.toString() || '',
      },
      riskMetrics: {
        standardDeviation: fund.analytics?.riskMetrics?.standardDeviation?.toString() || '',
        varRisk: fund.analytics?.riskMetrics?.varRisk?.toString() || '',
        sortRatio: fund.analytics?.riskMetrics?.sortRatio?.toString() || '',
        calmarRatio: fund.analytics?.riskMetrics?.calmarRatio?.toString() || '',
        correlation: fund.analytics?.riskMetrics?.correlation?.toString() || '',
      }
    });
    setDataLoaded(true);
  };

  // Initialize with data for current month
  useEffect(() => {
    loadSnapshotData(currentMonth);
  }, [fund.id]);

  // Update current month when selectedMonth prop changes
  useEffect(() => {
    if (selectedMonth && selectedMonth !== currentMonth) {
      setCurrentMonth(selectedMonth);
      loadSnapshotData(selectedMonth);
    }
  }, [selectedMonth]);

  const handleInputChange = (section: 'kpis' | 'riskMetrics', field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Convert empty strings to null and numbers to proper format
      const processedData = {
        analytics: {
          ...fund.analytics,
          kpis: Object.entries(formData.kpis).reduce((acc, [key, value]) => {
            acc[key] = value === '' ? null : parseFloat(value as string);
            return acc;
          }, {} as any),
          riskMetrics: Object.entries(formData.riskMetrics).reduce((acc, [key, value]) => {
            acc[key] = value === '' ? null : parseFloat(value as string);
            return acc;
          }, {} as any)
        }
      };

      // Submit with current month
      await onSubmit(processedData, currentMonth);

      // Update available months if needed
      if (onMonthChange && !availableMonths.includes(currentMonth)) {
        onMonthChange(currentMonth);
      }
    } catch (error) {
      console.error('Error submitting KPI and Risk Metrics:', error);
      setErrors({ submit: 'Failed to submit metrics data. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const kpiFields = [
    { key: 'totalReturn', label: 'Total Return (%)', type: 'number', step: '0.01' },
    { key: 'annualizedReturn', label: 'Annualized Return (%)', type: 'number', step: '0.01' },
    { key: 'volatility', label: 'Volatility (%)', type: 'number', step: '0.01' },
    { key: 'sharpeRatio', label: 'Sharpe Ratio', type: 'number', step: '0.01' },
    { key: 'sortinoRatio', label: 'Sortino Ratio', type: 'number', step: '0.01' },
    { key: 'calmarRatio', label: 'Calmar Ratio', type: 'number', step: '0.01' },
    { key: 'informationRatio', label: 'Information Ratio', type: 'number', step: '0.01' },
    { key: 'treynorRatio', label: 'Treynor Ratio', type: 'number', step: '0.01' },
    { key: 'alpha', label: 'Alpha', type: 'number', step: '0.01' },
    { key: 'beta', label: 'Beta', type: 'number', step: '0.01' },
    { key: 'maxDrawdown', label: 'Max Drawdown (%)', type: 'number', step: '0.01' },
    { key: 'trackingError', label: 'Tracking Error (%)', type: 'number', step: '0.01' },
  ];

  const riskMetricsFields = [
    { key: 'standardDeviation', label: 'Standard Deviation (%)', type: 'number', step: '0.01' },
    { key: 'varRisk', label: 'Value at Risk (%)', type: 'number', step: '0.01' },
    { key: 'sortRatio', label: 'SORT Ratio', type: 'number', step: '0.01' },
    { key: 'calmarRatio', label: 'Calmar Ratio', type: 'number', step: '0.01' },
    { key: 'correlation', label: 'Correlation', type: 'number', step: '0.01', min: '-1', max: '1' },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Month Selection */}
      <Card>
        <Card.Header>
          <Card.Title className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-blue-600" />
            Monthly KPI & Risk Metrics
          </Card.Title>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Select a month to view or edit performance and risk metrics for {fund.name}
          </p>
        </Card.Header>
        <Card.Content>
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <MonthSelector
              selectedMonth={currentMonth}
              onMonthChange={handleMonthChange}
              availableMonths={availableMonths}
              label="Select Month for Metrics Data *"
              className="max-w-md"
              disabled={loading}
            />
            {loading && (
              <p className="mt-2 text-sm text-blue-600 dark:text-blue-400">
                Loading data for {currentMonth}...
              </p>
            )}
            {dataLoaded && !loading && (
              <p className="mt-2 text-sm text-green-600 dark:text-green-400">
                {availableMonths.includes(currentMonth)
                  ? `Loaded existing data for ${currentMonth}`
                  : `No existing data for ${currentMonth} - showing defaults`}
              </p>
            )}
          </div>
          {errors.submit && (
            <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.submit}</p>
          )}
        </Card.Content>
      </Card>

      {/* Key Performance Indicators Section */}
      <Card>
        <Card.Header>
          <Card.Title className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-blue-600" />
            Key Performance Indicators
          </Card.Title>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Update the fund's key performance metrics and ratios
          </p>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {kpiFields.map((field) => (
              <div key={field.key}>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {field.label}
                </label>
                <Input
                  type={field.type}
                  step={field.step}
                  min={(field as any).min}
                  max={(field as any).max}
                  value={formData.kpis[field.key as keyof typeof formData.kpis]}
                  onChange={(e) => handleInputChange('kpis', field.key, e.target.value)}
                  placeholder={`Enter ${field.label.toLowerCase()}`}
                  disabled={disabled || loading}
                />
              </div>
            ))}
          </div>
        </Card.Content>
      </Card>

      {/* Risk Metrics Section */}
      <Card>
        <Card.Header>
          <Card.Title className="flex items-center gap-2">
            <Assessment className="w-5 h-5 text-red-600" />
            Risk Metrics
          </Card.Title>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Update the fund's risk assessment and measurement metrics
          </p>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {riskMetricsFields.map((field) => (
              <div key={field.key}>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {field.label}
                </label>
                <Input
                  type={field.type}
                  step={field.step}
                  min={(field as any).min}
                  max={(field as any).max}
                  value={formData.riskMetrics[field.key as keyof typeof formData.riskMetrics]}
                  onChange={(e) => handleInputChange('riskMetrics', field.key, e.target.value)}
                  placeholder={`Enter ${field.label.toLowerCase()}`}
                  disabled={disabled || loading}
                />
              </div>
            ))}
          </div>
        </Card.Content>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={disabled || loading}
          className="flex items-center gap-2"
        >
          <Cancel className="w-4 h-4" />
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={disabled || loading}
          className="flex items-center gap-2"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4" />
              Save Metrics for {currentMonth}
            </>
          )}
        </Button>
      </div>
    </form>
  );
}