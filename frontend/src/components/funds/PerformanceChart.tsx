'use client';

import { useEffect, useRef, useState } from 'react';

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from 'chart.js';
import { Line } from 'react-chartjs-2';

import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { ChartDataPoint, TimePeriod } from '@/types';

import TimePeriodSelector from './TimePeriodSelector';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface PerformanceChartProps {
  fundData: ChartDataPoint[];
  benchmarkData?: ChartDataPoint[];
  fundName: string;
  benchmarkName?: string;
  onPeriodChange?: (period: TimePeriod) => void;
  loading?: boolean;
}

const TIME_PERIODS: { value: TimePeriod; label: string }[] = [
  { value: '1M', label: '1 Month' },
  { value: '3M', label: '3 Months' },
  { value: '6M', label: '6 Months' },
  { value: '1Y', label: '1 Year' },
  { value: '3Y', label: '3 Years' },
  { value: '5Y', label: '5 Years' },
  { value: 'ALL', label: 'All Time' },
];

export default function PerformanceChart({
  fundData,
  benchmarkData,
  fundName,
  benchmarkName = 'Benchmark',
  onPeriodChange,
  loading = false,
}: PerformanceChartProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('1Y');
  const [showBenchmark, setShowBenchmark] = useState(!!benchmarkData);

  const handlePeriodChange = (period: TimePeriod) => {
    setSelectedPeriod(period);
    onPeriodChange?.(period);
  };

  // Prepare chart data
  const chartData = {
    labels: fundData.map(point => {
      const date = new Date(point.date);
      if (selectedPeriod === '1M') {
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      } else {
        return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
      }
    }),
    datasets: [
      {
        label: fundName,
        data: fundData.map(point => point.value),
        borderColor: 'rgb(59, 130, 246)', // blue-500
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 2,
        pointRadius: fundData.length > 100 ? 0 : 2,
        pointHoverRadius: 4,
        tension: 0.1,
      },
      ...(showBenchmark && benchmarkData ? [{
        label: benchmarkName,
        data: benchmarkData.map(point => point.value),
        borderColor: 'rgb(107, 114, 128)', // gray-500
        backgroundColor: 'rgba(107, 114, 128, 0.1)',
        borderWidth: 2,
        pointRadius: benchmarkData.length > 100 ? 0 : 2,
        pointHoverRadius: 4,
        tension: 0.1,
        borderDash: [5, 5],
      }] : []),
    ],
  };

  const options: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            const dataPoint = fundData[context.dataIndex];
            
            if (context.datasetIndex === 0 && dataPoint?.returns !== undefined) {
              return `${label}: ${value.toFixed(2)} (${dataPoint.returns >= 0 ? '+' : ''}${dataPoint.returns.toFixed(2)}%)`;
            }
            return `${label}: ${value.toFixed(2)}`;
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Time',
        },
        grid: {
          display: false,
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'NAV ($)',
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          callback: function(value) {
            return '$' + Number(value).toFixed(0);
          },
        },
      },
    },
    elements: {
      point: {
        hoverBackgroundColor: 'white',
        hoverBorderWidth: 2,
      },
    },
  };

  return (
    <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 transition-colors">
      <Card.Header>
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <Card.Title>Performance Chart</Card.Title>
          
          <div className="flex flex-col sm:flex-row gap-3">
            {/* Benchmark Toggle */}
            {benchmarkData && (
              <div className="flex items-center">
                <label className="flex items-center cursor-pointer group">
                  <input
                    type="checkbox"
                    checked={showBenchmark}
                    onChange={(e) => setShowBenchmark(e.target.checked)}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 border-gray-300 rounded group-hover:border-blue-500 transition-colors"
                    aria-describedby="benchmark-description"
                  />
                  <span className="text-sm text-gray-700 select-none group-hover:text-blue-700 transition-colors">
                    Show Benchmark
                  </span>
                </label>
                <span id="benchmark-description" className="sr-only">
                  Toggle to show or hide benchmark comparison on the chart
                </span>
              </div>
            )}

            {/* Time Period Selector */}
            <TimePeriodSelector
              selectedPeriod={selectedPeriod}
              onPeriodChange={handlePeriodChange}
              loading={loading}
              className="justify-center sm:justify-start"
            />
          </div>
        </div>
      </Card.Header>
      
      <Card.Content>
        <div className="relative" role="img" aria-label="Performance chart showing fund price over time">
          {loading && (
            <div 
              className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10"
              role="status"
              aria-live="polite"
            >
              <div className="flex items-center">
                <div 
                  className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"
                  aria-hidden="true"
                ></div>
                <span className="text-gray-600">Loading chart data...</span>
              </div>
            </div>
          )}
          
          <div className="h-64 sm:h-80 lg:h-96">
            <Line data={chartData} options={options} />
          </div>
        </div>
        
        {/* Chart Statistics */}
        {fundData.length > 0 && (
          <div
            className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700"
            role="region"
            aria-label="Chart statistics"
          >
            <h3 className="sr-only">Performance Statistics</h3>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-gray-600 dark:text-gray-400 font-medium mb-1">Current Value</div>
                <div
                  className="font-bold text-lg text-gray-900 dark:text-gray-100"
                  aria-label={`Current value: ${fundData[fundData.length - 1]?.value.toFixed(2)} rupees`}
                >
                  ₹{fundData[fundData.length - 1]?.value.toFixed(2)}
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-gray-600 dark:text-gray-400 font-medium mb-1">Period High</div>
                <div
                  className="font-bold text-lg text-green-700 dark:text-green-400"
                  aria-label={`Period high: ${Math.max(...fundData.map(p => p.value)).toFixed(2)} rupees`}
                >
                  ₹{Math.max(...fundData.map(p => p.value)).toFixed(2)}
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-gray-600 dark:text-gray-400 font-medium mb-1">Period Low</div>
                <div
                  className="font-bold text-lg text-red-700 dark:text-red-400"
                  aria-label={`Period low: ${Math.min(...fundData.map(p => p.value)).toFixed(2)} rupees`}
                >
                  ₹{Math.min(...fundData.map(p => p.value)).toFixed(2)}
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-gray-600 dark:text-gray-400 font-medium mb-1">Period Change</div>
                {fundData.length > 1 && (
                  <div
                    className={`font-bold text-lg ${
                      fundData[fundData.length - 1].value >= fundData[0].value ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                    }`}
                    aria-label={`Period change: ${fundData[fundData.length - 1].value >= fundData[0].value ? 'positive' : 'negative'} ${((fundData[fundData.length - 1].value - fundData[0].value) / fundData[0].value * 100).toFixed(2)} percent`}
                  >
                    {fundData[fundData.length - 1].value >= fundData[0].value ? '+' : ''}
                    {((fundData[fundData.length - 1].value - fundData[0].value) / fundData[0].value * 100).toFixed(2)}%
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </Card.Content>
    </Card>
  );
} 