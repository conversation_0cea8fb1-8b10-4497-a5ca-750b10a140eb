import Button from '@/components/ui/Button';
import { TimePeriod } from '@/types';

interface TimePeriodSelectorProps {
  selectedPeriod: TimePeriod;
  onPeriodChange: (period: TimePeriod) => void;
  loading?: boolean;
  className?: string;
}

const TIME_PERIODS: { value: TimePeriod; label: string; description: string }[] = [
  { value: '1M', label: '1M', description: '1 Month' },
  { value: '3M', label: '3M', description: '3 Months' },
  { value: '6M', label: '6M', description: '6 Months' },
  { value: '1Y', label: '1Y', description: '1 Year' },
  { value: '3Y', label: '3Y', description: '3 Years' },
  { value: '5Y', label: '5Y', description: '5 Years' },
  { value: 'ALL', label: 'ALL', description: 'All Time' },
];

export default function TimePeriodSelector({ 
  selectedPeriod, 
  onPeriodChange, 
  loading = false,
  className = '' 
}: TimePeriodSelectorProps) {
  const handleKeyDown = (event: React.KeyboardEvent, period: TimePeriod) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onPeriodChange(period);
    }
  };

  return (
    <div 
      className={`flex flex-wrap gap-1 ${className}`}
      role="tablist"
      aria-label="Select time period for analysis"
    >
      {TIME_PERIODS.map((period) => (
        <Button
          key={period.value}
          variant={selectedPeriod === period.value ? 'primary' : 'outline'}
          size="sm"
          onClick={() => onPeriodChange(period.value)}
          onKeyDown={(e) => handleKeyDown(e, period.value)}
          disabled={loading}
          aria-label={`${period.description}${selectedPeriod === period.value ? ' (currently selected)' : ''}`}
          aria-pressed={selectedPeriod === period.value}
          role="tab"
          aria-selected={selectedPeriod === period.value}
          tabIndex={selectedPeriod === period.value ? 0 : -1}
          className={`min-w-[3rem] transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
            selectedPeriod === period.value ? 'ring-2 ring-blue-300' : ''
          }`}
        >
          <span aria-hidden="true">{period.label}</span>
          <span className="sr-only">{period.description}</span>
        </Button>
      ))}
    </div>
  );
}

export { TIME_PERIODS }; 