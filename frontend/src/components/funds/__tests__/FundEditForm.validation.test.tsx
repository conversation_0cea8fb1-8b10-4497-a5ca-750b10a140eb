import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { Fund } from '@/types';

import FundEditForm from '../FundEditForm';

// Mock the translation hook
jest.mock('@/i18n/provider', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe('FundEditForm Validation', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should show inception date as required field', () => {
    render(
      <FundEditForm
        isEditing={false}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        loading={false}
      />
    );

    const inceptionDateLabel = screen.getByText('Inception Date');
    expect(inceptionDateLabel.parentElement).toHaveTextContent('*');
  });

  it('should show validation error when inception date is empty', async () => {
    const user = userEvent.setup();
    
    render(
      <FundEditForm
        isEditing={false}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        loading={false}
      />
    );

    // Fill required fields except inception date
    await user.type(screen.getByLabelText(/Fund Name/), 'Test Fund');
    await user.type(screen.getByLabelText(/Symbol/), 'TEST');
    await user.type(screen.getByLabelText(/Fund Manager/), 'Test Manager');

    // Try to submit without inception date
    const submitButton = screen.getByRole('button', { name: /save/i });
    await user.click(submitButton);

    // Should show validation error for inception date
    await waitFor(() => {
      expect(screen.getByText('Inception date is required')).toBeInTheDocument();
    });

    // Should not call onSubmit
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('should show validation error for future inception date', async () => {
    const user = userEvent.setup();
    
    render(
      <FundEditForm
        isEditing={false}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        loading={false}
      />
    );

    // Fill required fields with future inception date
    await user.type(screen.getByLabelText(/Fund Name/), 'Test Fund');
    await user.type(screen.getByLabelText(/Symbol/), 'TEST');
    await user.type(screen.getByLabelText(/Fund Manager/), 'Test Manager');
    
    // Set future date
    const futureDate = new Date();
    futureDate.setFullYear(futureDate.getFullYear() + 1);
    const futureDateString = futureDate.toISOString().split('T')[0];
    
    await user.type(screen.getByLabelText(/Inception Date/), futureDateString);

    // Try to submit
    const submitButton = screen.getByRole('button', { name: /save/i });
    await user.click(submitButton);

    // Should show validation error for future date
    await waitFor(() => {
      expect(screen.getByText(/cannot be in the future/)).toBeInTheDocument();
    });

    // Should not call onSubmit
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('should show all validation errors simultaneously', async () => {
    const user = userEvent.setup();
    
    render(
      <FundEditForm
        isEditing={false}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        loading={false}
      />
    );

    // Try to submit empty form
    const submitButton = screen.getByRole('button', { name: /save/i });
    await user.click(submitButton);

    // Should show multiple validation errors at once
    await waitFor(() => {
      expect(screen.getByText('Fund name is required')).toBeInTheDocument();
      expect(screen.getByText('Fund symbol is required')).toBeInTheDocument();
      expect(screen.getByText('Fund manager is required')).toBeInTheDocument();
      expect(screen.getByText('Inception date is required')).toBeInTheDocument();
    });

    // Should not call onSubmit
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('should submit successfully with valid data including inception date', async () => {
    const user = userEvent.setup();
    
    render(
      <FundEditForm
        isEditing={false}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        loading={false}
      />
    );

    // Fill all required fields with valid data
    await user.type(screen.getByLabelText(/Fund Name/), 'Test Fund');
    await user.type(screen.getByLabelText(/Symbol/), 'TEST');
    await user.type(screen.getByLabelText(/Fund Manager/), 'Test Manager');
    
    // Set valid past date
    const pastDate = new Date();
    pastDate.setFullYear(pastDate.getFullYear() - 1);
    const pastDateString = pastDate.toISOString().split('T')[0];
    
    await user.type(screen.getByLabelText(/Inception Date/), pastDateString);

    // Submit form
    const submitButton = screen.getByRole('button', { name: /save/i });
    await user.click(submitButton);

    // Should call onSubmit with correct data
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Test Fund',
          symbol: 'TEST',
          fundManager: 'Test Manager',
          inceptionDate: expect.any(Date),
        })
      );
    });
  });
});
