'use client';

import { useEffect, useState } from 'react';

import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { initializeTheme } from '@/store/slices/themeSlice';

interface ThemeProviderProps {
  children: React.ReactNode;
}

const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [mounted, setMounted] = useState(false);
  const dispatch = useAppDispatch();
  const { isDark } = useAppSelector((state) => state.theme);

  // Initialize theme and mark as mounted
  useEffect(() => {
    dispatch(initializeTheme());
    setMounted(true);
  }, [dispatch]);

  // Apply theme classes to html and body after mounting
  useEffect(() => {
    if (!mounted) {return;}

    const html = document.documentElement;

    // Apply dark class to html
    if (isDark) {
      html.classList.add('dark');
    } else {
      html.classList.remove('dark');
    }

    // Set color scheme for browser UI consistency
    html.style.colorScheme = isDark ? 'dark' : 'light';
  }, [mounted, isDark]);

  // Prevent hydration mismatch by not rendering theme-dependent content until mounted
  if (!mounted) {
    return <>{children}</>;
  }

  return <>{children}</>;
};

export default ThemeProvider;
