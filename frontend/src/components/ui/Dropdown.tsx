'use client'

import React, { useState, useRef, useEffect } from 'react'

import { BaseComponentProps } from '@/types'

interface DropdownProps extends BaseComponentProps {
  trigger: React.ReactNode
  children: React.ReactNode
  position?: 'left' | 'right'
  disabled?: boolean
}

interface DropdownItemProps extends BaseComponentProps {
  onClick?: () => void
  disabled?: boolean
  children: React.ReactNode
}

const Dropdown: React.FC<DropdownProps> & {
  Item: React.FC<DropdownItemProps>
} = ({ 
  trigger, 
  children, 
  position = 'right',
  disabled = false,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen])

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen)
    }
  }

  const handleClose = () => {
    setIsOpen(false)
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={handleToggle}
        disabled={disabled}
        className={`flex items-center ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {trigger}
      </button>
      
      {isOpen && (
        <div 
          className={`absolute top-full z-50 mt-1 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg ${
            position === 'left' ? 'left-0' : 'right-0'
          }`}
          role="menu"
          aria-orientation="vertical"
        >
          <div className="py-1" onClick={handleClose}>
            {children}
          </div>
        </div>
      )}
    </div>
  )
}

const DropdownItem: React.FC<DropdownItemProps> = ({ 
  onClick, 
  disabled = false,
  className = '',
  children 
}) => {
  const handleClick = () => {
    if (!disabled && onClick) {
      onClick()
    }
  }

  return (
    <button
      onClick={handleClick}
      disabled={disabled}
      className={`w-full text-left px-4 py-2 text-sm ${
        disabled 
          ? 'text-gray-400 dark:text-gray-500 cursor-not-allowed' 
          : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
      } ${className}`}
      role="menuitem"
    >
      {children}
    </button>
  )
}

Dropdown.Item = DropdownItem

export default Dropdown