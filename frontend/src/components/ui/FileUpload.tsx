'use client';

import { useCallback, useState, useRef } from 'react';

import {
  CloudUpload,
  AttachFile,
  Delete,
  CheckCircle,
  Error,
  PictureAsPdf,
} from '@mui/icons-material';

import { Button } from '@/components/ui';
import { useTranslation } from '@/i18n/provider';

export interface UploadFile {
  id: string;
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
}

interface FileUploadProps {
  onFilesSelected: (files: UploadFile[]) => void;
  onFileRemove: (fileId: string) => void;
  onUpload: (files: UploadFile[]) => void;
  files: UploadFile[];
  maxFiles?: number;
  maxFileSize?: number; // in MB
  acceptedFileTypes?: string[];
  disabled?: boolean;
  className?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFilesSelected,
  onFileRemove,
  onUpload,
  files,
  maxFiles = 10,
  maxFileSize = 10, // 10MB default
  acceptedFileTypes = ['.pdf'],
  disabled = false,
  className = '',
}) => {
  const { t } = useTranslation();
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = useCallback((file: File): string | null => {
    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!acceptedFileTypes.includes(fileExtension)) {
      return `File type not supported. Please upload ${acceptedFileTypes.join(', ')} files only.`;
    }

    // Check file size
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxFileSize) {
      return `File size too large. Maximum size is ${maxFileSize}MB.`;
    }

    return null;
  }, [acceptedFileTypes, maxFileSize]);

  const processFiles = useCallback((fileList: FileList) => {
    const newFiles: UploadFile[] = [];
    const errors: string[] = [];

    // Check total file count
    if (files.length + fileList.length > maxFiles) {
      errors.push(`Maximum ${maxFiles} files allowed. Please remove some files first.`);
      return;
    }

    Array.from(fileList).forEach((file) => {
      const error = validateFile(file);
      if (error) {
        errors.push(`${file.name}: ${error}`);
      } else {
        // Check for duplicates
        const isDuplicate = files.some(existingFile => 
          existingFile.file.name === file.name && 
          existingFile.file.size === file.size
        );
        
        if (!isDuplicate) {
          newFiles.push({
            id: `${Date.now()}-${Math.random().toString(36).substring(2)}`,
            file,
            status: 'pending',
            progress: 0,
          });
        } else {
          errors.push(`${file.name}: File already selected`);
        }
      }
    });

    if (errors.length > 0) {
      // You might want to show these errors in a toast or alert
      console.warn('File upload errors:', errors);
    }

    if (newFiles.length > 0) {
      onFilesSelected(newFiles);
    }
  }, [files, maxFiles, validateFile, onFilesSelected]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    if (disabled) {return;}

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      processFiles(droppedFiles);
    }
  }, [disabled, processFiles]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      processFiles(selectedFiles);
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [processFiles]);

  const handleBrowseClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled]);

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <Error className="w-5 h-5 text-red-500" />;
      case 'uploading':
        return (
          <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        );
      default:
        return <PictureAsPdf className="w-5 h-5 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) {return '0 Bytes';}
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const canUpload = files.length > 0 && files.some(f => f.status === 'pending');

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Drop Zone */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200
          ${isDragOver 
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleBrowseClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedFileTypes.join(',')}
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled}
        />
        
        <CloudUpload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        
        <div className="space-y-2">
          <p className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {isDragOver ? 'Drop files here' : 'Upload PDF Files'}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Drag and drop your PDF files here, or{' '}
            <span className="text-blue-600 dark:text-blue-400 font-medium">
              browse
            </span>{' '}
            to select files
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Maximum {maxFiles} files, {maxFileSize}MB each. PDF files only.
          </p>
        </div>
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Selected Files ({files.length})
            </h3>
            {canUpload && (
              <Button
                onClick={() => onUpload(files.filter(f => f.status === 'pending'))}
                disabled={disabled}
                className="flex items-center space-x-2"
              >
                <CloudUpload className="w-4 h-4" />
                <span>Upload Files</span>
              </Button>
            )}
          </div>

          <div className="space-y-2">
            {files.map((uploadFile) => (
              <div
                key={uploadFile.id}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  {getStatusIcon(uploadFile.status)}
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {uploadFile.file.name}
                    </p>
                    <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                      <span>{formatFileSize(uploadFile.file.size)}</span>
                      {uploadFile.status === 'uploading' && (
                        <span>• {uploadFile.progress}%</span>
                      )}
                      {uploadFile.error && (
                        <span className="text-red-500">• {uploadFile.error}</span>
                      )}
                    </div>
                    
                    {/* Progress Bar */}
                    {uploadFile.status === 'uploading' && (
                      <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                        <div
                          className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${uploadFile.progress}%` }}
                        />
                      </div>
                    )}
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onFileRemove(uploadFile.id);
                  }}
                  disabled={disabled || uploadFile.status === 'uploading'}
                  className="ml-2 text-gray-500 hover:text-red-500"
                >
                  <Delete className="w-4 h-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
