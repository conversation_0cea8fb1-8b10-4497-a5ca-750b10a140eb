'use client';

import { useState, useEffect } from 'react';

export interface MonthSelectorProps {
  selectedMonth: string;  // YYYY-MM format
  onMonthChange: (month: string) => void;
  availableMonths?: string[];  // Months with existing data
  label?: string;
  disabled?: boolean;
  className?: string;
  showCurrentMonth?: boolean;  // Whether to default to current month
}

export default function MonthSelector({
  selectedMonth,
  onMonthChange,
  availableMonths = [],
  label = "Select Month",
  disabled = false,
  className = "",
  showCurrentMonth = true
}: MonthSelectorProps) {
  const [currentMonth, setCurrentMonth] = useState<string>("");

  useEffect(() => {
    // Set current month in YYYY-MM format
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    setCurrentMonth(`${year}-${month}`);
  }, []);

  // Generate month options for the last 3 years
  const generateMonthOptions = (): { value: string; label: string; hasData: boolean }[] => {
    const options = [];
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonthNum = now.getMonth(); // 0-based

    // Generate options for last 36 months (3 years)
    for (let i = 0; i < 36; i++) {
      const date = new Date(currentYear, currentMonthNum - i, 1);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const value = `${year}-${month}`;
      
      // Format label
      const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      const monthName = monthNames[date.getMonth()];
      const label = `${monthName} ${year}`;
      
      // Check if this month has data
      const hasData = availableMonths.includes(value);
      
      options.push({ value, label, hasData });
    }

    return options;
  };

  const monthOptions = generateMonthOptions();

  const handleMonthChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newMonth = event.target.value;
    onMonthChange(newMonth);
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </label>
        {showCurrentMonth && currentMonth && (
          <span className="text-xs text-gray-500 dark:text-gray-400">
            Current: {monthOptions.find(opt => opt.value === currentMonth)?.label}
          </span>
        )}
      </div>
      
      <select
        value={selectedMonth}
        onChange={handleMonthChange}
        disabled={disabled}
        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <option value="">Select a month...</option>
        {monthOptions.map((option) => (
          <option 
            key={option.value} 
            value={option.value}
            className={option.hasData ? 'font-medium' : ''}
          >
            {option.label}
            {option.hasData ? ' ✓' : ''}
            {option.value === currentMonth ? ' (Current)' : ''}
          </option>
        ))}
      </select>
      
      {selectedMonth && availableMonths.length > 0 && (
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {availableMonths.includes(selectedMonth) ? (
            <span className="text-green-600 dark:text-green-400">
              ✓ Data available for this month
            </span>
          ) : (
            <span className="text-amber-600 dark:text-amber-400">
              ⚠ No existing data for this month
            </span>
          )}
        </div>
      )}
      
      {availableMonths.length > 0 && (
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Months with data: {availableMonths.length}
        </div>
      )}
    </div>
  );
}