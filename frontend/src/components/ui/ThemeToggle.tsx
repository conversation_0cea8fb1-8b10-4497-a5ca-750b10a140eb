'use client';

import { LightMode, DarkMode, Computer } from '@mui/icons-material';

import { useTheme } from '@/hooks/useTheme';
import { ThemeMode } from '@/store/slices/themeSlice';

interface ThemeToggleProps {
  variant?: 'button' | 'dropdown';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant = 'button',
  size = 'md',
  className = ''
}) => {
  const { mode, isDark, isInitialized, setTheme, toggleTheme } = useTheme();

  // Don't render until theme is initialized to prevent hydration mismatch
  if (!isInitialized) {
    return (
      <div className={`${variant === 'button' ? 'w-10 h-10' : 'w-32 h-10'} bg-gray-200 dark:bg-gray-700 rounded-md animate-pulse`} />
    );
  }

  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg',
  };

  if (variant === 'dropdown') {
    return (
      <div className={`relative inline-block text-left ${className}`}>
        <select
          value={mode}
          onChange={(e) => setTheme(e.target.value as ThemeMode)}
          className={`
            ${sizeClasses[size]} 
            bg-white dark:bg-gray-800 
            border border-gray-300 dark:border-gray-600 
            text-gray-900 dark:text-gray-100 
            rounded-md px-3 py-2 
            focus:outline-none focus:ring-2 focus:ring-blue-500 
            hover:bg-gray-50 dark:hover:bg-gray-700
            transition-colors duration-200
          `}
          aria-label="Select theme"
        >
          <option value="light">☀ Light</option>
          <option value="dark">☾ Dark</option>
          <option value="system">⚙ System</option>
        </select>
      </div>
    );
  }

  // Button variant - simple toggle between light/dark
  return (
    <button
      onClick={toggleTheme}
      className={`
        ${sizeClasses[size]} 
        ${className}
        flex items-center justify-center
        bg-white dark:bg-gray-800 
        border border-gray-300 dark:border-gray-600 
        text-gray-700 dark:text-gray-300 
        rounded-md 
        hover:bg-gray-50 dark:hover:bg-gray-700 
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800
        transition-all duration-200 ease-in-out
        shadow-sm hover:shadow-md
      `}
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
      title={`Switch to ${isDark ? 'light' : 'dark'} mode`}
    >
      <span className="transform transition-transform duration-200 hover:scale-110 flex items-center justify-center">
        {isDark ? (
          <LightMode className="w-5 h-5" />
        ) : (
          <DarkMode className="w-5 h-5" />
        )}
      </span>
    </button>
  );
};

export default ThemeToggle;
