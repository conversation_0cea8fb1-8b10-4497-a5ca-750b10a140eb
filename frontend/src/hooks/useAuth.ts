'use client'

import { useMemo } from 'react'

import { useSession } from 'next-auth/react'

export const useAuth = () => {
  const { data: session, status } = useSession()

  const authInfo = useMemo(() => {
    if (!session) {
      return {
        isAuthenticated: false,
        isLoading: status === 'loading',
        user: null,
        mfaEnabled: false,
        authTime: null,
        provider: null,
      }
    }

    return {
      isAuthenticated: true,
      isLoading: false,
      user: session.user,
      mfaEnabled: session.mfaEnabled || false,
      authTime: session.authTime ? new Date(session.authTime * 1000) : null,
      provider: session.provider,
      accessToken: session.accessToken,
      refreshToken: session.refreshToken,
      expiresAt: session.expiresAt ? new Date(session.expiresAt * 1000) : null,
    }
  }, [session, status])

  const isSessionExpiring = useMemo(() => {
    if (!authInfo.expiresAt) {return false}
    const now = new Date()
    const timeUntilExpiry = authInfo.expiresAt.getTime() - now.getTime()
    // Consider session expiring if less than 5 minutes remain
    return timeUntilExpiry < 5 * 60 * 1000
  }, [authInfo.expiresAt])

  const isSessionExpired = useMemo(() => {
    if (!authInfo.expiresAt) {return false}
    const now = new Date()
    return authInfo.expiresAt.getTime() < now.getTime()
  }, [authInfo.expiresAt])

  return {
    ...authInfo,
    isSessionExpiring,
    isSessionExpired,
    session,
  }
}

export default useAuth 