import { getSession } from 'next-auth/react';

import { UploadResult } from '@/components/ui/UploadStatus';
import { getApiToken, getTokenPreview, hasValidTokenForEnvironment } from '@/lib/token-utils';
import { Fund, ApiResponse, PaginatedResponse, FundDetails, FundPerformanceChart, TimePeriod, ChartDataPoint, Portfolio, PortfolioHolding, PortfolioTransaction, PortfolioPerformance, PortfolioCreateRequest, PortfolioUpdateRequest, AddHoldingRequest, PortfolioType, PortfolioStatus, TransactionType, FundSnapshot, FundSnapshotCreate, FundSnapshotUpdate, FundSnapshotResponse, FundSnapshotListParams } from '@/types';

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';
const AWS_REGION = process.env.NEXT_PUBLIC_AWS_REGION || 'ap-northeast-1';

console.log('🔧 API Configuration:', {
  API_BASE_URL,
  AWS_REGION
});

// Generic API request handler with AWS integration
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  console.log('🌐 Making API request to:', url);
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...options.headers,
    },
    // Add mode for CORS handling
    mode: 'cors',
    credentials: 'omit', // Don't send cookies for CORS requests
  };

  // Add authentication headers if available
  {
    try {
      const session = await getSession();
      console.log('🔐 Session check:', session ? 'Session found' : 'No session');
      const apiToken = getApiToken(session);
      
      console.log('🔐 Session details:', {
        hasAccessToken: !!session?.accessToken,
        hasIdToken: !!session?.idToken,
        hasValidToken: hasValidTokenForEnvironment(session),
        expiresAt: session?.expiresAt,
        provider: session?.provider,
        environment: process.env.NODE_ENV,
        isExpired: session?.expiresAt ? new Date(session.expiresAt * 1000) < new Date() : false,
        tokenLength: apiToken?.length || 0,
        tokenPreview: getTokenPreview(apiToken),
        userEmail: session?.user?.email,
        timeUntilExpiry: session?.expiresAt ? Math.round((session.expiresAt * 1000 - Date.now()) / 60000) : null
      });

      if (apiToken) {
        // Check if token is expired
        if (session?.expiresAt && new Date(session.expiresAt * 1000) < new Date()) {
          console.warn('⚠️ Token has expired, API will return 401 Unauthorized');
          console.warn('💡 Please refresh the page or sign in again');
        } else {
          defaultOptions.headers = {
            ...defaultOptions.headers,
            'Authorization': `Bearer ${apiToken}`,
          };
          console.log('✅ Added Authorization header to request');
          console.log('🔑 Using token type:', process.env.NODE_ENV === 'production' ? 'ID Token' : 'Access Token');
          if (session?.expiresAt) {
            console.log('🔑 Token expires at:', new Date(session.expiresAt * 1000).toLocaleString());
          } else {
            console.log('🔑 Token expiration time not available');
          }
        }
      } else {
        console.warn('⚠️ No valid token found in session, API will return 401 Unauthorized');
        console.warn('💡 Sign in to authenticate');
        console.warn('🔍 Token debug:', {
          hasAccessToken: !!session?.accessToken,
          hasIdToken: !!session?.idToken,
          environment: process.env.NODE_ENV
        });
      }
    } catch (error) {
      console.error('❌ Failed to get session for API request:', error);
    }
  }
  
  console.log('📋 Request headers:', defaultOptions.headers);
  console.log('🔍 Environment check:', {
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL
  });
  console.log('🚀 About to call fetch...');
  
  let response: Response;
  try {
    console.log('🚀 Making fetch request with options:', {
      url,
      method: options.method || 'GET',
      headers: defaultOptions.headers,
      mode: defaultOptions.mode,
      credentials: defaultOptions.credentials,
    });

    response = await fetch(url, {
      ...defaultOptions,
      ...options,
    });
    console.log('✅ Fetch completed successfully');
    console.log('📡 Response status:', response.status, response.statusText);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
  } catch (fetchError) {
    console.error('❌ Fetch failed with error:', fetchError);
    console.error('❌ Error type:', typeof fetchError);
    console.error('❌ Error constructor:', fetchError?.constructor?.name);

    // Log detailed error information
    console.error('Error details:', {
      name: fetchError instanceof Error ? fetchError.name : 'Unknown',
      message: fetchError instanceof Error ? fetchError.message : 'Unknown error',
      stack: fetchError instanceof Error ? fetchError.stack : 'No stack trace',
      cause: fetchError instanceof Error ? (fetchError as any).cause : undefined,
    });

    // Check for common network errors
    if (fetchError instanceof TypeError) {
      if (fetchError.message.includes('Failed to fetch')) {
        console.error('🌐 This is a "Failed to fetch" error, which could be due to:');
        console.error('  1. CORS policy blocking the request');
        console.error('  2. Network connectivity issues');
        console.error('  3. SSL/TLS certificate issues');
        console.error('  4. Server being unavailable');
        console.error('  5. Browser blocking the request');

        // Try to provide more specific guidance
        console.error('🔍 Debugging steps:');
        console.error('  1. Check browser Network tab for the actual error');
        console.error('  2. Check if the API endpoint is accessible via curl');
        console.error('  3. Verify CORS headers on the server');
        console.error('  4. Check if authentication headers are being sent correctly');

        throw new Error(`Network error: Unable to reach API at ${url}. This is likely a CORS, network, or authentication issue. Check browser console for details.`);
      }
    }

    // Re-throw the original error for other types
    throw fetchError;
  }

  if (!response.ok) {
    const errorText = await response.text();
    console.error('❌ API request failed:', response.status, response.statusText, errorText);
    
    // Handle specific HTTP error codes
    if (response.status === 401) {
      throw new Error(`Authentication failed: The API requires valid authentication. Please sign in to access fund data. (${response.status} ${response.statusText})`);
    } else if (response.status === 403) {
      throw new Error(`Access forbidden: You don't have permission to access this resource. (${response.status} ${response.statusText})`);
    } else if (response.status === 500) {
      throw new Error(`Server error: The API server encountered an internal error. Please try again later. (${response.status} ${response.statusText})`);
    } else {
      throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }
  }

  const jsonResponse = await response.json();
  console.log('📦 API response data:', jsonResponse);
  return jsonResponse;
}

// Helper function to convert backend Fund to frontend Fund format
function convertBackendFundToFrontend(backendFund: any): Fund {
  const nav = parseFloat(backendFund.nav || '0');
  const previousNav = parseFloat(backendFund.previous_nav || (nav * 0.99).toString()); // Use actual previousNav or fallback to estimate
  const change = nav - previousNav;
  const changePercent = previousNav > 0 ? (change / previousNav) * 100 : 0;

  return {
    id: backendFund.fund_id || backendFund.id,
    name: backendFund.name || 'Unnamed Fund',
    symbol: backendFund.bloomberg_ticker || backendFund.symbol || backendFund.fund_id || 'N/A',
    type: (backendFund.fund_type || 'equity') as Fund['type'],
    category: backendFund.custom_fields?.category || 'Mixed',
    subCategory: backendFund.custom_fields?.sub_category,
    nav: nav,
    previousNav: previousNav,
    change: change,
    changePercent: changePercent,
    volume: parseInt(backendFund.custom_fields?.volume || '0'),
    aum: parseFloat(backendFund.total_assets || '0'),
    expenseRatio: parseFloat(backendFund.expense_ratio || '0'),
    minimumInvestment: parseFloat(backendFund.minimum_investment || '0'),
    riskLevel: (backendFund.risk_level || 'moderate').toLowerCase() as Fund['riskLevel'],
    rating: parseInt(backendFund.custom_fields?.rating || '0'),
    inceptionDate: backendFund.inception_date || '2020-01-01',
    fundManager: backendFund.fund_manager || 'Unknown',
    fundManagerPhoto: backendFund.fund_manager_photo,
    fundManagerIntroduction: backendFund.fund_manager_introduction,
    fundManagerExperience: backendFund.fund_manager_experience,
    description: backendFund.description || backendFund.investment_objective || 'Professional fund management.',
    performance: {
      oneMonth: parseFloat(backendFund.performance_metrics?.one_month_return || '0'),
      threeMonths: parseFloat(backendFund.performance_metrics?.three_month_return || '0'),
      sixMonths: parseFloat(backendFund.performance_metrics?.six_month_return || '0'),
      oneYear: parseFloat(backendFund.performance_metrics?.one_year_return || '0'),
      threeYears: parseFloat(backendFund.performance_metrics?.three_year_return || '0'),
      fiveYears: parseFloat(backendFund.performance_metrics?.five_year_return || '0'),
    },
    holdings: backendFund.holdings ? {
      topHoldings: Array.isArray(backendFund.holdings.topHoldings) 
        ? backendFund.holdings.topHoldings.map((holding: any) => ({
            id: holding.symbol || Math.random().toString(36).substring(2, 11),
            name: holding.name || 'Unknown',
            symbol: holding.symbol || 'N/A',
            percentage: typeof holding.percentage === 'string' ? parseFloat(holding.percentage) : holding.percentage || 0,
            shares: typeof holding.shares === 'string' ? parseFloat(holding.shares) : holding.shares,
            marketValue: typeof holding.marketValue === 'string' ? parseFloat(holding.marketValue) : holding.marketValue,
            sector: holding.sector || 'Unknown'
          }))
        : [],
      sectorAllocation: backendFund.holdings.sectorAllocation || {},
      geographicAllocation: backendFund.holdings.geographicAllocation || {},
      assetAllocation: backendFund.holdings.assetAllocation || {},
      marketCapAllocation: backendFund.holdings.marketCapAllocation || {},
      currencyAllocation: backendFund.holdings.currencyAllocation || {},
      totalHoldingsCount: backendFund.holdings.totalHoldingsCount,
      holdingsConcentration: backendFund.holdings.holdingsConcentration
    } : { topHoldings: [] },
    sectors: backendFund.holdings?.sector_allocation 
      ? Object.entries(backendFund.holdings.sector_allocation).map(([name, percentage]) => ({
          name,
          percentage: parseFloat(percentage as string),
        }))
      : [],
    createdAt: backendFund.created_at || new Date().toISOString(),
    updatedAt: backendFund.updated_at || new Date().toISOString(),
  };
}

// Helper function to convert enriched backend fund data to frontend FundDetails format
function convertBackendFundDetailsToFrontend(backendFundDetails: any): FundDetails {
  // Start with the basic fund conversion
  const basicFund = convertBackendFundToFrontend(backendFundDetails);
  
  // Use the analytics data from backend directly
  const analyticsData = backendFundDetails.analytics;

  // Debug: Log what we received from backend
  console.log('🔍 Backend analytics data:', analyticsData);

  // Add the enriched analytics data that comes from the backend
  const fundDetails: FundDetails = {
    ...basicFund,
    analytics: analyticsData || {
      kpis: {
        totalReturn: 0,
        annualizedReturn: 0,
        volatility: 0,
        sharpeRatio: 0,
        sortinoRatio: 0,
        calmarRatio: 0,
        informationRatio: 0,
        treynorRatio: 0,
        alpha: 0,
        beta: 1,
        maxDrawdown: 0,
        trackingError: 0,
      },
      riskMetrics: {
        standardDeviation: 0,
        downSideRisk: 0,
        downsideDeviation: 0,
        varRisk: 0,
        var1d95: 0,
        var1d99: 0,
        cvar1d95: 0,
        cvar1d99: 0,
        sortRatio: 0,
        calmarRatio: 0,
        correlation: 0,
      },
      valuationMetrics: {
        priceToBook: 0,
        priceToEarnings: 0,
        priceToSales: 0,
        priceToCashFlow: 0,
        enterpriseValue: 0,
        evToRevenue: 0,
        evToEbitda: 0,
        returnOnEquity: 0,
        returnOnAssets: 0,
        debtToEquity: 0,
        dividendYield: 0,
        bookValuePerShare: 0,
      },
      technicalIndicators: {
        sma20: 0,
        sma50: 0,
        sma200: 0,
        rsi14: 50,
        macdLine: 0,
        macdSignal: 0,
        bollingerUpper: 0,
        bollingerLower: 0,
        vwap: 0,
        supportLevel: 0,
        resistanceLevel: 0,
      },
      assetAllocation: {
        stocks: 0,
        bonds: 0,
        cash: 0,
        other: 0,
      },
      geographicAllocation: {
        domestic: 0,
        international: 0,
        emerging: 0,
      },
      marketCapAllocation: {
        largeCap: 0,
        midCap: 0,
        smallCap: 0,
      },
      currencyAllocation: {},
      topHoldings: [],
      sectorAllocation: [],
    },
    historicalData: backendFundDetails.historicalData || [],
    currentPriceData: backendFundDetails.currentPriceData || {
      fundId: basicFund.id,
      asOf: new Date().toISOString(),
    },
    marketDataSummary: backendFundDetails.marketDataSummary || {
      lastUpdated: new Date(),
      dataSources: {},
      overallQuality: 'unknown' as const,
    },
    primaryBenchmark: backendFundDetails.primaryBenchmark || {
      benchmarkId: 'default',
      name: 'Default Benchmark',
      symbol: 'DEFAULT',
      asOf: new Date().toISOString(),
      currentValue: 100,
    },
    secondaryBenchmarks: backendFundDetails.secondaryBenchmarks || [],
    benchmark: backendFundDetails.benchmark || {
      name: 'NIFTY 50',
      symbol: 'NIFTY50',
      performance: {
        oneMonth: 0,
        threeMonths: 0,
        sixMonths: 0,
        oneYear: 0,
        threeYears: 0,
        fiveYears: 0,
      },
    },
    documents: backendFundDetails.documents || [],
  };
  
  return fundDetails;
}

// Helper function to convert frontend Fund to backend Fund format
function convertFrontendFundToBackend(frontendFund: Partial<Fund>, isCreating: boolean = false): any {
  const backendData: any = {};

  // For new fund creation, generate a unique fund_id if not provided
  if (isCreating && !frontendFund.id) {
    // Generate a unique fund ID based on name and timestamp
    const timestamp = Date.now();
    const namePrefix = frontendFund.name
      ? frontendFund.name.replace(/[^a-zA-Z0-9]/g, '').substring(0, 10).toUpperCase()
      : 'FUND';
    backendData.fund_id = `${namePrefix}-${timestamp}`;
  } else if (frontendFund.id) {
    backendData.fund_id = frontendFund.id;
  }
  if (frontendFund.name) {backendData.name = frontendFund.name;}
  if (frontendFund.type) {backendData.fund_type = frontendFund.type;}
  if (frontendFund.nav) {backendData.nav = frontendFund.nav.toString();}
  if (frontendFund.previousNav) {backendData.previous_nav = frontendFund.previousNav.toString();}
  if (frontendFund.aum) {backendData.total_assets = frontendFund.aum.toString();}
  if (frontendFund.expenseRatio) {backendData.expense_ratio = frontendFund.expenseRatio.toString();}
  if (frontendFund.minimumInvestment) {backendData.minimum_investment = frontendFund.minimumInvestment.toString();}
  if (frontendFund.riskLevel) {
    const riskMap: Record<Fund['riskLevel'], string> = {
      'very_low': 'very_low',
      'low': 'low',
      'moderate': 'moderate',
      'high': 'high',
      'very_high': 'very_high',
    };
    backendData.risk_level = riskMap[frontendFund.riskLevel];
  }
  if (frontendFund.fundManager) {backendData.fund_manager = frontendFund.fundManager;}
  if (frontendFund.fundManagerPhoto) {backendData.fund_manager_photo = frontendFund.fundManagerPhoto;}
  if (frontendFund.fundManagerIntroduction) {backendData.fund_manager_introduction = frontendFund.fundManagerIntroduction;}
  if (frontendFund.fundManagerExperience) {backendData.fund_manager_experience = frontendFund.fundManagerExperience;}
  if (frontendFund.description) {backendData.description = frontendFund.description;}
  if (frontendFund.symbol) {backendData.bloomberg_ticker = frontendFund.symbol;}
  
  // Add custom fields for frontend-specific data
  backendData.custom_fields = {
    category: frontendFund.category,
    sub_category: frontendFund.subCategory,
    volume: frontendFund.volume?.toString(),
    rating: frontendFund.rating?.toString(),
  };
  
  // Add analytics data if present (KPI & Risk Metrics)
  if ((frontendFund as any).analytics) {
    backendData.analytics = (frontendFund as any).analytics;
  }
  
  // Add holdings data if present
  if ((frontendFund as any).holdings) {
    backendData.holdings = (frontendFund as any).holdings;
  }
  
  return backendData;
}

// Parse market data functions
const parseValuationMetrics = (data: any) => {
  if (!data) {return {};}
  return {
    priceToBook: parseFloat(data.price_to_book) || 0,
    priceToEarnings: parseFloat(data.price_to_earnings) || 0,
    priceToSales: parseFloat(data.price_to_sales) || 0,
    priceToCashFlow: parseFloat(data.price_to_cash_flow) || 0,
    enterpriseValue: parseFloat(data.enterprise_value) || 0,
    evToRevenue: parseFloat(data.ev_to_revenue) || 0,
    evToEbitda: parseFloat(data.ev_to_ebitda) || 0,
    returnOnEquity: parseFloat(data.return_on_equity) || 0,
    returnOnAssets: parseFloat(data.return_on_assets) || 0,
    debtToEquity: parseFloat(data.debt_to_equity) || 0,
    dividendYield: parseFloat(data.dividend_yield) || 0,
    bookValuePerShare: parseFloat(data.book_value_per_share) || 0,
  };
};

const parseTechnicalIndicators = (data: any) => {
  if (!data) {return {};}
  return {
    sma20: parseFloat(data.sma_20) || 0,
    sma50: parseFloat(data.sma_50) || 0,
    sma200: parseFloat(data.sma_200) || 0,
    rsi14: parseFloat(data.rsi_14) || 50,
    macdLine: parseFloat(data.macd_line) || 0,
    macdSignal: parseFloat(data.macd_signal) || 0,
    bollingerUpper: parseFloat(data.bollinger_upper) || 0,
    bollingerLower: parseFloat(data.bollinger_lower) || 0,
    vwap: parseFloat(data.vwap) || 0,
    supportLevel: parseFloat(data.support_level) || 0,
    resistanceLevel: parseFloat(data.resistance_level) || 0,
  };
};

const parseRiskAnalytics = (data: any) => {
  if (!data) {return {};}
  return {
    var1d95: parseFloat(data.var_1d_95) || 0,
    var1d99: parseFloat(data.var_1d_99) || 0,
    cvar1d95: parseFloat(data.cvar_1d_95) || 0,
    cvar1d99: parseFloat(data.cvar_1d_99) || 0,
    sharpeRatio: parseFloat(data.sharpe_ratio) || 0,
    sortinoRatio: parseFloat(data.sortino_ratio) || 0,
    calmarRatio: parseFloat(data.calmar_ratio) || 0,
    maxDrawdown: parseFloat(data.max_drawdown) || 0,
    volatility: parseFloat(data.volatility) || 0,
    beta: parseFloat(data.beta) || 1,
    correlation: parseFloat(data.correlation) || 0,
  };
};

// Fund API functions
export const fundApi = {
  // Get all funds
  getFunds: async (pageSize: number = 100): Promise<ApiResponse<Fund[]>> => {
    console.log('🔍 getFunds called', { API_BASE_URL });

    try {
      console.log('🚀 Making API request to /funds...');

      // Try direct API call first
      let response;
      try {
        console.log('🔄 Attempting direct API call to /funds...');
        response = await apiRequest<{
          success: boolean;
          message: string;
          data: {
            funds: any[];
          };
        }>(`/funds?page_size=${pageSize}`);
      } catch (directApiError) {
        console.warn('❌ Direct API call failed, trying proxy endpoint...', directApiError);

        // If direct API fails (likely CORS), try the proxy endpoint
        try {
          console.log('🔄 Attempting proxy API call...');
          const proxyResponse = await fetch(`/api/proxy/funds?page_size=${pageSize}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
          });

          if (!proxyResponse.ok) {
            const errorText = await proxyResponse.text();

            // Check if it's a token expiration error
            if (proxyResponse.status === 401) {
              try {
                const errorData = JSON.parse(errorText);
                if (errorData.requiresSignIn) {
                  console.warn('🔄 Token expired, redirecting to sign in...');
                  // Redirect to sign in page
                  window.location.href = '/auth/signin?callbackUrl=' + encodeURIComponent(window.location.href);
                  throw new Error('Authentication required - redirecting to sign in');
                }
              } catch (e) {
                // If parsing fails, continue with normal error handling
              }
            }

            throw new Error(`Proxy API failed: ${proxyResponse.status} ${proxyResponse.statusText} - ${errorText}`);
          }

          response = await proxyResponse.json();
          console.log('✅ Proxy API response received:', response);
        } catch (proxyError) {
          console.error('❌ Proxy API also failed:', proxyError);
          throw directApiError; // Throw the original error
        }
      }

      console.log('✅ API response received:', response);
      console.log('📊 Response success flag:', response.success);
      console.log('📊 Response message:', response.message);
      
      // The backend response structure is: { data: { funds: [...] } }
      // We need to extract the funds array and create our expected format
      if (!response.data || !response.data.funds || !Array.isArray(response.data.funds)) {
        throw new Error('Invalid API response structure: expected data.funds to be an array');
      }
      
      console.log('📊 Raw funds data sample:', response.data.funds[0]);
      
      const funds = response.data.funds.map((fund: any, index: number) => {
        try {
          console.log(`🔄 Converting fund ${index}:`, fund.name || fund.fund_id);
          const converted = convertBackendFundToFrontend(fund);
          console.log(`✅ Converted fund ${index}:`, converted.name, converted.nav);
          return converted;
        } catch (error) {
          console.error(`❌ Failed to convert fund at index ${index}:`, fund, error);
          throw new Error(`Failed to convert fund "${fund.name || fund.fund_id}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      });

      console.log('✅ Successfully converted', funds.length, 'funds');
      console.log('📊 Sample converted fund:', funds[0]);
      
      // Return the expected format for the frontend
      return {
        data: funds,
        message: `Retrieved ${funds.length} funds successfully`,
        success: true, // Since we got here without throwing, it's successful
      };
    } catch (error) {
      console.error('❌ All API attempts failed:', error);
      throw error; // Re-throw the actual error
    }
  },

  // Get paginated funds
  getFundsPaginated: async (
    page: number = 1,
    limit: number = 20,
    filters?: Partial<{
      search: string;
      type: string;
      category: string;
      riskLevel: string;
    }>
  ): Promise<PaginatedResponse<Fund>> => {
    console.log('🔍 getFundsPaginated called');

    try {
      const queryParams = new URLSearchParams({
        page_size: limit.toString(),
        ...(filters?.search && { search: filters.search }),
        ...(filters?.type && { fund_type: filters.type }),
      });

      console.log('🚀 Making paginated API request to /funds with params:', queryParams.toString());
      const response = await apiRequest<{
        success: boolean;
        message: string;
        data: {
          funds: any[];
          pagination: {
            page: number;
            page_size: number;
            total_count: number;
            has_more: boolean;
          };
        };
      }>(`/funds?${queryParams}`);

      console.log('✅ AWS API paginated response received:', response);
      const funds = response.data.funds.map(convertBackendFundToFrontend);
      
      return {
        data: funds,
        message: response.message || 'Funds fetched successfully',
        success: response.success,
        pagination: {
          page: response.data.pagination.page,
          limit: response.data.pagination.page_size,
          total: response.data.pagination.total_count,
          totalPages: Math.ceil(response.data.pagination.total_count / response.data.pagination.page_size),
        },
      };
    } catch (error) {
      console.error('❌ AWS API paginated request failed:', error);
      throw error;
    }
  },

  // Get fund by ID
  getFundById: async (id: string): Promise<ApiResponse<Fund>> => {
    const response = await apiRequest<{
      success?: boolean;
      message?: string;
      data: any;
      timestamp?: string;
    }>(`/funds/${id}`);

    // Check if we have data (backend may not include success field)
    if (response.data) {
      const fund = convertBackendFundToFrontend(response.data);

      return {
        data: fund,
        message: response.message || 'Fund fetched successfully',
        success: true, // Set success to true if we have data
      };
    } else {
      throw new Error(response.message || 'No fund data received from API');
    }
  },

  // Search funds
  searchFunds: async (query: string): Promise<ApiResponse<Fund[]>> => {
    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: {
        funds: any[];
      };
    }>(`/funds?search=${encodeURIComponent(query)}`);

    const funds = response.data.funds.map(convertBackendFundToFrontend);
    
    return {
      data: funds,
      message: response.message || 'Search completed successfully',
      success: response.success,
    };
  },

  // Get enhanced market data for a fund
  getFundMarketData: async (fundId: string): Promise<ApiResponse<any>> => {
    console.log('🔍 getFundMarketData called for fund:', fundId);

    const response = await apiRequest<{
      success: boolean;
      data: any;
      message?: string;
    }>(`/funds/${fundId}/market-data`);

    console.log('✅ Market data response received:', response);

    return {
      success: true,
      data: response.data,
      message: response.message || 'Market data retrieved successfully',
    };
  },

  // Submit market data input
  submitMarketDataInput: async (fundId: string, marketData: any): Promise<ApiResponse<any>> => {
    console.log('🔍 submitMarketDataInput called for fund:', fundId);

    const response = await apiRequest<{
      success: boolean;
      data: any;
      message?: string;
    }>(`/funds/${fundId}/market-data`, {
      method: 'POST',
      body: JSON.stringify(marketData),
    });

    console.log('✅ Market data submission response received:', response);

    return {
      success: true,
      data: response.data,
      message: response.message || 'Market data submitted successfully',
    };
  },

  // Get detailed fund information
  getFundDetails: async (id: string): Promise<ApiResponse<FundDetails>> => {
    // Use the enhanced /funds/{id}/details endpoint for enriched analytics
    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: any;
    }>(`/funds/${id}/details`);

    // Convert the backend data to frontend format
    let fundDetails = convertBackendFundDetailsToFrontend(response.data);

    // Try to fetch enhanced market data
    try {
      console.log('🔍 Fetching enhanced market data for fund:', id);
      const marketDataResponse = await fundApi.getFundMarketData(id);

      if (marketDataResponse.success && marketDataResponse.data) {
        console.log('✅ Enhanced market data retrieved:', marketDataResponse.data);

        // Integrate market data into fund details
        fundDetails = {
          ...fundDetails,
          currentPriceData: marketDataResponse.data.price_data,
          marketDataSummary: {
            lastUpdated: marketDataResponse.data.last_updated,
            dataSources: marketDataResponse.data.data_sources || {},
            overallQuality: marketDataResponse.data.overall_quality || 'unknown',
          },
          analytics: {
            ...fundDetails.analytics,
            valuationMetrics: {
              ...fundDetails.analytics.valuationMetrics,
              ...parseValuationMetrics(marketDataResponse.data.valuation_metrics),
            },
            technicalIndicators: {
              ...fundDetails.analytics.technicalIndicators,
              ...parseTechnicalIndicators(marketDataResponse.data.technical_indicators),
            },
            riskMetrics: {
              ...fundDetails.analytics.riskMetrics,
              ...parseRiskAnalytics(marketDataResponse.data.risk_analytics),
            },
          },
        };

        console.log('✅ Enhanced fund details with market data:', fundDetails);
      } else {
        console.log('⚠️ Could not fetch enhanced market data, using basic fund details');
      }
    } catch (marketDataError) {
      console.warn('⚠️ Failed to fetch enhanced market data:', marketDataError);
      // Continue with basic fund details
    }

    return {
      data: fundDetails,
      message: response.message || 'Fund details fetched successfully',
      success: response.success,
    };
  },

  // Get fund performance chart data
  getFundPerformanceChart: async (
    id: string,
    period: TimePeriod,
    includeBenchmark: boolean = true
  ): Promise<ApiResponse<FundPerformanceChart>> => {
    const params = new URLSearchParams({
      period,
      includeBenchmark: includeBenchmark.toString(),
    });
    return apiRequest<ApiResponse<FundPerformanceChart>>(`/funds/${id}/performance?${params}`);
  },

  // Get fund historical data for specific period
  getFundHistoricalData: async (
    id: string,
    period: TimePeriod
  ): Promise<ApiResponse<ChartDataPoint[]>> => {
    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: {
        timePeriod: string;
        data: ChartDataPoint[];
        benchmarkData?: ChartDataPoint[];
      };
    }>(`/funds/${id}/historical?period=${period}`);

    return {
      data: response.data.data,
      message: response.message || 'Historical data fetched successfully',
      success: response.success,
    };
  },

  // Update fund
  updateFund: async (id: string, fundData: Partial<Fund>): Promise<ApiResponse<FundDetails>> => {
    // Convert frontend fund data to backend format
    const backendFundData = convertFrontendFundToBackend(fundData);
    
    // Debug: Log the data being sent to API
    console.log('🚀 Sending fund update data to API:', backendFundData);
    if (backendFundData.holdings) {
      console.log('📈 Holdings data included:', backendFundData.holdings);
    } else {
      console.log('⚠️  No holdings data in request');
    }
    
    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: any;
    }>(`/funds/${id}`, {
      method: 'PUT',
      body: JSON.stringify(backendFundData),
    });

    // Convert the response to FundDetails format instead of just Fund
    const fundDetails = convertBackendFundDetailsToFrontend(response.data);
    
    return {
      data: fundDetails,
      message: response.message || 'Fund updated successfully',
      success: true,
    };
  },

  // Create new fund
  createFund: async (fundData: Partial<Fund>): Promise<ApiResponse<FundDetails>> => {
    // Convert frontend fund data to backend format
    const backendFundData = convertFrontendFundToBackend(fundData, true);
    
    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: any;
    }>('/funds', {
      method: 'POST',
      body: JSON.stringify(backendFundData),
    });

    // Backend returns { data: { fund: { ... } } }
    const responseFundData = response.data.fund || response.data;
    const fund = convertBackendFundToFrontend(responseFundData);

    // For newly created funds, create fund details directly
    const fundDetails: FundDetails = {
      ...fund,
      analytics: {
        kpis: {
          totalReturn: 0,
          annualizedReturn: 0,
          volatility: 0,
          sharpeRatio: 0,
          sortinoRatio: 0,
          calmarRatio: 0,
          informationRatio: 0,
          treynorRatio: 0,
          alpha: 0,
          beta: 1,
          maxDrawdown: 0,
          trackingError: 0,
        },
        riskMetrics: {
          standardDeviation: 0,
          downSideRisk: 0,
          downsideDeviation: 0,
          varRisk: 0,
          var1d95: 0,
          var1d99: 0,
          cvar1d95: 0,
          cvar1d99: 0,
          sortRatio: 0,
          calmarRatio: 0,
          correlation: 0,
        },
        valuationMetrics: {
          priceToBook: 0,
          priceToEarnings: 0,
          priceToSales: 0,
          priceToCashFlow: 0,
          enterpriseValue: 0,
          evToRevenue: 0,
          evToEbitda: 0,
          returnOnEquity: 0,
          returnOnAssets: 0,
          debtToEquity: 0,
          dividendYield: 0,
          bookValuePerShare: 0,
        },
        technicalIndicators: {
          sma20: 0,
          sma50: 0,
          sma200: 0,
          rsi14: 50,
          macdLine: 0,
          macdSignal: 0,
          bollingerUpper: 0,
          bollingerLower: 0,
          vwap: 0,
          supportLevel: 0,
          resistanceLevel: 0,
        },
        assetAllocation: {
          stocks: 0,
          bonds: 0,
          cash: 0,
          other: 0,
        },
        geographicAllocation: {
          domestic: 100,
          international: 0,
          emerging: 0,
        },
        marketCapAllocation: {
          largeCap: 0,
          midCap: 0,
          smallCap: 0,
        },
        currencyAllocation: {
          USD: 100,
        },
        topHoldings: fund.holdings?.topHoldings?.map((holding: any) => ({
          ...holding,
          marketValue: 0,
          sector: 'Unknown',
          country: 'Unknown',
          currency: 'USD',
        })) || [],
        sectorAllocation: fund.sectors?.map(sector => ({
          ...sector,
          marketValue: 0,
          change: 0,
        })) || [],
      },
      historicalData: [],
      currentPriceData: {
        fundId: fund.id,
        asOf: new Date().toISOString(),
        nav: {
          timestamp: new Date().toISOString(),
          value: fund.nav,
          source: 'fund_company' as const,
          quality: 'good' as const,
          currency: 'USD',
        },
        volume: fund.volume,
        priceChange1d: fund.change,
        priceChange1dPct: fund.changePercent,
      },
      marketDataSummary: {
        lastUpdated: new Date().toISOString(),
        dataSources: {},
        overallQuality: 'good' as const,
      },
      primaryBenchmark: {
        benchmarkId: 'nifty50',
        name: 'NIFTY 50',
        symbol: 'NIFTY50',
        asOf: new Date().toISOString(),
        currentValue: 19500,
      },
      secondaryBenchmarks: [],
      benchmark: {
        name: 'NIFTY 50',
        symbol: 'NIFTY50',
        performance: {
          oneMonth: 0,
          threeMonths: 0,
          sixMonths: 0,
          oneYear: 0,
          threeYears: 0,
          fiveYears: 0,
        },
      },
      documents: [],
    };
    
    return {
      data: fundDetails,
      message: response.message || 'Fund created successfully',
      success: response.success,
    };
  },

  // Process PDF files synchronously
  submitPDFJobs: async (files: File[]): Promise<ApiResponse<{jobId: string, status: string, fileName: string, result?: any}[]>> => {
    console.log('🔍 submitPDFJobs called', {
      fileCount: files.length,
    });

    console.log('🚀 Processing PDF files synchronously...');

    // Process files synchronously
    const jobSubmissions: {jobId: string, status: string, fileName: string, result?: any}[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      try {
        console.log(`📄 Processing file ${i + 1}/${files.length}: ${file.name}`);

        // Create FormData for single file upload
        const formData = new FormData();
        formData.append('file', file);

        // Submit file to the PDF extraction endpoint via proxy (synchronous processing)
        const submitResponse = await fetch(`/api/proxy/funds/extract-pdf?save=true`, {
          method: 'POST',
          body: formData,
          headers: {
            'Accept': 'application/json',
            // No Authorization header needed - proxy handles authentication
            // No Content-Type header needed - FormData sets it automatically with boundary
          },
          credentials: 'include', // Use session cookies for authentication via proxy
        });

        if (!submitResponse.ok) {
          const errorText = await submitResponse.text();
          console.error(`❌ Failed to process ${file.name}:`, errorText);
          throw new Error(`Failed to process ${file.name}: ${errorText}`);
        }

        const submitResult = await submitResponse.json();
        console.log(`✅ Successfully processed ${file.name}`);

        // Since this is synchronous processing, we get the result immediately
        jobSubmissions.push({
          jobId: `sync-${Date.now()}-${i}`, // Generate a fake job ID for compatibility
          status: 'completed', // Mark as completed since processing is synchronous
          fileName: file.name,
          result: submitResult.data // Store the extracted fund data
        });

      } catch (error) {
        console.error(`❌ Failed to process ${file.name}:`, error);
        throw error;
      }
    }

    console.log(`✅ Successfully processed ${jobSubmissions.length} PDF files`);

    return {
      data: jobSubmissions,
      message: `Successfully submitted ${jobSubmissions.length} PDF processing jobs`,
      success: true,
    };
  },

  // Check status of PDF processing jobs
  checkPDFJobStatus: async (jobId: string): Promise<ApiResponse<any>> => {
    console.log('🔍 checkPDFJobStatus called for job:', jobId);

    // Get authentication session
    const session = await getSession();
    if (!session?.accessToken) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`${API_BASE_URL}/pdf-jobs/${jobId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.accessToken}`,
        'Accept': 'application/json',
      },
      mode: 'cors',
      credentials: 'omit',
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to get job status: ${errorText}`);
    }

    const result = await response.json();
    return {
      data: result.data,
      message: result.message,
      success: true,
    };
  },

  // Bulk PDF upload for fund data (synchronous processing)
  bulkUploadPDFs: async (files: File[]): Promise<ApiResponse<UploadResult[]>> => {
    console.log('🔍 bulkUploadPDFs called', {
      fileCount: files.length,
    });

    // Step 1: Process all files synchronously
    console.log('📤 Step 1: Processing files synchronously...');
    const jobSubmissionResponse = await fundApi.submitPDFJobs(files);

    if (!jobSubmissionResponse.success || !jobSubmissionResponse.data) {
      throw new Error('Failed to process PDF files');
    }

    const jobs = jobSubmissionResponse.data;
    console.log(`✅ Processed ${jobs.length} files synchronously`);

    // Step 2: Convert synchronous results to upload results
    console.log('📋 Step 2: Converting results...');
    const uploadResults: UploadResult[] = [];

    for (const job of jobs) {
      if (job.status === 'completed' && job.result) {
        // Job completed successfully
        // Convert backend fund format to frontend format
        const fundData = convertBackendFundToFrontend(job.result);
        
        const result: UploadResult = {
          id: `result-${job.jobId}`,
          fileName: job.fileName,
          status: 'success',
          message: 'Fund data extracted successfully',
          fundData: fundData,
          processingTime: 0, // Synchronous processing
        };
        uploadResults.push(result);
        console.log(`✅ Successfully processed ${job.fileName}`);
      } else {
        // Job failed or incomplete
        const result: UploadResult = {
          id: `result-${job.jobId}`,
          fileName: job.fileName,
          status: 'error',
          message: 'Failed to extract fund data',
          error: 'Processing failed or incomplete',
          processingTime: 0,
        };
        uploadResults.push(result);
        console.log(`❌ Failed to process ${job.fileName}`);
      }
    }

    console.log(`🎉 Completed processing ${uploadResults.length} files`);

    return {
      data: uploadResults,
      message: `Processed ${uploadResults.length} PDF files`,
      success: true,
    };
  },

  // Snapshot API methods

  // List all snapshots for a fund
  listFundSnapshots: async (
    fundId: string,
    params?: FundSnapshotListParams
  ): Promise<ApiResponse<FundSnapshotResponse[]>> => {
    console.log('🔍 listFundSnapshots called for fund:', fundId, 'with params:', params);

    const queryParams = new URLSearchParams();
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());

    const endpoint = `/funds/${fundId}/snapshots${queryParams.toString() ? `?${queryParams}` : ''}`;

    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: {
        snapshots: FundSnapshotResponse[];
        total_count?: number;
      };
    }>(endpoint);

    console.log('✅ Fund snapshots response received:', response);

    return {
      data: response.data.snapshots || [],
      message: response.message || 'Fund snapshots fetched successfully',
      success: response.success,
    };
  },

  // Get specific month's snapshot for a fund
  getFundSnapshot: async (
    fundId: string,
    month: string
  ): Promise<ApiResponse<FundSnapshotResponse>> => {
    console.log('🔍 getFundSnapshot called for fund:', fundId, 'month:', month);

    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: FundSnapshotResponse;
    }>(`/funds/${fundId}/snapshots/${month}`);

    console.log('✅ Fund snapshot response received:', response);

    return {
      data: response.data,
      message: response.message || 'Fund snapshot fetched successfully',
      success: response.success,
    };
  },

  // Create or update monthly snapshot for a fund
  createFundSnapshot: async (
    fundId: string,
    month: string,
    snapshotData: FundSnapshotCreate
  ): Promise<ApiResponse<FundSnapshotResponse>> => {
    console.log('🔍 createFundSnapshot called for fund:', fundId, 'month:', month);
    console.log('📊 Snapshot data:', snapshotData);

    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: FundSnapshotResponse;
    }>(`/funds/${fundId}/snapshots/${month}`, {
      method: 'POST',
      body: JSON.stringify(snapshotData),
    });

    console.log('✅ Fund snapshot creation response received:', response);

    return {
      data: response.data,
      message: response.message || 'Fund snapshot created successfully',
      success: response.success,
    };
  },

  // Delete a specific snapshot
  deleteFundSnapshot: async (
    fundId: string,
    month: string
  ): Promise<ApiResponse<{ deleted: boolean }>> => {
    console.log('🔍 deleteFundSnapshot called for fund:', fundId, 'month:', month);

    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: { deleted: boolean };
    }>(`/funds/${fundId}/snapshots/${month}`, {
      method: 'DELETE',
    });

    console.log('✅ Fund snapshot deletion response received:', response);

    return {
      data: response.data,
      message: response.message || 'Fund snapshot deleted successfully',
      success: response.success,
    };
  },
};

// Helper function to convert backend portfolio data to frontend format
function convertBackendPortfolioToFrontend(backendPortfolio: any): Portfolio {
  return {
    portfolioId: backendPortfolio.portfolio_id || backendPortfolio.portfolioId,
    name: backendPortfolio.name || 'Unnamed Portfolio',
    description: backendPortfolio.description || '',
    portfolioType: backendPortfolio.portfolio_type || backendPortfolio.portfolioType || 'personal',
    status: backendPortfolio.status || 'active',
    userId: backendPortfolio.user_id || backendPortfolio.userId,
    baseCurrency: backendPortfolio.base_currency || backendPortfolio.baseCurrency || 'USD',
    inceptionDate: backendPortfolio.inception_date || backendPortfolio.inceptionDate || new Date().toISOString(),
    totalValue: parseFloat(backendPortfolio.total_value || backendPortfolio.totalValue || '0'),
    totalCostBasis: parseFloat(backendPortfolio.total_cost_basis || backendPortfolio.totalCostBasis || '0'),
    cashBalance: parseFloat(backendPortfolio.cash_balance || backendPortfolio.cashBalance || '0'),
    totalGainLoss: parseFloat(backendPortfolio.total_gain_loss || backendPortfolio.totalGainLoss || '0'),
    totalGainLossPct: parseFloat(backendPortfolio.total_gain_loss_pct || backendPortfolio.totalGainLossPct || '0'),
    holdings: Array.isArray(backendPortfolio.holdings) 
      ? backendPortfolio.holdings.map((holding: any) => ({
          fundId: holding.fund_id || holding.fundId,
          fundName: holding.fund_name || holding.fundName,
          fundSymbol: holding.fund_symbol || holding.fundSymbol,
          shares: parseFloat(holding.shares || '0'),
          averageCost: parseFloat(holding.average_cost || holding.averageCost || '0'),
          currentPrice: parseFloat(holding.current_price || holding.currentPrice || '0'),
          marketValue: parseFloat(holding.market_value || holding.marketValue || '0'),
          costBasis: parseFloat(holding.cost_basis || holding.costBasis || '0'),
          unrealizedGainLoss: parseFloat(holding.unrealized_gain_loss || holding.unrealizedGainLoss || '0'),
          unrealizedGainLossPct: parseFloat(holding.unrealized_gain_loss_pct || holding.unrealizedGainLossPct || '0'),
          weight: parseFloat(holding.weight || '0'),
          firstPurchaseDate: holding.first_purchase_date || holding.firstPurchaseDate || new Date().toISOString(),
          lastUpdated: holding.last_updated || holding.lastUpdated || new Date().toISOString(),
        }))
      : [],
    recentTransactions: Array.isArray(backendPortfolio.recent_transactions) 
      ? backendPortfolio.recent_transactions.map((tx: any) => ({
          transactionId: tx.transaction_id || tx.transactionId,
          fundId: tx.fund_id || tx.fundId,
          fundName: tx.fund_name || tx.fundName,
          fundSymbol: tx.fund_symbol || tx.fundSymbol,
          transactionType: tx.transaction_type || tx.transactionType,
          transactionDate: tx.transaction_date || tx.transactionDate,
          settlementDate: tx.settlement_date || tx.settlementDate,
          shares: tx.shares !== null ? parseFloat(tx.shares || '0') : undefined,
          price: tx.price !== null ? parseFloat(tx.price || '0') : undefined,
          amount: parseFloat(tx.amount || '0'),
          fees: parseFloat(tx.fees || '0'),
          netAmount: parseFloat(tx.net_amount || tx.netAmount || '0'),
          description: tx.description || '',
          referenceNumber: tx.reference_number || tx.referenceNumber,
          createdAt: tx.created_at || tx.createdAt || new Date().toISOString(),
        }))
      : [],
    performance: backendPortfolio.performance ? {
      totalReturn: parseFloat(backendPortfolio.performance.total_return || backendPortfolio.performance.totalReturn || '0'),
      totalReturnPct: parseFloat(backendPortfolio.performance.total_return_pct || backendPortfolio.performance.totalReturnPct || '0'),
      oneMonthReturn: parseFloat(backendPortfolio.performance.one_month_return || backendPortfolio.performance.oneMonthReturn || '0'),
      threeMonthReturn: parseFloat(backendPortfolio.performance.three_month_return || backendPortfolio.performance.threeMonthReturn || '0'),
      sixMonthReturn: parseFloat(backendPortfolio.performance.six_month_return || backendPortfolio.performance.sixMonthReturn || '0'),
      oneYearReturn: parseFloat(backendPortfolio.performance.one_year_return || backendPortfolio.performance.oneYearReturn || '0'),
      threeYearReturn: parseFloat(backendPortfolio.performance.three_year_return || backendPortfolio.performance.threeYearReturn || '0'),
      fiveYearReturn: parseFloat(backendPortfolio.performance.five_year_return || backendPortfolio.performance.fiveYearReturn || '0'),
      inceptionReturn: parseFloat(backendPortfolio.performance.inception_return || backendPortfolio.performance.inceptionReturn || '0'),
      volatility: parseFloat(backendPortfolio.performance.volatility || '0'),
      sharpeRatio: parseFloat(backendPortfolio.performance.sharpe_ratio || backendPortfolio.performance.sharpeRatio || '0'),
      maxDrawdown: parseFloat(backendPortfolio.performance.max_drawdown || backendPortfolio.performance.maxDrawdown || '0'),
      benchmarkReturn: parseFloat(backendPortfolio.performance.benchmark_return || backendPortfolio.performance.benchmarkReturn || '0'),
      alpha: parseFloat(backendPortfolio.performance.alpha || '0'),
      beta: parseFloat(backendPortfolio.performance.beta || '0'),
      asOfDate: backendPortfolio.performance.as_of_date || backendPortfolio.performance.asOfDate || new Date().toISOString(),
    } : {
      totalReturn: 0, totalReturnPct: 0, oneMonthReturn: 0,
      threeMonthReturn: 0, sixMonthReturn: 0, oneYearReturn: 0, threeYearReturn: 0, fiveYearReturn: 0,
      inceptionReturn: 0, volatility: 0, sharpeRatio: 0, maxDrawdown: 0, benchmarkReturn: 0,
      alpha: 0, beta: 0, asOfDate: new Date().toISOString(),
    },
    riskLevel: backendPortfolio.risk_level || backendPortfolio.riskLevel || 'medium',
    benchmark: backendPortfolio.benchmark || 'S&P 500',
    tags: Array.isArray(backendPortfolio.tags) ? backendPortfolio.tags : [],
    customFields: backendPortfolio.custom_fields || backendPortfolio.customFields || {},
    createdAt: backendPortfolio.created_at || backendPortfolio.createdAt || new Date().toISOString(),
    updatedAt: backendPortfolio.updated_at || backendPortfolio.updatedAt || new Date().toISOString(),
    lastRebalanced: backendPortfolio.last_rebalanced || backendPortfolio.lastRebalanced,
    holdingsCount: backendPortfolio.holdings?.length || 0,
    transactionsCount: backendPortfolio.recent_transactions?.length || 0,
  };
}

// Portfolio API functions
export const portfolioApi = {
  // Get all portfolios
  getPortfolios: async (): Promise<ApiResponse<Portfolio[]>> => {
    console.log('🔍 getPortfolios called');

    console.log('🚀 Making API request to /portfolios...');

    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: {
        portfolios: any[];
      };
    }>('/portfolios');

    console.log('✅ Portfolios API response received:', response);

    if (response.data && response.data.portfolios) {
      return {
        data: response.data.portfolios.map(convertBackendPortfolioToFrontend),
        message: response.message || 'Portfolios fetched successfully',
        success: true,
      };
    }

    throw new Error('Invalid response format from portfolios API');
  },

  // Get portfolio by ID
  getPortfolioById: async (id: string): Promise<ApiResponse<Portfolio>> => {
    const response = await apiRequest<{
      success?: boolean;
      message?: string;
      data: any;
    }>(`/portfolios/${id}`);

    if (response.data) {
      return {
        data: response.data,
        message: response.message || 'Portfolio fetched successfully',
        success: response.success !== false,
      };
    }

    throw new Error('Portfolio not found');
  },

  // Create new portfolio
  createPortfolio: async (portfolioData: PortfolioCreateRequest): Promise<ApiResponse<Portfolio>> => {
    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: any;
    }>('/portfolios', {
      method: 'POST',
      body: JSON.stringify(portfolioData),
    });

    return {
      data: response.data,
      message: response.message || 'Portfolio created successfully',
      success: response.success,
    };
  },

  // Update portfolio
  updatePortfolio: async (id: string, portfolioData: PortfolioUpdateRequest): Promise<ApiResponse<Portfolio>> => {
    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: any;
    }>(`/portfolios/${id}`, {
      method: 'PUT',
      body: JSON.stringify(portfolioData),
    });

    return {
      data: response.data,
      message: response.message || 'Portfolio updated successfully',
      success: response.success,
    };
  },

  // Delete portfolio
  deletePortfolio: async (id: string): Promise<ApiResponse<{ deleted: boolean }>> => {
    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: { deleted: boolean };
    }>(`/portfolios/${id}`, {
      method: 'DELETE',
    });

    return {
      data: response.data,
      message: response.message || 'Portfolio deleted successfully',
      success: response.success,
    };
  },

  // Add holding to portfolio
  addHolding: async (portfolioId: string, holdingData: AddHoldingRequest): Promise<ApiResponse<Portfolio>> => {
    const response = await apiRequest<{
      success: boolean;
      message: string;
      data: any;
    }>(`/portfolios/${portfolioId}/holdings`, {
      method: 'POST',
      body: JSON.stringify(holdingData),
    });

    return {
      data: response.data,
      message: response.message || 'Holding added successfully',
      success: response.success,
    };
  },
};