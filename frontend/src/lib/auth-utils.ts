/**
 * Authentication utility functions for handling sign-in and sign-out operations
 */

import { signIn, signOut } from 'next-auth/react'

/**
 * Performs a complete sign-out operation, clearing all session data and redirecting
 * @param redirectTo - The path to redirect to after sign-out (default: '/')
 */
export async function performCompleteSignOut(redirectTo: string = '/') {
  try {
    console.log('🔐 Performing complete sign-out...')
    
    // Clear any local storage items related to auth
    if (typeof window !== 'undefined') {
      // Clear any auth-related items from localStorage
      const authKeys = Object.keys(localStorage).filter(key => 
        key.includes('auth') || 
        key.includes('token') || 
        key.includes('session')
      )
      
      authKeys.forEach(key => {
        localStorage.removeItem(key)
      })
      
      // Clear any auth-related items from sessionStorage
      const sessionAuthKeys = Object.keys(sessionStorage).filter(key => 
        key.includes('auth') || 
        key.includes('token') || 
        key.includes('session')
      )
      
      sessionAuthKeys.forEach(key => {
        sessionStorage.removeItem(key)
      })
    }
    
    // Perform NextAuth sign-out with redirect
    await signOut({ 
      callbackUrl: redirectTo,
      redirect: true 
    })
    
    console.log('✅ Complete sign-out successful')
  } catch (error) {
    console.error('❌ Error during complete sign-out:', error)
    
    // Fallback: force redirect even if sign-out fails
    if (typeof window !== 'undefined') {
      window.location.href = redirectTo
    }
  }
}

/**
 * Performs a fresh sign-in operation with Cognito
 * @param redirectTo - The path to redirect to after successful sign-in
 */
export async function performFreshSignIn(redirectTo?: string) {
  try {
    console.log('🔐 Performing fresh sign-in with Cognito')
    
    // Clear any existing session data before fresh sign-in
    if (typeof window !== 'undefined') {
      // Clear any stale auth data
      const authKeys = Object.keys(localStorage).filter(key => 
        key.includes('auth') || 
        key.includes('token')
      )
      
      authKeys.forEach(key => {
        localStorage.removeItem(key)
      })
    }
    
    // Determine the callback URL
    const callbackUrl = redirectTo || window?.location?.pathname || '/funds'
    
    // Perform NextAuth sign-in with Cognito
    // When redirect is true, this function will redirect the browser
    // and won't return a JSON response to parse
    await signIn('cognito', { 
      callbackUrl,
      redirect: true 
    })
    
    // This code won't be reached when redirect is true
    console.log('✅ Fresh sign-in initiated successfully')
  } catch (error) {
    console.error('❌ Error during fresh sign-in:', error)
    throw error
  }
}

/**
 * Checks if the current session is valid and not expired
 * @param session - The NextAuth session object
 * @returns boolean indicating if the session is valid
 */
export function isSessionValid(session: any): boolean {
  if (!session || !session.accessToken) {
    return false
  }
  
  // Check if session has expiration time and if it's still valid
  if (session.expiresAt) {
    const now = Math.floor(Date.now() / 1000)
    const isExpired = now >= session.expiresAt
    
    if (isExpired) {
      console.warn('⚠️ Session has expired')
      return false
    }
  }
  
  return true
}

/**
 * Gets the sign-in provider - always returns 'cognito'
 * @returns The provider string to use for sign-in
 */
export function getPreferredSignInProvider(): string {
  return 'cognito'
}

/**
 * Handles authentication errors and provides appropriate user feedback
 * @param error - The authentication error
 * @param context - Additional context about where the error occurred
 */
export function handleAuthError(error: any, context: string = 'authentication') {
  console.error(`❌ Auth error in ${context}:`, error)
  
  // You can extend this to show user-friendly error messages
  // or trigger error reporting services
  
  const errorMessage = error?.message || error?.toString() || 'Unknown authentication error'
  
  // For now, just log the error. In a real app, you might want to:
  // - Show a toast notification
  // - Redirect to an error page
  // - Send error to monitoring service
  
  return {
    error: true,
    message: errorMessage,
    context
  }
}

/**
 * Checks for any residual authentication data in browser storage
 * @returns Object containing any found auth-related data
 */
export function checkForResidualAuthData() {
  if (typeof window === 'undefined') {
    return {
      localStorage: [],
      sessionStorage: [],
      cookies: []
    }
  }

  // Check localStorage for auth-related items
  const localStorageAuthKeys = Object.keys(localStorage).filter(key => 
    key.includes('auth') || 
    key.includes('token') || 
    key.includes('session') ||
    key.includes('nextauth') ||
    key.includes('cognito')
  )

  // Check sessionStorage for auth-related items
  const sessionStorageAuthKeys = Object.keys(sessionStorage).filter(key => 
    key.includes('auth') || 
    key.includes('token') || 
    key.includes('session') ||
    key.includes('nextauth') ||
    key.includes('cognito')
  )

  // Check cookies for auth-related items
  const cookies = document.cookie.split(';')
    .map(cookie => cookie.trim().split('=')[0])
    .filter(name => 
      name.includes('auth') || 
      name.includes('token') || 
      name.includes('session') ||
      name.includes('nextauth') ||
      name.includes('cognito')
    )

  return {
    localStorage: localStorageAuthKeys,
    sessionStorage: sessionStorageAuthKeys,
    cookies: cookies
  }
}

/**
 * Refreshes the current session if possible
 * This is a placeholder for future implementation of token refresh logic
 */
export async function refreshSession() {
  console.log('🔄 Session refresh requested (not yet implemented)')
  
  // TODO: Implement session refresh logic
  // This would typically involve:
  // 1. Getting the current session
  // 2. Using refresh token to get new access token
  // 3. Updating the session with new token
  
  return false
}
