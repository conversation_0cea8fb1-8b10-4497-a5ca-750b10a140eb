import https from "https"

import NextAuth from "next-auth"
import type { Account, User } from "next-auth"
import type { NextAuthOptions } from "next-auth"
import type { JWT } from "next-auth/jwt"
import CognitoProvider from "next-auth/providers/cognito"

declare module "next-auth" {
  interface Session {
    accessToken?: string
    idToken?: string
    expiresAt?: number
    provider?: string
    mfaEnabled?: boolean
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    accessToken?: string
    idToken?: string
    refreshToken?: string
    expiresAt?: number
    provider?: string
    mfaEnabled?: boolean
  }
}

// Create custom HTTPS agent for development
const createHttpsAgent = () => {
  if (process.env.NODE_ENV === 'development') {
    return new https.Agent({
      rejectUnauthorized: false,
      timeout: 10000,
    })
  }
  return new https.Agent({
    timeout: 10000,
  })
}

console.log('🔧 Auth Configuration:', {
  NODE_ENV: process.env.NODE_ENV,
  NEXTAUTH_URL: process.env.NEXTAUTH_URL
})

// Extract region and user pool ID from environment
const cognitoIssuer = process.env.COGNITO_ISSUER!
const userPoolId = cognitoIssuer.split('/').pop()! // Extract user pool ID from issuer URL
const region = userPoolId.split('_')[0] // Extract region from user pool ID

// Function to refresh access token using refresh token
async function refreshAccessToken(refreshToken: string) {
  try {
    console.log('🔄 Refreshing access token...');

    const response = await fetch(`${cognitoIssuer}/oauth2/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: process.env.COGNITO_CLIENT_ID!,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Token refresh failed:', response.status, errorText);
      throw new Error(`Token refresh failed: ${response.status} ${errorText}`);
    }

    const tokens = await response.json();
    console.log('✅ Token refresh successful');

    return {
      access_token: tokens.access_token,
      expires_in: tokens.expires_in || 3600, // Default to 1 hour if not provided
    };
  } catch (error) {
    console.error('❌ Error refreshing token:', error);
    throw error;
  }
}

const cognitoProviderOptions = {
  clientId: process.env.COGNITO_CLIENT_ID!,
  issuer: cognitoIssuer,
  client: { token_endpoint_auth_method: "none" as const },
};

// This is a public client (no client secret required)

// Create providers array with only Cognito
const createProviders = () => {
  console.log('🔐 Adding Cognito provider')
  return [CognitoProvider(cognitoProviderOptions)]
}



export const authOptions: NextAuthOptions = {
  providers: createProviders(),

  // Ensure we have a secret
  secret: process.env.NEXTAUTH_SECRET,

  // Add some debugging
  logger: {
    error(code, metadata) {
      console.error('🔴 NextAuth Error:', code, metadata)
    },
    warn(code) {
      console.warn('🟡 NextAuth Warning:', code)
    },
    debug(code, metadata) {
      console.log('🔍 NextAuth Debug:', code, metadata)
    }
  },

  callbacks: {
    async jwt({ token, user, account, trigger }: { token: JWT; user: User; account: Account | null; trigger?: string }) {
      try {
        console.log('🔍 JWT callback called:', { 
          hasAccount: !!account, 
          provider: account?.provider,
          trigger,
          hasUser: !!user 
        })
        
        // Store essential data to reduce cookie size for Cognito tokens
        if (account) {
          console.log('🔍 Processing account in JWT callback:', {
            provider: account.provider,
            hasAccessToken: !!account.access_token,
            hasRefreshToken: !!account.refresh_token,
            expiresAt: account.expires_at
          })
          
          // Store both access token and ID token for different use cases
          token.accessToken = account.access_token
          token.idToken = account.id_token  // Store ID token for API Gateway
          token.expiresAt = account.expires_at
          token.provider = account.provider

          // Store refresh token for token refresh
          token.refreshToken = account.refresh_token

          // Minimal MFA check without storing full token
          if (account.id_token) {
            try {
              const idTokenPayload = JSON.parse(atob(account.id_token.split('.')[1]))
              token.mfaEnabled = !!idTokenPayload.auth_time
            } catch (error) {
              console.warn('Failed to parse ID token for MFA info:', error)
              token.mfaEnabled = false
            }
          }
        }
      } catch (error) {
        console.error('❌ Error in JWT callback:', error)
        // Return token even if there's an error to prevent auth failure
      }

      // Check if token needs refresh (refresh 5 minutes before expiry)
      if (token.accessToken && token.refreshToken && token.expiresAt) {
        const timeUntilExpiry = token.expiresAt * 1000 - Date.now();
        const shouldRefresh = timeUntilExpiry < 5 * 60 * 1000; // 5 minutes

        if (shouldRefresh) {
          console.log('🔄 Access token expiring soon, attempting refresh...');
          try {
            const refreshedTokens = await refreshAccessToken(token.refreshToken);
            
            // Update token with new values
            token.accessToken = refreshedTokens.access_token;
            token.expiresAt = Math.floor(Date.now() / 1000) + refreshedTokens.expires_in;
            
            console.log('✅ Token refreshed successfully');
          } catch (error) {
            console.error('❌ Token refresh failed:', error);
            // Return null to force re-authentication
            return null;
          }
        }
      }

      return token
    },
    async session({ session, token }: { session: any; token: JWT }) {
      try {
        console.log('🔍 Session callback called:', { 
          hasSession: !!session,
          hasToken: !!token,
          tokenProvider: token?.provider 
        })
        
        // Ensure session object exists
        if (!session) {
          console.warn('⚠️ Session is null, returning null')
          return null;
        }

        // Only send minimal essential properties to reduce cookie size
        session.accessToken = token.accessToken
        session.idToken = token.idToken  // Include ID token for API Gateway
        session.expiresAt = token.expiresAt
        session.provider = token.provider
        session.mfaEnabled = token.mfaEnabled

        // Remove any large user properties to reduce cookie size
        if (session.user) {
          // Keep only essential user info
          session.user = {
            id: session.user.id,
            email: session.user.email,
            name: session.user.name,
          }
        }

        console.log('✅ Session callback completed successfully')
        return session
      } catch (error) {
        console.error('❌ Error in session callback:', error)
        return null
      }
    },
    async signIn({ user, account, profile }: { user: User; account: Account | null; profile?: any }) {
      console.log('🔐 SignIn callback called:', {
        provider: account?.provider,
        userId: user?.id,
        userEmail: user?.email
      })

      // Allow Cognito login
      if (account?.provider === 'cognito') {
        console.log('🔐 Cognito login sign-in callback - allowing sign in')
        return true
      }

      console.log('🔐 Unknown provider, denying sign in')
      return false
    },
  },
  session: {
    strategy: "jwt" as const,
    maxAge: 24 * 60 * 60, // 24 hours
  },
  jwt: {
    maxAge: 24 * 60 * 60, // 24 hours
  },
  cookies: {
    sessionToken: {
      name: 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60, // 24 hours
      },
    },
  },
  pages: {
    signIn: "/auth/signin",
    signOut: "/auth/signout",
    error: "/auth/error",
  },

  // Add custom error handling
  events: {
    async signIn(message) {
      console.log('🔍 NextAuth signIn event:', message)
    },
    async signOut(message) {
      console.log('🔍 NextAuth signOut event:', message)
    },
    async createUser(message) {
      console.log('🔍 NextAuth createUser event:', message)
    },
    async session(message) {
      console.log('🔍 NextAuth session event:', message)
    }
  },

  debug: process.env.NODE_ENV === "development",
}

// Create NextAuth instance
const handler = NextAuth(authOptions)

// Export handlers for API routes
export { handler as GET, handler as POST }

// For backward compatibility and other uses
export const auth = handler
export const signIn = handler
export const signOut = handler

// Export handlers for the new App Router syntax
export const handlers = { GET: handler, POST: handler }