/**
 * Utility functions for Cognito authentication
 */

/**
 * Generate the Cognito hosted UI login URL
 * @param callbackPath - Optional callback path (defaults to current page)
 * @returns The complete Cognito login URL
 */
export function getCognitoLoginUrl(callbackPath?: string): string {
  const cognitoDomain = 'ap-northeast-1h2kkhguat.auth.ap-northeast-1.amazoncognito.com'
  const clientId = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '2jh76f894g6lv9vrus4qbb9hu7'
  
  // Determine the callback URL
  let callbackUrl: string
  if (typeof window !== 'undefined') {
    const baseUrl = window.location.origin
    callbackUrl = encodeURIComponent(`${baseUrl}/api/auth/callback/cognito`)
  } else {
    // Fallback for server-side rendering
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'
    callbackUrl = encodeURIComponent(`${baseUrl}/api/auth/callback/cognito`)
  }
  
  const cognitoUrl = `https://${cognitoDomain}/login?` +
    `client_id=${clientId}&` +
    `response_type=code&` +
    `scope=openid+profile+email+aws.cognito.signin.user.admin&` +
    `redirect_uri=${callbackUrl}`
  
  return cognitoUrl
}

/**
 * Redirect to Cognito hosted UI login page
 * @param callbackPath - Optional callback path (defaults to current page)
 */
export function redirectToCognitoLogin(callbackPath?: string): void {
  if (typeof window !== 'undefined') {
    window.location.href = getCognitoLoginUrl(callbackPath)
  }
}

/**
 * Generate the Cognito hosted UI logout URL
 * @param redirectPath - Optional redirect path after logout (defaults to home)
 * @returns The complete Cognito logout URL
 */
export function getCognitoLogoutUrl(redirectPath: string = '/'): string {
  const cognitoDomain = 'ap-northeast-1h2kkhguat.auth.ap-northeast-1.amazoncognito.com'
  const clientId = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '2jh76f894g6lv9vrus4qbb9hu7'
  
  // Determine the logout redirect URL
  let logoutUrl: string
  if (typeof window !== 'undefined') {
    const baseUrl = window.location.origin
    logoutUrl = encodeURIComponent(`${baseUrl}${redirectPath}`)
  } else {
    // Fallback for server-side rendering
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'
    logoutUrl = encodeURIComponent(`${baseUrl}${redirectPath}`)
  }
  
  const cognitoLogoutUrl = `https://${cognitoDomain}/logout?` +
    `client_id=${clientId}&` +
    `logout_uri=${logoutUrl}`
  
  return cognitoLogoutUrl
}

/**
 * Redirect to Cognito hosted UI logout page
 * @param redirectPath - Optional redirect path after logout (defaults to home)
 */
export function redirectToCognitoLogout(redirectPath: string = '/'): void {
  if (typeof window !== 'undefined') {
    window.location.href = getCognitoLogoutUrl(redirectPath)
  }
}
