// Application constants for FundFlow

export const APP_NAME = 'FundFlow';
export const APP_DESCRIPTION = 'Personal Finance Management Application';

// API Configuration
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';
export const API_TIMEOUT = 10000; // 10 seconds

// Pagination
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;

// Transaction Categories
export const EXPENSE_CATEGORIES = [
  { id: 'food', name: 'Food & Dining', icon: '🍽️', color: '#FF6B6B' },
  { id: 'transportation', name: 'Transportation', icon: '🚗', color: '#4ECDC4' },
  { id: 'shopping', name: 'Shopping', icon: '🛍️', color: '#45B7D1' },
  { id: 'entertainment', name: 'Entertainment', icon: '🎬', color: '#96CEB4' },
  { id: 'bills', name: 'Bills & Utilities', icon: '⚡', color: '#FFEAA7' },
  { id: 'healthcare', name: 'Healthcare', icon: '🏥', color: '#DDA0DD' },
  { id: 'education', name: 'Education', icon: '📚', color: '#98D8C8' },
  { id: 'travel', name: 'Travel', icon: '✈️', color: '#F7DC6F' },
  { id: 'other', name: 'Other', icon: '📦', color: '#BDC3C7' },
] as const;

export const INCOME_CATEGORIES = [
  { id: 'salary', name: 'Salary', icon: '💼', color: '#2ECC71' },
  { id: 'freelance', name: 'Freelance', icon: '💻', color: '#3498DB' },
  { id: 'investment', name: 'Investment', icon: '📈', color: '#9B59B6' },
  { id: 'business', name: 'Business', icon: '🏢', color: '#E67E22' },
  { id: 'gift', name: 'Gift', icon: '🎁', color: '#E74C3C' },
  { id: 'other', name: 'Other', icon: '💰', color: '#1ABC9C' },
] as const;

// Budget Periods
export const BUDGET_PERIODS = [
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'yearly', label: 'Yearly' },
] as const;

// Date Formats
export const DATE_FORMATS = {
  SHORT: 'MMM dd',
  MEDIUM: 'MMM dd, yyyy',
  LONG: 'MMMM dd, yyyy',
  FULL: 'EEEE, MMMM dd, yyyy',
} as const;

// Currency Options
export const SUPPORTED_CURRENCIES = [
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
  { code: 'JPY', symbol: '¥', name: 'Japanese Yen' },
  { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
  { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },
] as const;

// Theme Colors
export const THEME_COLORS = {
  primary: '#3B82F6',
  secondary: '#8B5CF6',
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#06B6D4',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  USER_PREFERENCES: 'fundflow_user_preferences',
  THEME: 'fundflow_theme',
  SIDEBAR_STATE: 'fundflow_sidebar_state',
  RECENT_SEARCHES: 'fundflow_recent_searches',
} as const; 