/**
 * SSL Initialization Module
 * 
 * This module initializes SSL configuration when the application starts.
 * Import and call initializeSSL() at the beginning of your application.
 */

import { configureGlobalSSL, validateSSLConfiguration } from './ssl-config';

/**
 * Initialize SSL configuration for the application
 */
export function initializeSSL(): void {
  try {
    console.log('🔧 Initializing SSL configuration...');
    
    // Configure global SSL settings
    configureGlobalSSL();
    
    // Validate configuration in development
    if (process.env.NODE_ENV === 'development') {
      validateSSLConfiguration();
    }
    
    console.log('✅ SSL initialization completed successfully');
  } catch (error) {
    console.error('❌ SSL initialization failed:', error);
    
    // In development, provide helpful guidance
    if (process.env.NODE_ENV === 'development') {
      console.error('\n💡 To fix SSL issues:');
      console.error('1. Run: npm run setup:ssl');
      console.error('2. Validate: npm run validate:ssl');
      console.error('3. Check: frontend/docs/SSL_CONFIGURATION.md');
    }
  }
}

/**
 * Check if SSL is properly configured
 */
export function isSSLConfigured(): boolean {
  return !!(
    process.env.SSL_CA_BUNDLE_PATH || 
    process.env.NODE_TLS_REJECT_UNAUTHORIZED === '0'
  );
}

/**
 * Get SSL configuration status
 */
export function getSSLStatus(): {
  configured: boolean;
  caBundlePath?: string;
  rejectUnauthorized: boolean;
  development: boolean;
} {
  return {
    configured: isSSLConfigured(),
    caBundlePath: process.env.SSL_CA_BUNDLE_PATH,
    rejectUnauthorized: process.env.NODE_TLS_REJECT_UNAUTHORIZED !== '0',
    development: process.env.NODE_ENV === 'development',
  };
}
