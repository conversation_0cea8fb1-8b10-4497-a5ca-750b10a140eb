/**
 * Token utility functions for authentication and API access
 */

import { Session } from 'next-auth';

/**
 * Extract the appropriate API token from the session
 * Uses ID token for AWS API Gateway, access token for local proxy
 * @param session - The NextAuth session object
 * @returns The token string or null if not available
 */
export function getApiToken(session: Session | null): string | null {
  if (!session) {
    return null;
  }

  // Always use ID token for AWS API Gateway (regardless of NODE_ENV)
  // API Gateway JWT authorizer expects ID tokens
  // Local proxy can handle either, but we'll use access token for consistency with existing local setup
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';
  
  if (apiBaseUrl.includes('amazonaws.com')) {
    console.log('🔑 Using ID token for AWS API Gateway');
    return session.idToken || null;
  } else {
    console.log('🔑 Using access token for local proxy');
    return session.accessToken || null;
  }
}

/**
 * Get a preview of the token for debugging (first and last 8 characters)
 * @param token - The token string
 * @returns A safe preview string or null
 */
export function getTokenPreview(token: string | null): string | null {
  if (!token || token.length < 16) {
    return null;
  }

  const start = token.substring(0, 8);
  const end = token.substring(token.length - 8);
  return `${start}...${end}`;
}

/**
 * Check if the session has a valid token for the current API endpoint
 * @param session - The NextAuth session object
 * @returns Boolean indicating if a valid token exists
 */
export function hasValidTokenForEnvironment(session: Session | null): boolean {
  if (!session) {
    return false;
  }

  // Check for token expiration
  if (session.expiresAt && new Date(session.expiresAt * 1000) < new Date()) {
    return false;
  }

  // Check for appropriate token based on API endpoint
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';
  
  if (apiBaseUrl.includes('amazonaws.com')) {
    return !!(session.idToken);
  } else {
    return !!(session.accessToken);
  }
}