<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Card Styles Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto space-y-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Card Styles Test</h1>

        <!-- Test Card Structure -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <!-- Card Header -->
            <div class="mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Test Card Title</h3>
            </div>
            <!-- Card Content -->
            <div>
                <p class="text-gray-700 dark:text-gray-300">This is the card content. It should be properly spaced from the header.</p>
                <p class="text-gray-700 dark:text-gray-300 mt-2">The header should have a bottom margin of 1rem (mb-4).</p>
            </div>
        </div>

        <!-- Test Card with Description -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <!-- Card Header -->
            <div class="mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Card with Description</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">This card includes a description below the title.</p>
            </div>
            <!-- Card Content -->
            <div>
                <p class="text-gray-700 dark:text-gray-300">Content should be properly separated from the header section.</p>
            </div>
        </div>

        <!-- Test Card with Border Bottom on Header -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <!-- Card Header with Border -->
            <div class="mb-6 pb-2 border-b border-gray-100 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Card with Header Border</h3>
            </div>
            <!-- Card Content -->
            <div>
                <p class="text-gray-700 dark:text-gray-300">This card has a border line separating the header from content.</p>
            </div>
        </div>

        <!-- Debug Info -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 class="font-bold text-yellow-800 mb-2">Debug Information</h3>
            <ul class="text-sm text-yellow-700 space-y-1">
                <li>• mb-4 = margin-bottom: 1rem (16px)</li>
                <li>• text-lg = font-size: 1.125rem (18px)</li>
                <li>• font-semibold = font-weight: 600</li>
                <li>• p-6 = padding: 1.5rem (24px)</li>
                <li>• shadow-sm = box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05)</li>
            </ul>
        </div>

        <!-- Toggle Dark Mode Button -->
        <button 
            onclick="document.documentElement.classList.toggle('dark')"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600"
        >
            Toggle Dark Mode
        </button>
    </div>
</body>
</html>
