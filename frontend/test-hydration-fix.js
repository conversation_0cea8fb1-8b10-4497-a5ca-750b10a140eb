const { spawn } = require('child_process');
const puppeteer = require('puppeteer');

async function testHydration() {
  let browser;
  
  try {
    console.log('🚀 Starting hydration test...');
    
    // Launch browser
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Listen for console errors (including hydration errors)
    const errors = [];
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Navigate to the app
    console.log('📄 Loading application...');
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });
    
    // Wait for React to hydrate
    await page.waitForTimeout(2000);
    
    // Check for hydration errors
    const hydrationErrors = errors.filter(error => 
      error.includes('Hydration') || 
      error.includes('server rendered HTML') ||
      error.includes('client')
    );
    
    if (hydrationErrors.length > 0) {
      console.log('❌ Hydration errors found:');
      hydrationErrors.forEach(error => console.log('  ', error));
      return false;
    } else {
      console.log('✅ No hydration errors detected!');
    }
    
    // Test theme toggle functionality
    console.log('🎨 Testing theme toggle...');
    
    // Look for theme toggle button
    const themeButton = await page.$('button[aria-label*="Switch to"]');
    if (themeButton) {
      await themeButton.click();
      await page.waitForTimeout(1000);
      console.log('✅ Theme toggle works!');
    } else {
      console.log('⚠️  Theme toggle button not found');
    }
    
    console.log('✅ Hydration test completed successfully!');
    return true;
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  testHydration().then((success) => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testHydration }; 