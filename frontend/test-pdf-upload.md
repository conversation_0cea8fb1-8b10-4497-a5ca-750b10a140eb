# PDF Upload Integration Test

## Overview
This document outlines how to test the newly integrated PDF fund extractor with the real AWS API.

## Changes Made

### 1. Environment Configuration
- ✅ **NEXT_PUBLIC_USE_AWS_API=true** - Enabled real AWS API usage
- ✅ **NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=false** - Disabled mock data fallback
- ✅ **NEXT_PUBLIC_ENABLE_MOCK_LOGIN=false** - Disabled mock authentication

### 2. API Integration
- ✅ **Updated bulkUploadPDFs function** to use `/funds/extract-pdf` endpoint
- ✅ **Sequential processing** - Files are processed one at a time as the Lambda function expects
- ✅ **Authentication headers** - JWT tokens are properly included in requests
- ✅ **Error handling** - Comprehensive error handling for individual file failures

### 3. API Endpoint Details
- **URL**: `https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev/funds/extract-pdf`
- **Method**: POST
- **Content-Type**: multipart/form-data
- **Authentication**: Bearer token (JWT from Cognito)
- **Query Parameter**: `?save=true` (automatically saves extracted funds to database)

## Testing Steps

### 1. Authentication Required
Since mock login is disabled, you need to authenticate with real Cognito:

1. **Navigate to**: http://localhost:3000
2. **Click "Sign In"** - This will redirect to Cognito authentication
3. **Use your Cognito credentials** to sign in
4. **Verify authentication** - Check that you see your user info in the UI

### 2. Access Bulk Upload Page
1. **Navigate to**: http://localhost:3000/funds/bulk-upload
2. **Verify page loads** without errors
3. **Check console** for API configuration logs

### 3. Test PDF Upload
1. **Select PDF files** (fund documents, factsheets, etc.)
2. **Click "Upload"** to start processing
3. **Monitor progress** - Files will be processed sequentially
4. **Check results** - Each file will show success/error status

### 4. Expected Behavior

#### Successful Upload
- ✅ **Status**: "success" (green)
- ✅ **Message**: "Fund data extracted successfully"
- ✅ **Fund Data**: Structured fund information extracted by AI
- ✅ **Database**: Fund automatically saved to DynamoDB
- ✅ **Actions**: "View" and "Edit" buttons available

#### Upload with Warnings
- ⚠️ **Status**: "warning" (yellow)
- ⚠️ **Message**: "Fund data extracted successfully with some warnings"
- ⚠️ **Warnings**: List of fields that couldn't be extracted automatically
- ✅ **Fund Data**: Partial fund information available
- ✅ **Actions**: "Edit" button to complete missing information

#### Failed Upload
- ❌ **Status**: "error" (red)
- ❌ **Message**: "Failed to extract fund data"
- ❌ **Error**: Specific error message (PDF format, authentication, etc.)
- ❌ **Actions**: "Retry" button available

### 5. Troubleshooting

#### Authentication Issues
- **Error**: "Authentication required. Please sign in to upload PDF files."
- **Solution**: Sign in with valid Cognito credentials
- **Check**: Verify JWT token is not expired

#### API Errors
- **Error**: "API request failed: 401 Unauthorized"
- **Solution**: Re-authenticate or refresh the page
- **Check**: Cognito session validity

#### PDF Processing Errors
- **Error**: "Failed to extract fund data"
- **Possible causes**:
  - PDF is password-protected
  - PDF contains only images (no text)
  - PDF format not supported
  - AI service temporarily unavailable

#### Network Issues
- **Error**: "Network request failed"
- **Solution**: Check internet connection and API Gateway availability
- **Check**: AWS service status

## Console Logs to Monitor

### API Configuration
```
🔧 API Configuration: {
  API_BASE_URL: 'https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev',
  AWS_REGION: 'ap-northeast-1',
  ENABLE_MOCK_MODE: false,
  USE_AWS_API: true,
  ENABLE_MOCK_FALLBACK: false
}
```

### File Processing
```
🚀 Processing PDF files with real API...
📄 Processing file 1/3: fund_document.pdf
✅ Successfully processed fund_document.pdf
```

### Authentication
```
🔐 Session check: Session found
✅ Added Authorization header to request
🔑 Token expires at: [timestamp]
```

## Success Criteria

1. ✅ **Authentication works** - Real Cognito login successful
2. ✅ **API calls succeed** - No 401/403 errors
3. ✅ **PDF processing works** - AI successfully extracts fund data
4. ✅ **Database integration** - Funds are saved to DynamoDB
5. ✅ **UI updates correctly** - Results display properly
6. ✅ **Navigation works** - View/Edit buttons work correctly

## Next Steps After Testing

1. **Performance optimization** - Consider parallel processing for multiple files
2. **Error recovery** - Add retry mechanisms for failed uploads
3. **Progress indicators** - Show real-time processing progress
4. **File validation** - Pre-validate PDF files before upload
5. **Batch operations** - Add bulk edit/delete capabilities

## API Monitoring

Monitor the Lambda function in AWS CloudWatch:
- **Function name**: `fundflow-dev-PDFFundExtractorFunction-*`
- **Logs**: Check for processing errors and performance metrics
- **Metrics**: Monitor invocation count, duration, and error rate
