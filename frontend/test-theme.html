<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors min-h-screen">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-6">Theme Test</h1>
        
        <div class="mb-6">
            <button id="toggleBtn" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600">
                Toggle Theme
            </button>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">Card 1</h2>
                <p class="text-gray-600 dark:text-gray-400">This is some content that should change color based on the theme.</p>
            </div>
            
            <div class="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">Card 2</h2>
                <p class="text-gray-600 dark:text-gray-400">Another card to test the theme switching.</p>
            </div>
        </div>
        
        <div class="mt-6">
            <p id="status" class="text-sm text-gray-500 dark:text-gray-400"></p>
        </div>
    </div>

    <script>
        const toggleBtn = document.getElementById('toggleBtn');
        const status = document.getElementById('status');
        const html = document.documentElement;
        
        function updateStatus() {
            const isDark = html.classList.contains('dark');
            status.textContent = `Current theme: ${isDark ? 'Dark' : 'Light'}`;
        }
        
        toggleBtn.addEventListener('click', () => {
            html.classList.toggle('dark');
            updateStatus();
        });
        
        updateStatus();
    </script>
</body>
</html>
