"""
Enhanced DynamoDB table creation script for market data support.
This script creates the enhanced table structure to support comprehensive market data.
"""

import boto3
from botocore.exceptions import ClientError
import json
import time
from datetime import datetime

def create_enhanced_funds_table(dynamodb_client, table_name: str, environment: str):
    """
    Create enhanced funds table with support for market data.
    
    Args:
        dynamodb_client: Boto3 DynamoDB client
        table_name: Name of the table to create
        environment: Environment (dev, staging, prod)
    """
    
    table_definition = {
        'TableName': table_name,
        'KeySchema': [
            {
                'AttributeName': 'PK',
                'KeyType': 'HASH'  # Partition key
            },
            {
                'AttributeName': 'SK',
                'KeyType': 'RANGE'  # Sort key
            }
        ],
        'AttributeDefinitions': [
            {
                'AttributeName': 'PK',
                'AttributeType': 'S'
            },
            {
                'AttributeName': 'SK',
                'AttributeType': 'S'
            },
            {
                'AttributeName': 'data_type',
                'AttributeType': 'S'
            },
            {
                'AttributeName': 'timestamp',
                'AttributeType': 'S'
            },
            {
                'AttributeName': 'data_source',
                'AttributeType': 'S'
            },
            {
                'AttributeName': 'data_quality',
                'AttributeType': 'S'
            }
        ],
        'GlobalSecondaryIndexes': [
            {
                'IndexName': 'GSI1-TypeDate',
                'KeySchema': [
                    {
                        'AttributeName': 'data_type',
                        'KeyType': 'HASH'
                    },
                    {
                        'AttributeName': 'timestamp',
                        'KeyType': 'RANGE'
                    }
                ],
                'Projection': {
                    'ProjectionType': 'ALL'
                }
            },
            {
                'IndexName': 'GSI2-FundType',
                'KeySchema': [
                    {
                        'AttributeName': 'PK',
                        'KeyType': 'HASH'
                    },
                    {
                        'AttributeName': 'data_type',
                        'KeyType': 'RANGE'
                    }
                ],
                'Projection': {
                    'ProjectionType': 'ALL'
                }
            },
            {
                'IndexName': 'GSI3-SourceQuality',
                'KeySchema': [
                    {
                        'AttributeName': 'data_source',
                        'KeyType': 'HASH'
                    },
                    {
                        'AttributeName': 'data_quality',
                        'KeyType': 'RANGE'
                    }
                ],
                'Projection': {
                    'ProjectionType': 'KEYS_ONLY'
                }
            }
        ],
        'BillingMode': 'PAY_PER_REQUEST',
        'StreamSpecification': {
            'StreamEnabled': True,
            'StreamViewType': 'NEW_AND_OLD_IMAGES'
        },
        'SSESpecification': {
            'Enabled': True
        },
        'Tags': [
            {
                'Key': 'Environment',
                'Value': environment
            },
            {
                'Key': 'Application',
                'Value': 'FundFlow'
            },
            {
                'Key': 'Purpose',
                'Value': 'Enhanced Market Data Storage'
            }
        ]
    }
    
    try:
        print(f"Creating table {table_name}...")
        response = dynamodb_client.create_table(**table_definition)
        print(f"Table creation initiated. Status: {response['TableDescription']['TableStatus']}")
        
        # Wait for table to be active
        waiter = dynamodb_client.get_waiter('table_exists')
        waiter.wait(TableName=table_name)
        print(f"Table {table_name} is now active!")
        
        return response
        
    except ClientError as e:
        if e.response['Error']['Code'] == 'ResourceInUseException':
            print(f"Table {table_name} already exists")
            return None
        else:
            print(f"Error creating table: {e}")
            raise

def create_benchmark_table(dynamodb_client, table_name: str, environment: str):
    """
    Create benchmark data table.
    
    Args:
        dynamodb_client: Boto3 DynamoDB client
        table_name: Name of the benchmark table
        environment: Environment (dev, staging, prod)
    """
    
    table_definition = {
        'TableName': table_name,
        'KeySchema': [
            {
                'AttributeName': 'benchmark_id',
                'KeyType': 'HASH'
            },
            {
                'AttributeName': 'timestamp',
                'KeyType': 'RANGE'
            }
        ],
        'AttributeDefinitions': [
            {
                'AttributeName': 'benchmark_id',
                'AttributeType': 'S'
            },
            {
                'AttributeName': 'timestamp',
                'AttributeType': 'S'
            }
        ],
        'BillingMode': 'PAY_PER_REQUEST',
        'StreamSpecification': {
            'StreamEnabled': True,
            'StreamViewType': 'NEW_AND_OLD_IMAGES'
        },
        'SSESpecification': {
            'Enabled': True
        },
        'Tags': [
            {
                'Key': 'Environment',
                'Value': environment
            },
            {
                'Key': 'Application',
                'Value': 'FundFlow'
            },
            {
                'Key': 'Purpose',
                'Value': 'Benchmark Data Storage'
            }
        ]
    }
    
    try:
        print(f"Creating benchmark table {table_name}...")
        response = dynamodb_client.create_table(**table_definition)
        print(f"Benchmark table creation initiated. Status: {response['TableDescription']['TableStatus']}")
        
        # Wait for table to be active
        waiter = dynamodb_client.get_waiter('table_exists')
        waiter.wait(TableName=table_name)
        print(f"Benchmark table {table_name} is now active!")
        
        return response
        
    except ClientError as e:
        if e.response['Error']['Code'] == 'ResourceInUseException':
            print(f"Benchmark table {table_name} already exists")
            return None
        else:
            print(f"Error creating benchmark table: {e}")
            raise

def setup_ttl(dynamodb_client, table_name: str):
    """
    Setup TTL for automatic data expiration.

    Args:
        dynamodb_client: Boto3 DynamoDB client
        table_name: Name of the table
    """

    try:
        print(f"Setting up TTL for table {table_name}...")
        response = dynamodb_client.update_time_to_live(
            TableName=table_name,
            TimeToLiveSpecification={
                'Enabled': True,
                'AttributeName': 'ttl'
            }
        )
        print(f"TTL setup completed for {table_name}")
        return response

    except ClientError as e:
        print(f"Error setting up TTL: {e}")
        raise


def enable_point_in_time_recovery(dynamodb_client, table_name: str):
    """
    Enable point-in-time recovery for the table.

    Args:
        dynamodb_client: Boto3 DynamoDB client
        table_name: Name of the table
    """

    try:
        print(f"Enabling point-in-time recovery for table {table_name}...")
        response = dynamodb_client.update_continuous_backups(
            TableName=table_name,
            PointInTimeRecoverySpecification={
                'PointInTimeRecoveryEnabled': True
            }
        )
        print(f"Point-in-time recovery enabled for {table_name}")
        return response

    except ClientError as e:
        print(f"Error enabling point-in-time recovery: {e}")
        # Don't raise - this is optional
        pass

def create_sample_data(dynamodb_resource, table_name: str):
    """
    Create comprehensive sample market data for testing.

    Args:
        dynamodb_resource: Boto3 DynamoDB resource
        table_name: Name of the table
    """

    table = dynamodb_resource.Table(table_name)
    current_time = datetime.now().isoformat()
    current_date = datetime.now().date().isoformat()

    # Sample fund info
    fund_info = {
        'PK': 'FUND#fund-123',
        'SK': 'INFO#latest',
        'data_type': 'INFO',
        'fund_id': 'fund-123',
        'name': 'Enhanced Growth Fund',
        'fund_type': 'equity',
        'status': 'active',
        'inception_date': '2020-01-01',
        'fund_manager': 'John Doe',
        'expense_ratio': '1.25',
        'minimum_investment': '1000',
        'nav': '125.45',
        'total_assets': '5000000000',
        'risk_level': 'moderate'
    }

    # Comprehensive price data
    price_data = {
        'PK': 'FUND#fund-123',
        'SK': f'PRICE#{current_time}',
        'data_type': 'PRICE',
        'fund_id': 'fund-123',
        'timestamp': current_time,
        'data_source': 'fund_company',
        'data_quality': 'excellent',
        'nav': {
            'value': '125.45',
            'currency': 'USD',
            'source': 'fund_company',
            'quality': 'excellent',
            'timestamp': current_time
        },
        'market_price': {
            'value': '125.50',
            'currency': 'USD',
            'source': 'yahoo_finance',
            'quality': 'good',
            'timestamp': current_time
        },
        'bid': {
            'value': '125.48',
            'currency': 'USD',
            'source': 'yahoo_finance',
            'quality': 'good',
            'timestamp': current_time
        },
        'ask': {
            'value': '125.52',
            'currency': 'USD',
            'source': 'yahoo_finance',
            'quality': 'good',
            'timestamp': current_time
        },
        'volume': 150000,
        'avg_volume_30d': 135000,
        'bid_ask_spread': '0.04',
        'bid_ask_spread_pct': '0.032',
        'market_cap': '5000000000',
        'shares_outstanding': 39840637,
        'price_change_1d': '0.65',
        'price_change_1d_pct': '0.52',
        'price_change_ytd': '12.45',
        'price_change_ytd_pct': '11.02',
        'ttl': int(time.time()) + (90 * 24 * 60 * 60)  # 90 days from now
    }

    # Comprehensive valuation metrics
    valuation_data = {
        'PK': 'FUND#fund-123',
        'SK': f'VALUATION#{current_date}',
        'data_type': 'VALUATION',
        'fund_id': 'fund-123',
        'as_of_date': current_date,
        'data_source': 'bloomberg',
        'data_quality': 'excellent',
        'price_to_book': '2.45',
        'price_to_earnings': '18.5',
        'price_to_sales': '3.2',
        'price_to_cash_flow': '12.8',
        'enterprise_value': '6000000000',
        'ev_to_revenue': '4.2',
        'ev_to_ebitda': '15.6',
        'return_on_equity': '15.2',
        'return_on_assets': '8.7',
        'return_on_invested_capital': '12.4',
        'debt_to_equity': '0.45',
        'current_ratio': '2.1',
        'quick_ratio': '1.8',
        'book_value_per_share': '51.2',
        'tangible_book_value': '48.9',
        'dividend_yield': '2.8',
        'dividend_payout_ratio': '35.5',
        'ttl': int(time.time()) + (2 * 365 * 24 * 60 * 60)  # 2 years from now
    }

    # Technical indicators
    technical_data = {
        'PK': 'FUND#fund-123',
        'SK': f'TECHNICAL#{current_time}',
        'data_type': 'TECHNICAL',
        'fund_id': 'fund-123',
        'timestamp': current_time,
        'data_source': 'bloomberg',
        'data_quality': 'good',
        'sma_20': '124.80',
        'sma_50': '123.45',
        'sma_200': '120.30',
        'ema_12': '125.12',
        'ema_26': '124.65',
        'rsi_14': '65.4',
        'macd_line': '0.85',
        'macd_signal': '0.72',
        'macd_histogram': '0.13',
        'bollinger_upper': '127.50',
        'bollinger_middle': '125.00',
        'bollinger_lower': '122.50',
        'atr_14': '2.15',
        'vwap': '125.20',
        'volume_sma_20': 142000,
        'support_level': '121.00',
        'resistance_level': '128.00',
        'ttl': int(time.time()) + (30 * 24 * 60 * 60)  # 30 days from now
    }

    # Risk analytics
    risk_data = {
        'PK': 'FUND#fund-123',
        'SK': f'RISK#{current_date}',
        'data_type': 'RISK',
        'fund_id': 'fund-123',
        'as_of_date': current_date,
        'data_source': 'internal',
        'data_quality': 'excellent',
        'var_1d_95': '-2.5',
        'var_1d_99': '-3.8',
        'var_10d_95': '-7.9',
        'var_10d_99': '-12.1',
        'cvar_1d_95': '-3.2',
        'cvar_1d_99': '-4.5',
        'sharpe_ratio': '1.25',
        'sortino_ratio': '1.45',
        'calmar_ratio': '0.85',
        'information_ratio': '0.65',
        'treynor_ratio': '0.125',
        'max_drawdown': '-15.2',
        'max_drawdown_duration': 45,
        'current_drawdown': '-2.1',
        'volatility_1m': '16.8',
        'volatility_3m': '18.2',
        'volatility_1y': '19.5',
        'downside_deviation': '11.2',
        'beta_vs_benchmark': '1.05',
        'correlation_vs_benchmark': '0.85',
        'tracking_error': '3.8',
        'ttl': int(time.time()) + (2 * 365 * 24 * 60 * 60)  # 2 years from now
    }

    # Market data input record
    input_data = {
        'PK': 'FUND#fund-123',
        'SK': f'INPUT#{current_time}#user-456',
        'data_type': 'INPUT',
        'fund_id': 'fund-123',
        'input_timestamp': current_time,
        'data_timestamp': current_time,
        'input_by': 'user-456',
        'nav': '125.45',
        'market_price': '125.50',
        'volume': 150000,
        'price_to_book': '2.45',
        'price_to_earnings': '18.5',
        'dividend_yield': '2.8',
        'volatility': '18.5',
        'beta': '1.05',
        'notes': 'Sample market data input for testing',
        'validated': True,
        'validation_notes': 'Data verified against official sources',
        'ttl': int(time.time()) + (365 * 24 * 60 * 60)  # 1 year from now
    }

    try:
        print("Creating comprehensive sample data...")
        with table.batch_writer() as batch:
            batch.put_item(Item=fund_info)
            batch.put_item(Item=price_data)
            batch.put_item(Item=valuation_data)
            batch.put_item(Item=technical_data)
            batch.put_item(Item=risk_data)
            batch.put_item(Item=input_data)

        print("✅ Comprehensive sample data created successfully!")
        print(f"   📊 Fund Info: {fund_info['name']}")
        print(f"   💰 NAV: ${price_data['nav']['value']}")
        print(f"   📈 P/E Ratio: {valuation_data['price_to_earnings']}")
        print(f"   📉 RSI: {technical_data['rsi_14']}")
        print(f"   ⚠️  VaR (95%): {risk_data['var_1d_95']}%")

    except ClientError as e:
        print(f"Error creating sample data: {e}")
        raise

def main():
    """Main function to create enhanced DynamoDB schema."""
    
    # Configuration
    environment = 'dev'  # Change as needed
    region = 'ap-northeast-1'  # Change as needed
    
    # Table names
    funds_table_name = f'fundflow-{environment}-funds-enhanced'
    benchmark_table_name = f'fundflow-{environment}-benchmarks'
    
    # Initialize DynamoDB clients
    dynamodb_client = boto3.client('dynamodb', region_name=region)
    dynamodb_resource = boto3.resource('dynamodb', region_name=region)
    
    try:
        # Create enhanced funds table
        create_enhanced_funds_table(dynamodb_client, funds_table_name, environment)
        
        # Create benchmark table
        create_benchmark_table(dynamodb_client, benchmark_table_name, environment)
        
        # Setup TTL
        setup_ttl(dynamodb_client, funds_table_name)
        setup_ttl(dynamodb_client, benchmark_table_name)

        # Enable point-in-time recovery
        enable_point_in_time_recovery(dynamodb_client, funds_table_name)
        enable_point_in_time_recovery(dynamodb_client, benchmark_table_name)

        # Create sample data
        create_sample_data(dynamodb_resource, funds_table_name)
        
        print("\n✅ Enhanced DynamoDB schema setup completed successfully!")
        print(f"📊 Funds table: {funds_table_name}")
        print(f"📈 Benchmark table: {benchmark_table_name}")
        
    except Exception as e:
        print(f"\n❌ Error setting up enhanced schema: {e}")
        raise

if __name__ == "__main__":
    main()
