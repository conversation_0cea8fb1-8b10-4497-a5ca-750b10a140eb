# Environment-Specific Parameters

This directory contains environment-specific parameter files for deploying the FundFlow application across different environments (dev, staging, prod).

## Parameter Files

### dev.json

Development environment configuration with:

- Debug logging enabled
- Lower memory allocation (512MB)
- Reduced API throttling limits
- Cost-optimized settings

### staging.json

Staging environment configuration with:

- Production-like settings for testing
- Increased memory allocation (1024MB)
- Higher API throttling limits
- Optional custom domain support

### prod.json

Production environment configuration with:

- Warning-level logging for performance
- Optimized memory allocation (1024MB)
- Extended timeout settings (60s)
- High API throttling limits
- Custom domain configuration

## Parameters Explained

### Core Infrastructure

| Parameter        | Description              | Dev   | Staging                  | Prod             |
| ---------------- | ------------------------ | ----- | ------------------------ | ---------------- |
| Environment      | Deployment environment   | dev   | staging                  | prod             |
| DomainName       | Custom API domain        | ""    | staging-api.fundflow.com | api.fundflow.com |
| CertificateArn   | ACM certificate ARN      | ""    | ""                       | arn:aws:acm:...  |
| LogLevel         | Lambda logging level     | DEBUG | INFO                     | WARNING          |
| LambdaMemorySize | Lambda memory (MB)       | 512   | 1024                     | 1024             |
| LambdaTimeout    | Lambda timeout (seconds) | 30    | 30                       | 60               |

### API Gateway Configuration

| Parameter             | Description              | Dev   | Staging | Prod |
| --------------------- | ------------------------ | ----- | ------- | ---- |
| ApiThrottleBurstLimit | API burst limit          | 100   | 200     | 500  |
| ApiThrottleRateLimit  | API rate limit (req/sec) | 50    | 100     | 250  |
| ApiCachingEnabled     | Enable API caching       | false | true    | true |
| ApiCacheTTL           | Cache TTL (seconds)      | 300   | 300     | 600  |
| RequestValidationMode | Request validation level | basic | full    | full |

### DynamoDB Configuration

| Parameter             | Description             | Dev             | Staging         | Prod        |
| --------------------- | ----------------------- | --------------- | --------------- | ----------- |
| DynamoDBBillingMode   | Billing mode            | PAY_PER_REQUEST | PAY_PER_REQUEST | PROVISIONED |
| DynamoDBReadCapacity  | Read capacity units     | 5               | 10              | 50          |
| DynamoDBWriteCapacity | Write capacity units    | 5               | 10              | 25          |
| BackupRetentionDays   | Backup retention (days) | 7               | 30              | 2555        |

### CloudWatch Monitoring

| Parameter                | Description                | Dev   | Staging                     | Prod                |
| ------------------------ | -------------------------- | ----- | --------------------------- | ------------------- |
| LogRetentionDays         | Log retention (days)       | 7     | 30                          | 365                 |
| EnableDetailedMonitoring | Enable detailed monitoring | false | true                        | true                |
| AlarmNotificationEmail   | Alert email address        | ""    | <EMAIL> | <EMAIL> |

### CloudFront Configuration

| Parameter                    | Description              | Dev            | Staging        | Prod           |
| ---------------------------- | ------------------------ | -------------- | -------------- | -------------- |
| CloudFrontPriceClass         | Distribution price class | PriceClass_100 | PriceClass_100 | PriceClass_All |
| CloudFrontCompressionEnabled | Enable compression       | true           | true           | true           |
| CloudFrontDefaultTTL         | Default cache TTL (sec)  | 86400          | 86400          | 86400          |
| CloudFrontMaxTTL             | Maximum cache TTL (sec)  | 31536000       | 31536000       | 31536000       |

### Security Configuration

| Parameter              | Description             | Dev   | Staging | Prod |
| ---------------------- | ----------------------- | ----- | ------- | ---- |
| EnableWAF              | Enable AWS WAF          | false | true    | true |
| SecurityHeadersEnabled | Enable security headers | true  | true    | true |
| IPWhitelistEnabled     | Enable IP restrictions  | false | false   | true |

## Deployment Options

### Option 1: Using samconfig.toml (Default)

The parameters are pre-configured in `samconfig.toml` for each environment:

```bash
# Deploy using samconfig.toml
./scripts/deploy.sh -e dev
./scripts/deploy.sh -e staging
./scripts/deploy.sh -e prod
```

### Option 2: Using Parameter Files

Use the JSON parameter files directly:

```bash
# Deploy using parameter files
./scripts/deploy.sh -e dev -f
./scripts/deploy.sh -e staging -f
./scripts/deploy.sh -e prod -f
```

## Customizing for Your Environment

### For Production Deployment

1. **Update prod.json** with your actual domain and certificate:

   ```json
   {
     "DomainName": "your-api-domain.com",
     "CertificateArn": "arn:aws:acm:region:account:certificate/your-cert-id"
   }
   ```

2. **Create ACM Certificate** in AWS Certificate Manager for your domain

3. **Deploy with custom domain**:
   ```bash
   ./scripts/deploy.sh -e prod -f
   ```

### For Custom Environments

Create additional parameter files (e.g., `test.json`, `demo.json`) and update the deployment script to support them.

## Environment Variables vs Parameters

- **Parameters**: Defined at deployment time, affect infrastructure resources
- **Environment Variables**: Set in Lambda functions at runtime
- **SAM Configuration**: Controls deployment behavior and stack settings

## Security Considerations

- Parameter files may contain sensitive information
- Consider using AWS Systems Manager Parameter Store for secrets
- ACM certificate ARNs are not sensitive but should be environment-specific
- Never commit real production credentials to version control

## Troubleshooting

### Common Issues

1. **Invalid parameter values**: Check parameter constraints in template.yaml
2. **Missing certificate**: Ensure ACM certificate exists before setting CertificateArn
3. **Domain validation**: Custom domains require proper DNS configuration
4. **Throttling limits**: High values may incur additional costs

### Validation

Validate parameters before deployment:

```bash
# Validate template with parameters
sam validate --parameter-overrides file://parameters/prod.json
```
