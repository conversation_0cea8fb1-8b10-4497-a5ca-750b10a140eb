{"Parameters": {"Environment": "dev", "DomainName": "", "CertificateArn": "", "LogLevel": "DEBUG", "LambdaMemorySize": "512", "LambdaTimeout": "30", "ApiThrottleBurstLimit": "100", "ApiThrottleRateLimit": "50", "DynamoDBBillingMode": "PAY_PER_REQUEST", "DynamoDBReadCapacity": "5", "DynamoDBWriteCapacity": "5", "BackupRetentionDays": "7", "LogRetentionDays": "7", "EnableDetailedMonitoring": "false", "AlarmNotificationEmail": "", "ApiCachingEnabled": "false", "ApiCacheTTL": "300", "RequestValidationMode": "basic", "CloudFrontPriceClass": "PriceClass_100", "CloudFrontCompressionEnabled": "true", "CloudFrontDefaultTTL": "86400", "CloudFrontMaxTTL": "31536000", "EnableWAF": "false", "SecurityHeadersEnabled": "true", "IPWhitelistEnabled": "false"}}