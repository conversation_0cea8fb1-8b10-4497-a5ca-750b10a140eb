{"Parameters": {"Environment": "prod", "DomainName": "api.fundflow.com", "CertificateArn": "arn:aws:acm:us-east-1:123456789012:certificate/example-certificate-id", "LogLevel": "WARNING", "LambdaMemorySize": "1024", "LambdaTimeout": "60", "ApiThrottleBurstLimit": "500", "ApiThrottleRateLimit": "250", "DynamoDBBillingMode": "PROVISIONED", "DynamoDBReadCapacity": "50", "DynamoDBWriteCapacity": "25", "BackupRetentionDays": "2555", "LogRetentionDays": "365", "EnableDetailedMonitoring": "true", "AlarmNotificationEmail": "<EMAIL>", "ApiCachingEnabled": "true", "ApiCacheTTL": "600", "RequestValidationMode": "full", "CloudFrontPriceClass": "PriceClass_All", "CloudFrontCompressionEnabled": "true", "CloudFrontDefaultTTL": "86400", "CloudFrontMaxTTL": "31536000", "EnableWAF": "true", "SecurityHeadersEnabled": "true", "IPWhitelistEnabled": "true"}}