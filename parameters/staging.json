{"Parameters": {"Environment": "staging", "DomainName": "staging-api.fundflow.com", "CertificateArn": "", "LogLevel": "INFO", "LambdaMemorySize": "1024", "LambdaTimeout": "30", "ApiThrottleBurstLimit": "200", "ApiThrottleRateLimit": "100", "DynamoDBBillingMode": "PAY_PER_REQUEST", "DynamoDBReadCapacity": "10", "DynamoDBWriteCapacity": "10", "BackupRetentionDays": "30", "LogRetentionDays": "30", "EnableDetailedMonitoring": "true", "AlarmNotificationEmail": "<EMAIL>", "ApiCachingEnabled": "true", "ApiCacheTTL": "300", "RequestValidationMode": "full", "CloudFrontPriceClass": "PriceClass_100", "CloudFrontCompressionEnabled": "true", "CloudFrontDefaultTTL": "86400", "CloudFrontMaxTTL": "31536000", "EnableWAF": "true", "SecurityHeadersEnabled": "true", "IPWhitelistEnabled": "false"}}