[tool:pytest]
testpaths = tests
python_paths = 
    .
    src
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
env = 
    AWS_ACCESS_KEY_ID=testing
    AWS_SECRET_ACCESS_KEY=testing
    AWS_SECURITY_TOKEN=testing
    AWS_SESSION_TOKEN=testing
    AWS_DEFAULT_REGION=us-east-1
    MOTO_S3_CUSTOM_ENDPOINTS=http://localhost:5000
    MOTO_DYNAMODB_CUSTOM_ENDPOINTS=http://localhost:8000
    PYTHONPATH={envtmpdir}/src
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests 