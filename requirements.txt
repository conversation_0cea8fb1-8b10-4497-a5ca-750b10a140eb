# Core AWS packages
boto3>=1.35.0
botocore>=1.35.0

# AWS Lambda Powertools - supports Python 3.13
aws-lambda-powertools[all]>=3.0.0

# AWS X-Ray SDK for tracing
aws-xray-sdk>=2.12.0

# Data validation and parsing - Python 3.13 compatible
pydantic>=2.5.0,<3.0.0
email-validator>=2.0.0

# Authentication and JWT handling
pyjwt>=2.8.0
python-jose[cryptography]>=3.3.0

# Date and time utilities
python-dateutil>=2.8.2

# Decimal handling - part of Python standard library
# decimal

# Typing extensions for better type hints - Python 3.13 compatible
typing-extensions>=4.9.0

# Development and testing (optional)
pytest>=7.4.0
pytest-mock>=3.12.0
moto>=4.2.0  # For AWS service mocking in tests

# Additional dependencies
requests>=2.31.0
python-dotenv>=1.0.0

# PDF processing dependencies
pdf2image>=1.16.0
Pillow>=10.0.0