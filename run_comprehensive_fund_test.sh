#!/bin/bash

# Comprehensive Fund Update Test Runner
# This script sets up the environment and runs the API Gateway + DynamoDB fund update tests

set -e

echo "🚀 Comprehensive Fund Update Test Runner"
echo "========================================="

# Check if we're in the right environment
if [[ ! -f "test_fund_update_comprehensive.py" ]]; then
    echo "❌ Error: test_fund_update_comprehensive.py not found in current directory"
    echo "Please run this script from the project root directory"
    exit 1
fi

# Activate conda environment if specified
if [[ -n "$CONDA_ENV" ]]; then
    echo "🐍 Activating conda environment: $CONDA_ENV"
    source $(conda info --base)/etc/profile.d/conda.sh
    conda activate $CONDA_ENV
elif [[ -d "$HOME/miniconda3/envs/ff_env" ]] || [[ -d "$HOME/anaconda3/envs/ff_env" ]]; then
    echo "🐍 Activating conda environment: ff_env"
    source $(conda info --base)/etc/profile.d/conda.sh
    conda activate ff_env
else
    echo "⚠️  No conda environment found, using system Python"
fi

# Check and install required Python packages
echo "📦 Checking Python dependencies..."

# Create a temporary requirements file for the test
cat > temp_test_requirements.txt << EOF
boto3>=1.26.0
requests>=2.28.0
pydantic>=2.0.0
EOF

# Install dependencies
echo "📥 Installing test dependencies..."
pip install -r temp_test_requirements.txt

# Clean up temp file
rm temp_test_requirements.txt

# Check AWS credentials
echo "🔑 Checking AWS credentials..."
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ Error: AWS credentials not configured or expired"
    echo "Please run 'aws configure' or check your ~/.aws/credentials file"
    exit 1
else
    echo "✅ AWS credentials verified"
fi

# Check AWS region
AWS_REGION=$(aws configure get region)
if [[ "$AWS_REGION" != "ap-northeast-1" ]]; then
    echo "⚠️  Warning: AWS region is set to '$AWS_REGION', but the test expects 'ap-northeast-1'"
    echo "You may need to update the region in the test script or AWS config"
fi

# Check DynamoDB table access
echo "🗄️  Checking DynamoDB table access..."
if aws dynamodb describe-table --table-name "fundflow-dev-funds" --region ap-northeast-1 > /dev/null 2>&1; then
    echo "✅ DynamoDB table 'fundflow-dev-funds' is accessible"
else
    echo "❌ Error: Cannot access DynamoDB table 'fundflow-dev-funds'"
    echo "Please check your AWS permissions and ensure the table exists"
    exit 1
fi

# Check API Gateway endpoint
echo "🌐 Checking API Gateway endpoint..."
API_URL="https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
if curl -s -o /dev/null -w "%{http_code}" "$API_URL/health" | grep -q "200\|404\|401"; then
    echo "✅ API Gateway endpoint is reachable"
else
    echo "⚠️  Warning: API Gateway endpoint may not be reachable"
    echo "The test will attempt to connect anyway..."
fi

# Run the test
echo ""
echo "🧪 Starting comprehensive fund update test..."
echo "This test will:"
echo "  ✓ Authenticate with AWS Cognito (or use mock auth)"
echo "  ✓ Create a test fund via API Gateway"
echo "  ✓ Update the fund using various scenarios"
echo "  ✓ Verify updates using both DynamoDB and API Gateway"
echo "  ✓ Clean up test data"
echo ""

# Make the Python script executable and run it
chmod +x test_fund_update_comprehensive.py
python test_fund_update_comprehensive.py

# Check the exit code
if [[ $? -eq 0 ]]; then
    echo ""
    echo "🎉 Test completed successfully!"
    echo "Check the output above for detailed results."
else
    echo ""
    echo "❌ Test completed with errors!"
    echo "Check the output above for error details."
    exit 1
fi

echo ""
echo "📋 Test Summary:"
echo "  • API Gateway integration: Tested"
echo "  • DynamoDB verification: Tested"
echo "  • Dual verification approach: Validated"
echo "  • Fund creation/update/deletion: Tested"
echo ""
echo "✅ Comprehensive fund update testing complete!" 