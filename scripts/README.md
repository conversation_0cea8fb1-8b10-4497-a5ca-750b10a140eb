# PDF Fund Information Extractor

This module provides functionality to extract fund information from PDF files and convert it to the standardized Fund data model using GPT-4o vision model via OpenRouter.

## Overview

The PDF Fund Extractor takes fund documents in PDF format, converts them to images, and uses GPT-4o's vision capabilities to extract structured fund data including:

- Basic fund information (name, type, currency, NAV)
- Performance metrics (returns, volatility, Sharpe ratio)
- Holdings information (top holdings, sector allocation)
- Management details (fund manager, expense ratios)
- Classification data (ISIN, CUSIP, Bloomberg ticker)

## Vision Model Advantages

Using GPT-4o's vision capabilities provides several advantages over text extraction:

- **Better Layout Understanding**: Can interpret tables, charts, and complex layouts
- **Handles Scanned Documents**: Works with image-based PDFs where text extraction fails
- **Visual Context**: Can understand relationships between visual elements
- **Robust to Formatting**: Less sensitive to PDF formatting inconsistencies
- **Chart Reading**: Can extract data from graphs and visual representations

## Installation

### Dependencies

Install the required Python packages:

```bash
pip install -r scripts/requirements.txt
```

The requirements include:

- `pdf2image>=1.16.0` - For PDF to image conversion
- `Pillow>=10.0.0` - For image processing
- `requests>=2.28.0` - For API calls
- `pydantic>=2.0.0` - For data validation

### System Dependencies

For PDF to image conversion, you'll also need `poppler-utils`:

**macOS:**

```bash
brew install poppler
```

**Ubuntu/Debian:**

```bash
sudo apt-get install poppler-utils
```

**Windows:**
Download and install poppler from: https://github.com/oschwartz10612/poppler-windows

### Project Dependencies

The extractor uses the Fund data model from the project's shared models:

- `src/shared/models/fund.py` - Fund data model definitions
- `src/shared/models/` - Supporting models (enums, nested models)

## Configuration

### API Key Setup

Set your OpenRouter API key as an environment variable:

```bash
export OPENROUTER_API_KEY="your_openrouter_api_key_here"
```

Or create a `.env` file in the project root:

```env
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

## Usage

### Command Line Interface

```bash
# Basic extraction
python scripts/pdf_fund_extractor.py path/to/fund_document.pdf

# Save output to specific file
python scripts/pdf_fund_extractor.py path/to/fund_document.pdf -o output.json

# Use specific API key
python scripts/pdf_fund_extractor.py path/to/fund_document.pdf -k your_api_key

# Verbose logging
python scripts/pdf_fund_extractor.py path/to/fund_document.pdf -v
```

### Python API

```python
from scripts.pdf_fund_extractor import PDFFundExtractor

# Initialize extractor
extractor = PDFFundExtractor(api_key="your_api_key")

# Extract fund data
fund = extractor.extract_fund_from_pdf("fund_document.pdf")

# Access extracted data
print(f"Fund Name: {fund.name}")
print(f"Fund Type: {fund.fund_type}")
print(f"Total Assets: {fund.total_assets}")

# Save to JSON
fund_data = fund.model_dump(mode="json")
```

### Batch Processing

```python
from pathlib import Path
from scripts.pdf_fund_extractor import PDFFundExtractor

extractor = PDFFundExtractor()
sample_dir = Path("sample")

for pdf_file in sample_dir.glob("*.pdf"):
    try:
        fund = extractor.extract_fund_from_pdf(pdf_file)
        output_file = pdf_file.with_suffix(".json")

        with open(output_file, 'w') as f:
            f.write(fund.model_dump_json(indent=2))

        print(f"✅ Processed: {pdf_file.name} -> {output_file.name}")

    except Exception as e:
        print(f"❌ Failed to process {pdf_file.name}: {e}")
```

## Supported Data Fields

### Required Fields

- `fund_id` - Unique identifier (auto-generated)
- `name` - Fund name
- `fund_type` - Type: equity, bond, mixed, money_market, alternative, index, etf

### Financial Details

- `nav` - Net Asset Value
- `currency` - USD, EUR, GBP, JPY, CAD, AUD, CHF
- `total_assets` - Assets under management
- `inception_date` - Fund inception date

### Performance Metrics

- Return periods: YTD, 1M, 3M, 6M, 1Y, 3Y, 5Y, inception
- Risk metrics: volatility, Sharpe ratio, max drawdown
- Market metrics: alpha, beta, correlation

### Holdings Information

- Top holdings with percentages
- Sector allocation percentages
- Geographic allocation percentages
- Asset allocation breakdown

### Management Information

- Fund manager name
- Management company
- Expense ratio
- Minimum investment amount

## Testing

### Quick Test

Run the vision extractor test script to verify functionality:

```bash
python scripts/test_vision_extractor.py
```

This will test:

- Basic initialization and vision model setup
- PDF text extraction from sample files
- Fund model conversion
- Real API extraction (if API key is available)

### Comprehensive Tests

Run the full pytest suite:

```bash
# Install pytest if not available
pip install pytest

# Run tests
python -m pytest tests/test_pdf_fund_extractor.py -v
```

### Sample PDF Files

The project includes sample PDF files in the `sample/` directory for testing:

- `AQR Apex teaser 20250430.pdf` - AQR fund document
- `Cerberus Business Finance - Presentation - Spring 2025.pdf` - Cerberus presentation
- `D.E. Shaw Teaser – Amrita Capital - 20250326.pdf` - D.E. Shaw document
- `秦岭全球机会基金_2024 02.pdf` - Chinese fund document
- And more...

## Error Handling

The extractor includes comprehensive error handling for:

### PDF Processing Errors

- File not found
- Corrupted or unreadable PDF files
- Password-protected documents
- Empty or image-only PDFs

### API Errors

- Invalid API key
- Rate limiting
- Network connectivity issues
- Malformed responses

### Data Validation Errors

- Missing required fields
- Invalid data types
- Out-of-range values
- Inconsistent data

### Example Error Handling

```python
from scripts.pdf_fund_extractor import PDFFundExtractor
from pydantic import ValidationError

extractor = PDFFundExtractor()

try:
    fund = extractor.extract_fund_from_pdf("document.pdf")
    print(f"Successfully extracted: {fund.name}")

except FileNotFoundError:
    print("PDF file not found")

except ValidationError as e:
    print(f"Data validation error: {e}")

except requests.RequestException as e:
    print(f"API request failed: {e}")

except Exception as e:
    print(f"Unexpected error: {e}")
```

## Performance Considerations

### PDF Size Limits

- Recommended: < 10MB per file
- Large files may timeout or incur high API costs
- Consider splitting large documents

### API Usage

- GPT-4o has per-token pricing
- Extraction typically uses 2000-4000 tokens per document
- Monitor usage to control costs

### Processing Time

- Simple documents: 10-30 seconds
- Complex documents: 30-90 seconds
- Network latency affects response time

## Troubleshooting

### Common Issues

1. **ImportError: No module named 'src'**

   - Ensure you're running from the project root
   - Check Python path configuration

2. **API Key Error**

   ```bash
   export OPENROUTER_API_KEY="your_key_here"
   ```

3. **PDF Text Extraction Failed**

   - Verify PDF is not password-protected
   - Check if PDF contains extractable text (not just images)

4. **Validation Errors**
   - Review extracted data for completeness
   - Check if required fields are present
   - Verify data types and ranges

### Debug Mode

Enable verbose logging for detailed debugging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

extractor = PDFFundExtractor()
fund = extractor.extract_fund_from_pdf("document.pdf")
```

### API Response Issues

If the AI returns malformed JSON:

1. Check the document quality and clarity
2. Try with a different document
3. Review the extraction prompt for improvements

## Project Structure

```
scripts/
├── pdf_fund_extractor.py      # Main extractor module
├── requirements.txt           # Python dependencies
├── test_vision_extractor.py      # Vision extractor test script
└── README.md                  # This documentation

tests/
└── test_pdf_fund_extractor.py    # Comprehensive test suite

sample/
├── AQR Apex teaser 20250430.pdf
├── Cerberus Business Finance.pdf
└── ...                        # Sample PDF files for testing

src/shared/models/
├── fund.py                    # Fund data model
└── ...                        # Supporting models
```

## API Limits and Costs

### OpenRouter Pricing

- GPT-4o: ~$5 per 1M tokens
- Typical extraction: 2000-4000 tokens
- Cost per document: ~$0.01-$0.02

### Rate Limits

- OpenRouter: Varies by plan
- Implement delays for batch processing
- Monitor API usage in dashboard

## Contributing

### Adding New Fields

To extract additional data fields:

1. Update the extraction prompt in `create_extraction_prompt()`
2. Modify the Fund model in `src/shared/models/fund.py`
3. Update the conversion logic in `convert_to_fund_model()`
4. Add validation tests

### Improving Extraction

- Enhance the AI prompt with better examples
- Add preprocessing for specific document formats
- Implement specialized parsers for common fund families

## License

This module is part of the FundFlow project and follows the project's licensing terms.

## Support

For issues and questions:

1. Check the troubleshooting section
2. Review test outputs for clues
3. Examine the extraction logs
4. Test with known working PDF files
