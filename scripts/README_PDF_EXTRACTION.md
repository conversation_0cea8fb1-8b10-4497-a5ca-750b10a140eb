# PDF Fund Information Extraction

This module provides two methods for extracting fund information from PDF documents using AI models.

## Features

### 1. Vision-based Extraction (Original)

- **Model**: `openai/gpt-4o`
- **Method**: Converts PDF pages to images and uses vision model
- **Best for**: PDFs with complex layouts, charts, tables, or visual elements

### 2. Direct PDF Extraction (New)

- **Model**: `deepseek/deepseek-r1-0528:free`
- **Method**: Sends entire PDF file directly to AI model as binary data
- **Best for**: All PDF types, faster processing, lower cost

## Installation

Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Setup

Set your OpenRouter API key:

```bash
export OPENROUTER_API_KEY="your-api-key-here"
```

## Usage

### Command Line Interface

#### Vision-based extraction (default):

```bash
python scripts/pdf_fund_extractor.py fund_document.pdf
```

#### Direct PDF extraction (new):

```bash
python scripts/pdf_fund_extractor.py --text-mode fund_document.pdf
```

#### Save output to JSON file:

```bash
python scripts/pdf_fund_extractor.py --text-mode fund_document.pdf -o output.json
```

#### Enable verbose logging:

```bash
python scripts/pdf_fund_extractor.py --text-mode --verbose fund_document.pdf
```

### Python API

```python
from pdf_fund_extractor import PDFFundExtractor

# Initialize extractor
extractor = PDFFundExtractor(api_key="your-api-key")

# Vision-based extraction
fund_vision = extractor.extract_fund_from_pdf("fund_document.pdf")

# Direct PDF extraction
fund_direct = extractor.extract_fund_from_pdf_direct("fund_document.pdf")

# Access extracted data
print(f"Fund Name: {fund_direct.name}")
print(f"Fund Type: {fund_direct.fund_type}")
print(f"NAV: {fund_direct.nav}")
print(f"Total Assets: {fund_direct.total_assets}")
```

## Comparison

| Feature        | Vision Mode | Direct Mode       |
| -------------- | ----------- | ----------------- |
| Model          | GPT-4o      | DeepSeek R1       |
| Cost           | Higher      | Lower (free tier) |
| Speed          | Slower      | Faster            |
| PDF Support    | All layouts | All PDF types     |
| Charts/Images  | ✅ Yes      | ✅ Yes            |
| Complex Tables | ✅ Better   | ✅ Good           |
| Simple Text    | ✅ Yes      | ✅ Excellent      |

## Output Format

Both methods return a standardized `Fund` model with the following fields:

- **Basic Info**: fund_id, name, fund_type, currency
- **Financial**: nav, total_assets, expense_ratio, minimum_investment
- **Management**: fund_manager, management_company
- **Identifiers**: isin, cusip, bloomberg_ticker
- **Performance**: returns, volatility, sharpe_ratio, etc.
- **Holdings**: top_holdings, sector_allocation, geographic_allocation

## Error Handling

The extractor includes comprehensive error handling for:

- Missing PDF files
- API connection issues
- Invalid PDF formats
- JSON parsing errors
- Model validation errors

## Testing

Test the new direct PDF functionality:

```bash
python scripts/test_pdf_text_extraction.py
```

## Dependencies

- `pdf2image`: PDF to image conversion (vision mode)
- `Pillow`: Image processing
- `requests`: API calls
- `pydantic`: Data validation
- `boto3`: AWS integration (if needed)

## Troubleshooting

### Common Issues

1. **API Key Error**: Ensure `OPENROUTER_API_KEY` is set
2. **PDF Not Found**: Check file path and permissions
3. **PDF Processing Failed**: Try vision mode for very complex layouts
4. **JSON Parse Error**: Check AI model response format

### Performance Tips

- Use direct mode for most documents (faster and cheaper)
- Use vision mode for complex layouts with charts/tables
- Enable verbose logging for debugging
- Save outputs to JSON for further processing
