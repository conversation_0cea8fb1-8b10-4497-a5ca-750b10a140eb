# Fund Extractor Local Testing

This directory contains scripts to test the fund extractor functionality locally without deploying to AWS.

## Files

- `fastapi_fund_extractor.py` - FastAPI server that mimics the Lambda function
- `test_fund_extractor_client.py` - Client script to test PDF extraction
- `run_fund_extractor_test.sh` - Convenience script to run both server and client

## Prerequisites

1. Set up your environment:
   ```bash
   conda activate ff_env
   ```

2. Set the OpenRouter API key:
   ```bash
   export OPENROUTER_API_KEY='your-api-key-here'
   ```

3. Install required packages (if not already installed):
   ```bash
   pip install fastapi uvicorn python-multipart
   ```

## Usage

### Option 1: Run Everything Automatically

```bash
./scripts/run_fund_extractor_test.sh
```

This will:
1. Start the FastAPI server
2. Wait for it to be ready
3. Run the test client with the sample PDF
4. Keep the server running for manual testing

### Option 2: Run Components Manually

1. Start the server:
   ```bash
   python scripts/fastapi_fund_extractor.py
   ```

2. In another terminal, run the client:
   ```bash
   python scripts/test_fund_extractor_client.py
   ```

### Option 3: Test with Different PDFs

Modify the `pdf_path` variable in `test_fund_extractor_client.py`:

```python
pdf_path = "/path/to/your/pdf/file.pdf"
```

### Option 4: Use the API Directly

Once the server is running, you can:

1. Visit the interactive API docs: http://localhost:8000/docs
2. Use curl to test:
   ```bash
   curl -X POST "http://localhost:8000/api/funds/extract" \
     -F "file=@/path/to/your/pdf/file.pdf" \
     -H "accept: application/json"
   ```

## API Endpoints

- `POST /api/funds/extract` - Extract fund information from PDF
  - Parameters:
    - `file`: PDF file (multipart/form-data)
    - `save`: boolean query parameter (simulates database save)
  - Returns: Extracted fund data in JSON format

- `GET /health` - Health check endpoint

## Debugging Tips

1. The server logs detailed information about the extraction process
2. Check the console output for any errors or warnings
3. The extracted fund data is saved to `extracted_fund_response.json`
4. The FastAPI server provides automatic API documentation at `/docs`

## Differences from Lambda Function

While the FastAPI server mimics the Lambda function logic exactly, there are a few differences:

1. **Authentication**: The Lambda function uses Cognito authentication. The FastAPI version accepts an optional Authorization header but doesn't validate it.

2. **Database Save**: The Lambda function saves to DynamoDB. The FastAPI version only simulates this.

3. **Async Job Processing**: The Lambda function supports async job processing for large PDFs. The FastAPI version processes synchronously.

4. **Error Responses**: Both follow the same error response format, but the FastAPI version provides more detailed error messages for debugging.

## Troubleshooting

1. **Server won't start**: Check if port 8000 is already in use. Change the port with:
   ```bash
   PORT=8001 python scripts/fastapi_fund_extractor.py
   ```

2. **API key error**: Make sure OPENROUTER_API_KEY is set correctly

3. **PDF processing timeout**: Large PDFs may take longer. The client has a 60-second timeout.

4. **Import errors**: Make sure you're in the project root directory when running the scripts