#!/usr/bin/env python3
"""
Add a portfolio with Cognito-style user ID for testing.
"""

import sys
import os
import boto3
from datetime import datetime, timezone
from decimal import Decimal

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from shared.repositories.portfolio_repository import PortfolioRepository
from shared.models.portfolio import PortfolioCreateRequest, PortfolioType, Currency


def main():
    print("🚀 Adding portfolio with Cognito-style user ID...")
    
    # Create portfolio repository
    portfolio_repo = PortfolioRepository(region='ap-northeast-1')
    
    # Create a portfolio with Cognito-style UUID (simulating real auth)
    cognito_user_id = "12345678-1234-1234-1234-123456789012"
    
    portfolio_data = PortfolioCreateRequest(
        name="Test Portfolio for Cognito User",
        description="A test portfolio created for the Cognito-style user ID to test the authentication flow",
        portfolio_type=PortfolioType.PERSONAL,
        base_currency=Currency.USD,
        inception_date=datetime.now(timezone.utc),
        cash_balance=Decimal("10000.00"),
        risk_level="moderate",
        tags=["test", "cognito", "authentication"]
    )
    
    try:
        portfolio = portfolio_repo.create_portfolio(portfolio_data, cognito_user_id)
        print(f"✅ Created portfolio: {portfolio.portfolio_id}")
        print(f"   Name: {portfolio.name}")
        print(f"   User ID: {portfolio.user_id}")
        print(f"   Status: {portfolio.status}")
        print(f"   Type: {portfolio.portfolio_type}")
        print(f"   Cash Balance: ${portfolio.cash_balance}")
        
        # Verify we can retrieve it
        print(f"\n🔍 Verifying retrieval...")
        result = portfolio_repo.list_portfolios(user_id=cognito_user_id)
        print(f"✅ Found {result['count']} portfolios for user {cognito_user_id}")
        
        for p in result['portfolios']:
            print(f"   - {p.portfolio_id}: {p.name}")
            
    except Exception as e:
        print(f"❌ Error creating portfolio: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()