#!/bin/bash

# Check AWS SAM Development Environment Status
# This script verifies that all required tools and configurations are properly set up

# set -e  # Don't exit on errors, we want to check everything

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 FundFlow Development Environment Check${NC}"
echo "Verifying all required tools and configurations..."
echo ""

# Track overall status
overall_status=0

# Function to check command availability
check_command() {
    local cmd=$1
    local name=$2
    local required=$3
    
    if command -v "$cmd" &> /dev/null; then
        version=$($cmd --version 2>&1 | head -n1)
        echo -e "${GREEN}✅ $name: ${NC}$version"
        return 0
    else
        if [ "$required" = true ]; then
            echo -e "${RED}❌ $name: Not installed (REQUIRED)${NC}"
            overall_status=1
        else
            echo -e "${YELLOW}⚠️  $name: Not installed (optional for deployment)${NC}"
        fi
        return 1
    fi
}

# Function to check AWS profile
check_aws_profile() {
    local profile=$1
    local env_name=$2
    
    if aws configure list --profile "$profile" &> /dev/null; then
        # Test if the profile actually works
        if aws sts get-caller-identity --profile "$profile" &> /dev/null; then
            account_id=$(aws sts get-caller-identity --profile "$profile" --query 'Account' --output text 2>/dev/null)
            echo -e "${GREEN}✅ AWS Profile ($env_name): ${NC}$profile (Account: $account_id)"
        else
            echo -e "${YELLOW}⚠️  AWS Profile ($env_name): ${NC}$profile (configured but authentication failed)"
            overall_status=1
        fi
    else
        echo -e "${RED}❌ AWS Profile ($env_name): ${NC}$profile (not configured)"
        overall_status=1
    fi
}

# Function to check template validity
check_template() {
    if sam validate &> /dev/null; then
        echo -e "${GREEN}✅ SAM Template: ${NC}Valid"
    else
        echo -e "${RED}❌ SAM Template: ${NC}Invalid"
        overall_status=1
    fi
}

echo "=== Required Tools ==="

# Check AWS CLI
check_command "aws" "AWS CLI" true

# Check SAM CLI  
check_command "sam" "SAM CLI" true

# Check Docker
check_command "docker" "Docker" false

echo ""
echo "=== AWS Profile Configuration ==="

# Check environment-specific profiles
check_aws_profile "fundflow-dev" "Development"
check_aws_profile "fundflow-staging" "Staging" 
check_aws_profile "fundflow-prod" "Production"

echo ""
echo "=== Template & Configuration ==="

# Check SAM template validity
check_template

# Check if samconfig.toml exists
if [ -f "samconfig.toml" ]; then
    echo -e "${GREEN}✅ SAM Config: ${NC}samconfig.toml exists"
else
    echo -e "${RED}❌ SAM Config: ${NC}samconfig.toml missing"
    overall_status=1
fi

# Check parameter files
echo ""
echo "=== Parameter Files ==="
for env in dev staging prod; do
    param_file="parameters/${env}.json"
    if [ -f "$param_file" ]; then
        echo -e "${GREEN}✅ Parameter File ($env): ${NC}$param_file"
    else
        echo -e "${RED}❌ Parameter File ($env): ${NC}$param_file missing"
        overall_status=1
    fi
done

# Check Python virtual environment
echo ""
echo "=== Python Environment ==="
if [ -n "$VIRTUAL_ENV" ]; then
    echo -e "${GREEN}✅ Virtual Environment: ${NC}Active ($VIRTUAL_ENV)"
else
    echo -e "${YELLOW}⚠️  Virtual Environment: ${NC}Not active (run: source ff_env/bin/activate)"
fi

echo ""
echo "=== Summary ==="

if [ $overall_status -eq 0 ]; then
    echo -e "${GREEN}🎉 Environment setup is complete!${NC}"
    echo ""
    echo "Next steps:"
    echo "  1. If Docker is not installed, install Docker Desktop for local testing"
    echo "  2. Run: ./scripts/deploy.sh -e dev -v (to test deployment)"
    echo "  3. Review: docs/development-setup.md for detailed instructions"
else
    echo -e "${RED}⚠️  Environment setup has issues that need attention${NC}"
    echo ""
    echo "To fix issues:"
    echo "  1. Run: ./scripts/setup-aws-profiles.sh (to configure AWS profiles)"
    echo "  2. Install missing tools (see docs/development-setup.md)"
    echo "  3. Re-run this script to verify fixes"
fi

exit $overall_status 