#!/bin/bash

# FundFlow Environment Configuration Comparison Script
# Compare parameter configurations across environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 FundFlow Environment Configuration Comparison${NC}"
echo ""

# Check if jq is available
if ! command -v jq &> /dev/null; then
    echo -e "${RED}Error: jq is required for this script${NC}"
    echo "Install with: brew install jq"
    exit 1
fi

# Function to get parameter value
get_param() {
    local env=$1
    local param=$2
    local file="parameters/${env}.json"
    
    if [[ -f "$file" ]]; then
        jq -r ".Parameters.$param // \"N/A\"" "$file"
    else
        echo "FILE_MISSING"
    fi
}

# Parameters to compare
params=(
    "Environment"
    "DomainName"
    "CertificateArn"
    "LogLevel"
    "LambdaMemorySize"
    "LambdaTimeout"
    "ApiThrottleBurstLimit"
    "ApiThrottleRateLimit"
)

# Print header
printf "%-25s %-15s %-25s %-15s\n" "Parameter" "Dev" "Staging" "Prod"
printf "%-25s %-15s %-25s %-15s\n" "==========" "===" "=======" "===="

# Compare each parameter
for param in "${params[@]}"; do
    dev_val=$(get_param "dev" "$param")
    staging_val=$(get_param "staging" "$param")
    prod_val=$(get_param "prod" "$param")
    
    # Truncate long values
    dev_val_short=$(echo "$dev_val" | cut -c1-14)
    staging_val_short=$(echo "$staging_val" | cut -c1-24)
    prod_val_short=$(echo "$prod_val" | cut -c1-14)
    
    printf "%-25s %-15s %-25s %-15s\n" "$param" "$dev_val_short" "$staging_val_short" "$prod_val_short"
done

echo ""
echo -e "${BLUE}Environment-Specific Notes:${NC}"
echo -e "${GREEN}• Dev:${NC} Cost-optimized, debug logging, no custom domain"
echo -e "${YELLOW}• Staging:${NC} Production-like, testing domain, moderate resources"
echo -e "${RED}• Prod:${NC} High performance, custom domain, warning-level logging" 