"""
Global pytest configuration and fixtures for FundFlow testing.
Sets up proper environment, mocking, and path configuration.
"""

import os
import sys
import pytest
from pathlib import Path
from unittest.mock import patch, MagicMock

# Use unified mock_aws for moto 5.x
from moto import mock_aws
import boto3

# Add the src directory to Python path for imports
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

# Set up AWS environment variables for testing
os.environ.update(
    {
        "AWS_ACCESS_KEY_ID": "********************",
        "AWS_SECRET_ACCESS_KEY": "YvXx0Bs6qwRRQT2KyOltC9f4B7GRjQA0gEeHRvva",
        "AWS_SECURITY_TOKEN": "testing",
        "AWS_SESSION_TOKEN": "testing",
        "AWS_DEFAULT_REGION": "us-east-1",
        "AWS_REGION": "us-east-1",
        "DYNAMODB_TABLE": "fund-management-dev",
        "USER_POOL_ID": "us-east-1_test123",
        "USER_POOL_CLIENT_ID": "test_client_id",
        # AWS Lambda Powertools environment variables
        "POWERTOOLS_SERVICE_NAME": "fund-management-api",
        "POWERTOOLS_METRICS_NAMESPACE": "FundFlow/Testing",
        "POWERTOOLS_LOGGER_LOG_EVENT": "false",
        "POWERTOOLS_LOGGER_SAMPLE_RATE": "0",
        "POWERTOOLS_TRACE_DISABLED": "true",
        "POWERTOOLS_METRICS_DISABLED": "true",
        "_LAMBDA_FUNCTION_NAME": "fund-api-test",
        "_LAMBDA_FUNCTION_VERSION": "1",
        "AWS_LAMBDA_FUNCTION_NAME": "fund-api-test",
        "AWS_LAMBDA_FUNCTION_VERSION": "1",
    }
)


@pytest.fixture(scope="session", autouse=True)
def setup_powertools_env():
    """Set up AWS Lambda Powertools environment variables for testing."""
    powertools_env = {
        "POWERTOOLS_SERVICE_NAME": "fund-management-api",
        "POWERTOOLS_METRICS_NAMESPACE": "FundFlow/Testing",
        "POWERTOOLS_LOGGER_LOG_EVENT": "false",
        "POWERTOOLS_LOGGER_SAMPLE_RATE": "0",
        "POWERTOOLS_TRACE_DISABLED": "true",
        "POWERTOOLS_METRICS_DISABLED": "true",
        "_LAMBDA_FUNCTION_NAME": "fund-api-test",
        "_LAMBDA_FUNCTION_VERSION": "1",
        "AWS_LAMBDA_FUNCTION_NAME": "fund-api-test",
        "AWS_LAMBDA_FUNCTION_VERSION": "1",
    }

    # Store original values
    original_values = {}
    for key, value in powertools_env.items():
        original_values[key] = os.environ.get(key)
        os.environ[key] = value

    yield

    # Restore original values
    for key, original_value in original_values.items():
        if original_value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = original_value


@pytest.fixture(scope="session", autouse=True)
def aws_credentials():
    """Mocked AWS Credentials for moto."""
    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    os.environ["AWS_SECURITY_TOKEN"] = "testing"
    os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "us-east-1"


@pytest.fixture(scope="function")
def mock_aws_services():
    """Mock all AWS services for testing."""
    with mock_aws():
        yield


@pytest.fixture(scope="function")
def mock_dynamodb_resource(mock_aws_services):
    """Mock DynamoDB resource for testing."""
    yield boto3.resource("dynamodb", region_name="us-east-1")


@pytest.fixture(scope="function")
def mock_s3_resource(mock_aws_services):
    """Mock S3 resource for testing."""
    yield boto3.resource("s3", region_name="us-east-1")


@pytest.fixture(scope="function")
def mock_cognito_client(mock_aws_services):
    """Mock Cognito client for testing."""
    yield boto3.client("cognito-idp", region_name="us-east-1")


@pytest.fixture(autouse=True)
def mock_powertools_logger():
    """Mock AWS Lambda Powertools Logger to avoid logging conflicts during testing."""
    with patch("aws_lambda_powertools.Logger") as mock_logger_class:
        mock_logger_instance = MagicMock()

        # Mock all logger methods to avoid conflicts
        mock_logger_instance.info = MagicMock()
        mock_logger_instance.warning = MagicMock()
        mock_logger_instance.error = MagicMock()
        mock_logger_instance.debug = MagicMock()
        mock_logger_instance.exception = MagicMock()

        def identity_decorator(*args, **kwargs):
            if len(args) == 1 and callable(args[0]):
                # Direct decoration: @decorator
                return args[0]
            else:
                # Parameterized decoration: @decorator(...)
                def wrapper(func):
                    return func

                return wrapper

        mock_logger_instance.inject_lambda_context = identity_decorator

        mock_logger_class.return_value = mock_logger_instance

        yield mock_logger_instance


@pytest.fixture(autouse=True)
def mock_powertools_metrics():
    """Mock AWS Lambda Powertools Metrics to avoid metric namespace issues."""
    with patch("aws_lambda_powertools.Metrics") as mock_metrics_class:
        mock_metrics_instance = MagicMock()

        # Mock metrics methods
        mock_metrics_instance.add_metric = MagicMock()

        def identity_decorator(*args, **kwargs):
            if len(args) == 1 and callable(args[0]):
                # Direct decoration: @decorator
                return args[0]
            else:
                # Parameterized decoration: @decorator(...)
                def wrapper(func):
                    return func

                return wrapper

        mock_metrics_instance.log_metrics = identity_decorator

        mock_metrics_class.return_value = mock_metrics_instance

        yield mock_metrics_instance


@pytest.fixture(autouse=True)
def mock_powertools_tracer():
    """Mock AWS Lambda Powertools Tracer to avoid tracing during tests."""
    with patch("aws_lambda_powertools.Tracer") as mock_tracer_class:
        mock_tracer_instance = MagicMock()

        # Mock tracer methods to be simple pass-through decorators
        def identity_decorator(*args, **kwargs):
            if len(args) == 1 and callable(args[0]):
                # Direct decoration: @decorator
                return args[0]
            else:
                # Parameterized decoration: @decorator(...)
                def wrapper(func):
                    return func

                return wrapper

        mock_tracer_instance.capture_lambda_handler = identity_decorator
        mock_tracer_instance.capture_method = identity_decorator

        mock_tracer_class.return_value = mock_tracer_instance

        yield mock_tracer_instance


@pytest.fixture(autouse=True)
def mock_responses_logger():
    """Mock the logger in shared.api.responses to avoid logging conflicts."""
    with patch("shared.api.responses.logger") as mock_logger:
        mock_logger.error = MagicMock()
        mock_logger.info = MagicMock()
        mock_logger.warning = MagicMock()
        mock_logger.debug = MagicMock()
        yield mock_logger


@pytest.fixture(autouse=True)
def mock_jwt_manager():
    """Mock JWT manager to avoid AWS calls during testing."""
    with patch("shared.security.jwt_utils.JWTManager") as mock_jwt:
        mock_instance = MagicMock()
        mock_instance.verify_token.return_value = {
            "valid": True,
            "user_info": {
                "user_id": "test-user-123",
                "email": "<EMAIL>",
                "role": "fund_manager",
            },
        }
        mock_jwt.return_value = mock_instance
        yield mock_instance


@pytest.fixture(autouse=True)
def mock_session_manager():
    """Mock session manager for all tests."""
    with patch("shared.api.auth_dependencies.SessionManager") as mock_session:
        mock_instance = MagicMock()
        mock_instance.validate_session.return_value = {
            "valid": True,
            "user_info": {
                "user_id": "test-user-123",
                "email": "<EMAIL>",
                "role": "fund_manager",
            },
        }
        mock_session.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def lambda_context():
    """Mock Lambda context for testing."""
    context = MagicMock()
    context.function_name = "fund-api"
    context.function_version = "1"
    context.invoked_function_arn = (
        "arn:aws:lambda:us-east-1:123456789012:function:fund-api"
    )
    context.memory_limit_in_mb = 128
    context.remaining_time_in_millis = lambda: 30000
    context.aws_request_id = "test-request-id"
    return context


@pytest.fixture
def sample_fund_data():
    """Sample fund data for testing."""
    return {
        "fund_id": "FUND-001",
        "name": "Tech Growth Fund",
        "description": "A technology-focused growth fund",
        "fund_type": "EQUITY",
        "status": "ACTIVE",
        "target_amount": 1000000.00,
        "current_amount": 750000.00,
        "minimum_investment": 10000.00,
        "inception_date": "2024-01-15",
        "expense_ratio": 0.75,
        "manager_name": "John Smith",
        "investment_strategy": "Growth-oriented technology investments",
    }
