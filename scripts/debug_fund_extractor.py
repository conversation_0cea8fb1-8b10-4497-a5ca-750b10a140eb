"""
Debug script to run the fund extractor with detailed logging.
"""

import subprocess
import sys
import time
import os

# Check environment variable
if not os.getenv("OPENROUTER_API_KEY"):
    print("❌ Error: OPENROUTER_API_KEY environment variable is not set")
    sys.exit(1)

# Use conda environment Python
python_path = os.path.expanduser("~/miniconda3/envs/ff_env/bin/python")

print("🚀 Starting FastAPI server with debug output...")
print("=" * 60)

# Start server process
server_proc = subprocess.Popen(
    [python_path, "scripts/fastapi_fund_extractor.py"],
    stdout=subprocess.PIPE,
    stderr=subprocess.STDOUT,
    text=True,
    bufsize=1
)

# Wait a bit for server to start
print("⏳ Waiting for server to start...")
time.sleep(3)

# Start client in a separate process
print("\n🧪 Running test client...")
print("=" * 60)

client_proc = subprocess.Popen(
    [python_path, "scripts/test_fund_extractor_client.py"],
    stdout=subprocess.PIPE,
    stderr=subprocess.STDOUT,
    text=True,
    bufsize=1
)

# Print client output
print("\nClient output:")
for line in client_proc.stdout:
    print(line, end='')

client_proc.wait()

print("\n\nServer output:")
print("=" * 60)

# Give server a moment to finish logging
time.sleep(1)

# Terminate server
server_proc.terminate()

# Print server output
for line in server_proc.stdout:
    print(line, end='')

print("\n✅ Test completed")