#!/bin/bash

# FundFlow SAM Guided Deployment Script
# This script uses the guided deployment to set up configuration interactively

set -e  # Exit on any error

echo "🚀 Starting FundFlow guided deployment..."

# Check if SAM CLI is available
if ! command -v sam &> /dev/null; then
    echo "❌ SAM CLI is not installed or not in PATH"
    echo "Please install SAM CLI first: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html"
    exit 1
fi

# Clean previous build artifacts
echo "🧹 Cleaning previous build artifacts..."
rm -rf .aws-sam/

# Build the application
echo "🔨 Building SAM application..."
if ! sam build --parallel; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build completed successfully"

# Deploy with guided mode
echo "🚀 Starting guided deployment..."
echo "This will prompt you for configuration values."
echo "Use these recommended values:"
echo "  Stack Name: fundflow-dev"
echo "  Region: ap-northeast-1"
echo "  Profile: fundflow-dev"
echo "  Confirm changes before deploy: N (for dev environment)"
echo "  Allow SAM CLI IAM role creation: Y"
echo "  Save parameters to samconfig.toml: Y"
echo ""

sam deploy --guided --profile fundflow-dev

echo "🎉 Guided deployment completed!" 