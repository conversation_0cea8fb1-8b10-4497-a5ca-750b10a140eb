#!/bin/bash

# FundFlow SAM Deployment Script
set -e

# Default values
ENVIRONMENT="dev"
REGION="ap-northeast-1"
PROFILE=""
VALIDATE_ONLY=false
BUILD_ONLY=false
USE_PARAMETER_FILE=false
DRY_RUN=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Help function
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment    Target environment (dev, staging, prod) [default: dev]"
    echo "  -r, --region         AWS region [default: ap-northeast-1]"
    echo "  -p, --profile        AWS profile to use"
    echo "  -f, --use-param-file Use parameter file instead of samconfig.toml overrides"
    echo "  -v, --validate-only  Only validate the template, don't deploy"
    echo "  -b, --build-only     Only build, don't deploy"
    echo "  -d, --dry-run        Preview changes without deploying (requires changeset)"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -e dev                    Deploy to dev environment"
    echo "  $0 -e prod -p production     Deploy to prod with specific AWS profile"
    echo "  $0 -v                        Validate template only"
    echo "  $0 -d -e prod                Dry run for production (preview changes)"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -p|--profile)
            PROFILE="$2"
            shift 2
            ;;
        -f|--use-param-file)
            USE_PARAMETER_FILE=true
            shift
            ;;
        -v|--validate-only)
            VALIDATE_ONLY=true
            shift
            ;;
        -b|--build-only)
            BUILD_ONLY=true
            shift
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    echo -e "${RED}Error: Environment must be one of: dev, staging, prod${NC}"
    exit 1
fi

# Production deployment safety check
if [[ "$ENVIRONMENT" == "prod" && "$DRY_RUN" == false ]]; then
    echo -e "${YELLOW}⚠️  WARNING: You are about to deploy to PRODUCTION!${NC}"
    echo -e "${YELLOW}This will affect live users and services.${NC}"
    echo ""
    read -p "Are you sure you want to continue? Type 'yes' to proceed: " confirmation
    if [[ "$confirmation" != "yes" ]]; then
        echo -e "${BLUE}Deployment cancelled by user.${NC}"
        exit 0
    fi
    echo ""
fi

# Set AWS profile if provided
if [[ -n "$PROFILE" ]]; then
    export AWS_PROFILE="$PROFILE"
    echo -e "${BLUE}Using AWS profile: $PROFILE${NC}"
fi

echo -e "${BLUE}🚀 FundFlow Deployment${NC}"
echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"
echo -e "${BLUE}Region: $REGION${NC}"
if [[ "$DRY_RUN" == true ]]; then
    echo -e "${YELLOW}Mode: DRY RUN (preview only)${NC}"
fi
echo ""

# Check if SAM CLI is installed
if ! command -v sam &> /dev/null; then
    echo -e "${RED}Error: SAM CLI is not installed. Please install it first.${NC}"
    echo "Installation instructions: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html"
    exit 1
fi

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}Error: AWS CLI is not configured or credentials are invalid.${NC}"
    exit 1
fi

# Verify correct AWS account for environment
if [[ "$ENVIRONMENT" == "prod" ]]; then
    account_id=$(aws sts get-caller-identity --query 'Account' --output text)
    echo -e "${BLUE}Deploying to AWS Account: $account_id${NC}"
    echo -e "${YELLOW}⚠️  Please verify this is the correct production account!${NC}"
    read -p "Continue with this account? (y/N): " account_confirm
    if [[ ! $account_confirm =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}Deployment cancelled by user.${NC}"
        exit 0
    fi
fi

echo -e "${GREEN}✓ Prerequisites check passed${NC}"

# Validate template
echo -e "${YELLOW}📋 Validating SAM template...${NC}"
if ! sam validate --region "$REGION"; then
    echo -e "${RED}❌ Template validation failed${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Template validation passed${NC}"

if [[ "$VALIDATE_ONLY" == true ]]; then
    echo -e "${GREEN}✓ Validation complete${NC}"
    exit 0
fi

# Build
echo -e "${YELLOW}🔨 Building SAM application...${NC}"
if ! sam build --region "$REGION"; then
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Build complete${NC}"

if [[ "$BUILD_ONLY" == true ]]; then
    echo -e "${GREEN}✓ Build complete${NC}"
    exit 0
fi

# Deploy
if [[ "$DRY_RUN" == true ]]; then
    echo -e "${YELLOW}🔍 Creating deployment preview (changeset)...${NC}"
else
    echo -e "${YELLOW}🚀 Deploying to $ENVIRONMENT environment...${NC}"
fi

if [[ "$USE_PARAMETER_FILE" == true ]]; then
    PARAM_FILE="parameters/${ENVIRONMENT}.json"
    if [[ ! -f "$PARAM_FILE" ]]; then
        echo -e "${RED}❌ Parameter file not found: $PARAM_FILE${NC}"
        exit 1
    fi
    echo -e "${BLUE}Using parameter file: $PARAM_FILE${NC}"
    
    # Deployment command with parameter file
    deploy_cmd="sam deploy --parameter-overrides file://$PARAM_FILE --stack-name fundflow-$ENVIRONMENT --region $REGION --capabilities CAPABILITY_IAM"
    if [[ "$DRY_RUN" == true ]]; then
        deploy_cmd="$deploy_cmd --no-execute-changeset"
    fi
    
    if ! eval "$deploy_cmd"; then
        echo -e "${RED}❌ Deployment failed${NC}"
        exit 1
    fi
else
    # Deployment command with samconfig
    deploy_cmd="sam deploy --config-env $ENVIRONMENT --region $REGION"
    if [[ "$DRY_RUN" == true ]]; then
        deploy_cmd="$deploy_cmd --no-execute-changeset"
    fi
    
    if ! eval "$deploy_cmd"; then
        echo -e "${RED}❌ Deployment failed${NC}"
        exit 1
    fi
fi

if [[ "$DRY_RUN" == true ]]; then
    echo -e "${GREEN}✅ Deployment preview complete!${NC}"
    echo -e "${BLUE}To execute the changeset, run the deployment without --dry-run${NC}"
    exit 0
fi

echo -e "${GREEN}✅ Deployment to $ENVIRONMENT completed successfully!${NC}"

# Get stack outputs
echo -e "${YELLOW}📊 Stack outputs:${NC}"
STACK_NAME="fundflow-$ENVIRONMENT"
aws cloudformation describe-stacks \
    --stack-name "$STACK_NAME" \
    --region "$REGION" \
    --query 'Stacks[0].Outputs' \
    --output table 2>/dev/null || echo -e "${YELLOW}Could not retrieve stack outputs${NC}"

echo -e "${GREEN}🎉 FundFlow deployment complete!${NC}" 