"""
FastAPI application to mimic the fund_extractor Lambda function for local debugging.
This provides the exact same logic as the Lambda function but in a FastAPI server.
"""

import os
import sys
import json
import base64
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timezone
from pathlib import Path

from fastapi import FastAP<PERSON>, UploadFile, File, HTTPException, Query, Header
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, ValidationError
import uvicorn

# Add the src directory to Python path to import our models
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), "src"))

from functions.api.pdf_fund_extractor import PDFFundExtractor
from shared.models.fund import Fund

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="Fund Extractor API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class ExtractionResponse(BaseModel):
    """Response model for fund extraction."""

    data: Dict[str, Any]
    message: str
    timestamp: str
    saved_to_database: Optional[bool] = None
    database_fund_id: Optional[str] = None
    database_error: Optional[str] = None


class ErrorResponse(BaseModel):
    """Error response model."""

    error: str
    message: str
    timestamp: str
    details: Optional[Dict[str, Any]] = None


def serialize_fund_data(fund: Fund) -> Dict[str, Any]:
    """Serialize fund data for JSON response, converting Decimal to float."""
    return json.loads(fund.model_dump_json())


@app.post("/api/funds/extract", response_model=ExtractionResponse)
async def extract_fund_from_pdf(
    file: UploadFile = File(...),
    save: bool = Query(False, description="Save extracted fund to database"),
    authorization: Optional[str] = Header(None),
):
    """
    Extract fund information from uploaded PDF file.

    This endpoint mimics the Lambda function behavior exactly:
    1. Accepts multipart form data with PDF file
    2. Uses PDFFundExtractor to process the PDF
    3. Returns extracted fund data in the same format
    4. Optionally saves to database (simulated in this case)

    Args:
        file: PDF file upload
        save: Whether to save to database (simulated)
        authorization: Authorization header (optional for local testing)

    Returns:
        Extracted fund data
    """
    try:
        # Log request info
        logger.info(
            f"PDF extraction request received",
            extra={
                "pdf_filename": file.filename,
                "content_type": file.content_type,
                "save": save,
            },
        )

        # Validate file type
        if not file.content_type or not file.content_type.startswith("application/pdf"):
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "INVALID_FILE_TYPE",
                    "message": "Only PDF files are supported",
                    "details": {"content_type": file.content_type},
                },
            )

        # Read file content
        file_content = await file.read()
        if not file_content:
            raise HTTPException(
                status_code=400,
                detail={"error": "EMPTY_FILE", "message": "Uploaded file is empty"},
            )

        # Convert to base64
        pdf_base64 = base64.b64encode(file_content).decode("utf-8")
        logger.info(
            f"File content read successfully",
            extra={
                "file_size_bytes": len(file_content),
                "base64_size_chars": len(pdf_base64),
            },
        )

        # Initialize extractor
        try:
            extractor = PDFFundExtractor()
        except ValueError as e:
            logger.error(f"Failed to initialize PDF extractor: {e}")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "CONFIGURATION_ERROR",
                    "message": "PDF extraction service is not properly configured",
                    "details": {"error": str(e)},
                },
            )

        # Extract fund data
        logger.info("Starting PDF extraction process")
        fund = extractor.extract_fund_from_pdf_direct(pdf_base64)

        logger.info(
            f"PDF extraction completed successfully",
            extra={
                "fund_name": fund.name,
                "fund_id": fund.fund_id,
                "fund_type": fund.fund_type,
            },
        )

        # Convert fund to response format
        fund_data = serialize_fund_data(fund)

        # Simulate database save if requested
        response_data = {
            "data": fund_data,
            "message": "Fund information successfully extracted from PDF",
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        if save:
            # In the Lambda function, this would actually save to DynamoDB
            # Here we just simulate it
            logger.info(f"Simulating save to database for fund: {fund.fund_id}")
            response_data["saved_to_database"] = True
            response_data["database_fund_id"] = fund.fund_id

        return response_data

    except ValidationError as e:
        logger.error(f"Fund model validation error: {e}")
        raise HTTPException(
            status_code=400,
            detail={
                "error": "VALIDATION_ERROR",
                "message": "Extracted fund data failed validation",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "details": {"validation_errors": str(e)},
            },
        )
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error during PDF extraction: {e}", exc_info=True)

        # Provide more specific error messages for common issues
        error_message = "An unexpected error occurred during PDF processing"
        if isinstance(e, OSError) and "File name too long" in str(e):
            error_message = "PDF file processing failed due to internal file handling error. Please try again."
        elif isinstance(e, OSError):
            error_message = "PDF file could not be processed due to a system error. Please try again."

        raise HTTPException(
            status_code=500,
            detail={
                "error": "EXTRACTION_ERROR",
                "message": error_message,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "details": {"error": str(e)},
            },
        )


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": "fund-extractor-api",
    }


if __name__ == "__main__":
    # Check for required environment variables
    if not os.getenv("OPENROUTER_API_KEY"):
        logger.error("OPENROUTER_API_KEY environment variable is required")
        sys.exit(1)

    # Run the server
    port = int(os.getenv("PORT", "8000"))
    logger.info(f"Starting FastAPI server on port {port}")
    uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
