"""
Find models that support PDF processing through OpenRouter.
"""

import os
import requests
import json

api_key = os.getenv("OPENROUTER_API_KEY")
if not api_key:
    print("❌ OPENROUTER_API_KEY environment variable not set")
    exit(1)

# Get list of models
models_url = "https://openrouter.ai/api/v1/models"
headers = {
    "Authorization": f"Bearer {api_key}",
}

print("🔍 Fetching available models...")
response = requests.get(models_url, headers=headers, timeout=10)

if response.status_code != 200:
    print(f"❌ Failed to fetch models: {response.status_code}")
    exit(1)

models_data = response.json()
models = models_data.get("data", [])

print(f"Found {len(models)} models\n")

# Look for models that might support PDFs
pdf_keywords = ["pdf", "document", "multimodal", "vision", "file", "claude-3", "gpt-4"]
potential_models = []

for model in models:
    model_id = model.get("id", "")
    description = model.get("description", "").lower()
    name = model.get("name", "").lower()
    
    # Check if model might support PDFs
    for keyword in pdf_keywords:
        if keyword in model_id.lower() or keyword in description or keyword in name:
            if model_id not in [m["id"] for m in potential_models]:
                potential_models.append({
                    "id": model_id,
                    "name": model.get("name"),
                    "description": model.get("description", "")[:100],
                    "context_length": model.get("context_length", "unknown"),
                    "pricing": model.get("pricing", {})
                })
                break

# Sort by likely PDF support (Claude and GPT-4 models first)
def sort_key(model):
    id_lower = model["id"].lower()
    if "claude" in id_lower and "opus" in id_lower:
        return 0
    elif "claude" in id_lower:
        return 1
    elif "gpt-4" in id_lower:
        return 2
    else:
        return 3

potential_models.sort(key=sort_key)

print("🎯 Models that might support PDF processing:\n")
for model in potential_models[:15]:  # Show top 15
    print(f"Model: {model['id']}")
    print(f"  Name: {model['name']}")
    print(f"  Context: {model['context_length']}")
    if model['description']:
        print(f"  Description: {model['description']}...")
    
    # Show pricing if available
    pricing = model.get('pricing', {})
    if pricing:
        prompt_price = pricing.get('prompt', 'N/A')
        completion_price = pricing.get('completion', 'N/A')
        print(f"  Pricing: ${prompt_price}/1K prompt, ${completion_price}/1K completion")
    print()

# Specifically check for Claude models since they often support various file types
print("\n📋 Claude models (known to support various file types):")
claude_models = [m for m in models if "claude" in m.get("id", "").lower()]
for model in claude_models[:5]:
    print(f"- {model['id']}")