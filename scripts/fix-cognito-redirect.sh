#!/bin/bash

# Fix Cognito Redirect Error for FundFlow
# This script updates the Cognito User Pool Client with correct callback URLs

echo "🔧 Fixing Cognito Redirect Error for FundFlow"
echo "=============================================="

# Configuration
AWS_PROFILE=${AWS_PROFILE:-fundflow-dev}
AWS_REGION=${AWS_REGION:-ap-northeast-1}
USER_POOL_ID="ap-northeast-1_H2kKHGUAT"
CLIENT_ID="2jh76f894g6lv9vrus4qbb9hu7"
PINGGY_URL="https://rmzqmzxtlz.a.pinggy.link"

echo "📋 Configuration:"
echo "  - AWS Profile: $AWS_PROFILE"
echo "  - AWS Region: $AWS_REGION"
echo "  - User Pool ID: $USER_POOL_ID"
echo "  - Client ID: $CLIENT_ID"
echo "  - Pinggy.link URL: $PINGGY_URL"
echo ""

# Check if AWS CLI is configured
echo "🔍 Checking AWS CLI configuration..."
if ! aws sts get-caller-identity --profile $AWS_PROFILE >/dev/null 2>&1; then
    echo "❌ AWS CLI not configured or credentials invalid"
    echo ""
    echo "📋 Manual Fix Instructions:"
    echo "=========================="
    echo "1. Go to AWS Console → Cognito → User Pools"
    echo "2. Select User Pool: $USER_POOL_ID"
    echo "3. Go to 'App integration' → 'App clients and analytics'"
    echo "4. Click on client: $CLIENT_ID"
    echo "5. Click 'Edit' in the 'Hosted UI' section"
    echo "6. Update 'Allowed callback URLs' to include:"
    echo "   - http://localhost:3000/api/auth/callback/cognito"
    echo "   - $PINGGY_URL/api/auth/callback/cognito"
    echo "7. Update 'Allowed sign-out URLs' to include:"
    echo "   - http://localhost:3000"
    echo "   - $PINGGY_URL"
    echo "8. Click 'Save changes'"
    echo ""
    echo "🔗 Direct link to Cognito Console:"
    echo "https://ap-northeast-1.console.aws.amazon.com/cognito/v2/idp/user-pools/$USER_POOL_ID/app-integration/clients/$CLIENT_ID"
    exit 1
fi

echo "✅ AWS CLI configured"

# Get current client configuration
echo ""
echo "🔍 Getting current client configuration..."
CLIENT_CONFIG=$(aws cognito-idp describe-user-pool-client \
    --user-pool-id "$USER_POOL_ID" \
    --client-id "$CLIENT_ID" \
    --region "$AWS_REGION" \
    --profile "$AWS_PROFILE" 2>/dev/null)

if [ $? -ne 0 ]; then
    echo "❌ Failed to get client configuration"
    echo "Please check your AWS credentials and User Pool/Client IDs"
    exit 1
fi

echo "✅ Retrieved client configuration"

# Extract current callback URLs
CURRENT_CALLBACK_URLS=$(echo "$CLIENT_CONFIG" | jq -r '.UserPoolClient.CallbackURLs[]?' 2>/dev/null | tr '\n' ' ')
CURRENT_LOGOUT_URLS=$(echo "$CLIENT_CONFIG" | jq -r '.UserPoolClient.LogoutURLs[]?' 2>/dev/null | tr '\n' ' ')

echo ""
echo "📋 Current Configuration:"
echo "   Callback URLs: $CURRENT_CALLBACK_URLS"
echo "   Logout URLs: $CURRENT_LOGOUT_URLS"

# Update the client with correct callback URLs
echo ""
echo "🔧 Updating User Pool Client with correct callback URLs..."

CALLBACK_URLS_JSON='[
    "http://localhost:3000/api/auth/callback/cognito",
    "'$PINGGY_URL'/api/auth/callback/cognito"
]'

LOGOUT_URLS_JSON='[
    "http://localhost:3000",
    "'$PINGGY_URL'"
]'

UPDATE_RESULT=$(aws cognito-idp update-user-pool-client \
    --user-pool-id "$USER_POOL_ID" \
    --client-id "$CLIENT_ID" \
    --callback-urls "http://localhost:3000/api/auth/callback/cognito" "$PINGGY_URL/api/auth/callback/cognito" \
    --logout-urls "http://localhost:3000" "$PINGGY_URL" \
    --allowed-o-auth-flows "code" \
    --allowed-o-auth-scopes "openid" "profile" "email" "aws.cognito.signin.user.admin" \
    --allowed-o-auth-flows-user-pool-client \
    --supported-identity-providers "COGNITO" \
    --region "$AWS_REGION" \
    --profile "$AWS_PROFILE" 2>&1)

if [ $? -eq 0 ]; then
    echo "✅ User Pool Client updated successfully"
    echo ""
    echo "📋 Updated Configuration:"
    echo "   Callback URLs:"
    echo "     - http://localhost:3000/api/auth/callback/cognito"
    echo "     - $PINGGY_URL/api/auth/callback/cognito"
    echo "   Logout URLs:"
    echo "     - http://localhost:3000"
    echo "     - $PINGGY_URL"
    echo "   OAuth Flows: Authorization Code Grant"
    echo "   OAuth Scopes: openid, profile, email, aws.cognito.signin.user.admin"
    echo ""
    echo "🎉 Redirect error should now be fixed!"
    echo ""
    echo "🔄 Next Steps:"
    echo "1. Restart your Next.js development server"
    echo "2. Try signing in again"
    echo "3. The redirect should now work with both localhost:3000 and pinggy.link"
else
    echo "❌ Failed to update User Pool Client:"
    echo "$UPDATE_RESULT"
    echo ""
    echo "📋 Manual Fix Instructions:"
    echo "=========================="
    echo "1. Go to AWS Console → Cognito → User Pools"
    echo "2. Select User Pool: $USER_POOL_ID"
    echo "3. Go to 'App integration' → 'App clients and analytics'"
    echo "4. Click on client: $CLIENT_ID"
    echo "5. Click 'Edit' in the 'Hosted UI' section"
    echo "6. Update 'Allowed callback URLs' to include:"
    echo "   - http://localhost:3000/api/auth/callback/cognito"
    echo "   - $PINGGY_URL/api/auth/callback/cognito"
    echo "7. Update 'Allowed sign-out URLs' to include:"
    echo "   - http://localhost:3000"
    echo "   - $PINGGY_URL"
    echo "8. Click 'Save changes'"
    echo ""
    echo "🔗 Direct link to Cognito Console:"
    echo "https://ap-northeast-1.console.aws.amazon.com/cognito/v2/idp/user-pools/$USER_POOL_ID/app-integration/clients/$CLIENT_ID"
    exit 1
fi
