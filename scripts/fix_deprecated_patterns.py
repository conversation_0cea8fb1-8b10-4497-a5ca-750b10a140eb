#!/usr/bin/env python3
"""
<PERSON>ript to fix Pydantic v2 deprecation warnings and datetime.now(timezone.utc) usage.
"""

import os
import re
from pathlib import Path
from typing import List, <PERSON><PERSON>


def find_python_files(root_dir: str) -> List[Path]:
    """Find all Python files in the project."""
    python_files = []
    for root, dirs, files in os.walk(root_dir):
        # Skip certain directories
        if any(
            skip in root
            for skip in [
                ".git",
                "__pycache__",
                ".pytest_cache",
                "node_modules",
                ".venv",
                "venv",
            ]
        ):
            continue
        for file in files:
            if file.endswith(".py"):
                python_files.append(Path(root) / file)
    return python_files


def fix_datetime_utcnow(content: str) -> str:
    """Fix datetime.now(timezone.utc) to datetime.now(timezone.utc)."""
    # Add timezone import if not present
    if "from datetime import" in content and "timezone" not in content:
        content = re.sub(
            r"from datetime import ([^,\n]+)",
            r"from datetime import \1, timezone",
            content,
        )

    # Replace datetime.now(timezone.utc) with datetime.now(timezone.utc)
    content = re.sub(r"datetime\.utcnow\(\)", "datetime.now(timezone.utc)", content)

    return content


def fix_pydantic_config(content: str) -> str:
    """Fix Pydantic class Config to model_config with ConfigDict."""
    # Ensure ConfigDict is imported
    if "from pydantic import" in content and "ConfigDict" not in content:
        content = re.sub(
            r"(from pydantic import [^,\n]+)([,\n])", r"\1, ConfigDict\2", content
        )

    # Replace class Config with model_config
    content = re.sub(
        r"    class Config:\s*\n((?:        [^\n]*\n)*)",
        lambda m: convert_config_to_dict(m.group(1)),
        content,
        flags=re.MULTILINE,
    )

    return content


def convert_config_to_dict(config_body: str) -> str:
    """Convert Config class body to model_config dict."""
    lines = config_body.strip().split("\n")
    config_items = []

    for line in lines:
        line = line.strip()
        if not line or line.startswith("#"):
            continue

        # Skip json_encoders - we'll handle with field_serializer
        if "json_encoders" in line:
            continue

        # Convert other config items
        if "=" in line:
            config_items.append(f"        {line}")

    if config_items:
        return (
            f"    model_config = ConfigDict(\n" + ",\n".join(config_items) + "\n    )\n"
        )
    else:
        return "    model_config = ConfigDict()\n"


def add_field_serializers(content: str) -> str:
    """Add field_serializer imports and methods for json_encoders replacement."""
    # Add field_serializer import
    if "from pydantic import" in content and "field_serializer" not in content:
        content = re.sub(
            r"(from pydantic import [^,\n]+)([,\n])", r"\1, field_serializer\2", content
        )

    # Find models with json_encoders and add appropriate serializers
    if "datetime: lambda v: v.isoformat()" in content:
        serializer = '''
    @field_serializer('*', when_used='json')
    def serialize_datetime(self, value: Any) -> Any:
        """Serialize datetime objects to ISO format."""
        if isinstance(value, datetime):
            return value.isoformat()
        return value
'''
        # Add after model_config if it doesn't already exist
        if "@field_serializer" not in content:
            content = re.sub(
                r"(    model_config = ConfigDict\([^)]*\)\n)",
                r"\1" + serializer,
                content,
            )

    if "Decimal: str" in content:
        serializer = '''
    @field_serializer('*', when_used='json')
    def serialize_decimal(self, value: Any) -> Any:
        """Serialize Decimal objects to string."""
        if isinstance(value, Decimal):
            return str(value)
        return value
'''
        # Add after model_config if it doesn't already exist
        if "@field_serializer" not in content and "serialize_decimal" not in content:
            content = re.sub(
                r"(    model_config = ConfigDict\([^)]*\)\n)",
                r"\1" + serializer,
                content,
            )

    return content


def fix_field_default_factories(content: str) -> str:
    """Fix Field default_factory for datetime.utcnow usage."""
    content = re.sub(
        r"Field\(default_factory=datetime\.utcnow\)",
        "Field(default_factory=lambda: datetime.now(timezone.utc))",
        content,
    )
    return content


def process_file(file_path: Path) -> bool:
    """Process a single Python file to fix deprecated patterns."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            original_content = f.read()

        content = original_content

        # Apply fixes
        content = fix_datetime_utcnow(content)
        content = fix_field_default_factories(content)
        content = fix_pydantic_config(content)
        content = add_field_serializers(content)

        # Only write if content changed
        if content != original_content:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"Fixed: {file_path}")
            return True

        return False

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False


def main():
    """Main function to fix all deprecated patterns in the project."""
    project_root = Path(".")

    # Find all Python files
    python_files = find_python_files(str(project_root))

    print(f"Found {len(python_files)} Python files")

    fixed_count = 0
    for file_path in python_files:
        if process_file(file_path):
            fixed_count += 1

    print(f"Fixed {fixed_count} files")


if __name__ == "__main__":
    main()
