#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix VS Code Python syntax error detection issues.
This script will help diagnose and fix common Python configuration problems in VS Code.
"""

import json
import os
import subprocess
import sys
from pathlib import Path


def check_python_interpreter():
    """Check if Python interpreter is working correctly."""
    print("🔍 Checking Python interpreter...")
    
    try:
        result = subprocess.run([sys.executable, "--version"], 
                              capture_output=True, text=True)
        print(f"✅ Python version: {result.stdout.strip()}")
        print(f"✅ Python executable: {sys.executable}")
        return True
    except Exception as e:
        print(f"❌ Error checking Python interpreter: {e}")
        return False


def check_vscode_settings():
    """Check and update VS Code settings for Python."""
    print("\n🔍 Checking VS Code settings...")
    
    vscode_dir = Path(".vscode")
    settings_file = vscode_dir / "settings.json"
    
    if not vscode_dir.exists():
        vscode_dir.mkdir()
        print("✅ Created .vscode directory")
    
    # Recommended settings for Python development
    recommended_settings = {
        "python.defaultInterpreterPath": "/Users/<USER>/miniconda3/envs/ff_env/bin/python",
        "python.languageServer": "Pylance",
        "python.analysis.typeCheckingMode": "basic",
        "python.analysis.autoImportCompletions": True,
        "python.analysis.extraPaths": ["${workspaceFolder}/src"],
        "python.linting.enabled": True,
        "python.linting.pylintEnabled": False,
        "python.linting.flake8Enabled": True,
        "python.linting.flake8Args": [
            "--max-line-length=88",
            "--ignore=E203,W503"
        ],
        "python.formatting.provider": "black",
        "python.testing.pytestEnabled": True,
        "python.testing.unittestEnabled": False,
        "files.exclude": {
            "**/__pycache__": True,
            "**/*.pyc": True
        }
    }
    
    if settings_file.exists():
        with open(settings_file, 'r') as f:
            current_settings = json.load(f)
        
        # Merge settings
        current_settings.update(recommended_settings)
        
        with open(settings_file, 'w') as f:
            json.dump(current_settings, f, indent=4)
        
        print("✅ Updated VS Code settings")
    else:
        with open(settings_file, 'w') as f:
            json.dump(recommended_settings, f, indent=4)
        
        print("✅ Created VS Code settings file")
    
    return True


def check_python_packages():
    """Check if required Python packages are installed."""
    print("\n🔍 Checking Python packages...")
    
    required_packages = [
        "flake8",
        "black", 
        "pylint",
        "mypy"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            print(f"❌ {package} is missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install"] + missing_packages, 
                          check=True)
            print("✅ Successfully installed missing packages")
        except subprocess.CalledProcessError as e:
            print(f"❌ Error installing packages: {e}")
            return False
    
    return True


def create_test_file():
    """Create a test file with syntax errors to verify detection."""
    print("\n🔍 Creating test file with syntax errors...")
    
    test_content = '''# Test file with intentional syntax errors
def test_function():
    print("Hello World"  # Missing closing parenthesis
    
    # Invalid indentation
  invalid_indent = "test"
    
    # Undefined variable
    result = undefined_variable + 5
    
    # Invalid syntax
    if True
        print("Missing colon")
        
    return result

# Another syntax error
def another_function(
    print("Missing closing parenthesis in function definition")
'''
    
    with open("test_syntax_errors.py", "w") as f:
        f.write(test_content)
    
    print("✅ Created test_syntax_errors.py with intentional syntax errors")
    print("   Open this file in VS Code to check if syntax errors are highlighted")
    
    return True


def main():
    """Main function to run all checks and fixes."""
    print("🚀 VS Code Python Syntax Error Detection Fix")
    print("=" * 50)
    
    success = True
    
    # Run all checks
    success &= check_python_interpreter()
    success &= check_vscode_settings()
    success &= check_python_packages()
    success &= create_test_file()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All checks completed successfully!")
        print("\n📋 Next steps:")
        print("1. Restart VS Code completely")
        print("2. Open test_syntax_errors.py in VS Code")
        print("3. Check if syntax errors are highlighted in red")
        print("4. If issues persist, try:")
        print("   - Press Cmd+Shift+P and run 'Python: Select Interpreter'")
        print("   - Choose the ff_env interpreter")
        print("   - Press Cmd+Shift+P and run 'Developer: Reload Window'")
    else:
        print("❌ Some checks failed. Please review the errors above.")
    
    return success


if __name__ == "__main__":
    main()
