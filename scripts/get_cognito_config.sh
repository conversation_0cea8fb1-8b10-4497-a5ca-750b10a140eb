#!/bin/bash

# Get Cognito Configuration Script for FundFlow
# This script retrieves the Cognito User Pool ID and Client ID from the deployed AWS stack

echo "🔐 FundFlow Cognito Configuration Retrieval"
echo "============================================"

# Set default values
ENVIRONMENT=${ENVIRONMENT:-dev}
AWS_PROFILE=${AWS_PROFILE:-fundflow-dev}
AWS_REGION=${AWS_REGION:-ap-northeast-1}
STACK_NAME="fundflow-${ENVIRONMENT}"

echo "📋 Configuration:"
echo "  - Environment: $ENVIRONMENT"
echo "  - AWS Profile: $AWS_PROFILE"
echo "  - AWS Region: $AWS_REGION"
echo "  - Stack Name: $STACK_NAME"
echo ""

# Check AWS credentials
echo "🔐 Checking AWS credentials..."
if ! AWS_PROFILE=$AWS_PROFILE aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS credentials not found or invalid for profile: $AWS_PROFILE"
    echo "Please ensure AWS credentials are configured properly."
    exit 1
fi

echo "✅ AWS credentials verified"
echo ""

# Get Cognito User Pool ID
echo "📥 Retrieving Cognito User Pool ID..."
USER_POOL_ID=$(AWS_PROFILE=$AWS_PROFILE aws cloudformation describe-stacks \
    --stack-name "$STACK_NAME" \
    --query 'Stacks[0].Outputs[?OutputKey==`UserPoolId`].OutputValue' \
    --output text \
    --region $AWS_REGION 2>/dev/null)

if [ -z "$USER_POOL_ID" ] || [ "$USER_POOL_ID" = "None" ]; then
    echo "❌ Could not retrieve User Pool ID from CloudFormation stack"
    echo "Trying alternative method..."
    
    # Try to find user pools by name
    USER_POOL_ID=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp list-user-pools \
        --max-results 20 \
        --region $AWS_REGION \
        --query "UserPools[?contains(Name, '$STACK_NAME')].Id" \
        --output text 2>/dev/null)
fi

# Get Cognito User Pool Client ID
echo "📥 Retrieving Cognito User Pool Client ID..."
CLIENT_ID=$(AWS_PROFILE=$AWS_PROFILE aws cloudformation describe-stacks \
    --stack-name "$STACK_NAME" \
    --query 'Stacks[0].Outputs[?OutputKey==`UserPoolClientId`].OutputValue' \
    --output text \
    --region $AWS_REGION 2>/dev/null)

if [ -z "$CLIENT_ID" ] || [ "$CLIENT_ID" = "None" ]; then
    echo "❌ Could not retrieve Client ID from CloudFormation stack"
    if [ -n "$USER_POOL_ID" ]; then
        echo "Trying to get client ID from User Pool..."
        CLIENT_ID=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp list-user-pool-clients \
            --user-pool-id "$USER_POOL_ID" \
            --region $AWS_REGION \
            --query 'UserPoolClients[0].ClientId' \
            --output text 2>/dev/null)
    fi
fi

echo ""
echo "🎯 Results:"
echo "===================="

if [ -n "$USER_POOL_ID" ] && [ "$USER_POOL_ID" != "None" ]; then
    echo "✅ User Pool ID: $USER_POOL_ID"
    ISSUER_URL="https://cognito-idp.${AWS_REGION}.amazonaws.com/${USER_POOL_ID}"
    echo "✅ Issuer URL: $ISSUER_URL"
else
    echo "❌ User Pool ID: Not found"
fi

if [ -n "$CLIENT_ID" ] && [ "$CLIENT_ID" != "None" ]; then
    echo "✅ Client ID: $CLIENT_ID"
else
    echo "❌ Client ID: Not found"
fi

echo ""

# Generate environment variables if both values are found
if [ -n "$USER_POOL_ID" ] && [ "$USER_POOL_ID" != "None" ] && [ -n "$CLIENT_ID" ] && [ "$CLIENT_ID" != "None" ]; then
    echo "🔧 Environment Variables for .env.local:"
    echo "========================================="
    echo "COGNITO_CLIENT_ID=$CLIENT_ID"
    echo "COGNITO_CLIENT_SECRET="
    echo "COGNITO_ISSUER=$ISSUER_URL"
    echo ""
    echo "📝 Next Steps:"
    echo "1. Copy the above environment variables"
    echo "2. Replace the placeholder values in frontend/.env.local"
    echo "3. Restart your frontend development server"
    echo "4. Test the authentication flow"
    echo ""
    
    # Ask if user wants to update .env.local automatically
    read -p "Would you like to automatically update frontend/.env.local with these values? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cd "$(dirname "$0")/.."
        if [ -f "frontend/.env.local" ]; then
            echo "📝 Updating frontend/.env.local..."
            
            # Create backup
            cp frontend/.env.local frontend/.env.local.backup
            
            # Update the file
            sed -i.tmp "s/COGNITO_CLIENT_ID=.*/COGNITO_CLIENT_ID=$CLIENT_ID/" frontend/.env.local
            sed -i.tmp "s|COGNITO_ISSUER=.*|COGNITO_ISSUER=$ISSUER_URL|" frontend/.env.local
            rm frontend/.env.local.tmp
            
            echo "✅ Updated frontend/.env.local"
            echo "📄 Backup saved as frontend/.env.local.backup"
        else
            echo "❌ frontend/.env.local not found"
        fi
    fi
    
else
    echo "❌ Unable to retrieve complete Cognito configuration"
    echo ""
    echo "🔧 Manual Steps:"
    echo "1. Check if the CloudFormation stack '$STACK_NAME' exists"
    echo "2. Verify the stack has Cognito resources deployed"
    echo "3. Check AWS permissions for CloudFormation and Cognito access"
    echo ""
    echo "🛠️ Alternative: Manual Configuration"
    echo "1. Go to AWS Console → Cognito → User Pools"
    echo "2. Find your user pool (should be named like '$STACK_NAME-users')"
    echo "3. Copy the User Pool ID from the overview"
    echo "4. Go to App integration → App clients"
    echo "5. Copy the Client ID"
    echo "6. Update frontend/.env.local with these values"
fi 