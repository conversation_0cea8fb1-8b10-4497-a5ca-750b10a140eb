"""
Lambda wrapper to fix Python import path issues.
This wrapper adds the current directory to Python path so 'src' imports work correctly.
"""

import sys
import os

# Add the current directory to Python path to make 'src' imports work
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Also add the parent directory in case we need it
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Now import the actual handler
try:
    from functions.api.funds import handler as funds_handler

    def handler(event, context):
        """Wrapper handler that fixes import paths"""
        return funds_handler(event, context)

except ImportError as e:
    print(f"Import error: {e}")
    print(f"Current directory: {current_dir}")
    print(f"Python path: {sys.path}")
    print(f"Directory contents: {os.listdir(current_dir)}")

    def handler(event, context):
        """Fallback handler for import errors"""
        return {"statusCode": 500, "body": f"Import error: {str(e)}"}
