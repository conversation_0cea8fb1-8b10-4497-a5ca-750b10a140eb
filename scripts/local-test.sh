#!/bin/bash

# FundFlow Local Testing Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PORT=3000
API_PORT=3001
LAMBDA_PORT=3002

# Help function
show_help() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  api           Start SAM local API Gateway"
    echo "  lambda        Start SAM local Lambda service"
    echo "  invoke        Invoke a specific Lambda function"
    echo "  build         Build the SAM application"
    echo "  validate      Validate the SAM template"
    echo ""
    echo "Options:"
    echo "  -p, --port           Port for the service [default: 3000 for API, 3002 for Lambda]"
    echo "  -f, --function       Function name to invoke (required for 'invoke' command)"
    echo "  -e, --event          Event file path for function invocation"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build                               Build the application"
    echo "  $0 api                                 Start local API Gateway on port 3001"
    echo "  $0 api -p 8080                         Start local API Gateway on port 8080"
    echo "  $0 lambda                              Start local Lambda service"
    echo "  $0 invoke -f GetFunds                  Invoke GetFunds function"
    echo "  $0 invoke -f GetFunds -e events/get-funds.json"
    echo ""
}

# Parse command line arguments
COMMAND=""
FUNCTION_NAME=""
EVENT_FILE=""

if [[ $# -eq 0 ]]; then
    show_help
    exit 1
fi

COMMAND="$1"
shift

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--port)
            if [[ "$COMMAND" == "api" ]]; then
                API_PORT="$2"
            elif [[ "$COMMAND" == "lambda" ]]; then
                LAMBDA_PORT="$2"
            fi
            shift 2
            ;;
        -f|--function)
            FUNCTION_NAME="$2"
            shift 2
            ;;
        -e|--event)
            EVENT_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            show_help
            exit 1
            ;;
    esac
done

echo -e "${BLUE}🧪 FundFlow Local Testing${NC}"
echo ""

# Check if SAM CLI is installed
if ! command -v sam &> /dev/null; then
    echo -e "${RED}Error: SAM CLI is not installed. Please install it first.${NC}"
    echo "Installation instructions: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}Error: Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Prerequisites check passed${NC}"

case $COMMAND in
    build)
        echo -e "${YELLOW}🔨 Building SAM application...${NC}"
        sam build
        echo -e "${GREEN}✓ Build complete${NC}"
        ;;
    
    validate)
        echo -e "${YELLOW}📋 Validating SAM template...${NC}"
        sam validate
        echo -e "${GREEN}✓ Template validation passed${NC}"
        ;;
    
    api)
        echo -e "${YELLOW}🚀 Starting local API Gateway on port $API_PORT...${NC}"
        echo -e "${BLUE}API will be available at: http://localhost:$API_PORT${NC}"
        echo -e "${BLUE}Press Ctrl+C to stop${NC}"
        echo ""
        sam local start-api --port "$API_PORT" --warm-containers EAGER
        ;;
    
    lambda)
        echo -e "${YELLOW}🚀 Starting local Lambda service on port $LAMBDA_PORT...${NC}"
        echo -e "${BLUE}Lambda endpoint will be available at: http://localhost:$LAMBDA_PORT${NC}"
        echo -e "${BLUE}Press Ctrl+C to stop${NC}"
        echo ""
        sam local start-lambda --port "$LAMBDA_PORT" --warm-containers EAGER
        ;;
    
    invoke)
        if [[ -z "$FUNCTION_NAME" ]]; then
            echo -e "${RED}Error: Function name is required for invoke command${NC}"
            echo "Use -f or --function to specify the function name"
            exit 1
        fi
        
        echo -e "${YELLOW}⚡ Invoking function: $FUNCTION_NAME${NC}"
        
        if [[ -n "$EVENT_FILE" ]]; then
            if [[ ! -f "$EVENT_FILE" ]]; then
                echo -e "${RED}Error: Event file not found: $EVENT_FILE${NC}"
                exit 1
            fi
            echo -e "${BLUE}Using event file: $EVENT_FILE${NC}"
            sam local invoke "$FUNCTION_NAME" --event "$EVENT_FILE"
        else
            echo -e "${BLUE}Using default empty event${NC}"
            sam local invoke "$FUNCTION_NAME" --event <(echo '{}')
        fi
        ;;
    
    *)
        echo -e "${RED}Error: Unknown command '$COMMAND'${NC}"
        show_help
        exit 1
        ;;
esac 