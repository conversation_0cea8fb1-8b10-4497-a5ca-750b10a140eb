import requests
import json
import hashlib
import os

# Configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")  # Replace with your actual API key
PDF_FILE_PATH = "/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"


def generate_file_id(file_path):
    """Generate a unique file ID based on file content"""
    with open(file_path, "rb") as f:
        file_hash = hashlib.md5(f.read()).hexdigest()
    return f"extracted-2025-{file_hash[:8]}"


def upload_file_to_openrouter(file_path, api_key):
    """Upload file to OpenRouter and get processing response"""
    url = "https://openrouter.ai/api/v1/files"

    headers = {
        "Authorization": f"Bearer {api_key}",
    }

    files = {"file": (file_path, open(file_path, "rb"), "application/pdf")}

    try:
        response = requests.post(url, headers=headers, files=files)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error uploading file: {e}")
        return None


def get_extraction_result(file_id, api_key):
    """Get the extraction result for a processed file"""
    url = f"https://openrouter.ai/api/v1/files/{file_id}/extractions"

    headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error getting extraction result: {e}")
        return None


def main():
    # Step 1: Upload the PDF file
    print("Uploading file to OpenRouter...")
    upload_response = upload_file_to_openrouter(PDF_FILE_PATH, OPENROUTER_API_KEY)

    if not upload_response:
        print("Failed to upload file")
        return

    file_id = upload_response.get("id", generate_file_id(PDF_FILE_PATH))
    print(f"File uploaded successfully. File ID: {file_id}")

    # Step 2: Get the extraction result
    print("Waiting for extraction to complete...")
    extraction_result = get_extraction_result(file_id, OPENROUTER_API_KEY)

    if not extraction_result:
        print("Failed to get extraction result")
        return

    # Step 3: Process the response
    print("Extraction completed successfully!")
    print("\nExtracted Fund Data:")
    print(json.dumps(extraction_result, indent=2))

    # You can save the result to a file
    with open("extracted_fund_data.json", "w") as f:
        json.dump(extraction_result, f, indent=2)
    print("\nResults saved to 'extracted_fund_data.json'")


if __name__ == "__main__":
    main()
