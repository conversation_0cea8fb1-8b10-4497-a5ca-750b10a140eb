"""
PDF Fund Information Extractor

This module provides functionality to extract fund information from PDF files
and convert it to the standardized Fund data model using AI models.
Supports two extraction modes:
1. Vision mode: Converts PDF to images and uses GPT-4o vision model
2. Direct mode: Sends entire PDF file directly to deepseek/deepseek-r1-0528:free model
"""

import os
import json
import logging
import base64
from typing import Optional, Dict, Any, Union, List
from pathlib import Path
from decimal import Decimal
from datetime import datetime
from pydantic import ValidationError

import requests


# Add the src directory to Python path to import our models
import sys

sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), "src"))

from shared.models.fund import (
    Fund,
    PerformanceMetrics,
    Holdings,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PDFFundExtractor:
    """
    Extracts fund information from PDF files using AI models.

    Supports two extraction modes:
    1. Vision mode: Converts PDF pages to images and uses GPT-4o vision model
    2. Direct mode: Sends entire PDF file directly to deepseek/deepseek-r1-0528:free model
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the PDF Fund Extractor.

        Args:
            api_key: OpenRouter API key. If not provided, will read from OPENROUTER_API_KEY env var.
        """
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        if not self.api_key:
            raise ValueError(
                "OpenRouter API key is required. Set OPENROUTER_API_KEY environment variable."
            )

        self.openrouter_base_url = "https://openrouter.ai/api/v1"
        # Choose a reliable model with good PDF support
        # self.text_model = "deepseek/deepseek-r1-0528:free"
        # self.text_model = "google/gemini-2.0-flash-exp"

        # Use a proven model for PDF processing
        # Gemini 1.5 Flash has excellent multimodal support and is well-tested for PDFs
        self.text_model = "google/gemini-2.5-flash"

        # Fallback models if the primary one fails
        self.fallback_models = [
            "google/gemini-flash-1.5",
        ]

        # Verify API key on initialization
        self.verify_api_key()

    def verify_api_key(self) -> bool:
        """
        Verify that the API key is valid by making a test request.

        Returns:
            True if API key is valid, raises exception otherwise
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        try:
            logger.info("Verifying OpenRouter API key...")
            response = requests.get(
                f"{self.openrouter_base_url}/models",
                headers=headers,
                timeout=30,
            )

            if response.status_code == 401:
                raise ValueError(
                    "Invalid OpenRouter API key. Please check your OPENROUTER_API_KEY environment variable."
                )
            elif response.status_code != 200:
                logger.warning(
                    f"API key verification returned status {response.status_code}, but proceeding..."
                )
            else:
                logger.info("API key verified successfully")

            return True

        except requests.RequestException as e:
            logger.warning(f"Could not verify API key (network issue): {e}")
            return True  # Assume it's valid and let the actual API call handle authentication

    def read_pdf_as_base64(self, pdf_path: Union[str, Path]) -> str:
        """
        Read PDF file and encode it as base64 for direct AI model processing.

        Args:
            pdf_path: Path to the PDF file

        Returns:
            Base64-encoded PDF content
        """
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")

        logger.info(f"Reading PDF file as base64: {pdf_path}")

        try:
            with open(pdf_path, "rb") as pdf_file:
                pdf_bytes = pdf_file.read()
                pdf_base64 = base64.b64encode(pdf_bytes).decode("utf-8")

            logger.info(f"Successfully encoded PDF ({len(pdf_bytes)} bytes) as base64")
            return pdf_base64

        except Exception as e:
            logger.error(f"Error reading PDF file: {e}")
            raise

    def create_extraction_prompt(self) -> str:
        """
        Create a prompt for the AI vision model to extract fund information from images.

        Returns:
            Formatted prompt for AI processing
        """
        return """You are a financial data extraction expert. Please analyze the fund document images and extract fund information in a structured JSON format.

Extract the following information from the document:

**Required Fields:**
- fund_id: Generate a unique identifier based on fund name/ticker
- name: Full fund name
- fund_type: One of [equity, bond, mixed, money_market, alternative, index, etf]

**Financial Details:**
- nav: Net Asset Value (as decimal number)
- currency: One of [USD, EUR, GBP, JPY, CAD, AUD, CHF]
- total_assets: Total assets under management (as decimal number)
- inception_date: Fund inception date (ISO format: YYYY-MM-DD)

**Management Information:**
- fund_manager: Fund manager name
- management_company: Management company name
- expense_ratio: Annual expense ratio as percentage (as decimal)
- minimum_investment: Minimum investment amount (as decimal)

**Classification:**
- isin: ISIN code if available
- cusip: CUSIP identifier if available
- bloomberg_ticker: Bloomberg ticker if available

**Metadata:**
- description: Brief fund description
- investment_objective: Investment objective/strategy
- benchmark: Benchmark index if mentioned
- risk_level: One of [very_low, low, moderate, high, very_high]

**Performance Metrics (if available):**
- ytd_return: Year-to-date return percentage
- one_month_return: 1-month return percentage
- three_month_return: 3-month return percentage
- six_month_return: 6-month return percentage
- one_year_return: 1-year return percentage
- three_year_return: 3-year return percentage
- five_year_return: 5-year return percentage
- volatility: Volatility percentage
- sharpe_ratio: Sharpe ratio
- max_drawdown: Maximum drawdown percentage (negative value)
- alpha: Alpha vs benchmark
- beta: Beta vs benchmark

**Holdings Information (if available):**
- top_holdings: List of top holdings with name and percentage
- sector_allocation: Sector allocation percentages
- geographic_allocation: Geographic allocation percentages

**Instructions:**
1. Carefully examine all pages of the fund document
2. Extract only information that is clearly visible in the images
3. Use null for any fields that are not available
4. Convert all percentage values to decimal format (e.g., 15.5% becomes 15.5)
5. Generate a reasonable fund_id based on the fund name (lowercase, hyphenated)
6. Infer fund_type from the description and holdings
7. Return ONLY valid JSON without any additional text or markdown formatting

**Response (JSON only):**"""

    def call_pdf_model_api(self, prompt: str, pdf_base64: str) -> Dict[str, Any]:
        """
        Call OpenRouter API with model to process the extraction prompt and PDF file directly.

        Args:
            prompt: The extraction prompt
            pdf_base64: Base64-encoded PDF content

        Returns:
            API response containing extracted fund data
        """
        # Try primary model first, then fallbacks
        models_to_try = [self.text_model] + self.fallback_models

        for model in models_to_try:
            try:
                logger.info(f"Trying model: {model}")
                return self._call_single_model(prompt, pdf_base64, model)
            except Exception as e:
                logger.warning(f"Model {model} failed: {e}")
                if model == models_to_try[-1]:  # Last model
                    raise e
                logger.info(f"Falling back to next model...")

        raise RuntimeError("All models failed")

    def _call_single_model(
        self, prompt: str, pdf_base64: str, model: str
    ) -> Dict[str, Any]:
        """
        Call OpenRouter API with a specific model to process the extraction prompt and PDF.

        Args:
            prompt: The extraction prompt
            pdf_base64: Base64-encoded PDF content
            model: Model to use

        Returns:
            API response containing extracted fund data
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        # Create content array with text prompt and PDF file
        # Use the correct OpenRouter PDF format
        data_url = f"data:application/pdf;base64,{pdf_base64}"

        content = [
            {"type": "text", "text": prompt},
            {
                "type": "file",
                "file": {"filename": "fund_document.pdf", "file_data": data_url},
            },
        ]

        # Add PDF processing plugins for better accuracy
        plugins = [
            {
                "id": "file-parser",
                "pdf": {
                    "engine": "pdf-text"  # Free option, fallback to mistral-ocr if needed
                },
            }
        ]

        payload = {
            "model": model,
            "messages": [{"role": "user", "content": content}],
            "plugins": plugins,
            "max_tokens": 4000,
            "temperature": 0.1,  # Low temperature for consistent extraction
        }

        logger.info(f"Calling OpenRouter API with model: {model}")
        logger.info(
            f"Processing PDF file directly ({len(pdf_base64)} base64 characters)"
        )

        response = requests.post(
            f"{self.openrouter_base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=180,
        )

        # Log response status and content for debugging
        logger.info(f"Response status code: {response.status_code}")
        if response.status_code != 200:
            logger.error(f"Response content: {response.text}")

        response.raise_for_status()

        api_response = response.json()
        response_content = api_response["choices"][0]["message"]["content"]

        # Clean the response content to extract JSON
        # Remove markdown code blocks if present
        if "```json" in response_content:
            # Extract JSON from markdown code block
            start = response_content.find("```json") + 7
            end = response_content.find("```", start)
            if end != -1:
                response_content = response_content[start:end].strip()
        elif "```" in response_content:
            # Handle generic code blocks
            start = response_content.find("```") + 3
            end = response_content.find("```", start)
            if end != -1:
                response_content = response_content[start:end].strip()

        # Parse the JSON response
        fund_data = json.loads(response_content)
        logger.info(
            f"Successfully extracted fund data from AI PDF response using model: {model}"
        )
        logger.info(f"Extracted data: {fund_data}")
        return fund_data

    def convert_to_fund_model(self, fund_data: Dict[str, Any]) -> Fund:
        """
        Convert extracted fund data to Fund model.

        Args:
            fund_data: Dictionary containing extracted fund data

        Returns:
            Fund model instance
        """
        try:
            # Handle decimal conversions - only convert non-None values
            for field in ["nav", "total_assets", "expense_ratio", "minimum_investment"]:
                if fund_data.get(field) is not None:
                    fund_data[field] = Decimal(str(fund_data[field]))

            # Handle performance metrics - check if they're nested or flat
            perf_fields = [
                "ytd_return",
                "one_month_return",
                "three_month_return",
                "six_month_return",
                "one_year_return",
                "three_year_return",
                "five_year_return",
                "volatility",
                "sharpe_ratio",
                "max_drawdown",
                "alpha",
                "beta",
            ]

            # Check if performance metrics are provided as flat fields
            perf_data = {}
            for field in perf_fields:
                if fund_data.get(field) is not None:
                    perf_data[field] = Decimal(str(fund_data.pop(field)))

            # Or if they're already nested
            if fund_data.get("performance_metrics"):
                nested_perf = fund_data["performance_metrics"]
                for field in perf_fields:
                    if nested_perf.get(field) is not None:
                        perf_data[field] = Decimal(str(nested_perf[field]))

            # Create PerformanceMetrics object if we have any performance data
            if perf_data:
                fund_data["performance_metrics"] = PerformanceMetrics(**perf_data)

            # Handle holdings - check if they're nested or flat
            holdings_fields = [
                "top_holdings",
                "sector_allocation",
                "geographic_allocation",
            ]
            holdings_data = {}

            # Check if holdings data is provided as flat fields
            for field in holdings_fields:
                if fund_data.get(field) is not None:
                    holdings_data[field] = fund_data.pop(field)

            # Or if they're already nested
            if fund_data.get("holdings"):
                nested_holdings = fund_data["holdings"]
                for field in holdings_fields:
                    if nested_holdings.get(field) is not None:
                        holdings_data[field] = nested_holdings[field]

            # Process holdings data if we have any
            if holdings_data:
                # Convert allocation lists to dictionaries with Decimal values
                for alloc_field in ["sector_allocation", "geographic_allocation"]:
                    if holdings_data.get(alloc_field):
                        alloc_data = holdings_data[alloc_field]
                        if isinstance(alloc_data, list):
                            # Convert list of {sector/region: name, percentage: value} to dict
                            holdings_data[alloc_field] = {
                                item.get(
                                    "sector", item.get("region", "Unknown")
                                ): Decimal(str(item["percentage"]))
                                for item in alloc_data
                                if item.get("percentage")
                                is not None  # Skip None percentages
                            }
                        elif isinstance(alloc_data, dict):
                            # Convert dict values to Decimal, skip None values
                            holdings_data[alloc_field] = {
                                k: Decimal(str(v))
                                for k, v in alloc_data.items()
                                if v is not None  # Skip None values
                            }

                # Only create Holdings object if we have meaningful data
                if any(holdings_data.values()):
                    fund_data["holdings"] = Holdings(**holdings_data)

            # Handle date conversion
            if fund_data.get("inception_date"):
                if isinstance(fund_data["inception_date"], str):
                    date_str = fund_data["inception_date"]
                    # Handle different date formats
                    if "T" in date_str:
                        # Full datetime string
                        fund_data["inception_date"] = datetime.fromisoformat(
                            date_str.replace("Z", "+00:00")
                        )
                    else:
                        # Simple date string (YYYY-MM-DD)
                        # Parse as date and convert to timezone-aware datetime
                        from datetime import timezone

                        parsed_date = datetime.fromisoformat(date_str)
                        fund_data["inception_date"] = parsed_date.replace(
                            tzinfo=timezone.utc
                        )

            # Create Fund instance
            fund = Fund(**fund_data)
            logger.info(f"Successfully created Fund model for: {fund.name}")
            return fund

        except ValidationError as e:
            logger.error(f"Validation error creating Fund model: {e}")
            logger.error(f"Fund data: {fund_data}")
            raise
        except Exception as e:
            logger.error(f"Error converting to Fund model: {e}")
            logger.error(f"Fund data: {fund_data}")
            raise

    def extract_fund_from_pdf_direct(
        self, pdf_path: Union[str, Path], output_path: Optional[Union[str, Path]] = None
    ) -> Fund:
        """
        Extract fund information from a PDF file by sending the entire PDF directly to AI model.
        This method sends the PDF file as binary data to the AI model without any preprocessing.

        Args:
            pdf_path: Path to the PDF file
            output_path: Optional path to save the extracted data as JSON

        Returns:
            Fund model instance with extracted data
        """
        logger.info(f"Starting direct PDF fund extraction from: {pdf_path}")

        # Read PDF as base64
        pdf_base64 = self.read_pdf_as_base64(pdf_path)

        # Create extraction prompt
        prompt = self.create_extraction_prompt()

        # Call AI API with PDF file directly
        fund_data = self.call_pdf_model_api(prompt, pdf_base64)

        # Convert to Fund model
        fund = self.convert_to_fund_model(fund_data)

        # Save to file if requested
        if output_path:
            output_path = Path(output_path)
            output_data = fund.model_dump(mode="json")

            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Saved extracted fund data to: {output_path}")

        logger.info("Direct PDF fund extraction completed successfully")
        return fund


def main():
    """
    Extract fund information from hardcoded PDF file.
    """
    # Hardcoded parameters
    pdf_path = "/Users/<USER>/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"
    pdf_path = "/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"
    output_path = "extracted_fund_data.json"
    #  use environment variable OPENROUTER_API_KEY
    api_key = os.getenv("OPENROUTER_API_KEY")

    verbose = True  # Enable verbose logging

    # Configure logging
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    print("🚀 Starting PDF Fund Extractor")
    print(f"📄 PDF file: {pdf_path}")
    print(f"💾 Output file: {output_path}")

    # Check if PDF file exists
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        print(f"❌ Error: PDF file not found at {pdf_path}")
        return 1

    # Check if API key is set
    if not api_key:
        logger.error("OPENROUTER_API_KEY environment variable not set")
        print("❌ Error: OPENROUTER_API_KEY environment variable not set")
        print("Please set your OpenRouter API key:")
        print("export OPENROUTER_API_KEY='your-api-key-here'")
        return 1

    try:
        # Initialize extractor (API key from environment variable)
        print("🔑 Initializing extractor with API key verification...")
        extractor = PDFFundExtractor(api_key=api_key)

        print(f"🤖 Using model: {extractor.text_model}")
        print("📋 Processing PDF file...")

        # Always use direct PDF mode (deepseek model)
        fund = extractor.extract_fund_from_pdf_direct(pdf_path, output_path)

        # Print summary
        print(f"\n✅ Successfully extracted fund information:")
        print(f"   Fund ID: {fund.fund_id}")
        print(f"   Name: {fund.name}")
        print(f"   Type: {fund.fund_type}")
        print(f"   Currency: {fund.currency}")
        if fund.total_assets:
            print(f"   Total Assets: {fund.total_assets:,.2f} {fund.currency}")
        if fund.nav:
            print(f"   NAV: {fund.nav}")

        print(f"   Saved to: {output_path}")

    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        print(f"❌ Configuration error: {e}")
        return 1
    except FileNotFoundError as e:
        logger.error(f"File error: {e}")
        print(f"❌ File error: {e}")
        return 1
    except Exception as e:
        logger.error(f"Extraction failed: {e}")
        print(f"❌ Extraction failed: {e}")
        return 1

    return 0


if __name__ == "__main__":
    main()
