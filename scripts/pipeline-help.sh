#!/bin/bash

# FundFlow Deployment Pipeline Quick Reference
# This script provides help and common commands for the deployment pipeline

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

show_header() {
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                 FundFlow Deployment Pipeline                 ║${NC}"
    echo -e "${BLUE}║                      Quick Reference                         ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

show_environments() {
    echo -e "${CYAN}📍 Available Environments:${NC}"
    echo -e "  ${GREEN}dev${NC}      - Development environment"
    echo -e "  ${YELLOW}staging${NC}  - Staging/integration environment"
    echo -e "  ${RED}prod${NC}     - Production environment"
    echo ""
}

show_common_commands() {
    echo -e "${CYAN}🔧 Common Commands:${NC}"
    echo ""
    
    echo -e "${YELLOW}Environment Setup:${NC}"
    echo "  ./scripts/check-environment.sh                 # Check setup status"
    echo "  ./scripts/setup-aws-profiles.sh                # Configure AWS profiles"
    echo ""
    
    echo -e "${YELLOW}Validation & Testing:${NC}"
    echo "  ./scripts/validate-deployment.sh -e dev        # Validate deployment readiness"
    echo "  ./scripts/local-test.sh build                  # Build application locally"
    echo "  ./scripts/local-test.sh validate               # Validate template"
    echo "  ./scripts/local-test.sh api                    # Start local API (requires Docker)"
    echo ""
    
    echo -e "${YELLOW}Deployment:${NC}"
    echo "  ./scripts/deploy.sh -e dev                     # Deploy to development"
    echo "  ./scripts/deploy.sh -e staging                 # Deploy to staging" 
    echo "  ./scripts/deploy.sh -e prod                    # Deploy to production"
    echo ""
    
    echo -e "${YELLOW}Deployment with Options:${NC}"
    echo "  ./scripts/deploy.sh -e prod -d                 # Dry run (preview changes)"
    echo "  ./scripts/deploy.sh -e prod -v                 # Validate only"
    echo "  ./scripts/deploy.sh -e prod -f                 # Use parameter file"
    echo "  ./scripts/deploy.sh -e prod -p fundflow-prod   # Use specific AWS profile"
    echo ""
    
    echo -e "${YELLOW}Monitoring & Verification:${NC}"
    echo "  aws cloudformation describe-stacks --stack-name fundflow-dev"
    echo "  aws cloudformation describe-stack-events --stack-name fundflow-dev"
    echo ""
}

show_git_workflow() {
    echo -e "${CYAN}🌳 Git Workflow (Automated CI/CD):${NC}"
    echo ""
    
    echo -e "${YELLOW}Feature Development:${NC}"
    echo "  git checkout -b feature/my-feature"
    echo "  # Make changes..."
    echo "  git commit -m 'feat: add new feature'"
    echo "  git push origin feature/my-feature"
    echo "  # Create PR → automatically deploys to dev"
    echo ""
    
    echo -e "${YELLOW}Staging Deployment:${NC}"
    echo "  git checkout develop"
    echo "  git merge feature/my-feature"
    echo "  git push origin develop  # → Auto-deploys to staging"
    echo ""
    
    echo -e "${YELLOW}Production Deployment:${NC}"
    echo "  git checkout main"
    echo "  git merge develop"
    echo "  git push origin main     # → Auto-deploys to production (with approval)"
    echo ""
}

show_troubleshooting() {
    echo -e "${CYAN}🚨 Common Issues & Solutions:${NC}"
    echo ""
    
    echo -e "${RED}Docker not available:${NC}"
    echo "  # Install Docker Desktop"
    echo "  brew install --cask docker"
    echo "  open /Applications/Docker.app"
    echo ""
    
    echo -e "${RED}AWS credentials not configured:${NC}"
    echo "  aws configure --profile fundflow-dev"
    echo "  # Or run: ./scripts/setup-aws-profiles.sh"
    echo ""
    
    echo -e "${RED}Template validation fails:${NC}"
    echo "  sam validate --region us-east-1"
    echo "  # Check YAML syntax and resource references"
    echo ""
    
    echo -e "${RED}Stack in failed state:${NC}"
    echo "  aws cloudformation delete-stack --stack-name fundflow-dev"
    echo "  # Then redeploy"
    echo ""
}

show_github_actions() {
    echo -e "${CYAN}🤖 GitHub Actions CI/CD:${NC}"
    echo ""
    
    echo -e "${YELLOW}Manual Trigger:${NC}"
    echo "  1. Go to GitHub Actions → 'Deploy FundFlow Infrastructure'"
    echo "  2. Click 'Run workflow'"
    echo "  3. Select environment and options"
    echo "  4. Click 'Run workflow'"
    echo ""
    
    echo -e "${YELLOW}Required Secrets:${NC}"
    echo "  AWS_ACCESS_KEY_ID_DEV"
    echo "  AWS_SECRET_ACCESS_KEY_DEV"
    echo "  AWS_ACCESS_KEY_ID_STAGING"
    echo "  AWS_SECRET_ACCESS_KEY_STAGING"
    echo "  AWS_ACCESS_KEY_ID_PROD"
    echo "  AWS_SECRET_ACCESS_KEY_PROD"
    echo ""
    
    echo -e "${YELLOW}Environment Protection:${NC}"
    echo "  development: No restrictions"
    echo "  staging: Restricted to develop/release branches"
    echo "  production: Requires reviewer approval"
    echo ""
}

show_quick_start() {
    echo -e "${CYAN}🚀 Quick Start Guide:${NC}"
    echo ""
    
    echo -e "${YELLOW}1. Initial Setup:${NC}"
    echo "  ./scripts/check-environment.sh"
    echo "  ./scripts/setup-aws-profiles.sh  # If needed"
    echo ""
    
    echo -e "${YELLOW}2. Validate Everything:${NC}"
    echo "  ./scripts/validate-deployment.sh -e dev"
    echo ""
    
    echo -e "${YELLOW}3. Deploy to Dev:${NC}"
    echo "  ./scripts/deploy.sh -e dev"
    echo ""
    
    echo -e "${YELLOW}4. Test Deployment:${NC}"
    echo "  # Check stack outputs"
    echo "  aws cloudformation describe-stacks --stack-name fundflow-dev --query 'Stacks[0].Outputs'"
    echo ""
    
    echo -e "${YELLOW}5. Promote to Staging:${NC}"
    echo "  git checkout develop"
    echo "  git push origin develop  # Triggers auto-deployment"
    echo ""
}

show_help() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  overview      Show deployment overview"
    echo "  commands      Show common commands"
    echo "  git           Show Git workflow"
    echo "  github        Show GitHub Actions info"
    echo "  troubleshoot  Show troubleshooting guide"
    echo "  quickstart    Show quick start guide"
    echo "  all           Show all information"
    echo ""
    echo "Examples:"
    echo "  $0 commands      # Show common deployment commands"
    echo "  $0 git           # Show Git workflow"
    echo "  $0 troubleshoot  # Show troubleshooting guide"
    echo ""
}

case ${1:-all} in
    overview|env|environments)
        show_header
        show_environments
        ;;
    commands|cmd)
        show_header
        show_common_commands
        ;;
    git|workflow)
        show_header
        show_git_workflow
        ;;
    github|actions|ci)
        show_header
        show_github_actions
        ;;
    troubleshoot|trouble|issues)
        show_header
        show_troubleshooting
        ;;
    quickstart|quick|start)
        show_header
        show_quick_start
        ;;
    all)
        show_header
        show_environments
        show_quick_start
        show_common_commands
        show_git_workflow
        show_github_actions
        show_troubleshooting
        ;;
    help|-h|--help)
        show_help
        ;;
    *)
        echo -e "${RED}Unknown command: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac 