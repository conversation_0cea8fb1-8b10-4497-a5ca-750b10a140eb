#!/usr/bin/env python3
"""
Populate comprehensive sample data for FundFlow application.
This includes portfolios, market data, historical data, holdings, and asset breakdowns.
"""

import os
import sys
import json
import boto3
import random
from datetime import datetime, timedelta, timezone
from decimal import Decimal
import uuid
from typing import List, Dict, Any

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from shared.models.fund import Fund, Holdings, FundDynamoDBItem
from shared.models.portfolio import (
    Portfolio, PortfolioHolding, PortfolioTransaction, PortfolioPerformance,
    PortfolioType, TransactionType, PortfolioStatus, PortfolioDynamoDBItem
)
from shared.models.market_data import (
    PriceData, MarketDataPoint, MarketDataSource, DataQuality,
    ValuationMetrics, TechnicalIndicators, RiskAnalytics, BenchmarkData
)

# Initialize DynamoDB
dynamodb = boto3.resource('dynamodb', region_name='ap-northeast-1')

# Table names
FUNDS_TABLE = 'fundflow-dev-funds'
PORTFOLIOS_TABLE = 'fundflow-dev-portfolios'
MARKET_DATA_TABLE = 'fundflow-dev-market-data'
HISTORICAL_DATA_TABLE = 'fundflow-dev-historical-data'

# Sample user IDs
SAMPLE_USER_IDS = [
    'user-001',
    'user-002',
    'user-003'
]

# Benchmark indices
BENCHMARKS = [
    {'id': 'NIFTY50', 'name': 'NIFTY 50', 'symbol': 'NIFTY'},
    {'id': 'SENSEX', 'name': 'BSE SENSEX', 'symbol': 'SENSEX'},
    {'id': 'NIFTYMID', 'name': 'NIFTY Midcap 150', 'symbol': 'NIFTYMID150'},
    {'id': 'NIFTYSMALL', 'name': 'NIFTY Smallcap 250', 'symbol': 'NIFTYSMALL250'}
]


def get_existing_funds() -> List[Dict[str, Any]]:
    """Get all existing funds from DynamoDB."""
    table = dynamodb.Table(FUNDS_TABLE)
    funds = []
    
    scan_kwargs = {}
    done = False
    
    while not done:
        try:
            response = table.scan(**scan_kwargs)
            funds.extend(response.get('Items', []))
            
            # Check if there are more items to scan
            if 'LastEvaluatedKey' in response:
                scan_kwargs['ExclusiveStartKey'] = response['LastEvaluatedKey']
            else:
                done = True
        except Exception as e:
            print(f"Error scanning funds table: {e}")
            done = True
    
    return funds


def generate_market_data_point(base_value: Decimal, currency: str = 'USD') -> MarketDataPoint:
    """Generate a market data point with some randomness."""
    # Add some random variation (-2% to +2%)
    variation = Decimal(str(random.uniform(0.98, 1.02)))
    value = base_value * variation
    
    return MarketDataPoint(
        timestamp=datetime.now(timezone.utc),
        value=value,
        source=random.choice(list(MarketDataSource)),
        quality=random.choice([DataQuality.EXCELLENT, DataQuality.GOOD]),
        currency=currency
    )


def generate_price_data(fund_id: str, nav: Decimal) -> Dict[str, Any]:
    """Generate comprehensive price data for a fund."""
    now = datetime.now(timezone.utc)
    
    # Generate market price close to NAV
    market_price = nav * Decimal(str(random.uniform(0.98, 1.02)))
    
    # Generate bid/ask spread
    spread = Decimal(str(random.uniform(0.001, 0.005)))  # 0.1% to 0.5% spread
    bid = market_price * (Decimal('1') - spread/2)
    ask = market_price * (Decimal('1') + spread/2)
    
    # Generate OHLC data
    daily_range = Decimal(str(random.uniform(0.01, 0.03)))  # 1% to 3% daily range
    high = market_price * (Decimal('1') + daily_range/2)
    low = market_price * (Decimal('1') - daily_range/2)
    open_price = market_price * Decimal(str(random.uniform(0.99, 1.01)))
    
    # Generate volume
    volume = random.randint(100000, 10000000)
    avg_volume_30d = int(volume * random.uniform(0.8, 1.2))
    
    # Calculate price changes
    price_change_1d = market_price * Decimal(str(random.uniform(-0.03, 0.03)))
    price_change_1d_pct = (price_change_1d / market_price) * 100
    price_change_ytd = market_price * Decimal(str(random.uniform(-0.2, 0.3)))
    price_change_ytd_pct = (price_change_ytd / market_price) * 100
    
    price_data = PriceData(
        fund_id=fund_id,
        as_of=now,
        nav=generate_market_data_point(nav),
        market_price=generate_market_data_point(market_price),
        bid=generate_market_data_point(bid),
        ask=generate_market_data_point(ask),
        open_price=generate_market_data_point(open_price),
        high_price=generate_market_data_point(high),
        low_price=generate_market_data_point(low),
        close_price=generate_market_data_point(market_price),
        volume=volume,
        avg_volume_30d=avg_volume_30d,
        bid_ask_spread=ask - bid,
        bid_ask_spread_pct=(ask - bid) / market_price * 100,
        market_cap=nav * Decimal(str(random.randint(1000000, 100000000))),
        shares_outstanding=random.randint(1000000, 100000000),
        price_change_1d=price_change_1d,
        price_change_1d_pct=price_change_1d_pct,
        price_change_ytd=price_change_ytd,
        price_change_ytd_pct=price_change_ytd_pct
    )
    
    return price_data.model_dump()


def generate_valuation_metrics(fund_id: str) -> Dict[str, Any]:
    """Generate valuation metrics for a fund."""
    valuation = ValuationMetrics(
        fund_id=fund_id,
        as_of=datetime.now(timezone.utc).date(),
        price_to_book=Decimal(str(random.uniform(0.5, 5.0))),
        price_to_earnings=Decimal(str(random.uniform(10, 40))),
        price_to_sales=Decimal(str(random.uniform(0.5, 10))),
        price_to_cash_flow=Decimal(str(random.uniform(5, 25))),
        enterprise_value=Decimal(str(random.uniform(1000000, 100000000000))),
        ev_to_revenue=Decimal(str(random.uniform(1, 20))),
        ev_to_ebitda=Decimal(str(random.uniform(5, 30))),
        return_on_equity=Decimal(str(random.uniform(-10, 30))),
        return_on_assets=Decimal(str(random.uniform(-5, 20))),
        return_on_invested_capital=Decimal(str(random.uniform(-5, 25))),
        debt_to_equity=Decimal(str(random.uniform(0, 2))),
        current_ratio=Decimal(str(random.uniform(0.5, 3))),
        quick_ratio=Decimal(str(random.uniform(0.3, 2.5))),
        book_value_per_share=Decimal(str(random.uniform(10, 1000))),
        tangible_book_value=Decimal(str(random.uniform(10, 900))),
        dividend_yield=Decimal(str(random.uniform(0, 5))),
        dividend_payout_ratio=Decimal(str(random.uniform(0, 80)))
    )
    
    return valuation.model_dump()


def generate_technical_indicators(fund_id: str, current_price: Decimal) -> Dict[str, Any]:
    """Generate technical indicators for a fund."""
    # Generate moving averages
    sma_200 = current_price * Decimal(str(random.uniform(0.9, 1.1)))
    sma_50 = current_price * Decimal(str(random.uniform(0.95, 1.05)))
    sma_20 = current_price * Decimal(str(random.uniform(0.98, 1.02)))
    
    indicators = TechnicalIndicators(
        fund_id=fund_id,
        as_of=datetime.now(timezone.utc),
        sma_20=sma_20,
        sma_50=sma_50,
        sma_200=sma_200,
        ema_12=current_price * Decimal(str(random.uniform(0.98, 1.02))),
        ema_26=current_price * Decimal(str(random.uniform(0.97, 1.03))),
        rsi_14=Decimal(str(random.uniform(20, 80))),
        macd_line=Decimal(str(random.uniform(-2, 2))),
        macd_signal=Decimal(str(random.uniform(-2, 2))),
        macd_histogram=Decimal(str(random.uniform(-1, 1))),
        bollinger_upper=current_price * Decimal('1.02'),
        bollinger_middle=current_price,
        bollinger_lower=current_price * Decimal('0.98'),
        atr_14=current_price * Decimal(str(random.uniform(0.01, 0.03))),
        vwap=current_price * Decimal(str(random.uniform(0.99, 1.01))),
        volume_sma_20=random.randint(100000, 5000000),
        support_level=current_price * Decimal('0.95'),
        resistance_level=current_price * Decimal('1.05')
    )
    
    return indicators.model_dump()


def generate_risk_analytics(fund_id: str) -> Dict[str, Any]:
    """Generate risk analytics for a fund."""
    analytics = RiskAnalytics(
        fund_id=fund_id,
        as_of=datetime.now(timezone.utc).date(),
        var_1d_95=Decimal(str(random.uniform(-5, -1))),
        var_1d_99=Decimal(str(random.uniform(-7, -2))),
        var_10d_95=Decimal(str(random.uniform(-15, -5))),
        var_10d_99=Decimal(str(random.uniform(-20, -7))),
        cvar_1d_95=Decimal(str(random.uniform(-7, -2))),
        cvar_1d_99=Decimal(str(random.uniform(-10, -3))),
        sharpe_ratio=Decimal(str(random.uniform(-1, 3))),
        sortino_ratio=Decimal(str(random.uniform(-1, 3.5))),
        calmar_ratio=Decimal(str(random.uniform(-0.5, 2))),
        information_ratio=Decimal(str(random.uniform(-1, 2))),
        treynor_ratio=Decimal(str(random.uniform(-5, 15))),
        max_drawdown=Decimal(str(random.uniform(-30, -5))),
        max_drawdown_duration=random.randint(30, 365),
        current_drawdown=Decimal(str(random.uniform(-15, 0))),
        volatility_1m=Decimal(str(random.uniform(5, 30))),
        volatility_3m=Decimal(str(random.uniform(8, 35))),
        volatility_1y=Decimal(str(random.uniform(10, 40))),
        downside_deviation=Decimal(str(random.uniform(5, 25))),
        beta_vs_benchmark=Decimal(str(random.uniform(0.5, 1.5))),
        correlation_vs_benchmark=Decimal(str(random.uniform(0.3, 0.95))),
        tracking_error=Decimal(str(random.uniform(2, 15)))
    )
    
    return analytics.model_dump()


def generate_benchmark_data(benchmark_info: Dict[str, str]) -> Dict[str, Any]:
    """Generate benchmark data."""
    benchmark = BenchmarkData(
        benchmark_id=benchmark_info['id'],
        name=benchmark_info['name'],
        symbol=benchmark_info['symbol'],
        as_of=datetime.now(timezone.utc),
        current_value=Decimal(str(random.uniform(10000, 20000))),
        return_1d=Decimal(str(random.uniform(-3, 3))),
        return_1w=Decimal(str(random.uniform(-5, 5))),
        return_1m=Decimal(str(random.uniform(-10, 10))),
        return_3m=Decimal(str(random.uniform(-15, 20))),
        return_6m=Decimal(str(random.uniform(-20, 30))),
        return_1y=Decimal(str(random.uniform(-25, 40))),
        return_3y=Decimal(str(random.uniform(-30, 60))),
        return_5y=Decimal(str(random.uniform(-20, 80))),
        volatility=Decimal(str(random.uniform(10, 30))),
        max_drawdown=Decimal(str(random.uniform(-40, -10)))
    )
    
    return benchmark.model_dump()


def generate_enhanced_holdings(fund: Dict[str, Any]) -> Dict[str, Any]:
    """Generate enhanced holdings data for a fund."""
    # Top holdings
    top_holdings = []
    holdings_names = [
        'Reliance Industries', 'TCS', 'HDFC Bank', 'Infosys', 'ICICI Bank',
        'HUL', 'ITC', 'Kotak Bank', 'L&T', 'Axis Bank', 'SBI', 'Bajaj Finance',
        'Maruti Suzuki', 'Asian Paints', 'Nestle India', 'Wipro', 'HCL Tech',
        'UltraTech Cement', 'Sun Pharma', 'Titan Company'
    ]
    
    num_holdings = random.randint(10, 20)
    selected_holdings = random.sample(holdings_names, min(num_holdings, len(holdings_names)))
    
    total_percentage = Decimal('0')
    for i, holding_name in enumerate(selected_holdings):
        percentage = Decimal(str(random.uniform(1, 15)))
        total_percentage += percentage
        
        holding = {
            'name': holding_name,
            'symbol': holding_name.replace(' ', '').upper()[:10],
            'percentage': str(percentage),
            'shares': random.randint(10000, 1000000),
            'market_value': str(Decimal(str(random.uniform(1000000, ********)))),
            'sector': random.choice(['Technology', 'Finance', 'Healthcare', 'Consumer', 'Industrial'])
        }
        top_holdings.append(holding)
    
    # Normalize percentages to sum to ~95% (leaving 5% for cash/others)
    normalization_factor = Decimal('95') / total_percentage
    for holding in top_holdings:
        holding['percentage'] = str(Decimal(holding['percentage']) * normalization_factor)
    
    # Sector allocation
    sectors = ['Technology', 'Finance', 'Healthcare', 'Consumer', 'Industrial', 'Energy', 'Materials', 'Utilities']
    sector_allocation = {}
    remaining = Decimal('100')
    
    for i, sector in enumerate(sectors[:-1]):
        allocation = Decimal(str(random.uniform(5, 30)))
        sector_allocation[sector] = min(allocation, remaining)
        remaining -= sector_allocation[sector]
    
    sector_allocation[sectors[-1]] = remaining
    
    # Geographic allocation
    geographic_allocation = {
        'India': Decimal(str(random.uniform(60, 85))),
        'US': Decimal(str(random.uniform(5, 20))),
        'Europe': Decimal(str(random.uniform(0, 10))),
        'Asia Pacific': Decimal(str(random.uniform(0, 10))),
        'Others': Decimal(str(random.uniform(0, 5)))
    }
    
    # Normalize to 100%
    total_geo = sum(geographic_allocation.values())
    geographic_allocation = {k: (v / total_geo) * 100 for k, v in geographic_allocation.items()}
    
    # Asset allocation
    asset_allocation = {
        'Equity': Decimal(str(random.uniform(70, 95))),
        'Debt': Decimal(str(random.uniform(0, 20))),
        'Cash': Decimal(str(random.uniform(2, 10))),
        'Others': Decimal(str(random.uniform(0, 5)))
    }
    
    # Normalize to 100%
    total_asset = sum(asset_allocation.values())
    asset_allocation = {k: (v / total_asset) * 100 for k, v in asset_allocation.items()}
    
    # Market cap allocation
    market_cap_allocation = {
        'Large Cap': Decimal(str(random.uniform(40, 70))),
        'Mid Cap': Decimal(str(random.uniform(20, 40))),
        'Small Cap': Decimal(str(random.uniform(5, 20)))
    }
    
    # Normalize to 100%
    total_cap = sum(market_cap_allocation.values())
    market_cap_allocation = {k: (v / total_cap) * 100 for k, v in market_cap_allocation.items()}
    
    # Currency allocation
    currency_allocation = {
        'INR': Decimal(str(random.uniform(70, 90))),
        'USD': Decimal(str(random.uniform(5, 20))),
        'EUR': Decimal(str(random.uniform(0, 5))),
        'Others': Decimal(str(random.uniform(0, 5)))
    }
    
    # Normalize to 100%
    total_currency = sum(currency_allocation.values())
    currency_allocation = {k: (v / total_currency) * 100 for k, v in currency_allocation.items()}
    
    holdings = Holdings(
        top_holdings=top_holdings,
        sector_allocation=sector_allocation,
        geographic_allocation=geographic_allocation,
        asset_allocation=asset_allocation,
        market_cap_allocation=market_cap_allocation,
        currency_allocation=currency_allocation,
        total_holdings_count=len(top_holdings),
        holdings_concentration=sum(Decimal(h['percentage']) for h in top_holdings[:10]),
        last_updated=datetime.now(timezone.utc)
    )
    
    return holdings.model_dump()


def generate_historical_performance_data(fund_id: str, inception_date: datetime) -> List[Dict[str, Any]]:
    """Generate historical performance data for a fund."""
    historical_data = []
    
    # Generate monthly data points from inception to now
    current_date = inception_date
    now = datetime.now(timezone.utc)
    base_nav = Decimal('100')  # Starting NAV
    
    while current_date < now:
        # Random monthly return between -5% and +8%
        monthly_return = Decimal(str(random.uniform(-0.05, 0.08)))
        base_nav = base_nav * (Decimal('1') + monthly_return)
        
        data_point = {
            'fund_id': fund_id,
            'date': current_date.isoformat(),
            'nav': str(base_nav),
            'return_1m': str(monthly_return * 100),
            'return_ytd': str(Decimal(str(random.uniform(-10, 30)))),
            'return_1y': str(Decimal(str(random.uniform(-15, 40)))),
            'return_3y': str(Decimal(str(random.uniform(-20, 60)))),
            'return_5y': str(Decimal(str(random.uniform(-25, 80)))),
            'volatility': str(Decimal(str(random.uniform(10, 35)))),
            'sharpe_ratio': str(Decimal(str(random.uniform(-0.5, 2.5)))),
            'data_source': random.choice(['fund_company', 'bloomberg', 'reuters']),
            'data_quality': random.choice(['excellent', 'good', 'fair'])
        }
        
        historical_data.append(data_point)
        
        # Move to next month
        if current_date.month == 12:
            current_date = current_date.replace(year=current_date.year + 1, month=1)
        else:
            # Handle month transition properly (e.g., Jan 31 -> Feb 28/29)
            next_month = current_date.month + 1
            try:
                current_date = current_date.replace(month=next_month)
            except ValueError:
                # Day doesn't exist in next month (e.g., Jan 31 -> Feb 31)
                # Move to last day of next month
                if next_month == 2:
                    # February - check for leap year
                    if current_date.year % 4 == 0 and (current_date.year % 100 != 0 or current_date.year % 400 == 0):
                        current_date = current_date.replace(month=2, day=29)
                    else:
                        current_date = current_date.replace(month=2, day=28)
                elif next_month in [4, 6, 9, 11]:
                    # April, June, September, November have 30 days
                    current_date = current_date.replace(month=next_month, day=30)
                else:
                    # Other months have 31 days
                    current_date = current_date.replace(month=next_month, day=31)
    
    return historical_data


def create_portfolio_for_user(user_id: str, funds: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Create a sample portfolio for a user."""
    portfolio_id = f"portfolio-{uuid.uuid4().hex[:8]}"
    portfolio_name = f"Portfolio {random.choice(['Growth', 'Balanced', 'Conservative', 'Aggressive', 'Income'])} {random.randint(1, 100)}"
    
    # Select random funds for the portfolio
    num_holdings = random.randint(3, 8)
    selected_funds = random.sample(funds, min(num_holdings, len(funds)))
    
    # Create holdings
    holdings = []
    total_value = Decimal('0')
    total_cost_basis = Decimal('0')
    
    for fund in selected_funds:
        shares = Decimal(str(random.uniform(100, 10000)))
        # Handle NAV conversion safely
        nav_value = fund.get('nav', 100)
        if isinstance(nav_value, str) and nav_value.strip() == '':
            nav_decimal = Decimal('100')
        else:
            try:
                nav_decimal = Decimal(str(nav_value))
            except:
                nav_decimal = Decimal('100')
        
        average_cost = nav_decimal * Decimal(str(random.uniform(0.8, 1.1)))
        current_price = nav_decimal
        
        market_value = shares * current_price
        cost_basis = shares * average_cost
        unrealized_gain_loss = market_value - cost_basis
        unrealized_gain_loss_pct = (unrealized_gain_loss / cost_basis) * 100 if cost_basis > 0 else Decimal('0')
        
        total_value += market_value
        total_cost_basis += cost_basis
        
        holding = PortfolioHolding(
            fund_id=fund['fund_id'],
            fund_name=fund['name'],
            fund_symbol=fund.get('bloomberg_ticker', fund['fund_id'][:8].upper()),
            shares=shares,
            average_cost=average_cost,
            current_price=current_price,
            market_value=market_value,
            cost_basis=cost_basis,
            unrealized_gain_loss=unrealized_gain_loss,
            unrealized_gain_loss_pct=unrealized_gain_loss_pct,
            weight=Decimal('0'),  # Will calculate after all holdings
            first_purchase_date=datetime.now(timezone.utc) - timedelta(days=random.randint(30, 365))
        )
        
        holdings.append(holding)
    
    # Calculate weights
    for holding in holdings:
        holding.weight = (holding.market_value / total_value) * 100 if total_value > 0 else Decimal('0')
    
    # Add cash balance
    cash_balance = Decimal(str(random.uniform(10000, 100000)))
    total_value += cash_balance
    
    # Generate sample transactions
    transactions = []
    for i in range(min(10, len(holdings) * 2)):
        fund = random.choice(selected_funds)
        transaction_type = random.choice([TransactionType.BUY, TransactionType.SELL, TransactionType.DIVIDEND])
        
        if transaction_type in [TransactionType.BUY, TransactionType.SELL]:
            shares = Decimal(str(random.uniform(10, 1000)))
            # Handle NAV conversion safely
            nav_value = fund.get('nav', 100)
            if isinstance(nav_value, str) and nav_value.strip() == '':
                nav_decimal = Decimal('100')
            else:
                try:
                    nav_decimal = Decimal(str(nav_value))
                except:
                    nav_decimal = Decimal('100')
            price = nav_decimal * Decimal(str(random.uniform(0.95, 1.05)))
            amount = shares * price
            fees = amount * Decimal('0.001')  # 0.1% fee
        else:
            shares = None
            price = None
            amount = Decimal(str(random.uniform(100, 5000)))
            fees = Decimal('0')
        
        transaction = PortfolioTransaction(
            transaction_id=f"txn-{uuid.uuid4().hex[:8]}",
            fund_id=fund['fund_id'],
            fund_name=fund['name'],
            fund_symbol=fund.get('bloomberg_ticker', fund['fund_id'][:8].upper()),
            transaction_type=transaction_type,
            transaction_date=datetime.now(timezone.utc) - timedelta(days=random.randint(1, 180)),
            shares=shares,
            price=price,
            amount=amount,
            fees=fees,
            net_amount=amount - fees if transaction_type == TransactionType.SELL else amount + fees
        )
        
        transactions.append(transaction)
    
    # Sort transactions by date (most recent first)
    transactions.sort(key=lambda x: x.transaction_date, reverse=True)
    
    # Generate performance metrics
    performance = PortfolioPerformance(
        total_return=total_value - total_cost_basis,
        total_return_pct=((total_value - total_cost_basis) / total_cost_basis * 100) if total_cost_basis > 0 else Decimal('0'),
        one_day_return=Decimal(str(random.uniform(-3, 3))),
        one_week_return=Decimal(str(random.uniform(-5, 5))),
        one_month_return=Decimal(str(random.uniform(-10, 10))),
        three_month_return=Decimal(str(random.uniform(-15, 20))),
        six_month_return=Decimal(str(random.uniform(-20, 30))),
        one_year_return=Decimal(str(random.uniform(-25, 40))),
        three_year_return=Decimal(str(random.uniform(-30, 60))),
        five_year_return=Decimal(str(random.uniform(-20, 80))),
        inception_return=Decimal(str(random.uniform(-30, 100))),
        volatility=Decimal(str(random.uniform(10, 30))),
        sharpe_ratio=Decimal(str(random.uniform(-0.5, 2.5))),
        max_drawdown=Decimal(str(random.uniform(-30, -5))),
        benchmark_return=Decimal(str(random.uniform(-20, 35))),
        alpha=Decimal(str(random.uniform(-5, 10))),
        beta=Decimal(str(random.uniform(0.5, 1.5)))
    )
    
    # Calculate gain/loss
    total_gain_loss = total_value - total_cost_basis
    total_gain_loss_pct = ((total_gain_loss / total_cost_basis) * 100) if total_cost_basis > 0 else Decimal('0')
    
    # Create portfolio with explicit values to avoid recursion
    portfolio = Portfolio(
        portfolio_id=portfolio_id,
        name=portfolio_name,
        description=f"A {portfolio_name.split()[1].lower()} portfolio focused on long-term wealth creation",
        portfolio_type=random.choice(list(PortfolioType)),
        status=PortfolioStatus.ACTIVE,
        user_id=user_id,
        base_currency='USD',
        inception_date=datetime.now(timezone.utc) - timedelta(days=random.randint(180, 1095)),
        total_value=total_value,
        total_cost_basis=total_cost_basis,
        cash_balance=cash_balance,
        total_gain_loss=total_gain_loss,
        total_gain_loss_pct=total_gain_loss_pct,
        holdings=holdings,
        recent_transactions=transactions[:10],  # Keep only 10 most recent
        performance=performance,
        risk_level=random.choice(['low', 'moderate', 'high']),
        benchmark=random.choice(['NIFTY50', 'SENSEX', 'NIFTYMID', 'NIFTYSMALL']),
        tags=['diversified', 'long-term', 'growth'],
        custom_fields={'rebalance_frequency': 'quarterly', 'target_allocation': 'aggressive'}
    )
    
    return PortfolioDynamoDBItem.to_dynamodb_item(portfolio)


def update_fund_with_comprehensive_data(fund: Dict[str, Any]) -> Dict[str, Any]:
    """Update fund with comprehensive market data and holdings."""
    fund_id = fund['fund_id']
    nav_value = fund.get('nav', 100)
    # Handle case where nav might already be a string or Decimal
    if isinstance(nav_value, str) and nav_value.strip() == '':
        nav = Decimal('100')
    else:
        try:
            nav = Decimal(str(nav_value))
        except:
            nav = Decimal('100')
    
    # Generate all market data components
    price_data = generate_price_data(fund_id, nav)
    valuation_metrics = generate_valuation_metrics(fund_id)
    technical_indicators = generate_technical_indicators(fund_id, nav)
    risk_analytics = generate_risk_analytics(fund_id)
    
    # Generate benchmark data
    benchmark_data = [generate_benchmark_data(b) for b in random.sample(BENCHMARKS, 2)]
    
    # Generate enhanced holdings
    holdings = generate_enhanced_holdings(fund)
    
    # Update fund with new data
    fund['holdings'] = holdings
    fund['market_data'] = {
        'fund_id': fund_id,
        'last_updated': datetime.now(timezone.utc).isoformat(),
        'price_data': price_data,
        'valuation_metrics': valuation_metrics,
        'technical_indicators': technical_indicators,
        'risk_analytics': risk_analytics,
        'primary_benchmark': benchmark_data[0] if benchmark_data else None,
        'secondary_benchmarks': benchmark_data[1:] if len(benchmark_data) > 1 else []
    }
    
    # Also store individual data components for separate queries
    fund['current_price_data'] = price_data
    fund['valuation_metrics'] = valuation_metrics
    fund['technical_indicators'] = technical_indicators
    fund['risk_analytics'] = risk_analytics
    fund['benchmark_data'] = benchmark_data
    
    return fund


def populate_data():
    """Main function to populate all data."""
    print("Starting comprehensive data population...")
    
    # Get existing funds
    print("\nFetching existing funds...")
    funds = get_existing_funds()
    print(f"Found {len(funds)} existing funds")
    
    if not funds:
        print("No funds found in database. Please run populate_dynamodb.py first.")
        return
    
    # Update funds with comprehensive data
    print("\nUpdating funds with comprehensive market data and holdings...")
    funds_table = dynamodb.Table(FUNDS_TABLE)
    
    for i, fund in enumerate(funds):
        print(f"\nProcessing fund {i+1}/{len(funds)}: {fund['name']}")
        
        # Update fund with comprehensive data
        updated_fund = update_fund_with_comprehensive_data(fund)
        
        # Convert datetime objects to strings for DynamoDB
        def convert_datetime(obj):
            if isinstance(obj, dict):
                return {k: convert_datetime(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_datetime(v) for v in obj]
            elif isinstance(obj, datetime):
                return obj.isoformat()
            elif hasattr(obj, 'isoformat'):  # Handle date objects
                return obj.isoformat()
            return obj
        
        updated_fund = convert_datetime(updated_fund)
        
        # Update fund in DynamoDB
        try:
            funds_table.put_item(Item=updated_fund)
            print(f"  ✓ Updated fund with market data and holdings")
        except Exception as e:
            print(f"  ✗ Error updating fund: {e}")
        
        # Generate historical data
        inception_date = fund.get('inception_date')
        if inception_date:
            if isinstance(inception_date, str):
                inception_date = datetime.fromisoformat(inception_date.replace('Z', '+00:00'))
            # Ensure inception_date is timezone-aware
            if inception_date.tzinfo is None:
                inception_date = inception_date.replace(tzinfo=timezone.utc)
            
            historical_data = generate_historical_performance_data(fund['fund_id'], inception_date)
            print(f"  ✓ Generated {len(historical_data)} historical data points")
            
            # Store historical data (in real scenario, this would go to a separate table)
            # For now, we'll just print that it's been generated
    
    # Create portfolios for sample users
    print("\n\nCreating sample portfolios...")
    portfolios_table = dynamodb.Table(PORTFOLIOS_TABLE)
    
    for user_id in SAMPLE_USER_IDS:
        print(f"\nCreating portfolios for {user_id}...")
        
        # Create 1-3 portfolios per user
        num_portfolios = random.randint(1, 3)
        for i in range(num_portfolios):
            portfolio = create_portfolio_for_user(user_id, funds)
            
            try:
                portfolios_table.put_item(Item=portfolio)
                print(f"  ✓ Created portfolio: {portfolio['name']}")
            except Exception as e:
                print(f"  ✗ Error creating portfolio: {e}")
    
    print("\n\n✅ Data population completed successfully!")
    print("\nSummary:")
    print(f"- Updated {len(funds)} funds with comprehensive market data")
    print(f"- Generated holdings data for all funds")
    print(f"- Created portfolios for {len(SAMPLE_USER_IDS)} users")
    print("\nThe frontend should now have access to:")
    print("- Portfolio data with holdings and transactions")
    print("- Market data (price, valuation, technical indicators)")
    print("- Risk analytics and performance metrics")
    print("- Holdings breakdown (sector, geographic, asset allocation)")
    print("- Historical performance data")


if __name__ == "__main__":
    populate_data()