#!/usr/bin/env python3
"""
<PERSON>ript to populate DynamoDB with mock fund data for testing purposes.
This script will add sample fund data to the DynamoDB table.
"""

import json
import boto3
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
import random
import os
import sys
from typing import List, Dict, Any, cast


def generate_mock_funds(count=50):
    """Generate mock fund data for testing."""

    fund_types = ["equity", "bond", "mixed", "money_market", "index", "etf"]
    risk_levels = ["low", "moderate", "high"]
    categories = [
        "Large Cap",
        "Mid Cap",
        "Small Cap",
        "Multi Cap",
        "Sectoral",
        "International",
    ]
    fund_managers = [
        "Aditya Birla",
        "HDFC",
        "ICICI Prudential",
        "SBI",
        "Axis",
        "Kotak",
        "Reliance",
        "UTI",
    ]
    companies = ["AMC", "Mutual Fund", "Asset Management", "Capital", "Investments"]

    funds = []

    for i in range(count):
        # Generate basic fund data
        nav = Decimal(str(round(15 + random.random() * 500, 2)))
        total_assets = Decimal(str(round(100 + random.random() * 50000, 2)))
        expense_ratio = Decimal(str(round(0.5 + random.random() * 2.5, 2)))
        minimum_investment = Decimal(str(random.choice([500, 1000, 5000, 10000])))

        fund_manager = random.choice(fund_managers)
        category = random.choice(categories)
        fund_type = random.choice(fund_types)

        # Generate performance metrics
        ytd_return = Decimal(str(round(random.uniform(-15, 25), 2)))
        one_year_return = Decimal(str(round(random.uniform(-10, 30), 2)))
        three_year_return = Decimal(str(round(random.uniform(5, 35), 2)))
        five_year_return = Decimal(str(round(random.uniform(8, 40), 2)))
        volatility = Decimal(str(round(random.uniform(5, 25), 2)))
        sharpe_ratio = Decimal(str(round(random.uniform(0.5, 2.5), 2)))
        max_drawdown = Decimal(str(round(random.uniform(-30, -5), 2)))

        # Generate holdings data
        top_holdings = [
            {
                "name": "Reliance Industries",
                "symbol": "RELIANCE",
                "percentage": Decimal(str(round(random.uniform(5, 10), 2))),
                "sector": "Oil & Gas",
            },
            {
                "name": "HDFC Bank",
                "symbol": "HDFCBANK",
                "percentage": Decimal(str(round(random.uniform(4, 8), 2))),
                "sector": "Banking",
            },
            {
                "name": "Infosys",
                "symbol": "INFY",
                "percentage": Decimal(str(round(random.uniform(3, 7), 2))),
                "sector": "Information Technology",
            },
        ]

        sector_allocation = {
            "Financial Services": Decimal(str(round(random.uniform(20, 30), 1))),
            "Information Technology": Decimal(str(round(random.uniform(15, 25), 1))),
            "Consumer Goods": Decimal(str(round(random.uniform(10, 20), 1))),
            "Healthcare": Decimal(str(round(random.uniform(5, 15), 1))),
            "Energy": Decimal(str(round(random.uniform(5, 15), 1))),
        }

        geographic_allocation = {
            "Domestic": Decimal(str(round(random.uniform(70, 95), 1))),
            "International": Decimal(str(round(random.uniform(5, 30), 1))),
        }

        # Create fund object
        fund = {
            "fund_id": f"fund-{str(uuid.uuid4())[:8]}",
            "name": f"{fund_manager} {category} Fund {i+1}",
            "fund_type": fund_type,
            "status": "active",
            "nav": nav,
            "currency": "USD",
            "inception_date": (
                datetime.now() - timedelta(days=random.randint(365, 3650))
            ).isoformat(),
            "total_assets": total_assets,
            "risk_level": random.choice(risk_levels),
            "fund_manager": fund_manager,
            "management_company": f"{fund_manager} {random.choice(companies)}",
            "expense_ratio": expense_ratio,
            "minimum_investment": minimum_investment,
            "bloomberg_ticker": f'{category.upper().replace(" ", "")}{str(i+1).zfill(3)}',
            "description": f"A professionally managed {fund_type} fund focusing on {category.lower()} investments with long-term capital appreciation goals.",
            "investment_objective": f"To provide long-term capital appreciation by investing primarily in {category.lower()} securities.",
            "benchmark": f"{category} Index",
            "performance_metrics": {
                "ytd_return": ytd_return,
                "one_year_return": one_year_return,
                "three_year_return": three_year_return,
                "five_year_return": five_year_return,
                "volatility": volatility,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
            },
            "holdings": {
                "top_holdings": top_holdings,
                "sector_allocation": sector_allocation,
                "geographic_allocation": geographic_allocation,
            },
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "tags": [
                fund_type,
                category.lower().replace(" ", "_"),
                risk_levels[random.randint(0, len(risk_levels) - 1)],
            ],
            "custom_fields": {
                "category": category,
                "sub_category": random.choice(
                    ["Growth", "Value", "Dividend", "Income"]
                ),
                "volume": str(random.randint(10000, 1000000)),
                "rating": str(random.randint(3, 5)),
            },
        }

        funds.append(fund)

    return funds


def populate_dynamodb_table(
    table_name: str, funds: List[Dict[str, Any]], region: str = "ap-northeast-1"
):
    """Populate DynamoDB table with fund data."""

    try:
        # Initialize DynamoDB resource
        dynamodb = boto3.resource("dynamodb", region_name=region)
        # Get table reference - cast to help with type inference
        table = cast(Any, dynamodb).Table(table_name)

        print(f"Populating {table_name} table with {len(funds)} funds...")

        # Batch write items to DynamoDB
        batch_size = 25  # DynamoDB batch write limit

        for i in range(0, len(funds), batch_size):
            batch = funds[i : i + batch_size]

            with table.batch_writer() as batch_writer:
                for fund in batch:
                    try:
                        batch_writer.put_item(Item=fund)
                        print(f"✓ Added fund: {fund['name']}")
                    except Exception as e:
                        print(f"✗ Error adding fund {fund['name']}: {str(e)}")
                        continue

        print(f"\n✅ Successfully populated {table_name} with mock fund data!")

        # Verify the data was inserted
        response = table.scan(Limit=5)
        print(f"\n📊 Sample data verification:")
        for item in response["Items"]:
            print(f"  - {item['name']} (ID: {item['fund_id']})")

    except Exception as e:
        print(f"❌ Error populating DynamoDB table: {str(e)}")
        sys.exit(1)


def main():
    """Main function to run the DynamoDB population script."""

    print("🚀 FundFlow DynamoDB Population Script")
    print("=" * 50)

    # Configuration
    region = os.getenv("AWS_REGION", "ap-northeast-1")
    environment = os.getenv("ENVIRONMENT", "dev")
    table_name = f"fundflow-{environment}-funds"
    fund_count = int(os.getenv("FUND_COUNT", "50"))

    print(f"📋 Configuration:")
    print(f"  - Region: {region}")
    print(f"  - Environment: {environment}")
    print(f"  - Table Name: {table_name}")
    print(f"  - Fund Count: {fund_count}")
    print()

    # Check AWS credentials
    try:
        sts = boto3.client("sts", region_name=region)
        identity = sts.get_caller_identity()
        print(f"🔐 AWS Identity: {identity.get('Arn', 'Unknown')}")
    except Exception as e:
        print(f"❌ AWS credentials not found or invalid: {str(e)}")
        print("Please ensure AWS credentials are configured properly.")
        sys.exit(1)

    # Generate mock fund data
    print(f"\n📦 Generating {fund_count} mock funds...")
    funds = generate_mock_funds(fund_count)
    print(f"✅ Generated {len(funds)} funds")

    # Populate DynamoDB
    print(f"\n💾 Populating DynamoDB table...")
    populate_dynamodb_table(table_name, funds, region)

    print(f"\n🎉 Script completed successfully!")
    print(f"You can now test the frontend with AWS backend integration.")


if __name__ == "__main__":
    main()
