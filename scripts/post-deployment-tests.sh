#!/bin/bash

# FundFlow Post-Deployment Test Suite
# This script performs comprehensive validation of deployed infrastructure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Default values
ENVIRONMENT="dev"
REGION="us-east-1"
TIMEOUT=120
SKIP_HEALTH_CHECK=false

# Function to display usage
usage() {
    echo "FundFlow Post-Deployment Test Suite"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment    Environment (dev|staging|prod) [default: dev]"
    echo "  -r, --region         AWS region [default: us-east-1]"
    echo "  -t, --timeout        Test timeout in seconds [default: 120]"
    echo "  --skip-health        Skip health check tests"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -e prod -r us-east-1"
    echo "  $0 --environment staging --timeout 180"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --skip-health)
            SKIP_HEALTH_CHECK=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    echo -e "${RED}❌ Invalid environment: $ENVIRONMENT${NC}"
    echo "Valid environments: dev, staging, prod"
    exit 1
fi

STACK_NAME="fundflow-$ENVIRONMENT"

echo -e "${BLUE}🧪 FundFlow Post-Deployment Test Suite${NC}"
echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"
echo -e "${BLUE}Stack: $STACK_NAME${NC}"
echo -e "${BLUE}Region: $REGION${NC}"
echo ""

# Function to test with timeout
test_with_timeout() {
    local test_name="$1"
    local test_command="$2"
    local timeout_seconds="$3"
    
    echo -e "${YELLOW}Testing: $test_name${NC}"
    
    if timeout "$timeout_seconds" bash -c "$test_command"; then
        echo -e "${GREEN}✅ $test_name: PASSED${NC}"
        return 0
    else
        echo -e "${RED}❌ $test_name: FAILED${NC}"
        return 1
    fi
}

# Initialize test counters
TESTS_PASSED=0
TESTS_FAILED=0

# Test function wrapper
run_test() {
    local test_name="$1"
    local test_command="$2"
    local timeout_seconds="${3:-30}"
    
    if test_with_timeout "$test_name" "$test_command" "$timeout_seconds"; then
        ((TESTS_PASSED++))
    else
        ((TESTS_FAILED++))
    fi
}

echo "=== Infrastructure Validation ==="

# Test 1: Stack Status
run_test "CloudFormation Stack Status" '
STACK_STATUS=$(aws cloudformation describe-stacks \
  --stack-name "'"$STACK_NAME"'" \
  --region "'"$REGION"'" \
  --query "Stacks[0].StackStatus" \
  --output text 2>/dev/null)

if [[ "$STACK_STATUS" == "CREATE_COMPLETE" || "$STACK_STATUS" == "UPDATE_COMPLETE" ]]; then
    echo "Stack status: $STACK_STATUS"
    exit 0
else
    echo "Invalid stack status: $STACK_STATUS"
    exit 1
fi
' 30

# Get stack outputs for subsequent tests
echo -e "${YELLOW}Retrieving stack outputs...${NC}"
API_URL=$(aws cloudformation describe-stacks \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
  --output text 2>/dev/null) || echo ""

CLOUDFRONT_URL=$(aws cloudformation describe-stacks \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --query 'Stacks[0].Outputs[?OutputKey==`CloudFrontUrl`].OutputValue' \
  --output text 2>/dev/null) || echo ""

FUND_TABLE=$(aws cloudformation describe-stacks \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --query 'Stacks[0].Outputs[?OutputKey==`FundTableName`].OutputValue' \
  --output text 2>/dev/null) || echo ""

USER_TABLE=$(aws cloudformation describe-stacks \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --query 'Stacks[0].Outputs[?OutputKey==`UserTableName`].OutputValue' \
  --output text 2>/dev/null) || echo ""

REPORT_TABLE=$(aws cloudformation describe-stacks \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --query 'Stacks[0].Outputs[?OutputKey==`ReportTableName`].OutputValue' \
  --output text 2>/dev/null) || echo ""

echo "=== API Gateway Tests ==="

if [[ -n "$API_URL" && "$API_URL" != "None" ]]; then
    echo -e "${GREEN}API Gateway URL: $API_URL${NC}"
    
    # Test 2: API Gateway Health Check
    if [[ "$SKIP_HEALTH_CHECK" != "true" ]]; then
        run_test "API Gateway Health Endpoint" '
        response=$(curl -s -w "%{http_code}" -o /dev/null "'"$API_URL"'/health")
        if [[ "$response" == "200" ]]; then
            echo "Health endpoint returned HTTP 200"
            exit 0
        else
            echo "Health endpoint returned HTTP $response"
            exit 1
        fi
        ' 30
    fi
    
    # Test 3: API Gateway CORS
    run_test "API Gateway CORS Headers" '
    cors_response=$(curl -s -I -X OPTIONS "'"$API_URL"'/health" -H "Origin: https://example.com")
    if echo "$cors_response" | grep -i "access-control-allow-origin" > /dev/null; then
        echo "CORS headers present"
        exit 0
    else
        echo "CORS headers missing"
        exit 1
    fi
    ' 30
    
    # Test 4: API Gateway Authentication (should return 401 for protected endpoints)
    run_test "API Gateway Authentication" '
    auth_response=$(curl -s -w "%{http_code}" -o /dev/null "'"$API_URL"'/funds")
    if [[ "$auth_response" == "401" ]]; then
        echo "Authentication properly enforced (HTTP 401)"
        exit 0
    else
        echo "Unexpected authentication response: HTTP $auth_response"
        exit 1
    fi
    ' 30
else
    echo -e "${RED}❌ API Gateway URL not found${NC}"
    ((TESTS_FAILED++))
fi

echo "=== CloudFront Tests ==="

if [[ -n "$CLOUDFRONT_URL" && "$CLOUDFRONT_URL" != "None" ]]; then
    echo -e "${GREEN}CloudFront URL: $CLOUDFRONT_URL${NC}"
    
    # Test 5: CloudFront Accessibility
    run_test "CloudFront Distribution" '
    cf_response=$(curl -s -w "%{http_code}" -o /dev/null "'"$CLOUDFRONT_URL"'")
    if [[ "$cf_response" == "200" || "$cf_response" == "403" ]]; then
        echo "CloudFront accessible (HTTP $cf_response)"
        exit 0
    else
        echo "CloudFront not accessible: HTTP $cf_response"
        exit 1
    fi
    ' 30
    
    # Test 6: CloudFront Cache Headers
    run_test "CloudFront Cache Configuration" '
    cache_headers=$(curl -s -I "'"$CLOUDFRONT_URL"'" | grep -i "cache-control\|x-cache" | head -1)
    if [[ -n "$cache_headers" ]]; then
        echo "Cache headers present: $cache_headers"
        exit 0
    else
        echo "Cache headers missing"
        exit 1
    fi
    ' 30
else
    echo -e "${RED}❌ CloudFront URL not found${NC}"
    ((TESTS_FAILED++))
fi

echo "=== DynamoDB Tests ==="

# Test 7: Fund Table
if [[ -n "$FUND_TABLE" && "$FUND_TABLE" != "None" ]]; then
    run_test "DynamoDB Fund Table" '
    table_status=$(aws dynamodb describe-table \
      --table-name "'"$FUND_TABLE"'" \
      --region "'"$REGION"'" \
      --query "Table.TableStatus" \
      --output text 2>/dev/null)
    
    if [[ "$table_status" == "ACTIVE" ]]; then
        echo "Fund table active: '"$FUND_TABLE"'"
        exit 0
    else
        echo "Fund table not active: $table_status"
        exit 1
    fi
    ' 30
else
    echo -e "${YELLOW}⚠️ Fund table name not found${NC}"
fi

# Test 8: User Table
if [[ -n "$USER_TABLE" && "$USER_TABLE" != "None" ]]; then
    run_test "DynamoDB User Table" '
    table_status=$(aws dynamodb describe-table \
      --table-name "'"$USER_TABLE"'" \
      --region "'"$REGION"'" \
      --query "Table.TableStatus" \
      --output text 2>/dev/null)
    
    if [[ "$table_status" == "ACTIVE" ]]; then
        echo "User table active: '"$USER_TABLE"'"
        exit 0
    else
        echo "User table not active: $table_status"
        exit 1
    fi
    ' 30
else
    echo -e "${YELLOW}⚠️ User table name not found${NC}"
fi

# Test 9: Report Table
if [[ -n "$REPORT_TABLE" && "$REPORT_TABLE" != "None" ]]; then
    run_test "DynamoDB Report Table" '
    table_status=$(aws dynamodb describe-table \
      --table-name "'"$REPORT_TABLE"'" \
      --region "'"$REGION"'" \
      --query "Table.TableStatus" \
      --output text 2>/dev/null)
    
    if [[ "$table_status" == "ACTIVE" ]]; then
        echo "Report table active: '"$REPORT_TABLE"'"
        exit 0
    else
        echo "Report table not active: $table_status"
        exit 1
    fi
    ' 30
else
    echo -e "${YELLOW}⚠️ Report table name not found${NC}"
fi

echo "=== Cognito Tests ==="

# Test 10: Cognito User Pool
USER_POOL_ID=$(aws cloudformation describe-stacks \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --query 'Stacks[0].Outputs[?OutputKey==`UserPoolId`].OutputValue' \
  --output text 2>/dev/null) || echo ""

if [[ -n "$USER_POOL_ID" && "$USER_POOL_ID" != "None" ]]; then
    run_test "Cognito User Pool" '
    pool_status=$(aws cognito-idp describe-user-pool \
      --user-pool-id "'"$USER_POOL_ID"'" \
      --region "'"$REGION"'" \
      --query "UserPool.Status" \
      --output text 2>/dev/null)
    
    if [[ "$pool_status" == "Enabled" ]]; then
        echo "User pool active: '"$USER_POOL_ID"'"
        exit 0
    else
        echo "User pool not active: $pool_status"
        exit 1
    fi
    ' 30
else
    echo -e "${YELLOW}⚠️ User Pool ID not found${NC}"
fi

echo ""
echo "=== Test Summary ==="
TOTAL_TESTS=$((TESTS_PASSED + TESTS_FAILED))
echo -e "${GREEN}Tests Passed: $TESTS_PASSED${NC}"
echo -e "${RED}Tests Failed: $TESTS_FAILED${NC}"
echo "Total Tests: $TOTAL_TESTS"

if [[ $TESTS_FAILED -eq 0 ]]; then
    echo -e "${GREEN}🎉 All tests passed! Deployment validation successful.${NC}"
    exit 0
else
    echo -e "${RED}❌ Some tests failed. Please review the deployment.${NC}"
    exit 1
fi 