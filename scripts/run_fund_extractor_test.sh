#!/bin/bash

# Script to run the FastAPI fund extractor server and test client

echo "🚀 Fund Extractor Local Testing Script"
echo "======================================"

# Check if OPENROUTER_API_KEY is set
if [ -z "$OPENROUTER_API_KEY" ]; then
    echo "❌ Error: OPENROUTER_API_KEY environment variable is not set"
    echo "Please set it with: export OPENROUTER_API_KEY='your-api-key'"
    exit 1
fi

# Activate conda environment if available
if command -v conda &> /dev/null; then
    echo "📦 Activating conda environment..."
    conda activate ff_env 2>/dev/null || echo "⚠️  Could not activate ff_env, continuing..."
fi

# Function to cleanup on exit
cleanup() {
    echo -e "\n🛑 Stopping server..."
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID 2>/dev/null
        wait $SERVER_PID 2>/dev/null
    fi
    exit 0
}

# Set trap to cleanup on exit
trap cleanup EXIT INT TERM

# Start the FastAPI server in the background
echo -e "\n📡 Starting FastAPI server..."
python scripts/fastapi_fund_extractor.py &
SERVER_PID=$!

# Wait for server to start
echo "⏳ Waiting for server to start..."
for i in {1..10}; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ Server is ready!"
        break
    fi
    if [ $i -eq 10 ]; then
        echo "❌ Server failed to start after 10 seconds"
        exit 1
    fi
    sleep 1
done

# Run the test client
echo -e "\n🧪 Running test client..."
python scripts/test_fund_extractor_client.py

# Keep server running for manual testing if desired
echo -e "\n✅ Test completed! Server is still running on http://localhost:8000"
echo "📝 You can test the API manually with:"
echo "   - Swagger UI: http://localhost:8000/docs"
echo "   - ReDoc: http://localhost:8000/redoc"
echo ""
echo "Press Ctrl+C to stop the server..."

# Wait for user to stop
wait $SERVER_PID