#!/bin/bash

# Setup AWS Profiles for FundFlow Development
# This script helps configure AWS profiles for different environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 FundFlow AWS Profile Setup${NC}"
echo "This script will help you configure AWS profiles for different environments."
echo ""

# Function to configure a profile
configure_profile() {
    local env=$1
    local profile_name="fundflow-${env}"
    
    echo -e "${YELLOW}Configuring profile: ${profile_name}${NC}"
    echo "Please provide AWS credentials for the ${env} environment:"
    
    read -p "AWS Access Key ID: " access_key_id
    read -s -p "AWS Secret Access Key: " secret_access_key
    echo ""
    read -p "Default region [us-east-1]: " region
    region=${region:-us-east-1}
    read -p "Default output format [json]: " output_format
    output_format=${output_format:-json}
    
    # Configure the profile
    aws configure set aws_access_key_id "$access_key_id" --profile "$profile_name"
    aws configure set aws_secret_access_key "$secret_access_key" --profile "$profile_name"
    aws configure set region "$region" --profile "$profile_name"
    aws configure set output "$output_format" --profile "$profile_name"
    
    echo -e "${GREEN}✅ Profile ${profile_name} configured successfully${NC}"
    echo ""
}

# Function to test a profile
test_profile() {
    local profile_name=$1
    echo -e "${BLUE}Testing profile: ${profile_name}${NC}"
    
    if aws sts get-caller-identity --profile "$profile_name" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Profile ${profile_name} is working${NC}"
        aws sts get-caller-identity --profile "$profile_name" --query 'Account' --output text | xargs -I {} echo "Account ID: {}"
    else
        echo -e "${RED}❌ Profile ${profile_name} failed authentication${NC}"
    fi
    echo ""
}

# Main configuration flow
echo "We'll configure profiles for three environments: dev, staging, and prod"
echo "You can use the same AWS account for all environments or different accounts."
echo ""

# Check if profiles already exist
existing_profiles=$(aws configure list-profiles | grep "fundflow-" || true)
if [ ! -z "$existing_profiles" ]; then
    echo -e "${YELLOW}Found existing FundFlow profiles:${NC}"
    echo "$existing_profiles"
    read -p "Do you want to reconfigure these profiles? (y/N): " reconfigure
    if [[ ! $reconfigure =~ ^[Yy]$ ]]; then
        echo "Skipping profile configuration."
        exit 0
    fi
fi

# Configure each environment
for env in dev staging prod; do
    configure_profile "$env"
done

echo -e "${GREEN}🎉 All profiles configured!${NC}"
echo ""

# Test all profiles
echo -e "${BLUE}Testing all profiles...${NC}"
for env in dev staging prod; do
    test_profile "fundflow-${env}"
done

echo -e "${GREEN}✅ AWS Profile setup complete!${NC}"
echo ""
echo "You can now use these profiles with:"
echo "  aws --profile fundflow-dev s3 ls"
echo "  sam deploy --profile fundflow-staging"
echo ""
echo "Your samconfig.toml is already configured to use these profiles automatically." 