#!/bin/bash

# Minimal Cognito Resource Server Setup for FundFlow API
# This version only creates the resource server without modifying the existing client
# Safer approach that avoids OAuth configuration conflicts

echo "🔧 Minimal Cognito Resource Server Setup for FundFlow API"
echo "==========================================================="

# Configuration
ENVIRONMENT=${ENVIRONMENT:-dev}
AWS_PROFILE=${AWS_PROFILE:-fundflow-dev}
AWS_REGION=${AWS_REGION:-ap-northeast-1}
USER_POOL_ID="ap-northeast-1_H2kKHGUAT"

# Resource Server Configuration
RESOURCE_SERVER_ID="fundflow-api"
RESOURCE_SERVER_NAME="FundFlow API Resource Server"

echo "📋 Configuration:"
echo "  - Environment: $ENVIRONMENT"
echo "  - AWS Profile: $AWS_PROFILE"
echo "  - AWS Region: $AWS_REGION"
echo "  - User Pool ID: $USER_POOL_ID"
echo "  - Resource Server ID: $RESOURCE_SERVER_ID"
echo ""

# Check AWS credentials
echo "🔐 Checking AWS credentials..."
if ! AWS_PROFILE=$AWS_PROFILE aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS credentials not found or invalid for profile: $AWS_PROFILE"
    echo "Please ensure AWS credentials are configured properly."
    exit 1
fi

echo "✅ AWS credentials verified"
echo ""

# Step 1: Create Resource Server
echo "🔧 Creating Resource Server..."
RESOURCE_SERVER_EXISTS=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp describe-resource-server \
    --user-pool-id "$USER_POOL_ID" \
    --identifier "$RESOURCE_SERVER_ID" \
    --region "$AWS_REGION" 2>/dev/null || echo "not_found")

if [ "$RESOURCE_SERVER_EXISTS" = "not_found" ]; then
    echo "   Creating new resource server..."
    
    AWS_PROFILE=$AWS_PROFILE aws cognito-idp create-resource-server \
        --user-pool-id "$USER_POOL_ID" \
        --identifier "$RESOURCE_SERVER_ID" \
        --name "$RESOURCE_SERVER_NAME" \
        --scopes ScopeName="funds:read",ScopeDescription="Read access to funds API" \
               ScopeName="funds:write",ScopeDescription="Write access to funds API" \
               ScopeName="users:read",ScopeDescription="Read access to users API" \
               ScopeName="users:write",ScopeDescription="Write access to users API" \
               ScopeName="reports:read",ScopeDescription="Read access to reports API" \
               ScopeName="reports:write",ScopeDescription="Write access to reports API" \
               ScopeName="admin:all",ScopeDescription="Full access" \
        --region "$AWS_REGION"
    
    if [ $? -eq 0 ]; then
        echo "   ✅ Resource server created successfully"
    else
        echo "   ❌ Failed to create resource server"
        exit 1
    fi
else
    echo "   ✅ Resource server already exists"
    echo "   Resource server details:"
    echo "$RESOURCE_SERVER_EXISTS" | jq -r '.ResourceServer | "   Name: \(.Name)\n   ID: \(.Identifier)\n   Scopes: \(.Scopes | map(.ScopeName) | join(", "))"'
fi

echo ""

# Step 2: Display Resource Server Information
echo "📋 Resource Server Information:"
echo "================================"
RESOURCE_SERVER_INFO=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp describe-resource-server \
    --user-pool-id "$USER_POOL_ID" \
    --identifier "$RESOURCE_SERVER_ID" \
    --region "$AWS_REGION")

echo "Resource Server ID: $RESOURCE_SERVER_ID"
echo "Resource Server Name: $RESOURCE_SERVER_NAME"
echo ""
echo "Available Scopes:"
echo "$RESOURCE_SERVER_INFO" | jq -r '.ResourceServer.Scopes[] | "- \(.ScopeName): \(.ScopeDescription)"'

echo ""
echo "✅ Resource Server Setup Complete!"
echo ""
echo "🔧 Next Steps:"
echo "=============="
echo "1. Update API Gateway template with scopes (already done in template.yaml)"
echo "2. Deploy the updated template: sam deploy --config-env dev"
echo "3. Configure your app client to use these scopes (manual step needed)"
echo ""
echo "📝 Manual Client Configuration Required:"
echo "========================================="
echo "Go to AWS Cognito Console → User Pools → Your Pool → App Integration → App clients"
echo "Select your app client and add these scopes under 'Custom scopes':"
echo ""
echo "$RESOURCE_SERVER_INFO" | jq -r '.ResourceServer.Scopes[] | "- fundflow-api/\(.ScopeName)"'
echo ""
echo "💡 This minimal approach avoids OAuth configuration conflicts"
echo "   You can configure OAuth flows manually in the console if needed" 