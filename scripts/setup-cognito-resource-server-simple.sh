#!/bin/bash

# Simple Cognito Resource Server Setup for FundFlow API
# This version focuses on setting up the resource server and basic scopes
# It works with existing clients without requiring client secret generation

echo "🔧 Simple Cognito Resource Server Setup for FundFlow API"
echo "========================================================="

# Configuration
ENVIRONMENT=${ENVIRONMENT:-dev}
AWS_PROFILE=${AWS_PROFILE:-fundflow-dev}
AWS_REGION=${AWS_REGION:-ap-northeast-1}
USER_POOL_ID="ap-northeast-1_H2kKHGUAT"
CLIENT_ID="2jh76f894g6lv9vrus4qbb9hu7"

# Resource Server Configuration
RESOURCE_SERVER_ID="fundflow-api"
RESOURCE_SERVER_NAME="FundFlow API Resource Server"

echo "📋 Configuration:"
echo "  - Environment: $ENVIRONMENT"
echo "  - AWS Profile: $AWS_PROFILE"
echo "  - AWS Region: $AWS_REGION"
echo "  - User Pool ID: $USER_POOL_ID"
echo "  - Client ID: $CLIENT_ID"
echo "  - Resource Server ID: $RESOURCE_SERVER_ID"
echo ""

# Check AWS credentials
echo "🔐 Checking AWS credentials..."
if ! AWS_PROFILE=$AWS_PROFILE aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS credentials not found or invalid for profile: $AWS_PROFILE"
    echo "Please ensure AWS credentials are configured properly."
    exit 1
fi

echo "✅ AWS credentials verified"
echo ""

# Step 1: Create Resource Server
echo "🔧 Creating Resource Server..."
RESOURCE_SERVER_EXISTS=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp describe-resource-server \
    --user-pool-id "$USER_POOL_ID" \
    --identifier "$RESOURCE_SERVER_ID" \
    --region "$AWS_REGION" 2>/dev/null || echo "not_found")

if [ "$RESOURCE_SERVER_EXISTS" = "not_found" ]; then
    echo "   Creating new resource server..."
    
    AWS_PROFILE=$AWS_PROFILE aws cognito-idp create-resource-server \
        --user-pool-id "$USER_POOL_ID" \
        --identifier "$RESOURCE_SERVER_ID" \
        --name "$RESOURCE_SERVER_NAME" \
        --scopes ScopeName="funds:read",ScopeDescription="Read access to funds API" \
               ScopeName="funds:write",ScopeDescription="Write access to funds API" \
               ScopeName="users:read",ScopeDescription="Read access to users API" \
               ScopeName="users:write",ScopeDescription="Write access to users API" \
               ScopeName="reports:read",ScopeDescription="Read access to reports API" \
               ScopeName="reports:write",ScopeDescription="Write access to reports API" \
               ScopeName="admin:all",ScopeDescription="Full administrative access" \
        --region "$AWS_REGION"
    
    if [ $? -eq 0 ]; then
        echo "   ✅ Resource server created successfully"
    else
        echo "   ❌ Failed to create resource server"
        exit 1
    fi
else
    echo "   ✅ Resource server already exists"
fi

echo ""

# Step 2: Update User Pool Client (basic configuration)
echo "🔧 Updating User Pool Client for OAuth2..."

# Get current client configuration
echo "   Getting current client configuration..."
CURRENT_CONFIG=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp describe-user-pool-client \
    --user-pool-id "$USER_POOL_ID" \
    --client-id "$CLIENT_ID" \
    --region "$AWS_REGION")

# Extract current settings
CLIENT_NAME=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.ClientName')
REFRESH_TOKEN_VALIDITY=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.RefreshTokenValidity // 30')
ACCESS_TOKEN_VALIDITY=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.AccessTokenValidity // 60')
ID_TOKEN_VALIDITY=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.IdTokenValidity // 60')

# Get existing OAuth configuration if any
EXISTING_CALLBACK_URLS=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.CallbackURLs // [] | @json')
EXISTING_LOGOUT_URLS=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.LogoutURLs // [] | @json')
EXISTING_OAUTH_FLOWS=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.AllowedOAuthFlows // [] | @json')

echo "   Current OAuth configuration:"
echo "   - Callback URLs: $EXISTING_CALLBACK_URLS"
echo "   - Logout URLs: $EXISTING_LOGOUT_URLS"
echo "   - OAuth Flows: $EXISTING_OAUTH_FLOWS"

echo "   Updating client with scopes (keeping existing OAuth config)..."

# Prepare the update command based on existing OAuth configuration
if [ "$EXISTING_CALLBACK_URLS" = "[]" ] || [ "$EXISTING_CALLBACK_URLS" = "null" ]; then
    echo "   No existing OAuth flows detected, updating for basic auth only..."
    
    # Update without OAuth flows (just scopes for future use)
    AWS_PROFILE=$AWS_PROFILE aws cognito-idp update-user-pool-client \
        --user-pool-id "$USER_POOL_ID" \
        --client-id "$CLIENT_ID" \
        --client-name "$CLIENT_NAME" \
        --explicit-auth-flows "ALLOW_USER_PASSWORD_AUTH" "ALLOW_REFRESH_TOKEN_AUTH" "ALLOW_ADMIN_USER_PASSWORD_AUTH" \
        --supported-identity-providers "COGNITO" \
        --access-token-validity "$ACCESS_TOKEN_VALIDITY" \
        --id-token-validity "$ID_TOKEN_VALIDITY" \
        --refresh-token-validity "$REFRESH_TOKEN_VALIDITY" \
        --token-validity-units AccessToken=minutes,IdToken=minutes,RefreshToken=days \
        --region "$AWS_REGION"
else
    echo "   Existing OAuth flows detected, preserving them and adding scopes..."
    
    # Parse existing URLs
    CALLBACK_URLS_ARRAY=$(echo "$EXISTING_CALLBACK_URLS" | jq -r '.[]' | tr '\n' ' ')
    LOGOUT_URLS_ARRAY=$(echo "$EXISTING_LOGOUT_URLS" | jq -r '.[]' | tr '\n' ' ')
    
    # Build callback URLs parameter
    if [ "$CALLBACK_URLS_ARRAY" != "" ]; then
        CALLBACK_URLS_PARAM="--callback-urls $CALLBACK_URLS_ARRAY"
    else
        CALLBACK_URLS_PARAM=""
    fi
    
    # Build logout URLs parameter  
    if [ "$LOGOUT_URLS_ARRAY" != "" ]; then
        LOGOUT_URLS_PARAM="--logout-urls $LOGOUT_URLS_ARRAY"
    else
        LOGOUT_URLS_PARAM=""
    fi
    
    # Update with existing OAuth configuration plus new scopes
    AWS_PROFILE=$AWS_PROFILE aws cognito-idp update-user-pool-client \
        --user-pool-id "$USER_POOL_ID" \
        --client-id "$CLIENT_ID" \
        --client-name "$CLIENT_NAME" \
        --explicit-auth-flows "ALLOW_USER_PASSWORD_AUTH" "ALLOW_REFRESH_TOKEN_AUTH" "ALLOW_ADMIN_USER_PASSWORD_AUTH" \
        --supported-identity-providers "COGNITO" \
        --allowed-o-auth-flows "code" "implicit" \
        --allowed-o-auth-scopes "email" "openid" "profile" \
            "fundflow-api/funds:read" \
            "fundflow-api/funds:write" \
            "fundflow-api/users:read" \
            "fundflow-api/users:write" \
            "fundflow-api/reports:read" \
            "fundflow-api/reports:write" \
            "fundflow-api/admin:all" \
        --allowed-o-auth-flows-user-pool-client \
        $CALLBACK_URLS_PARAM \
        $LOGOUT_URLS_PARAM \
        --access-token-validity "$ACCESS_TOKEN_VALIDITY" \
        --id-token-validity "$ID_TOKEN_VALIDITY" \
        --refresh-token-validity "$REFRESH_TOKEN_VALIDITY" \
        --token-validity-units AccessToken=minutes,IdToken=minutes,RefreshToken=days \
        --region "$AWS_REGION"
fi

if [ $? -eq 0 ]; then
    echo "   ✅ User Pool Client updated successfully"
else
    echo "   ❌ Failed to update User Pool Client"
    exit 1
fi

echo ""

# Step 3: Get the updated client configuration
echo "🔑 Retrieving updated client configuration..."
UPDATED_CONFIG=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp describe-user-pool-client \
    --user-pool-id "$USER_POOL_ID" \
    --client-id "$CLIENT_ID" \
    --region "$AWS_REGION")

CLIENT_SECRET=$(echo "$UPDATED_CONFIG" | jq -r '.UserPoolClient.ClientSecret // "Not Generated"')
OAUTH_FLOWS=$(echo "$UPDATED_CONFIG" | jq -r '.UserPoolClient.AllowedOAuthFlows[]' | tr '\n' ' ')

echo "✅ Setup Complete!"
echo ""
echo "📋 Updated Configuration:"
echo "=========================="
echo "User Pool ID: $USER_POOL_ID"
echo "Client ID: $CLIENT_ID"
echo "Client Name: $CLIENT_NAME"
echo "Client Secret: $CLIENT_SECRET"
echo "OAuth Flows: $OAUTH_FLOWS"
echo "Resource Server ID: $RESOURCE_SERVER_ID"
echo ""
echo "Available Scopes:"
echo "- fundflow-api/funds:read"
echo "- fundflow-api/funds:write"
echo "- fundflow-api/users:read"
echo "- fundflow-api/users:write"
echo "- fundflow-api/reports:read"
echo "- fundflow-api/reports:write"
echo "- fundflow-api/admin:all"
echo ""

if [ "$CLIENT_SECRET" = "Not Generated" ]; then
    echo "⚠️  Note: Client secret not generated (normal for public clients)"
    echo "   For machine-to-machine authentication, you may need to:"
    echo "   1. Create a separate app client with 'client_credentials' flow"
    echo "   2. Or use user authentication flows instead"
    echo ""
    echo "🔧 Alternative Authentication Methods:"
    echo "   - User Password Auth (ADMIN_USER_PASSWORD_AUTH)"
    echo "   - Authorization Code with PKCE (for web apps)"
    echo "   - ID Token validation (for user sessions)"
else
    echo "✅ Client secret available for OAuth2 client_credentials flow"
fi

echo ""
echo "🔧 Next Steps:"
echo "1. Deploy updated API Gateway template: sam deploy --config-env dev"
echo "2. Test with updated test script: cd tests/integration && python test_aws_api_client.py"
echo "3. Update frontend authentication to use new scopes"
echo ""
echo "💡 The API Gateway now expects tokens with proper scopes for authorization" 