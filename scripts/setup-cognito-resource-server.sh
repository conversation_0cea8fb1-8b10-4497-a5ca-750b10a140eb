#!/bin/bash

# Setup Cognito Resource Server for FundFlow API
# This script creates a resource server in Cognito for API Gateway authentication

echo "🔧 Setting up Cognito Resource Server for FundFlow API"
echo "======================================================="

# Configuration
ENVIRONMENT=${ENVIRONMENT:-dev}
AWS_PROFILE=${AWS_PROFILE:-fundflow-dev}
AWS_REGION=${AWS_REGION:-ap-northeast-1}
USER_POOL_ID="ap-northeast-1_H2kKHGUAT"
CLIENT_ID="2jh76f894g6lv9vrus4qbb9hu7"

# Resource Server Configuration
RESOURCE_SERVER_ID="fundflow-api"
RESOURCE_SERVER_NAME="FundFlow API Resource Server"

echo "📋 Configuration:"
echo "  - Environment: $ENVIRONMENT"
echo "  - AWS Profile: $AWS_PROFILE"
echo "  - AWS Region: $AWS_REGION"
echo "  - User Pool ID: $USER_POOL_ID"
echo "  - Client ID: $CLIENT_ID"
echo "  - Resource Server ID: $RESOURCE_SERVER_ID"
echo ""

# Check AWS credentials
echo "🔐 Checking AWS credentials..."
if ! AWS_PROFILE=$AWS_PROFILE aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS credentials not found or invalid for profile: $AWS_PROFILE"
    echo "Please ensure AWS credentials are configured properly."
    exit 1
fi

echo "✅ AWS credentials verified"
echo ""

# Step 1: Create Resource Server
echo "🔧 Creating Resource Server..."
RESOURCE_SERVER_EXISTS=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp describe-resource-server \
    --user-pool-id "$USER_POOL_ID" \
    --identifier "$RESOURCE_SERVER_ID" \
    --region "$AWS_REGION" 2>/dev/null || echo "not_found")

if [ "$RESOURCE_SERVER_EXISTS" = "not_found" ]; then
    echo "   Creating new resource server..."
    
    AWS_PROFILE=$AWS_PROFILE aws cognito-idp create-resource-server \
        --user-pool-id "$USER_POOL_ID" \
        --identifier "$RESOURCE_SERVER_ID" \
        --name "$RESOURCE_SERVER_NAME" \
        --scopes ScopeName="funds:read",ScopeDescription="Read access to funds API" \
               ScopeName="funds:write",ScopeDescription="Write access to funds API" \
               ScopeName="users:read",ScopeDescription="Read access to users API" \
               ScopeName="users:write",ScopeDescription="Write access to users API" \
               ScopeName="reports:read",ScopeDescription="Read access to reports API" \
               ScopeName="reports:write",ScopeDescription="Write access to reports API" \
               ScopeName="admin:all",ScopeDescription="Full access" \
        --region "$AWS_REGION"
    
    if [ $? -eq 0 ]; then
        echo "   ✅ Resource server created successfully"
    else
        echo "   ❌ Failed to create resource server"
        exit 1
    fi
else
    echo "   ✅ Resource server already exists"
fi

echo ""

# Step 2: Update User Pool Client to support resource server scopes
echo "🔧 Updating User Pool Client..."

# Get current client configuration
echo "   Getting current client configuration..."
CURRENT_CONFIG=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp describe-user-pool-client \
    --user-pool-id "$USER_POOL_ID" \
    --client-id "$CLIENT_ID" \
    --region "$AWS_REGION")

# Extract current settings
CLIENT_NAME=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.ClientName')
CALLBACK_URLS=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.CallbackURLs // [] | @json')
LOGOUT_URLS=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.LogoutURLs // [] | @json')
REFRESH_TOKEN_VALIDITY=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.RefreshTokenValidity // 30')
ACCESS_TOKEN_VALIDITY=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.AccessTokenValidity // 60')
ID_TOKEN_VALIDITY=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.IdTokenValidity // 60')

echo "   Updating client with resource server scopes..."

# Check if client already has a secret
EXISTING_SECRET=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.ClientSecret // "null"')

if [ "$EXISTING_SECRET" = "null" ]; then
    echo "   Client doesn't have a secret, need to recreate with secret..."
    
    # Delete and recreate the client to generate a secret
    # Note: This will invalidate existing tokens
    echo "   Warning: This will invalidate existing tokens for this client"
    
    # Create a new client with secret
    NEW_CLIENT_RESPONSE=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp create-user-pool-client \
        --user-pool-id "$USER_POOL_ID" \
        --client-name "${CLIENT_NAME}-with-secret" \
        --explicit-auth-flows "ALLOW_USER_PASSWORD_AUTH" "ALLOW_REFRESH_TOKEN_AUTH" "ALLOW_ADMIN_USER_PASSWORD_AUTH" \
        --supported-identity-providers "COGNITO" \
        --allowed-o-auth-flows "code" "implicit" "client_credentials" \
        --allowed-o-auth-scopes "email" "openid" "profile" \
            "fundflow-api/funds:read" \
            "fundflow-api/funds:write" \
            "fundflow-api/users:read" \
            "fundflow-api/users:write" \
            "fundflow-api/reports:read" \
            "fundflow-api/reports:write" \
            "fundflow-api/admin:all" \
        --allowed-o-auth-flows-user-pool-client \
        --generate-secret \
        --access-token-validity "$ACCESS_TOKEN_VALIDITY" \
        --id-token-validity "$ID_TOKEN_VALIDITY" \
        --refresh-token-validity "$REFRESH_TOKEN_VALIDITY" \
        --token-validity-units AccessToken=minutes,IdToken=minutes,RefreshToken=days \
        --region "$AWS_REGION" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        NEW_CLIENT_ID=$(echo "$NEW_CLIENT_RESPONSE" | jq -r '.UserPoolClient.ClientId')
        echo "   ✅ New client created with secret: $NEW_CLIENT_ID"
        echo "   ⚠️  Please update your configuration to use the new client ID: $NEW_CLIENT_ID"
        CLIENT_ID="$NEW_CLIENT_ID"
    else
        echo "   ❌ Failed to create new client with secret"
        echo "   Trying to update existing client without secret generation..."
    fi
fi

# Update the existing client (or skip if we created a new one)
if [ "$EXISTING_SECRET" != "null" ] || [ $? -ne 0 ]; then
    echo "   Updating existing client configuration..."
    AWS_PROFILE=$AWS_PROFILE aws cognito-idp update-user-pool-client \
        --user-pool-id "$USER_POOL_ID" \
        --client-id "$CLIENT_ID" \
        --client-name "$CLIENT_NAME" \
        --explicit-auth-flows "ALLOW_USER_PASSWORD_AUTH" "ALLOW_REFRESH_TOKEN_AUTH" "ALLOW_ADMIN_USER_PASSWORD_AUTH" \
        --supported-identity-providers "COGNITO" \
        --allowed-o-auth-flows "code" "implicit" "client_credentials" \
        --allowed-o-auth-scopes "email" "openid" "profile" \
            "fundflow-api/funds:read" \
            "fundflow-api/funds:write" \
            "fundflow-api/users:read" \
            "fundflow-api/users:write" \
            "fundflow-api/reports:read" \
            "fundflow-api/reports:write" \
            "fundflow-api/admin:all" \
        --allowed-o-auth-flows-user-pool-client \
        --access-token-validity "$ACCESS_TOKEN_VALIDITY" \
        --id-token-validity "$ID_TOKEN_VALIDITY" \
        --refresh-token-validity "$REFRESH_TOKEN_VALIDITY" \
        --token-validity-units AccessToken=minutes,IdToken=minutes,RefreshToken=days \
        --region "$AWS_REGION"
fi

if [ $? -eq 0 ]; then
    echo "   ✅ User Pool Client updated successfully"
else
    echo "   ❌ Failed to update User Pool Client"
    exit 1
fi

echo ""

# Step 3: Get the updated client secret
echo "🔑 Retrieving updated client configuration..."
UPDATED_CONFIG=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp describe-user-pool-client \
    --user-pool-id "$USER_POOL_ID" \
    --client-id "$CLIENT_ID" \
    --region "$AWS_REGION")

CLIENT_SECRET=$(echo "$UPDATED_CONFIG" | jq -r '.UserPoolClient.ClientSecret // "N/A"')
UPDATED_CLIENT_NAME=$(echo "$UPDATED_CONFIG" | jq -r '.UserPoolClient.ClientName // "N/A"')

echo "✅ Setup Complete!"
echo ""
echo "📋 Updated Configuration:"
echo "=========================="
echo "User Pool ID: $USER_POOL_ID"
echo "Client ID: $CLIENT_ID"
echo "Client Name: $UPDATED_CLIENT_NAME"
echo "Client Secret: $CLIENT_SECRET"
echo "Resource Server ID: $RESOURCE_SERVER_ID"

# Update test configuration if client ID changed
if [ "$CLIENT_ID" != "2jh76f894g6lv9vrus4qbb9hu7" ]; then
    echo ""
    echo "⚠️  IMPORTANT: Client ID has changed!"
    echo "Please update the following files with the new Client ID:"
    echo "  - tests/integration/test_aws_api_client.py (line ~23)"
    echo "  - Any other configuration files"
    echo "  - Frontend environment variables"
fi
echo ""
echo "Available Scopes:"
echo "- fundflow-api/funds:read"
echo "- fundflow-api/funds:write"
echo "- fundflow-api/users:read"
echo "- fundflow-api/users:write"
echo "- fundflow-api/reports:read"
echo "- fundflow-api/reports:write"
echo "- fundflow-api/admin:all"
echo ""
echo "🔧 Next Steps:"
echo "1. Update your test script to use client_credentials flow"
echo "2. Use the client secret for authentication"
echo "3. Update API Gateway authorizer to validate scopes"
echo ""
echo "💡 Note: Save the client secret securely as it won't be shown again"

aws cognito-idp update-user-pool-client \
  --user-pool-id $USER_POOL_ID \
  --client-id $CLIENT_ID \
  --allowed-o-auth-scopes openid profile email fundflow-api/admin:all \
  --region $REGION 