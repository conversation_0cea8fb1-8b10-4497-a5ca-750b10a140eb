#!/bin/bash

# Setup Test Data Script for FundFlow
# This script populates DynamoDB with mock fund data for testing

echo "🚀 FundFlow Test Data Setup Script"
echo "=================================="

# Check if AWS CLI is available
if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI is not installed. Please install it first."
    exit 1
fi

# Set default values
ENVIRONMENT=${ENVIRONMENT:-dev}
FUND_COUNT=${FUND_COUNT:-50}
AWS_PROFILE=${AWS_PROFILE:-fundflow-dev}
AWS_REGION=${AWS_REGION:-ap-northeast-1}

echo "📋 Configuration:"
echo "  - Environment: $ENVIRONMENT"
echo "  - Fund Count: $FUND_COUNT"
echo "  - AWS Profile: $AWS_PROFILE"
echo "  - AWS Region: $AWS_REGION"
echo ""

# Check AWS credentials
echo "🔐 Checking AWS credentials..."
if ! AWS_PROFILE=$AWS_PROFILE aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS credentials not found or invalid for profile: $AWS_PROFILE"
    echo "Please ensure AWS credentials are configured properly."
    exit 1
fi

# Check if Python script exists
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PYTHON_SCRIPT="$SCRIPT_DIR/populate_dynamodb.py"

if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo "❌ Python script not found: $PYTHON_SCRIPT"
    exit 1
fi

# Check if boto3 is installed
if ! python3 -c "import boto3" &> /dev/null; then
    echo "❌ boto3 is not installed. Installing..."
    pip3 install boto3
fi

# Run the population script
echo "🔄 Running DynamoDB population script..."
AWS_PROFILE=$AWS_PROFILE \
ENVIRONMENT=$ENVIRONMENT \
FUND_COUNT=$FUND_COUNT \
AWS_REGION=$AWS_REGION \
python3 "$PYTHON_SCRIPT"

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Test data setup completed successfully!"
    echo ""
    echo "📝 Next steps:"
    echo "  1. Start the frontend: cd frontend && npm run dev"
    echo "  2. Create a .env.local file in frontend/ with:"
    echo "     NEXT_PUBLIC_USE_AWS_API=true"
    echo "     NEXT_PUBLIC_API_BASE_URL=https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
    echo "  3. Test the API integration in your browser"
    echo ""
else
    echo ""
    echo "❌ Test data setup failed. Check the error messages above."
    exit 1
fi 