#!/bin/bash

# FundFlow Test Environment Setup Script
# This script configures the Python environment for testing in Cursor

echo "Setting up FundFlow test environment..."

# Activate conda environment
echo "Activating conda environment ff_env..."
eval "$(conda shell.bash hook)"
conda activate ff_env

# Fix PATH to prioritize conda over pyenv
echo "Configuring PATH to use conda Python..."
export PATH="/Users/<USER>/miniconda3/envs/ff_env/bin:$PATH"

# Unset pyenv variables that might interfere
unset PYENV_ROOT
unset PYENV_VERSION

# Set up Python path for proper imports
export PYTHONPATH="/Volumes/Macintosh HD/Users/<USER>/Projects/FundFlow/src:$PYTHONPATH"

# Set up AWS environment variables for testing
export AWS_ACCESS_KEY_ID="testing"
export AWS_SECRET_ACCESS_KEY="testing"
export AWS_SECURITY_TOKEN="testing"
export AWS_SESSION_TOKEN="testing"
export AWS_DEFAULT_REGION="ap-northeast-1"
export AWS_REGION="ap-northeast-1"

# Set up project-specific environment variables
export DYNAMODB_TABLE="fund-management-dev"
export USER_POOL_ID="us-east-1_test123"
export USER_POOL_CLIENT_ID="test_client_id"

# Verify setup
echo "Environment setup complete!"
echo "Python executable: $(which python)"
echo "Pip executable: $(which pip)"
echo "Python version: $(python --version)"
echo "PYTHONPATH: $PYTHONPATH"

echo ""
echo "To use this environment in your shell, run:"
echo "source setup_test_env.sh"
echo ""
echo "To run tests:"
echo "pytest tests/"
echo "pytest tests/test_funds_api.py -v"
echo "pytest tests/test_funds_api.py::TestFundsAPIHandler::test_handler_routes_get_funds_list -v" 