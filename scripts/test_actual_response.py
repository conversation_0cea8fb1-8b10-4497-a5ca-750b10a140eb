"""
Test what deepseek actually returns and how to handle it.
"""

import os
import sys
import json
import base64
import requests

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), "src"))

from functions.api.pdf_fund_extractor import PDFFundExtractor

# Initialize extractor
extractor = PDFFundExtractor()

# Read PDF
pdf_path = "/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"
pdf_base64 = extractor.read_pdf_as_base64(pdf_path)

print(f"📄 PDF loaded: {len(pdf_base64)} base64 chars")

# Use a much simpler prompt
simple_prompt = """Look at this document and extract basic fund information. Return a JSON object with these fields:
- fund_id: "test-001"
- name: The fund name if you can see it, otherwise "Unknown Fund"
- fund_type: "equity"
- status: "active"
- currency: "USD"

Return ONLY the JSON object."""

# Call the API directly
headers = {
    "Authorization": f"Bearer {extractor.api_key}",
    "Content-Type": "application/json",
}

content = [
    {"type": "text", "text": simple_prompt},
    {"type": "image_url", "image_url": {"url": f"data:application/pdf;base64,{pdf_base64}"}}
]

payload = {
    "model": "deepseek/deepseek-r1-0528:free",
    "messages": [{"role": "user", "content": content}],
    "max_tokens": 1000,
    "temperature": 0.1
}

print("\n📡 Sending request...")
response = requests.post(
    "https://openrouter.ai/api/v1/chat/completions",
    headers=headers,
    json=payload,
    timeout=60
)

print(f"Status: {response.status_code}")

if response.status_code == 200:
    data = response.json()
    
    if "choices" in data and data["choices"]:
        content = data["choices"][0]["message"]["content"]
        print(f"\n✅ Response content: {content}")
        
        # Try to parse as JSON
        try:
            fund_data = json.loads(content)
            print(f"\n✅ Parsed JSON successfully:")
            print(json.dumps(fund_data, indent=2))
        except json.JSONDecodeError as e:
            print(f"\n❌ Failed to parse JSON: {e}")
            print(f"Raw content: {content}")
    else:
        print(f"\n⚠️  No choices in response")
        print(json.dumps(data, indent=2))
else:
    print(f"\n❌ HTTP Error: {response.status_code}")
    print(response.text[:500])