"""
Test Claude models with PDF content.
"""

import os
import requests
import json
import base64

api_key = os.getenv("OPENROUTER_API_KEY")
if not api_key:
    print("❌ OPENROUTER_API_KEY environment variable not set")
    exit(1)

# Read small PDF sample
pdf_path = "/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"
with open(pdf_path, "rb") as f:
    pdf_sample = f.read(50000)  # 50KB sample
    pdf_base64 = base64.b64encode(pdf_sample).decode("utf-8")

print(f"📄 Testing with PDF sample: {len(pdf_sample)} bytes\n")

# Models to test
models = [
    "anthropic/claude-3.5-haiku",  # Cheapest Claude
    "anthropic/claude-3-haiku",    # Older but might work
    "openai/gpt-4o-mini",          # For comparison
]

url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json",
}

for model in models:
    print(f"🧪 Testing {model}...")
    
    # Try sending PDF as image_url (how OpenAI expects multimodal content)
    payload = {
        "model": model,
        "messages": [{
            "role": "user",
            "content": [
                {"type": "text", "text": "What type of document is this? Reply in one sentence."},
                {"type": "image_url", "image_url": {"url": f"data:application/pdf;base64,{pdf_base64}"}}
            ]
        }],
        "max_tokens": 100,
        "temperature": 0.1
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            if "choices" in data and data["choices"]:
                content = data["choices"][0]["message"]["content"]
                print(f"  ✅ Success: {content}")
            else:
                print(f"  ⚠️  Empty response")
        else:
            if response.headers.get('content-type', '').startswith('application/json'):
                error_data = response.json()
                error_msg = error_data.get("error", {}).get("message", "Unknown error")
            else:
                error_msg = response.text[:100]
            print(f"  ❌ Error ({response.status_code}): {error_msg}")
            
    except Exception as e:
        print(f"  ❌ Exception: {str(e)}")
    
    print()

# Try a working model with just text extraction
print("\n📝 Testing with a model that works (for reference)...")

simple_prompt = """Based on a fund prospectus document, extract the following information and return as JSON:
- fund_name
- fund_type (equity/bond/mixed/etc)
- currency

Example fund name: "Ariake Capital Fund"

Return only valid JSON."""

payload = {
    "model": "openai/gpt-4o-mini",
    "messages": [{"role": "user", "content": simple_prompt}],
    "max_tokens": 200,
    "temperature": 0.1
}

response = requests.post(url, headers=headers, json=payload, timeout=10)
if response.status_code == 200:
    data = response.json()
    if "choices" in data:
        print(f"✅ Text-only extraction works: {data['choices'][0]['message']['content']}")