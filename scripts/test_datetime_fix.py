#!/usr/bin/env python3
"""
Simple test to verify the datetime conversion fix.
"""

import os
import sys
from datetime import datetime, timezone
from decimal import Decimal

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), "src"))

from shared.models.fund import Fund, PerformanceMetrics, Holdings


def test_datetime_conversion():
    """Test the datetime conversion with sample data."""
    print("🧪 Testing datetime conversion fix...")
    
    # Sample data similar to what the AI returns
    sample_data = {
        'fund_id': 'test-fund',
        'name': 'Test Fund',
        'fund_type': 'equity',
        'nav': Decimal('125.75'),
        'currency': 'USD',
        'total_assets': Decimal('2500000000.0'),
        'inception_date': '2010-05-15',  # This is the problematic field
        'fund_manager': '<PERSON>',
        'management_company': 'Alpha Investments',
        'expense_ratio': Decimal('0.75'),
        'minimum_investment': Decimal('1000.0'),
        'description': 'Test fund description',
        'risk_level': 'moderate',
    }
    
    print(f"Original inception_date: {sample_data['inception_date']} (type: {type(sample_data['inception_date'])})")
    
    # Convert the date string to timezone-aware datetime
    if isinstance(sample_data['inception_date'], str):
        date_str = sample_data['inception_date']
        if "T" in date_str:
            # Full datetime string
            sample_data['inception_date'] = datetime.fromisoformat(
                date_str.replace("Z", "+00:00")
            )
        else:
            # Simple date string (YYYY-MM-DD)
            parsed_date = datetime.fromisoformat(date_str)
            sample_data['inception_date'] = parsed_date.replace(tzinfo=timezone.utc)
    
    print(f"Converted inception_date: {sample_data['inception_date']} (type: {type(sample_data['inception_date'])})")
    print(f"Timezone info: {sample_data['inception_date'].tzinfo}")
    
    # Try to create the Fund model
    try:
        fund = Fund(**sample_data)
        print("✅ Successfully created Fund model!")
        print(f"Fund name: {fund.name}")
        print(f"Inception date: {fund.inception_date}")
        return True
    except Exception as e:
        print(f"❌ Error creating Fund model: {e}")
        return False


def test_performance_metrics_conversion():
    """Test performance metrics conversion."""
    print("\n🧪 Testing performance metrics conversion...")
    
    # Sample performance data as flat fields (like AI returns)
    perf_data = {
        'ytd_return': 8.5,
        'one_month_return': 1.2,
        'three_month_return': 3.4,
        'volatility': 15.0,
        'sharpe_ratio': 0.85,
        'max_drawdown': -20.5,
        'alpha': 1.2,
        'beta': 0.95,
    }
    
    # Convert to Decimal
    converted_perf = {}
    for field, value in perf_data.items():
        if value is not None:
            converted_perf[field] = Decimal(str(value))
    
    try:
        perf_metrics = PerformanceMetrics(**converted_perf)
        print("✅ Successfully created PerformanceMetrics model!")
        print(f"YTD Return: {perf_metrics.ytd_return}")
        print(f"Sharpe Ratio: {perf_metrics.sharpe_ratio}")
        return True
    except Exception as e:
        print(f"❌ Error creating PerformanceMetrics model: {e}")
        return False


def test_holdings_conversion():
    """Test holdings conversion."""
    print("\n🧪 Testing holdings conversion...")
    
    # Sample holdings data as lists (like AI returns)
    holdings_data = {
        'top_holdings': [
            {'name': 'Apple Inc.', 'percentage': 5.0},
            {'name': 'Microsoft Corp.', 'percentage': 4.5}
        ],
        'sector_allocation': [
            {'sector': 'Technology', 'percentage': 25.0},
            {'sector': 'Financials', 'percentage': 15.0}
        ],
        'geographic_allocation': [
            {'region': 'North America', 'percentage': 60.0},
            {'region': 'Europe', 'percentage': 25.0}
        ]
    }
    
    # Convert allocation lists to dictionaries with Decimal values
    processed_holdings = {}
    processed_holdings['top_holdings'] = holdings_data['top_holdings']
    
    for alloc_field in ["sector_allocation", "geographic_allocation"]:
        if holdings_data.get(alloc_field):
            alloc_data = holdings_data[alloc_field]
            if isinstance(alloc_data, list):
                processed_holdings[alloc_field] = {
                    item.get("sector", item.get("region", "Unknown")): Decimal(str(item["percentage"]))
                    for item in alloc_data
                }
    
    try:
        holdings = Holdings(**processed_holdings)
        print("✅ Successfully created Holdings model!")
        print(f"Top holdings: {len(holdings.top_holdings) if holdings.top_holdings else 0} items")
        print(f"Sector allocation: {holdings.sector_allocation}")
        return True
    except Exception as e:
        print(f"❌ Error creating Holdings model: {e}")
        return False


def main():
    """Run all tests."""
    print("Testing Fund Model Data Conversion")
    print("=" * 40)
    
    success_count = 0
    total_tests = 3
    
    if test_datetime_conversion():
        success_count += 1
    
    if test_performance_metrics_conversion():
        success_count += 1
    
    if test_holdings_conversion():
        success_count += 1
    
    print(f"\n📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 All tests passed! The datetime fix should work.")
        return 0
    else:
        print("❌ Some tests failed. Check the error messages above.")
        return 1


if __name__ == "__main__":
    exit(main())
