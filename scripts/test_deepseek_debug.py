"""
Debug deepseek responses to understand what's happening.
"""

import os
import requests
import json
import base64

api_key = os.getenv("OPENROUTER_API_KEY")
if not api_key:
    print("❌ OPENROUTER_API_KEY environment variable not set")
    exit(1)

# Read PDF
pdf_path = "/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"
with open(pdf_path, "rb") as f:
    # Test with different sizes
    pdf_full = f.read()
    
print(f"📄 Full PDF size: {len(pdf_full)} bytes")

url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json",
}

# Try with full PDF but don't raise on error response
pdf_base64 = base64.b64encode(pdf_full).decode("utf-8")
print(f"📄 Base64 size: {len(pdf_base64)} chars")

# Simple prompt
prompt = "Extract fund name from this PDF and return as JSON with field 'fund_name'. Return only JSON."

content = [
    {"type": "text", "text": prompt},
    {"type": "image_url", "image_url": {"url": f"data:application/pdf;base64,{pdf_base64}"}}
]

payload = {
    "model": "deepseek/deepseek-r1-0528:free",
    "messages": [{"role": "user", "content": content}],
    "max_tokens": 1000,
    "temperature": 0.1
}

print("\n🧪 Sending request to deepseek...")
response = requests.post(url, headers=headers, json=payload, timeout=180)

print(f"\nStatus Code: {response.status_code}")
print(f"Response Headers: {dict(response.headers)}")

try:
    data = response.json()
    print(f"\nResponse JSON keys: {list(data.keys())}")
    
    # Print the full response for debugging
    print("\nFull Response:")
    print(json.dumps(data, indent=2))
    
    # Check different possible response formats
    if "choices" in data and data["choices"]:
        print("\n✅ Found choices!")
        for i, choice in enumerate(data["choices"]):
            print(f"\nChoice {i}:")
            print(f"  Message: {choice.get('message', {})}")
            if 'message' in choice and 'content' in choice['message']:
                content = choice['message']['content']
                print(f"  Content: {content}")
    
    if "error" in data:
        print(f"\n⚠️  Error in response: {data['error']}")
        
except Exception as e:
    print(f"\n❌ Failed to parse response: {e}")
    print(f"Raw response: {response.text[:1000]}")