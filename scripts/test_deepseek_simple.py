"""
Test deepseek model with a simple request to verify it's working.
"""

import os
import requests
import json

api_key = os.getenv("OPENROUTER_API_KEY")
if not api_key:
    print("❌ OPENROUTER_API_KEY environment variable not set")
    exit(1)

url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json",
}

# Test 1: Simple text request to verify model works
print("🧪 Test 1: Simple text with deepseek")
payload = {
    "model": "deepseek/deepseek-r1-0528:free",
    "messages": [{"role": "user", "content": "Extract fund information from this text: 'Ariake Capital Fund is an equity fund with USD currency.' Return only JSON with fields: fund_name, fund_type, currency"}],
    "max_tokens": 500,
    "temperature": 0.1
}

response = requests.post(url, headers=headers, json=payload, timeout=30)
print(f"Status: {response.status_code}")

if response.status_code == 200:
    data = response.json()
    print(f"Response keys: {list(data.keys())}")
    
    if "choices" in data and data["choices"]:
        content = data["choices"][0]["message"]["content"]
        print(f"✅ Model response: {content}")
    elif "error" in data:
        print(f"❌ API Error: {data['error']}")
    else:
        print(f"⚠️  Unexpected response: {json.dumps(data, indent=2)}")
else:
    print(f"❌ HTTP Error: {response.text[:500]}")

# Test 2: Try with a smaller file attachment
print("\n\n🧪 Test 2: Testing with tiny PDF (100 bytes)")

# Create a minimal PDF header (not a valid PDF, but tests the API)
mini_pdf = b"%PDF-1.4\n1 0 obj\n<< /Type /Catalog >>\nendobj\nxref\n0 0\ntrailer\n<< /Size 0 >>\nstartxref\n0\n%%EOF"
import base64
mini_pdf_base64 = base64.b64encode(mini_pdf).decode("utf-8")

payload = {
    "model": "deepseek/deepseek-r1-0528:free",
    "messages": [{
        "role": "user",
        "content": [
            {"type": "text", "text": "What type of file is this?"},
            {"type": "image_url", "image_url": {"url": f"data:application/pdf;base64,{mini_pdf_base64}"}}
        ]
    }],
    "max_tokens": 100,
    "temperature": 0.1
}

response = requests.post(url, headers=headers, json=payload, timeout=30)
print(f"Status: {response.status_code}")

if response.status_code == 200:
    data = response.json()
    print(f"Response keys: {list(data.keys())}")
    
    if "choices" in data and data["choices"]:
        content = data["choices"][0]["message"]["content"]
        print(f"✅ Model response: {content}")
    elif "error" in data:
        print(f"❌ API Error: {data['error']}")
    else:
        print(f"⚠️  Unexpected response: {json.dumps(data, indent=2)}")
else:
    try:
        error_data = response.json()
        print(f"❌ Error response: {json.dumps(error_data, indent=2)}")
    except:
        print(f"❌ HTTP Error: {response.text[:500]}")