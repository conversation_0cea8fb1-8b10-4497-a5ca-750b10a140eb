"""
Client script to test the FastAPI fund extractor server.
Sends a PDF file to the server and displays the extracted fund information.
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime
import requests
from typing import Optional, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class FundExtractorClient:
    """Client for testing the fund extractor API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """Initialize the client with API base URL."""
        self.base_url = base_url.rstrip("/")
        self.session = requests.Session()
        # Add headers to mimic browser request
        self.session.headers.update({
            "User-Agent": "FundExtractor-TestClient/1.0",
        })
    
    def health_check(self) -> Dict[str, Any]:
        """Check if the API server is healthy."""
        try:
            response = self.session.get(f"{self.base_url}/health")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Health check failed: {e}")
            raise
    
    def extract_fund_from_pdf(
        self, 
        pdf_path: str, 
        save_to_db: bool = False,
        auth_token: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Extract fund information from a PDF file.
        
        Args:
            pdf_path: Path to the PDF file
            save_to_db: Whether to save to database (simulated)
            auth_token: Optional authorization token
            
        Returns:
            Extracted fund data
        """
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        # Prepare the file for upload
        with open(pdf_path, "rb") as f:
            files = {"file": (pdf_path.name, f, "application/pdf")}
            
            # Prepare query parameters
            params = {"save": str(save_to_db).lower()}
            
            # Add authorization header if provided
            headers = {}
            if auth_token:
                headers["Authorization"] = f"Bearer {auth_token}"
            
            # Make the request
            logger.info(f"Uploading PDF file: {pdf_path.name}")
            logger.info(f"File size: {pdf_path.stat().st_size:,} bytes")
            
            try:
                response = self.session.post(
                    f"{self.base_url}/api/funds/extract",
                    files=files,
                    params=params,
                    headers=headers,
                    timeout=60  # 60 second timeout for PDF processing
                )
                response.raise_for_status()
                return response.json()
                
            except requests.exceptions.Timeout:
                logger.error("Request timed out - PDF processing may take longer")
                raise
            except requests.exceptions.HTTPError as e:
                # Try to parse error response
                try:
                    error_data = e.response.json()
                    logger.error(f"API Error: {error_data}")
                except:
                    logger.error(f"HTTP Error: {e.response.status_code} - {e.response.text}")
                raise
            except requests.RequestException as e:
                logger.error(f"Request failed: {e}")
                raise


def format_fund_summary(fund_data: Dict[str, Any]) -> str:
    """Format fund data into a readable summary."""
    fund = fund_data.get("data", {})
    
    summary = []
    summary.append("\n" + "="*60)
    summary.append("📊 EXTRACTED FUND INFORMATION")
    summary.append("="*60)
    
    # Basic Information
    summary.append("\n📋 Basic Information:")
    summary.append(f"   Fund ID: {fund.get('fund_id', 'N/A')}")
    summary.append(f"   Name: {fund.get('name', 'N/A')}")
    summary.append(f"   Type: {fund.get('fund_type', 'N/A')}")
    summary.append(f"   Status: {fund.get('status', 'N/A')}")
    summary.append(f"   Currency: {fund.get('currency', 'N/A')}")
    
    # Financial Information
    if fund.get('nav') or fund.get('total_assets'):
        summary.append("\n💰 Financial Information:")
        if fund.get('nav'):
            summary.append(f"   NAV: {fund['nav']} {fund.get('currency', '')}")
        if fund.get('total_assets'):
            total_assets = float(fund['total_assets'])
            summary.append(f"   Total Assets: {total_assets:,.2f} {fund.get('currency', '')}")
    
    # Management Information
    if fund.get('fund_manager') or fund.get('management_company'):
        summary.append("\n👔 Management:")
        if fund.get('fund_manager'):
            summary.append(f"   Fund Manager: {fund['fund_manager']}")
        if fund.get('management_company'):
            summary.append(f"   Management Company: {fund['management_company']}")
    
    # Performance Metrics
    perf = fund.get('performance_metrics', {})
    if perf:
        summary.append("\n📈 Performance Metrics:")
        metrics = [
            ('YTD Return', 'ytd_return'),
            ('1 Year Return', 'one_year_return'),
            ('3 Year Return', 'three_year_return'),
            ('5 Year Return', 'five_year_return'),
            ('Volatility', 'volatility'),
            ('Sharpe Ratio', 'sharpe_ratio'),
        ]
        for label, key in metrics:
            if perf.get(key) is not None:
                value = float(perf[key])
                if 'return' in key:
                    summary.append(f"   {label}: {value:.2f}%")
                else:
                    summary.append(f"   {label}: {value:.2f}")
    
    # Holdings Information
    holdings = fund.get('holdings', {})
    if holdings:
        # Top Holdings
        top_holdings = holdings.get('top_holdings', [])
        if top_holdings:
            summary.append("\n🏦 Top Holdings:")
            for i, holding in enumerate(top_holdings[:5], 1):
                name = holding.get('name', 'Unknown')
                percentage = float(holding.get('percentage', 0))
                summary.append(f"   {i}. {name}: {percentage:.2f}%")
        
        # Sector Allocation
        sectors = holdings.get('sector_allocation', {})
        if sectors:
            summary.append("\n📊 Sector Allocation:")
            sorted_sectors = sorted(sectors.items(), key=lambda x: float(x[1]), reverse=True)
            for sector, percentage in sorted_sectors[:5]:
                summary.append(f"   {sector}: {float(percentage):.2f}%")
    
    # Additional Information
    if fund.get('description'):
        summary.append("\n📝 Description:")
        desc = fund['description']
        # Wrap long descriptions
        if len(desc) > 100:
            desc = desc[:100] + "..."
        summary.append(f"   {desc}")
    
    summary.append("\n" + "="*60)
    
    return "\n".join(summary)


def main():
    """Main function to test the fund extractor API."""
    # Configuration
    pdf_path = "/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"
    api_url = "http://localhost:8000"
    save_to_db = False
    
    # Create client
    client = FundExtractorClient(api_url)
    
    try:
        # Check server health
        logger.info("Checking server health...")
        health = client.health_check()
        logger.info(f"Server status: {health['status']}")
        
        # Extract fund from PDF
        logger.info(f"\nExtracting fund information from PDF...")
        logger.info(f"PDF file: {pdf_path}")
        
        start_time = datetime.now()
        result = client.extract_fund_from_pdf(pdf_path, save_to_db)
        end_time = datetime.now()
        
        # Calculate processing time
        processing_time = (end_time - start_time).total_seconds()
        logger.info(f"\n✅ Extraction completed in {processing_time:.2f} seconds")
        
        # Display results
        if result.get("data"):
            print(format_fund_summary(result))
            
            # Save full response to file
            output_file = "extracted_fund_response.json"
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            logger.info(f"\n💾 Full response saved to: {output_file}")
            
            # Show database save status
            if result.get("saved_to_database"):
                logger.info(f"\n✅ Fund saved to database with ID: {result.get('database_fund_id')}")
            elif save_to_db:
                logger.warning(f"\n⚠️  Database save was requested but failed: {result.get('database_error', 'Unknown error')}")
        else:
            logger.error("\n❌ No fund data extracted")
            print(json.dumps(result, indent=2))
            
    except FileNotFoundError as e:
        logger.error(f"\n❌ File not found: {e}")
        sys.exit(1)
    except requests.exceptions.ConnectionError:
        logger.error(f"\n❌ Could not connect to server at {api_url}")
        logger.error("Make sure the FastAPI server is running: python scripts/fastapi_fund_extractor.py")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()