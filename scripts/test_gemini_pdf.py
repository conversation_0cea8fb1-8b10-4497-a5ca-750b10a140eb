"""
Test Gemini model with different content types.
"""

import os
import requests
import json
import base64

api_key = os.getenv("OPENROUTER_API_KEY")
if not api_key:
    print("❌ OPENROUTER_API_KEY environment variable not set")
    exit(1)

url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json",
}

# Test 1: Simple text
print("🧪 Test 1: Simple text with Gemini")
payload = {
    "model": "google/gemini-2.5-flash",
    "messages": [{"role": "user", "content": "Say hello"}],
    "max_tokens": 100
}

response = requests.post(url, headers=headers, json=payload, timeout=10)
print(f"Status: {response.status_code}")
if response.status_code == 200:
    print("✅ Text works!")
else:
    print(f"❌ Error: {response.text[:200]}")

# Test 2: Image as base64
print("\n🧪 Test 2: Image with Gemini")
# Create a simple 1x1 red pixel PNG
red_pixel = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg=="

payload = {
    "model": "google/gemini-2.5-flash",
    "messages": [{
        "role": "user",
        "content": [
            {"type": "text", "text": "What color is this image?"},
            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{red_pixel}"}}
        ]
    }],
    "max_tokens": 100
}

response = requests.post(url, headers=headers, json=payload, timeout=10)
print(f"Status: {response.status_code}")
if response.status_code == 200:
    data = response.json()
    if "choices" in data:
        print(f"✅ Image works! Response: {data['choices'][0]['message']['content']}")
else:
    print(f"❌ Error: {response.text[:200]}")

# Test 3: Small PDF sample
print("\n🧪 Test 3: PDF with different MIME types")

# Read first 10KB of PDF
pdf_path = "/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"
with open(pdf_path, "rb") as f:
    pdf_sample = f.read(10000)
    pdf_base64 = base64.b64encode(pdf_sample).decode("utf-8")

# Try different MIME types
mime_types = [
    "application/pdf",
    "image/pdf",  # Some APIs treat PDFs as images
    "application/octet-stream"
]

for mime_type in mime_types:
    print(f"\n  Testing MIME type: {mime_type}")
    
    payload = {
        "model": "google/gemini-2.5-flash",
        "messages": [{
            "role": "user",
            "content": [
                {"type": "text", "text": "What type of document is this?"},
                {"type": "image_url", "image_url": {"url": f"data:{mime_type};base64,{pdf_base64}"}}
            ]
        }],
        "max_tokens": 100
    }
    
    response = requests.post(url, headers=headers, json=payload, timeout=10)
    print(f"  Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if "choices" in data and data["choices"]:
            print(f"  ✅ Works! Response: {data['choices'][0]['message']['content'][:100]}")
    else:
        error_json = response.json() if response.headers.get('content-type', '').startswith('application/json') else None
        if error_json and "error" in error_json:
            print(f"  ❌ Error: {error_json['error'].get('message', 'Unknown error')[:100]}")
        else:
            print(f"  ❌ Error: {response.text[:100]}")

# Test 4: Check if we need to use a different format
print("\n\n🧪 Test 4: Alternative content format")

# Try sending PDF as file content directly (some APIs support this)
payload = {
    "model": "google/gemini-2.5-flash",
    "messages": [{
        "role": "user",
        "content": f"This is a PDF file encoded in base64: {pdf_base64[:100]}... What type of document is this?"
    }],
    "max_tokens": 100
}

response = requests.post(url, headers=headers, json=payload, timeout=10)
if response.status_code == 200:
    print("✅ Text with base64 works, but won't analyze PDF content properly")