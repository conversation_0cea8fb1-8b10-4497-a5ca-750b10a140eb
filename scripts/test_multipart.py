#!/usr/bin/env python3
"""
Test script to verify multipart parsing logic
"""

import base64
from typing import Dict, Any, Optional

def parse_multipart_data(event: Dict[str, Any]) -> Optional[str]:
    """
    Parse multipart form data from API Gateway event to extract PDF file.
    """
    try:
        # Check if body is base64 encoded
        is_base64_encoded = event.get("isBase64Encoded", False)
        body = event.get("body", "")

        if not body:
            print("No body found in request")
            return None

        # Get content type from headers
        headers = event.get("headers", {})
        content_type = headers.get("content-type") or headers.get("Content-Type", "")

        if not content_type.startswith("multipart/form-data"):
            print(f"Invalid content type: {content_type}")
            return None

        # Extract boundary from content type
        boundary = None
        for part in content_type.split(";"):
            part = part.strip()
            if part.startswith("boundary="):
                boundary = part[9:]  # Remove 'boundary='
                # Remove quotes if present
                if boundary.startswith('"') and boundary.endswith('"'):
                    boundary = boundary[1:-1]
                break

        if not boundary:
            print("No boundary found in multipart data")
            return None

        # Decode body if base64 encoded
        if is_base64_encoded:
            try:
                body_bytes = base64.b64decode(body)
            except Exception as e:
                print(f"Failed to decode base64 body: {e}")
                return None
        else:
            # Convert string body to bytes
            body_bytes = body.encode('latin-1')  # Use latin-1 to preserve binary data

        # Convert back to string for parsing
        body_str = body_bytes.decode('latin-1')
        
        # Split by boundary
        parts = body_str.split(f"--{boundary}")
        
        for part in parts:
            if not part.strip():
                continue
                
            # Look for Content-Disposition header with filename
            if "Content-Disposition:" in part and "filename=" in part:
                lines = part.split('\r\n')
                
                # Find the empty line that separates headers from content
                content_start_idx = -1
                for i, line in enumerate(lines):
                    if line.strip() == "":
                        content_start_idx = i + 1
                        break
                
                if content_start_idx > 0 and content_start_idx < len(lines):
                    # Extract file content (everything after the empty line)
                    content_lines = lines[content_start_idx:]
                    
                    # Remove the final boundary marker if present
                    if content_lines and content_lines[-1].startswith('--'):
                        content_lines = content_lines[:-1]
                    
                    # Join content and convert to bytes
                    file_content = '\r\n'.join(content_lines)
                    file_bytes = file_content.encode('latin-1')
                    
                    # Return as base64
                    return base64.b64encode(file_bytes).decode('utf-8')

        print("No file found in multipart data")
        return None

    except Exception as e:
        print(f"Error parsing multipart data: {e}")
        return None

# Test with a simple multipart example
if __name__ == "__main__":
    # Create a simple test event
    test_event = {
        "headers": {
            "content-type": "multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW"
        },
        "body": """------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name="file"; filename="test.pdf"\r\nContent-Type: application/pdf\r\n\r\nPDF content here\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW--\r\n""",
        "isBase64Encoded": False
    }
    
    result = parse_multipart_data(test_event)
    if result:
        print("Success! Extracted file content as base64:")
        print(result[:50] + "..." if len(result) > 50 else result)
        # Decode to verify
        decoded = base64.b64decode(result).decode('latin-1')
        print("Decoded content:", decoded)
    else:
        print("Failed to extract file")
