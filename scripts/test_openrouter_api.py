"""
Test OpenRouter API with a simple text request to verify API key and connectivity.
"""

import os
import requests
import json

# Get API key
api_key = os.getenv("OPENROUTER_API_KEY")
if not api_key:
    print("❌ OPENROUTER_API_KEY environment variable not set")
    exit(1)

print(f"✅ API Key found: {api_key[:10]}...")

# Test with a simple text request
url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json",
}

# Simple text request
payload = {
    "model": "deepseek/deepseek-r1-0528:free",
    "messages": [
        {
            "role": "user",
            "content": "What is 2+2? Reply with just the number."
        }
    ],
    "max_tokens": 100,
    "temperature": 0.1
}

print(f"\n📡 Testing OpenRouter API with model: {payload['model']}")
print("Request: What is 2+2?")

try:
    response = requests.post(url, headers=headers, json=payload, timeout=10)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if "choices" in data and data["choices"]:
            content = data["choices"][0]["message"]["content"]
            print(f"✅ API Response: {content}")
        else:
            print(f"⚠️  Unexpected response format: {json.dumps(data, indent=2)}")
    else:
        print(f"❌ Error response: {response.text}")
        
except Exception as e:
    print(f"❌ Request failed: {e}")

# Now test if the model supports multimodal content
print("\n\n📸 Testing if model supports images/PDFs...")

payload_multimodal = {
    "model": "deepseek/deepseek-r1-0528:free",
    "messages": [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": "Can you process images or PDFs?"},
                {"type": "image_url", "image_url": {"url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="}}
            ]
        }
    ],
    "max_tokens": 100
}

try:
    response = requests.post(url, headers=headers, json=payload_multimodal, timeout=10)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Model supports multimodal content")
    else:
        error_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else {"error": response.text}
        print(f"❌ Model does not support multimodal content: {json.dumps(error_data, indent=2)}")
        
except Exception as e:
    print(f"❌ Multimodal test failed: {e}")

# List available models
print("\n\n📋 Checking available models...")
models_url = "https://openrouter.ai/api/v1/models"

try:
    response = requests.get(models_url, headers=headers, timeout=10)
    if response.status_code == 200:
        models = response.json()
        print(f"Found {len(models.get('data', []))} models")
        
        # Look for models that support PDFs or images
        vision_models = []
        for model in models.get('data', [])[:50]:  # Check first 50 models
            model_id = model.get('id', '')
            if any(keyword in model_id.lower() for keyword in ['vision', 'gpt-4', 'claude', 'gemini']):
                vision_models.append(model_id)
        
        if vision_models:
            print(f"\n🔍 Potential vision/multimodal models:")
            for model in vision_models[:10]:
                print(f"   - {model}")
    else:
        print(f"❌ Failed to fetch models: {response.status_code}")
        
except Exception as e:
    print(f"❌ Models list request failed: {e}")