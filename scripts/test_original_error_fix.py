#!/usr/bin/env python3
"""
Test the fix for the original datetime error with the exact data that was failing.
"""

import os
import sys
from datetime import datetime, timezone
from decimal import Decimal

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), "src"))
sys.path.append(os.path.dirname(__file__))

from pdf_fund_extractor import PDFFundExtractor


def test_original_error_data():
    """Test with the exact data that was causing the original error."""
    print("🧪 Testing Original Error Fix")
    print("=" * 40)
    
    # This is the exact data that was extracted and causing the error
    extracted_data = {
        'fund_id': 'global-equity-fund', 
        'name': 'Global Equity Fund', 
        'fund_type': 'equity', 
        'nav': 125.75, 
        'currency': 'USD', 
        'total_assets': 2500000000.0, 
        'inception_date': '2010-05-15',  # This was the problematic field
        'fund_manager': '<PERSON>', 
        'management_company': 'Alpha Investments', 
        'expense_ratio': 0.75, 
        'minimum_investment': 1000.0, 
        'isin': 'US*********0', 
        'cusip': '*********', 
        'bloomberg_ticker': 'GLEQ:US', 
        'description': 'The fund seeks long-term capital appreciation by investing in a diversified portfolio of global equities.', 
        'investment_objective': 'To achieve capital growth through a globally diversified equity portfolio.', 
        'benchmark': 'MSCI World Index', 
        'risk_level': 'moderate', 
        'ytd_return': 8.5, 
        'one_month_return': 1.2, 
        'three_month_return': 3.4, 
        'six_month_return': 6.7, 
        'one_year_return': 12.3, 
        'three_year_return': 9.8, 
        'five_year_return': 11.2, 
        'volatility': 15.0, 
        'sharpe_ratio': 0.85, 
        'max_drawdown': -20.5, 
        'alpha': 1.2, 
        'beta': 0.95, 
        'top_holdings': [
            {'name': 'Apple Inc.', 'percentage': 5.0}, 
            {'name': 'Microsoft Corp.', 'percentage': 4.5}, 
            {'name': 'Amazon.com Inc.', 'percentage': 4.0}
        ], 
        'sector_allocation': [
            {'sector': 'Technology', 'percentage': 25.0}, 
            {'sector': 'Financials', 'percentage': 15.0}, 
            {'sector': 'Healthcare', 'percentage': 12.0}
        ], 
        'geographic_allocation': [
            {'region': 'North America', 'percentage': 60.0}, 
            {'region': 'Europe', 'percentage': 25.0}, 
            {'region': 'Asia', 'percentage': 15.0}
        ]
    }
    
    print(f"📊 Testing with extracted data:")
    print(f"   Fund: {extracted_data['name']}")
    print(f"   Inception Date: {extracted_data['inception_date']} (type: {type(extracted_data['inception_date'])})")
    print(f"   Performance fields: {len([k for k in extracted_data.keys() if 'return' in k or k in ['volatility', 'sharpe_ratio', 'alpha', 'beta']])} items")
    print(f"   Holdings fields: {len([k for k in extracted_data.keys() if 'allocation' in k or 'holdings' in k])} items")
    
    try:
        # Initialize extractor and test the conversion
        extractor = PDFFundExtractor()
        
        print("\n🔄 Converting extracted data to Fund model...")
        fund = extractor.convert_to_fund_model(extracted_data)
        
        print("✅ SUCCESS! Fund model created successfully!")
        print(f"   Fund ID: {fund.fund_id}")
        print(f"   Name: {fund.name}")
        print(f"   Type: {fund.fund_type}")
        print(f"   Currency: {fund.currency}")
        print(f"   Inception Date: {fund.inception_date}")
        print(f"   NAV: {fund.nav}")
        print(f"   Total Assets: {fund.total_assets}")
        
        if fund.performance_metrics:
            print(f"   Performance Metrics: ✅ Created")
            print(f"     YTD Return: {fund.performance_metrics.ytd_return}")
            print(f"     Sharpe Ratio: {fund.performance_metrics.sharpe_ratio}")
        
        if fund.holdings:
            print(f"   Holdings: ✅ Created")
            print(f"     Top Holdings: {len(fund.holdings.top_holdings) if fund.holdings.top_holdings else 0} items")
            print(f"     Sector Allocation: {len(fund.holdings.sector_allocation) if fund.holdings.sector_allocation else 0} sectors")
            print(f"     Geographic Allocation: {len(fund.holdings.geographic_allocation) if fund.holdings.geographic_allocation else 0} regions")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        print(f"   Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run the test."""
    print("Original Error Fix Verification")
    print("Testing the exact data that was causing:")
    print("'can't compare offset-naive and offset-aware datetimes'\n")
    
    success = test_original_error_data()
    
    if success:
        print("\n🎉 FIXED! The original error has been resolved.")
        print("The datetime conversion and data structure handling now works correctly.")
    else:
        print("\n❌ The error still exists. Check the traceback above.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
