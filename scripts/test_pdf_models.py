"""
Test different models with PDF content to find which ones work.
"""

import os
import requests
import json
import base64

# Get API key
api_key = os.getenv("OPENROUTER_API_KEY")
if not api_key:
    print("❌ OPENROUTER_API_KEY environment variable not set")
    exit(1)

# Read a small portion of the PDF for testing
pdf_path = "/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"
with open(pdf_path, "rb") as f:
    pdf_bytes = f.read()[:50000]  # First 50KB only for testing
    pdf_base64 = base64.b64encode(pdf_bytes).decode("utf-8")

print(f"📄 PDF sample size: {len(pdf_bytes)} bytes ({len(pdf_base64)} base64 chars)")

# Models to test
models_to_test = [
    "deepseek/deepseek-r1-0528:free",
    "google/gemini-2.5-flash",
    "anthropic/claude-3-haiku",
    "openai/gpt-4o-mini",
    "meta-llama/llama-3.2-11b-vision-instruct:free"
]

url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json",
}

for model in models_to_test:
    print(f"\n🧪 Testing model: {model}")
    
    # Try PDF as image_url
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "What type of document is this? Reply in 10 words or less."},
                    {"type": "image_url", "image_url": {"url": f"data:application/pdf;base64,{pdf_base64}"}}
                ]
            }
        ],
        "max_tokens": 100,
        "temperature": 0.1
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            if "choices" in data and data["choices"]:
                content = data["choices"][0]["message"]["content"]
                print(f"   ✅ Success: {content[:100]}")
            else:
                print(f"   ⚠️  Empty response")
        else:
            error_msg = response.json().get("error", {}).get("message", response.text) if response.headers.get('content-type', '').startswith('application/json') else response.text
            print(f"   ❌ Error ({response.status_code}): {error_msg[:100]}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)[:100]}")

# Try extracting text from PDF using a different approach
print("\n\n📝 Testing text extraction approach...")

# Try with just text prompt (no PDF) to see if models work at all
simple_prompt = """Extract fund information from this text and return as JSON:
Fund Name: Ariake Capital Fund
Fund Type: Equity
Currency: USD

Return only JSON with these fields: fund_name, fund_type, currency"""

for model in ["deepseek/deepseek-r1-0528:free", "openai/gpt-4o-mini"]:
    print(f"\n🧪 Testing {model} with text-only prompt...")
    
    payload = {
        "model": model,
        "messages": [
            {"role": "user", "content": simple_prompt}
        ],
        "max_tokens": 500,
        "temperature": 0.1
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            if "choices" in data and data["choices"]:
                content = data["choices"][0]["message"]["content"]
                print(f"   ✅ Response: {content[:200]}")
        else:
            print(f"   ❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")