#!/usr/bin/env python3
"""
Test script for the new text-based PDF fund extraction functionality.
"""

import os
import sys
from pathlib import Path

# Add the scripts directory to Python path
sys.path.append(os.path.dirname(__file__))

from pdf_fund_extractor import PDFFundExtractor


def test_direct_pdf_extraction():
    """
    Test the new direct PDF extraction functionality.
    """
    print("🧪 Testing Direct PDF Fund Extraction")
    print("=" * 50)
    
    # Check if API key is available
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("❌ Error: OPENROUTER_API_KEY environment variable not set")
        print("Please set your OpenRouter API key:")
        print("export OPENROUTER_API_KEY='your-api-key-here'")
        return False
    
    try:
        # Initialize extractor
        print("🔧 Initializing PDF Fund Extractor...")
        extractor = PDFFundExtractor(api_key=api_key)
        print(f"✅ Vision model: {extractor.model}")
        print(f"✅ Text model: {extractor.text_model}")
        
        # Test with a sample PDF (you'll need to provide a real PDF path)
        sample_pdf="/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"
        
        if not Path(sample_pdf).exists():
            print(f"⚠️  Sample PDF not found: {sample_pdf}")
            print("Please provide a fund document PDF to test with.")
            print("\nUsage examples:")
            print("1. Vision mode (images): python pdf_fund_extractor.py your_fund.pdf")
            print("2. Direct mode (deepseek): python pdf_fund_extractor.py --text-mode your_fund.pdf")
            return True
        
        print(f"📄 Testing with PDF: {sample_pdf}")
        
        # Test PDF base64 encoding
        print("\n🔍 Testing PDF base64 encoding...")
        pdf_base64 = extractor.read_pdf_as_base64(sample_pdf)
        print(f"✅ Encoded PDF as base64 ({len(pdf_base64)} characters)")
        print(f"📝 Base64 prefix: {pdf_base64[:50]}...")

        # Test full direct PDF extraction
        print("\n🤖 Testing direct PDF fund extraction...")
        fund = extractor.extract_fund_from_pdf_direct(sample_pdf)
        
        print(f"✅ Successfully extracted fund information:")
        print(f"   Fund ID: {fund.fund_id}")
        print(f"   Name: {fund.name}")
        print(f"   Type: {fund.fund_type}")
        print(f"   Currency: {fund.currency}")
        if fund.total_assets:
            print(f"   Total Assets: {fund.total_assets:,.2f} {fund.currency}")
        if fund.nav:
            print(f"   NAV: {fund.nav}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False


def main():
    """
    Main test function.
    """
    print("PDF Fund Extractor - Direct PDF Mode Test")
    print("This script tests the new direct PDF extraction functionality")
    print("that sends entire PDF files to deepseek/deepseek-r1-0528:free model.\n")
    
    success = test_direct_pdf_extraction()
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("\nTo use the new direct PDF extraction:")
        print("python scripts/pdf_fund_extractor.py --text-mode your_fund.pdf")
        print("\nTo use the original vision-based extraction:")
        print("python scripts/pdf_fund_extractor.py your_fund.pdf")
    else:
        print("\n❌ Test failed. Please check the error messages above.")
        return 1
    
    return 0


if __name__ == "__main__":
    main()
