#!/usr/bin/env python3
"""
Test portfolio API functionality without JWT dependencies.
"""

import requests
import json

def test_portfolio_api():
    """Test the portfolio API endpoint."""
    
    # API base URL for dev environment
    api_base_url = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
    
    # Test portfolios endpoint
    portfolios_url = f"{api_base_url}/portfolios"
    
    print("🧪 Testing Portfolio API...")
    print(f"📡 URL: {portfolios_url}")
    
    # Test without authentication (should return 401)
    print("\n--- Test 1: No Authentication ---")
    try:
        response = requests.get(portfolios_url, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test with mock bearer token (should return 401 but different message)
    print("\n--- Test 2: Invalid Bearer <PERSON> ---")
    try:
        headers = {"Authorization": "Bearer invalid-token"}
        response = requests.get(portfolios_url, headers=headers, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
        
    print("\n✅ Portfolio API tests completed!")
    print("💡 To test with valid authentication, you need to:")
    print("   1. Sign in through the frontend at https://0fca-58-176-137-190.ngrok-free.app/auth/signin")
    print("   2. Get a valid access token from the browser session")
    print("   3. Use that token in the Authorization header")


if __name__ == "__main__":
    test_portfolio_api()