#!/usr/bin/env python3
"""
Test script to verify the validation error fix.
"""

import sys
import os

# Add the src directory to the path so we can import the modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from shared.api.responses import APIResponse
from shared.utils.validation_response import ValidationResponseHandler
from shared.validation import ValidationResult

def test_api_response_validation_error():
    """Test APIResponse.validation_error with different parameter combinations."""
    print("Testing APIResponse.validation_error...")
    
    # Test 1: Only message (should work now)
    try:
        response1 = APIResponse.validation_error("Fund ID is required")
        print("✓ Test 1 passed: validation_error with only message")
        print(f"  Response: {response1}")
    except Exception as e:
        print(f"✗ Test 1 failed: {e}")
    
    # Test 2: Message and validation_errors
    try:
        response2 = APIResponse.validation_error(
            "Invalid fund data",
            [{"field": "fund_id", "message": "Fund ID is required"}]
        )
        print("✓ Test 2 passed: validation_error with message and errors")
        print(f"  Response: {response2}")
    except Exception as e:
        print(f"✗ Test 2 failed: {e}")
    
    # Test 3: Message, validation_errors, and details
    try:
        response3 = APIResponse.validation_error(
            "Validation failed",
            [{"field": "name", "message": "Name is required"}],
            {"summary": {"total_errors": 1}}
        )
        print("✓ Test 3 passed: validation_error with all parameters")
        print(f"  Response: {response3}")
    except Exception as e:
        print(f"✗ Test 3 failed: {e}")

def test_validation_response_handler():
    """Test ValidationResponseHandler.create_validation_error_response."""
    print("\nTesting ValidationResponseHandler...")
    
    # Create a mock validation result with errors
    validation_result = ValidationResult()
    validation_result.add_error("fund_id", "Fund ID is required", "REQUIRED_FIELD")
    validation_result.add_error("name", "Name is too short", "MIN_LENGTH")
    validation_result.add_warning("risk_level", "Risk level not specified", "MISSING_OPTIONAL")
    
    try:
        response = ValidationResponseHandler.create_validation_error_response(validation_result)
        print("✓ ValidationResponseHandler test passed")
        print(f"  Response status: {response.get('statusCode')}")
        print(f"  Response body keys: {list(response.get('body', {}).keys()) if isinstance(response.get('body'), dict) else 'body is string'}")
    except Exception as e:
        print(f"✗ ValidationResponseHandler test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_response_validation_error()
    test_validation_response_handler()
    print("\nTest completed!")
