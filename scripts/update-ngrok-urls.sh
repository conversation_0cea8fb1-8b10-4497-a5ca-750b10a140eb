#!/bin/bash

# Update Cognito App Client with New ngrok URLs
# This script updates the callback and logout URLs for the Cognito app client

echo "🔧 Updating Cognito App Client with New pinggy.link URLs"
echo "=================================================="

# Configuration
USER_POOL_ID="ap-northeast-1_H2kKHGUAT"
CLIENT_ID="2jh76f894g6lv9vrus4qbb9hu7"
AWS_PROFILE="fundflow-dev"
AWS_REGION="ap-northeast-1"
NEW_PINGGY_URL="https://rmzqmzxtlz.a.pinggy.link"

echo "📋 Configuration:"
echo "  - User Pool ID: $USER_POOL_ID"
echo "  - Client ID: $CLIENT_ID"
echo "  - AWS Profile: $AWS_PROFILE"
echo "  - AWS Region: $AWS_REGION"
echo "  - New pinggy.link URL: $NEW_PINGGY_URL"
echo ""

# Check AWS credentials
echo "🔐 Checking AWS credentials..."
if ! AWS_PROFILE=$AWS_PROFILE aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS credentials not found or invalid for profile: $AWS_PROFILE"
    echo "Please ensure AWS credentials are configured properly."
    exit 1
fi

echo "✅ AWS credentials verified"
echo ""

# Get current client configuration
echo "📥 Getting current client configuration..."
CURRENT_CONFIG=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp describe-user-pool-client \
    --user-pool-id "$USER_POOL_ID" \
    --client-id "$CLIENT_ID" \
    --region "$AWS_REGION" 2>/dev/null)

if [ $? -ne 0 ]; then
    echo "❌ Failed to get current client configuration"
    exit 1
fi

# Extract current settings
CLIENT_NAME=$(echo "$CURRENT_CONFIG" | jq -r '.UserPoolClient.ClientName')
echo "✅ Found client: $CLIENT_NAME"

# Update the app client with new URLs
echo "🔄 Updating app client with new pinggy.link URLs..."

# Define the callback and logout URLs
CALLBACK_URLS=(
    "http://localhost:3000/api/auth/callback/cognito"
    "${NEW_PINGGY_URL}/api/auth/callback/cognito"
)

LOGOUT_URLS=(
    "http://localhost:3000"
    "${NEW_PINGGY_URL}"
)

# Convert arrays to space-separated strings for AWS CLI
CALLBACK_URLS_STR=$(printf '%s ' "${CALLBACK_URLS[@]}")
LOGOUT_URLS_STR=$(printf '%s ' "${LOGOUT_URLS[@]}")

echo "   - Callback URLs: ${CALLBACK_URLS_STR}"
echo "   - Logout URLs: ${LOGOUT_URLS_STR}"

# Update the client
AWS_PROFILE=$AWS_PROFILE aws cognito-idp update-user-pool-client \
    --user-pool-id "$USER_POOL_ID" \
    --client-id "$CLIENT_ID" \
    --client-name "$CLIENT_NAME" \
    --callback-urls ${CALLBACK_URLS_STR} \
    --logout-urls ${LOGOUT_URLS_STR} \
    --supported-identity-providers "COGNITO" \
    --allowed-o-auth-flows "code" \
    --allowed-o-auth-scopes "openid" "profile" "email" \
    --allowed-o-auth-flows-user-pool-client \
    --explicit-auth-flows "ALLOW_USER_PASSWORD_AUTH" "ALLOW_REFRESH_TOKEN_AUTH" "ALLOW_ADMIN_USER_PASSWORD_AUTH" \
    --region "$AWS_REGION"

if [ $? -eq 0 ]; then
    echo "✅ Successfully updated Cognito app client"
else
    echo "❌ Failed to update Cognito app client"
    exit 1
fi

echo ""
echo "🎯 Update Complete!"
echo "=================="
echo "✅ Callback URLs updated to include: ${NEW_PINGGY_URL}/api/auth/callback/cognito"
echo "✅ Logout URLs updated to include: ${NEW_PINGGY_URL}"
echo ""
echo "🔧 Next Steps:"
echo "1. Update your frontend/.env.local file with:"
echo "   NEXT_PUBLIC_APP_URL=${NEW_PINGGY_URL}"
echo "   NEXTAUTH_URL=${NEW_PINGGY_URL}"
echo ""
echo "2. Restart your Next.js development server"
echo "3. Test the authentication flow"
echo ""
echo "💡 You can test with: ${NEW_PINGGY_URL}/auth/test"