#!/bin/bash

# FundFlow Deployment Validation Script
# Validates environment-specific configurations before deployment

set -e

# Default values
ENVIRONMENT="dev"
REGION="us-east-1"
PROFILE=""
FIX_ISSUES=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Help function
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment    Target environment (dev, staging, prod) [default: dev]"
    echo "  -r, --region         AWS region [default: us-east-1]"
    echo "  -p, --profile        AWS profile to use"
    echo "  -f, --fix            Attempt to fix common issues automatically"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -e prod                   Validate production deployment readiness"
    echo "  $0 -e staging -f             Validate and fix staging issues"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -p|--profile)
            PROFILE="$2"
            shift 2
            ;;
        -f|--fix)
            FIX_ISSUES=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    echo -e "${RED}Error: Environment must be one of: dev, staging, prod${NC}"
    exit 1
fi

# Set AWS profile if provided
if [[ -n "$PROFILE" ]]; then
    export AWS_PROFILE="$PROFILE"
fi

echo -e "${BLUE}🔍 FundFlow Deployment Validation${NC}"
echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"
echo -e "${BLUE}Region: $REGION${NC}"
echo ""

# Track validation status
validation_status=0

# Function to check and optionally fix issues
check_and_fix() {
    local check_name="$1"
    local check_command="$2"
    local fix_command="$3"
    local required="$4"
    
    echo -e "${YELLOW}Checking: $check_name${NC}"
    
    if eval "$check_command" &> /dev/null; then
        echo -e "${GREEN}✅ $check_name: PASS${NC}"
        return 0
    else
        if [[ "$required" == "true" ]]; then
            echo -e "${RED}❌ $check_name: FAIL (REQUIRED)${NC}"
            validation_status=1
        else
            echo -e "${YELLOW}⚠️  $check_name: FAIL (OPTIONAL)${NC}"
        fi
        
        if [[ "$FIX_ISSUES" == "true" && -n "$fix_command" ]]; then
            echo -e "${BLUE}   Attempting to fix...${NC}"
            if eval "$fix_command"; then
                echo -e "${GREEN}   ✅ Fixed successfully${NC}"
                return 0
            else
                echo -e "${RED}   ❌ Fix failed${NC}"
            fi
        fi
        return 1
    fi
}

echo "=== Infrastructure Validation ==="

# Check SAM CLI
check_and_fix "SAM CLI installed" \
    "command -v sam" \
    "" \
    "true"

# Check AWS CLI
check_and_fix "AWS CLI installed" \
    "command -v aws" \
    "" \
    "true"

# Check AWS credentials
check_and_fix "AWS credentials configured" \
    "aws sts get-caller-identity" \
    "" \
    "true"

# Check SAM template validity
check_and_fix "SAM template valid" \
    "sam validate --region $REGION" \
    "" \
    "true"

# Check parameter file exists
PARAM_FILE="parameters/${ENVIRONMENT}.json"
check_and_fix "Parameter file exists" \
    "test -f $PARAM_FILE" \
    "" \
    "true"

echo ""
echo "=== Environment-Specific Validation ==="

# Validate parameter file structure
if [[ -f "$PARAM_FILE" ]]; then
    echo -e "${YELLOW}Validating parameter file structure...${NC}"
    
    # Check required parameters
    required_params=("Environment" "LogLevel" "LambdaMemorySize" "LambdaTimeout")
    param_valid=true
    
    for param in "${required_params[@]}"; do
        if ! jq -e ".Parameters.$param" "$PARAM_FILE" &> /dev/null; then
            echo -e "${RED}❌ Missing required parameter: $param${NC}"
            param_valid=false
            validation_status=1
        fi
    done
    
    if [[ "$param_valid" == true ]]; then
        echo -e "${GREEN}✅ Parameter file structure: VALID${NC}"
    fi
    
    # Validate environment-specific values
    env_value=$(jq -r '.Parameters.Environment' "$PARAM_FILE")
    if [[ "$env_value" != "$ENVIRONMENT" ]]; then
        echo -e "${RED}❌ Parameter file Environment mismatch: expected $ENVIRONMENT, got $env_value${NC}"
        validation_status=1
    else
        echo -e "${GREEN}✅ Environment parameter matches: $ENVIRONMENT${NC}"
    fi
fi

# Environment-specific checks
case $ENVIRONMENT in
    "prod")
        echo ""
        echo "=== Production-Specific Validation ==="
        
        # Check for custom domain configuration
        if [[ -f "$PARAM_FILE" ]]; then
            domain_name=$(jq -r '.Parameters.DomainName' "$PARAM_FILE")
            cert_arn=$(jq -r '.Parameters.CertificateArn' "$PARAM_FILE")
            
            if [[ "$domain_name" == "" || "$domain_name" == "null" ]]; then
                echo -e "${YELLOW}⚠️  No custom domain configured for production${NC}"
            else
                echo -e "${GREEN}✅ Custom domain configured: $domain_name${NC}"
                
                if [[ "$cert_arn" == "" || "$cert_arn" == "null" ]]; then
                    echo -e "${RED}❌ Certificate ARN required for custom domain${NC}"
                    validation_status=1
                else
                    echo -e "${GREEN}✅ Certificate ARN configured${NC}"
                fi
            fi
        fi
        
        # Check for production-appropriate log level
        if [[ -f "$PARAM_FILE" ]]; then
            log_level=$(jq -r '.Parameters.LogLevel' "$PARAM_FILE")
            if [[ "$log_level" == "DEBUG" ]]; then
                echo -e "${YELLOW}⚠️  DEBUG logging enabled in production (consider INFO or WARNING)${NC}"
            else
                echo -e "${GREEN}✅ Production log level: $log_level${NC}"
            fi
        fi
        ;;
        
    "staging")
        echo ""
        echo "=== Staging-Specific Validation ==="
        echo -e "${GREEN}✅ Staging environment validation complete${NC}"
        ;;
        
    "dev")
        echo ""
        echo "=== Development-Specific Validation ==="
        echo -e "${GREEN}✅ Development environment validation complete${NC}"
        ;;
esac

# Check if stack already exists
echo ""
echo "=== Deployment Target Validation ==="
STACK_NAME="fundflow-$ENVIRONMENT"

if aws cloudformation describe-stacks --stack-name "$STACK_NAME" --region "$REGION" &> /dev/null; then
    echo -e "${BLUE}ℹ️  Stack already exists: $STACK_NAME${NC}"
    echo -e "${BLUE}   This will be an UPDATE deployment${NC}"
    
    # Get current stack status
    stack_status=$(aws cloudformation describe-stacks \
        --stack-name "$STACK_NAME" \
        --region "$REGION" \
        --query 'Stacks[0].StackStatus' \
        --output text)
    
    if [[ "$stack_status" == "ROLLBACK_COMPLETE" || "$stack_status" == "CREATE_FAILED" ]]; then
        echo -e "${RED}❌ Stack is in failed state: $stack_status${NC}"
        echo -e "${YELLOW}   Consider deleting the stack before redeploying${NC}"
        validation_status=1
    else
        echo -e "${GREEN}✅ Stack status: $stack_status${NC}"
    fi
else
    echo -e "${BLUE}ℹ️  New stack deployment: $STACK_NAME${NC}"
    echo -e "${BLUE}   This will be a CREATE deployment${NC}"
fi

echo ""
echo "=== Summary ==="

if [[ $validation_status -eq 0 ]]; then
    echo -e "${GREEN}🎉 Validation passed! Ready to deploy to $ENVIRONMENT${NC}"
    echo ""
    echo "To deploy, run:"
    echo "  ./scripts/deploy.sh -e $ENVIRONMENT"
    exit 0
else
    echo -e "${RED}⚠️  Validation failed! Please fix the issues before deploying${NC}"
    
    if [[ "$FIX_ISSUES" == false ]]; then
        echo ""
        echo "To attempt automatic fixes, run:"
        echo "  $0 -e $ENVIRONMENT --fix"
    fi
    
    exit 1
fi 