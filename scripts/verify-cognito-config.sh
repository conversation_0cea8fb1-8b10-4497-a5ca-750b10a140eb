#!/bin/bash

# Verify and Update Cognito Configuration for FundFlow
# This script checks and updates the Cognito User Pool Client configuration

echo "🔍 Verifying Cognito Configuration for FundFlow"
echo "==============================================="

# Configuration
AWS_PROFILE=${AWS_PROFILE:-fundflow-dev}
AWS_REGION=${AWS_REGION:-ap-northeast-1}
USER_POOL_ID="ap-northeast-1_H2kKHGUAT"
CLIENT_ID="2jh76f894g6lv9vrus4qbb9hu7"
PINGGY_URL="https://rmzqmzxtlz.a.pinggy.link"

echo "📋 Configuration:"
echo "  - AWS Profile: $AWS_PROFILE"
echo "  - AWS Region: $AWS_REGION"
echo "  - User Pool ID: $USER_POOL_ID"
echo "  - Client ID: $CLIENT_ID"
echo "  - Pinggy.link URL: $PINGGY_URL"
echo ""

# Check if the User Pool exists
echo "🔍 Checking User Pool..."
USER_POOL_INFO=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp describe-user-pool \
    --user-pool-id "$USER_POOL_ID" \
    --region $AWS_REGION 2>/dev/null)

if [ $? -eq 0 ]; then
    echo "✅ User Pool found: $USER_POOL_ID"
    USER_POOL_NAME=$(echo "$USER_POOL_INFO" | jq -r '.UserPool.Name')
    echo "   Name: $USER_POOL_NAME"
else
    echo "❌ User Pool not found: $USER_POOL_ID"
    echo "Please check your AWS credentials and User Pool ID"
    exit 1
fi

# Check if the Client exists
echo ""
echo "🔍 Checking User Pool Client..."
CLIENT_INFO=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp describe-user-pool-client \
    --user-pool-id "$USER_POOL_ID" \
    --client-id "$CLIENT_ID" \
    --region $AWS_REGION 2>/dev/null)

if [ $? -eq 0 ]; then
    echo "✅ User Pool Client found: $CLIENT_ID"
    CLIENT_NAME=$(echo "$CLIENT_INFO" | jq -r '.UserPoolClient.ClientName')
    echo "   Name: $CLIENT_NAME"
    
    # Check current callback URLs
    CALLBACK_URLS=$(echo "$CLIENT_INFO" | jq -r '.UserPoolClient.CallbackURLs[]?' 2>/dev/null)
    LOGOUT_URLS=$(echo "$CLIENT_INFO" | jq -r '.UserPoolClient.LogoutURLs[]?' 2>/dev/null)
    
    echo ""
    echo "📋 Current Configuration:"
    echo "   Callback URLs:"
    if [ -n "$CALLBACK_URLS" ]; then
        echo "$CALLBACK_URLS" | while read url; do
            echo "     - $url"
        done
    else
        echo "     - None configured"
    fi
    
    echo "   Logout URLs:"
    if [ -n "$LOGOUT_URLS" ]; then
        echo "$LOGOUT_URLS" | while read url; do
            echo "     - $url"
        done
    else
        echo "     - None configured"
    fi
else
    echo "❌ User Pool Client not found: $CLIENT_ID"
    echo "Please check your Client ID"
    exit 1
fi

# Update the client with correct callback URLs
echo ""
echo "🔧 Updating User Pool Client with correct callback URLs..."

CALLBACK_URLS_JSON='[
    "http://localhost:3000/api/auth/callback/cognito",
    "'$PINGGY_URL'/api/auth/callback/cognito"
]'

LOGOUT_URLS_JSON='[
    "http://localhost:3000",
    "'$PINGGY_URL'"
]'

UPDATE_RESULT=$(AWS_PROFILE=$AWS_PROFILE aws cognito-idp update-user-pool-client \
    --user-pool-id "$USER_POOL_ID" \
    --client-id "$CLIENT_ID" \
    --callback-urls "$CALLBACK_URLS_JSON" \
    --logout-urls "$LOGOUT_URLS_JSON" \
    --allowed-o-auth-flows "code" \
    --allowed-o-auth-scopes "openid" "profile" "email" "aws.cognito.signin.user.admin" \
    --allowed-o-auth-flows-user-pool-client \
    --supported-identity-providers "COGNITO" \
    --explicit-auth-flows "ALLOW_USER_PASSWORD_AUTH" "ALLOW_ADMIN_USER_PASSWORD_AUTH" "ALLOW_REFRESH_TOKEN_AUTH" "ALLOW_USER_SRP_AUTH" \
    --region $AWS_REGION 2>&1)

if [ $? -eq 0 ]; then
    echo "✅ User Pool Client updated successfully"
    echo ""
    echo "📋 Updated Configuration:"
    echo "   Callback URLs:"
    echo "     - http://localhost:3000/api/auth/callback/cognito"
    echo "     - $PINGGY_URL/api/auth/callback/cognito"
    echo "   Logout URLs:"
    echo "     - http://localhost:3000"
    echo "     - $PINGGY_URL"
    echo "   OAuth Flows: Authorization Code Grant"
    echo "   OAuth Scopes: openid, profile, email, aws.cognito.signin.user.admin"
else
    echo "❌ Failed to update User Pool Client:"
    echo "$UPDATE_RESULT"
    exit 1
fi

echo ""
echo "🎯 Next Steps:"
echo "1. Restart your Next.js development server"
echo "2. Clear your browser cache and cookies"
echo "3. Try signing in again"
echo ""
echo "💡 If you're still having issues:"
echo "1. Check that your pinggy.link URL is still active: $PINGGY_URL"
echo "2. Update the NEXTAUTH_URL in your .env.local if pinggy.link URL changed"
echo "3. Verify the Cognito issuer URL is accessible:"
echo "   https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_H2kKHGUAT/.well-known/openid_configuration"
