"""
Authentication Lambda function.
Handles authentication operations via API Gateway with AWS Cognito integration.
"""

import json
import os
import time
from datetime import datetime, timezone
from typing import Any, Dict

import boto3
from aws_lambda_powertools import Logger, Tracer, Metrics
from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_powertools.metrics import MetricUnit
from botocore.exceptions import ClientError
from pydantic import ValidationError, field_serializer, ConfigDict

from shared.api.responses import APIResponse, RequestValidator, CORSHandler
from shared.models.requests import (
    UserPasswordResetRequest,
    UserPasswordChangeRequest,
    UserLoginRequest,
    UserRegisterRequest,
    TokenRefreshRequest,
    ConfirmRegistrationRequest,
)
from shared.models.responses import (
    UserLoginResponse,
    UserRegisterResponse,
    TokenRefreshResponse,
    ConfirmRegistrationResponse,
    UserSummaryResponse,
)
from shared.security.session_manager import (
    <PERSON><PERSON><PERSON><PERSON>,
    SecurityEnforcer,
    SessionExpiredError,
    InvalidSessionError,
    SecurityViolationError,
)
from shared.security.jwt_utils import jwt_manager

# Initialize AWS Lambda Powertools
logger = Logger()
tracer = Tracer()
metrics = Metrics()

# Initialize Cognito client
cognito_client = boto3.client("cognito-idp")

# Get Cognito User Pool Client ID from environment
USER_POOL_CLIENT_ID = os.environ.get("USER_POOL_CLIENT_ID")
if not USER_POOL_CLIENT_ID:
    logger.error("USER_POOL_CLIENT_ID environment variable not set")

# Initialize security components
session_manager = SessionManager()
security_enforcer = SecurityEnforcer()


@logger.inject_lambda_context(log_event=True)
@tracer.capture_lambda_handler
@metrics.log_metrics(capture_cold_start_metric=True)
def handler(event: Dict[str, Any], context: LambdaContext) -> Dict[str, Any]:
    """
    Authentication API endpoint handler.

    Handles password management operations based on HTTP method and path.

    Returns:
        API Gateway response
    """

    # Handle CORS preflight requests
    if CORSHandler.is_options_request(event):
        return CORSHandler.handle_options()

    try:
        # Extract request information
        http_method = event.get("httpMethod")
        path = event.get("path", "")

        logger.info(f"Processing {http_method} request to {path}")

        # Perform security validation
        security_result = security_enforcer.validate_request_security(event)

        if not security_result.get("rate_limit_passed", True):
            logger.warning("Request blocked due to rate limiting")
            metrics.add_metric(
                name="RateLimitViolations", unit=MetricUnit.Count, value=1
            )
            return APIResponse.error(
                "RATE_LIMIT_EXCEEDED", "Too many requests. Please try again later.", 429
            )

        if not security_result.get("csrf_valid", True):
            logger.warning("Request blocked due to invalid CSRF token")
            metrics.add_metric(name="CSRFViolations", unit=MetricUnit.Count, value=1)
            return APIResponse.forbidden("Invalid or missing CSRF token")

        if "error" in security_result:
            logger.error(f"Security validation error: {security_result['error']}")
            return APIResponse.internal_server_error("Security validation failed")

        # Route to appropriate handler based on HTTP method and path
        if http_method == "POST":
            if path.endswith("/auth/forgot-password"):
                # POST /auth/forgot-password - Initiate password reset
                return handle_forgot_password(event)
            elif path.endswith("/auth/confirm-forgot-password"):
                # POST /auth/confirm-forgot-password - Confirm password reset
                return handle_confirm_forgot_password(event)
            elif path.endswith("/auth/login"):
                # POST /auth/login - Login
                return handle_login(event)
            elif path.endswith("/auth/register"):
                # POST /auth/register - Register
                return handle_register(event)
            elif path.endswith("/auth/confirm-registration"):
                # POST /auth/confirm-registration - Confirm registration
                return handle_confirm_registration(event)
            elif path.endswith("/auth/token/refresh"):
                # POST /auth/token/refresh - Refresh access token
                return handle_token_refresh(event)
            else:
                logger.warning(f"Unsupported POST path: {path}")
                return APIResponse.not_found("Endpoint not found")

        elif http_method == "PUT":
            if path.endswith("/auth/change-password"):
                # PUT /auth/change-password - Change password for authenticated user
                return handle_change_password(event)
            else:
                logger.warning(f"Unsupported PUT path: {path}")
                return APIResponse.not_found("Endpoint not found")

        elif http_method == "DELETE":
            if path.endswith("/auth/session"):
                # DELETE /auth/session - Logout (invalidate session)
                return handle_logout(event)
            elif path.endswith("/auth/sessions"):
                # DELETE /auth/sessions - Logout all devices (invalidate all sessions)
                return handle_logout_all_devices(event)
            else:
                logger.warning(f"Unsupported DELETE path: {path}")
                return APIResponse.not_found("Endpoint not found")

        else:
            logger.warning(f"Unsupported method: {http_method}")
            return APIResponse.error(
                "METHOD_NOT_ALLOWED", f"Method {http_method} not allowed", 405
            )

    except Exception as e:
        logger.exception(
            "Unhandled error in auth handler",
            extra={"error": str(e), "request_id": context.aws_request_id},
        )

        metrics.add_metric(name="AuthAPIErrors", unit=MetricUnit.Count, value=1)

        return APIResponse.internal_server_error(
            message="An unexpected error occurred", error_id=context.aws_request_id
        )


@tracer.capture_method
def handle_forgot_password(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST /auth/forgot-password - Initiate password reset flow."""

    try:
        # Parse and validate request body
        body = RequestValidator.validate_json_body(event)
        try:
            request_data = UserPasswordResetRequest(**body)
        except ValidationError as e:
            logger.warning(f"Invalid forgot password request: {e}")
            return APIResponse.validation_error("Invalid request data", e.errors())

        logger.info(
            f"Processing forgot password request for email: {request_data.email}"
        )

        # Call Cognito forgot password API
        try:
            response = cognito_client.forgot_password(
                ClientId=USER_POOL_CLIENT_ID, Username=request_data.email
            )

            # Extract delivery details from response
            delivery_details = response.get("CodeDeliveryDetails", {})
            destination = delivery_details.get("Destination", "")
            delivery_medium = delivery_details.get("DeliveryMedium", "EMAIL")

            logger.info(
                f"Password reset code sent to {destination} via {delivery_medium}"
            )

            metrics.add_metric(
                name="ForgotPasswordRequests", unit=MetricUnit.Count, value=1
            )

            return APIResponse.success(
                data={
                    "message": "Password reset code has been sent to your email address",
                    "delivery_medium": delivery_medium,
                    "destination": (
                        _mask_email(destination)
                        if delivery_medium == "EMAIL"
                        else destination
                    ),
                }
            )

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            error_message = e.response["Error"]["Message"]

            logger.warning(
                f"Cognito forgot password error: {error_code} - {error_message}"
            )

            if error_code == "UserNotFoundException":
                # For security reasons, don't reveal if user exists or not
                return APIResponse.success(
                    data={
                        "message": "If an account with that email exists, a password reset code has been sent"
                    }
                )
            elif error_code == "InvalidParameterException":
                return APIResponse.bad_request("Invalid email address format")
            elif error_code == "LimitExceededException":
                return APIResponse.error(
                    "RATE_LIMIT_EXCEEDED",
                    "Too many password reset attempts. Please try again later.",
                    429,
                )
            else:
                logger.error(
                    f"Unexpected Cognito error: {error_code} - {error_message}"
                )
                return APIResponse.internal_server_error(
                    "Failed to process password reset request"
                )

    except Exception as e:
        logger.exception("Error in forgot password handler")
        raise


@tracer.capture_method
def handle_confirm_forgot_password(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST /auth/confirm-forgot-password - Confirm password reset with verification code."""

    try:
        # Parse and validate request body
        body = RequestValidator.validate_json_body(event)

        # Validate required fields
        required_fields = ["email", "confirmation_code", "new_password"]
        for field in required_fields:
            if field not in body or not body[field]:
                return APIResponse.bad_request(f"Missing required field: {field}")

        email = body["email"]
        confirmation_code = body["confirmation_code"]
        new_password = body["new_password"]

        # Validate password strength (basic validation)
        if len(new_password) < 8:
            return APIResponse.bad_request(
                "Password must be at least 8 characters long"
            )

        logger.info(f"Processing confirm forgot password for email: {email}")

        # Call Cognito confirm forgot password API
        try:
            cognito_client.confirm_forgot_password(
                ClientId=USER_POOL_CLIENT_ID,
                Username=email,
                ConfirmationCode=confirmation_code,
                Password=new_password,
            )

            logger.info(f"Password successfully reset for user: {email}")

            metrics.add_metric(
                name="PasswordResetConfirmations", unit=MetricUnit.Count, value=1
            )

            return APIResponse.success(
                data={
                    "message": "Password has been successfully reset. You can now log in with your new password."
                }
            )

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            error_message = e.response["Error"]["Message"]

            logger.warning(
                f"Cognito confirm forgot password error: {error_code} - {error_message}"
            )

            if error_code == "CodeMismatchException":
                return APIResponse.bad_request("Invalid verification code")
            elif error_code == "ExpiredCodeException":
                return APIResponse.bad_request(
                    "Verification code has expired. Please request a new one."
                )
            elif error_code == "InvalidPasswordException":
                return APIResponse.bad_request(
                    "Password does not meet the required criteria"
                )
            elif error_code == "UserNotFoundException":
                return APIResponse.bad_request("User not found")
            elif error_code == "LimitExceededException":
                return APIResponse.error(
                    "RATE_LIMIT_EXCEEDED",
                    "Too many attempts. Please try again later.",
                    429,
                )
            else:
                logger.error(
                    f"Unexpected Cognito error: {error_code} - {error_message}"
                )
                return APIResponse.internal_server_error("Failed to reset password")

    except Exception as e:
        logger.exception("Error in confirm forgot password handler")
        raise


@tracer.capture_method
def handle_login(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST /auth/login - Login."""

    try:
        # Parse and validate request body
        body = RequestValidator.validate_json_body(event)
        try:
            request_data = UserLoginRequest(**body)
        except ValidationError as e:
            logger.warning(f"Invalid login request: {e}")
            return APIResponse.validation_error("Invalid request data", e.errors())

        logger.info(f"Processing login request for email: {request_data.email}")

        # Use JWT manager for authentication
        try:
            auth_result = jwt_manager.authenticate_user(
                email=request_data.email, password=request_data.password
            )

            # Create user summary response from user info
            user_info = auth_result["user_info"]
            user_summary = UserSummaryResponse(
                user_id=user_info.get("user_sub", user_info.get("username", "")),
                email=user_info.get("email", ""),
                first_name=user_info.get("given_name", ""),
                last_name=user_info.get("family_name", ""),
                full_name=f"{user_info.get('given_name', '')} {user_info.get('family_name', '')}".strip(),
                role=user_info.get("custom:role", "user"),  # Default role
                department=user_info.get("custom:department"),
                job_title=user_info.get("custom:job_title"),
                status="active",  # Default status for successful login
                is_verified=user_info.get("email_verified", "false").lower() == "true",
                created_at=datetime.now(timezone.utc),  # Will be populated from DB in real implementation
                updated_at=datetime.now(timezone.utc),
            )

            # Create login response
            login_response = UserLoginResponse(
                access_token=auth_result["access_token"],
                refresh_token=auth_result["refresh_token"],
                token_type=auth_result["token_type"],
                expires_in=auth_result["expires_in"],
                user_info=user_summary,
                success=True,
                message="Login successful",
            )

            logger.info("Login successful")
            metrics.add_metric(name="LoginRequests", unit=MetricUnit.Count, value=1)

            return APIResponse.success(data=login_response.dict())

        except ValueError as e:
            logger.warning(f"Authentication failed: {str(e)}")
            metrics.add_metric(name="FailedLogins", unit=MetricUnit.Count, value=1)
            return APIResponse.bad_request(str(e))

        except Exception as e:
            logger.error(f"Unexpected error during authentication: {str(e)}")
            return APIResponse.internal_server_error("Failed to process login")

    except Exception as e:
        logger.exception("Error in login handler")
        raise


@tracer.capture_method
def handle_register(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST /auth/register - Register."""

    try:
        # Parse and validate request body
        body = RequestValidator.validate_json_body(event)
        try:
            request_data = UserRegisterRequest(**body)
        except ValidationError as e:
            logger.warning(f"Invalid register request: {e}")
            return APIResponse.validation_error("Invalid request data", e.errors())

        logger.info(f"Processing register request for email: {request_data.email}")

        # Use JWT manager for registration
        try:
            register_result = jwt_manager.register_user(
                email=request_data.email,
                password=request_data.password,
                first_name=request_data.first_name,
                last_name=request_data.last_name,
                phone_number=request_data.phone_number,
            )

            # Create registration response
            register_response = UserRegisterResponse(
                user_id=register_result["user_sub"],
                email=register_result["email"],
                verification_required=register_result["confirmation_required"],
                message=register_result["message"],
                success=True,
            )

            logger.info("User registered successfully")
            metrics.add_metric(name="RegisterRequests", unit=MetricUnit.Count, value=1)

            return APIResponse.success(data=register_response.dict())

        except ValueError as e:
            logger.warning(f"Registration failed: {str(e)}")
            metrics.add_metric(
                name="FailedRegistrations", unit=MetricUnit.Count, value=1
            )
            return APIResponse.bad_request(str(e))

        except Exception as e:
            logger.error(f"Unexpected error during registration: {str(e)}")
            return APIResponse.internal_server_error("Failed to process registration")

    except Exception as e:
        logger.exception("Error in register handler")
        raise


@tracer.capture_method
def handle_confirm_registration(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST /auth/confirm-registration - Confirm user registration."""

    try:
        # Parse and validate request body
        body = RequestValidator.validate_json_body(event)
        try:
            request_data = ConfirmRegistrationRequest(**body)
        except ValidationError as e:
            logger.warning(f"Invalid confirm registration request: {e}")
            return APIResponse.validation_error("Invalid request data", e.errors())

        logger.info(
            f"Processing confirm registration request for email: {request_data.email}"
        )

        # Use JWT manager for confirmation
        try:
            confirm_result = jwt_manager.confirm_registration(
                email=request_data.email,
                confirmation_code=request_data.confirmation_code,
            )

            # Create confirmation response
            confirm_response = ConfirmRegistrationResponse(
                email=confirm_result["email"],
                account_verified=confirm_result["account_verified"],
                message=confirm_result["message"],
                success=True,
            )

            logger.info("Registration confirmed successfully")
            metrics.add_metric(
                name="ConfirmRegistrationRequests", unit=MetricUnit.Count, value=1
            )

            return APIResponse.success(data=confirm_response.dict())

        except ValueError as e:
            logger.warning(f"Registration confirmation failed: {str(e)}")
            metrics.add_metric(
                name="FailedConfirmations", unit=MetricUnit.Count, value=1
            )
            return APIResponse.bad_request(str(e))

        except Exception as e:
            logger.error(f"Unexpected error during confirmation: {str(e)}")
            return APIResponse.internal_server_error("Failed to process confirmation")

    except Exception as e:
        logger.exception("Error in confirm registration handler")
        raise


@tracer.capture_method
def handle_token_refresh(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST /auth/token/refresh - Refresh access token."""

    try:
        # Parse and validate request body
        body = RequestValidator.validate_json_body(event)
        try:
            request_data = TokenRefreshRequest(**body)
        except ValidationError as e:
            logger.warning(f"Invalid token refresh request: {e}")
            return APIResponse.validation_error("Invalid request data", e.errors())

        logger.info("Processing token refresh request")

        # Use JWT manager for token refresh
        try:
            refresh_result = jwt_manager.refresh_access_token(
                refresh_token=request_data.refresh_token
            )

            # Create token refresh response
            refresh_response = TokenRefreshResponse(
                access_token=refresh_result["access_token"],
                token_type=refresh_result["token_type"],
                expires_in=refresh_result["expires_in"],
                success=True,
                message="Token refreshed successfully",
            )

            logger.info("Token refreshed successfully")
            metrics.add_metric(
                name="TokenRefreshRequests", unit=MetricUnit.Count, value=1
            )

            return APIResponse.success(data=refresh_response.dict())

        except ValueError as e:
            logger.warning(f"Token refresh failed: {str(e)}")
            metrics.add_metric(
                name="FailedTokenRefresh", unit=MetricUnit.Count, value=1
            )
            return APIResponse.bad_request(str(e))

        except Exception as e:
            logger.error(f"Unexpected error during token refresh: {str(e)}")
            return APIResponse.internal_server_error("Failed to refresh token")

    except Exception as e:
        logger.exception("Error in token refresh handler")
        raise


@tracer.capture_method
def handle_change_password(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle PUT /auth/change-password - Change password for authenticated user."""

    try:
        # Validate session and get user context
        try:
            session_result = session_manager.validate_session(event)

            if not session_result.get("valid", False):
                error_type = session_result.get("error_type", "InvalidSessionError")
                if error_type == "SessionExpiredError":
                    logger.warning("Session expired during password change")
                    metrics.add_metric(
                        name="ExpiredSessions", unit=MetricUnit.Count, value=1
                    )
                    return APIResponse.unauthorized(
                        "Session has expired. Please log in again."
                    )
                else:
                    logger.warning(
                        f"Invalid session: {session_result.get('error', 'Unknown error')}"
                    )
                    return APIResponse.unauthorized("Invalid or expired session")

            user_info = session_result["user_info"]
            session_info = session_result["session_info"]
            access_token = _extract_access_token(event)

            if not access_token:
                return APIResponse.unauthorized("Access token is required")

            # Log session refresh warning if needed
            if session_result.get("requires_refresh", False):
                logger.info(
                    f"Session for user {user_info.get('user_sub')} requires refresh soon"
                )

            logger.info(
                "Session validated for change password",
                extra={
                    "user_id": user_info.get("user_sub"),
                    "email": user_info.get("email"),
                    "session_duration": session_info.get("session_duration"),
                },
            )
        except Exception as e:
            logger.error(f"Failed to validate session: {str(e)}")
            return APIResponse.unauthorized("Session validation failed")

        # Parse and validate request body
        body = RequestValidator.validate_json_body(event)
        try:
            request_data = UserPasswordChangeRequest(**body)
        except ValidationError as e:
            logger.warning(f"Invalid change password request: {e}")
            return APIResponse.validation_error("Invalid request data", e.errors())

        logger.info(
            f"Processing change password request for user: {user_info.get('user_sub')}"
        )

        # Call Cognito change password API
        try:
            cognito_client.change_password(
                AccessToken=access_token,
                PreviousPassword=request_data.current_password,
                ProposedPassword=request_data.new_password,
            )

            logger.info(
                f"Password successfully changed for user: {user_info.get('user_sub')}"
            )

            metrics.add_metric(name="PasswordChanges", unit=MetricUnit.Count, value=1)

            return APIResponse.success(
                data={"message": "Password has been successfully changed"}
            )

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            error_message = e.response["Error"]["Message"]

            logger.warning(
                f"Cognito change password error: {error_code} - {error_message}"
            )

            if error_code == "NotAuthorizedException":
                return APIResponse.bad_request("Current password is incorrect")
            elif error_code == "InvalidPasswordException":
                return APIResponse.bad_request(
                    "New password does not meet the required criteria"
                )
            elif error_code == "LimitExceededException":
                return APIResponse.error(
                    "RATE_LIMIT_EXCEEDED",
                    "Too many password change attempts. Please try again later.",
                    429,
                )
            else:
                logger.error(
                    f"Unexpected Cognito error: {error_code} - {error_message}"
                )
                return APIResponse.internal_server_error("Failed to change password")

    except Exception as e:
        logger.exception("Error in change password handler")
        raise


@tracer.capture_method
def handle_logout(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle DELETE /auth/session - Logout and invalidate session with token blacklisting."""

    try:
        # Get access token for blacklisting
        access_token = _extract_access_token(event)

        if not access_token:
            logger.warning("No access token found for logout")
            return APIResponse.success(data={"message": "Successfully logged out"})

        # Validate session first (this will also validate the token)
        session_result = session_manager.validate_session(event)

        user_info = None
        if session_result.get("valid", False):
            user_info = session_result["user_info"]
            logger.info(f"Processing logout for user: {user_info.get('user_sub')}")
        else:
            # Session already invalid, but still try to blacklist the token
            logger.info(
                "Logout attempt with invalid session, will still blacklist token"
            )

        # Extract user agent and IP address for audit logging
        headers = event.get("headers", {})
        user_agent = headers.get("User-Agent", "").strip()

        # Get IP address from various sources
        ip_address = (
            headers.get("X-Forwarded-For", "").split(",")[0].strip()
            or headers.get("X-Real-IP", "").strip()
            or event.get("requestContext", {}).get("identity", {}).get("sourceIp", "")
        )

        # Blacklist the current token
        blacklist_success = jwt_manager.blacklist_token(
            access_token=access_token,
            reason="User logout",
            user_agent=user_agent if user_agent else None,
            ip_address=ip_address if ip_address else None,
        )

        # Also invalidate session using existing session manager
        session_success = session_manager.invalidate_session(access_token)

        if blacklist_success:
            logger.info(
                f"Successfully blacklisted token during logout",
                extra={
                    "user_id": user_info.get("user_sub") if user_info else "unknown",
                    "ip_address": ip_address,
                    "user_agent": user_agent,
                },
            )
            metrics.add_metric(name="SuccessfulLogouts", unit=MetricUnit.Count, value=1)
        else:
            logger.warning("Failed to blacklist token during logout")
            metrics.add_metric(name="FailedLogouts", unit=MetricUnit.Count, value=1)

        # Return success response regardless of blacklist status
        # This prevents revealing system internals and ensures good UX
        return APIResponse.success(
            data={
                "message": "Successfully logged out",
                "logged_out_at": int(time.time()),
            }
        )

    except Exception as e:
        logger.exception("Error in logout handler")
        # For logout, we should generally return success even on errors
        # to avoid revealing system internals and ensure user experience
        metrics.add_metric(name="LogoutErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.success(data={"message": "Successfully logged out"})


@tracer.capture_method
def handle_logout_all_devices(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle DELETE /auth/sessions - Logout from all devices by blacklisting all user tokens."""

    try:
        # Validate session to get user information
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            logger.warning("Invalid session for logout all devices")
            return APIResponse.unauthorized("Valid session required")

        user_info = session_result["user_info"]
        user_id = user_info.get("user_sub")

        if not user_id:
            logger.error("No user ID found in session for logout all devices")
            return APIResponse.unauthorized("Invalid user session")

        logger.info(f"Processing logout all devices for user: {user_id}")

        # Extract user agent and IP address for audit logging
        headers = event.get("headers", {})
        user_agent = headers.get("User-Agent", "").strip()

        # Get IP address from various sources
        ip_address = (
            headers.get("X-Forwarded-For", "").split(",")[0].strip()
            or headers.get("X-Real-IP", "").strip()
            or event.get("requestContext", {}).get("identity", {}).get("sourceIp", "")
        )

        # Blacklist all tokens for this user
        blacklisted_count = jwt_manager.blacklist_user_tokens(
            user_id=user_id,
            reason="User logout all devices",
        )

        # Also revoke tokens with Cognito (global sign out)
        try:
            access_token = _extract_access_token(event)
            if access_token:
                jwt_manager.revoke_token(access_token)
                logger.info(f"Successfully revoked Cognito tokens for user: {user_id}")
        except Exception as e:
            logger.warning(f"Failed to revoke Cognito tokens: {str(e)}")
            # Continue even if Cognito revocation fails

        logger.info(
            f"Successfully logged out user from all devices",
            extra={
                "user_id": user_id,
                "blacklisted_tokens": blacklisted_count,
                "ip_address": ip_address,
                "user_agent": user_agent,
            },
        )

        metrics.add_metric(name="LogoutAllDevices", unit=MetricUnit.Count, value=1)
        metrics.add_metric(
            name="TokensBlacklisted", unit=MetricUnit.Count, value=blacklisted_count
        )

        return APIResponse.success(
            data={
                "message": "Successfully logged out from all devices",
                "logged_out_at": int(time.time()),
                "devices_logged_out": blacklisted_count,
            }
        )

    except Exception as e:
        logger.exception("Error in logout all devices handler")
        metrics.add_metric(
            name="LogoutAllDevicesErrors", unit=MetricUnit.Count, value=1
        )
        return APIResponse.internal_server_error("Failed to logout from all devices")


def _mask_email(email: str) -> str:
    """Mask email address for security (show only first char and domain)."""
    if "@" not in email:
        return email

    local, domain = email.split("@", 1)
    if len(local) <= 2:
        masked_local = local[0] + "*"
    else:
        masked_local = local[0] + "*" * (len(local) - 2) + local[-1]

    return f"{masked_local}@{domain}"


def _extract_access_token(event: Dict[str, Any]) -> str:
    """Extract access token from Authorization header."""
    headers = event.get("headers", {})

    # Check for Authorization header (case-insensitive)
    auth_header = None
    for key, value in headers.items():
        if key.lower() == "authorization":
            auth_header = value
            break

    if not auth_header:
        return None

    # Extract token from "Bearer <token>" format
    if auth_header.startswith("Bearer "):
        return auth_header[7:]  # Remove "Bearer " prefix

    return None
