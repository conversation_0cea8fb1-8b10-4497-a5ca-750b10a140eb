"""
Fund Extractor Lambda Function.

This Lambda function accepts PDF files via API Gateway and uses AI to extract
fund information, converting it to the standardized Fund data model.
Uses the extract_fund_from_pdf_direct method for direct PDF processing.
"""

import os
import json
import base64
from typing import Any, Dict, Optional
from datetime import datetime, timezone
import boto3

from aws_lambda_powertools import Logger, Tracer, Metrics
from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_powertools.metrics import MetricUnit
from pydantic import ValidationError
import requests

from shared.api.responses import APIResponse, CORSHandler
from shared.api.auth_dependencies import create_user_context
from shared.database import get_fund_repository

# Import the PDFFundExtractor class
from .pdf_fund_extractor import PDFFundExtractor

# Initialize AWS Lambda Powertools
logger = Logger()
tracer = Tracer()
metrics = Metrics()


def parse_multipart_data(event: Dict[str, Any]) -> Optional[str]:
    """
    Parse multipart form data from API Gateway event to extract PDF file.

    Args:
        event: API Gateway event containing multipart form data

    Returns:
        Base64-encoded PDF content or None if not found
    """
    try:
        # Check if body is base64 encoded
        is_base64_encoded = event.get("isBase64Encoded", False)
        body = event.get("body", "")

        if not body:
            logger.error("No body found in request")
            return None

        # Get content type from headers
        headers = event.get("headers", {})
        content_type = headers.get("content-type") or headers.get("Content-Type", "")

        if not content_type.startswith("multipart/form-data"):
            logger.error(f"Invalid content type: {content_type}")
            return None

        # Extract boundary from content type
        boundary = None
        for part in content_type.split(";"):
            part = part.strip()
            if part.startswith("boundary="):
                boundary = part[9:]  # Remove 'boundary='
                # Remove quotes if present
                if boundary.startswith('"') and boundary.endswith('"'):
                    boundary = boundary[1:-1]
                break

        if not boundary:
            logger.error("No boundary found in multipart data")
            return None

        # Decode body if base64 encoded
        if is_base64_encoded:
            try:
                body_bytes = base64.b64decode(body)
            except Exception as e:
                logger.error(f"Failed to decode base64 body: {e}")
                return None
        else:
            # Convert string body to bytes
            body_bytes = body.encode("latin-1")  # Use latin-1 to preserve binary data

        # Convert back to string for parsing
        body_str = body_bytes.decode("latin-1")

        # Split by boundary
        parts = body_str.split(f"--{boundary}")

        for part in parts:
            if not part.strip():
                continue

            # Look for Content-Disposition header with filename
            if "Content-Disposition:" in part and "filename=" in part:
                lines = part.split("\r\n")

                # Find the empty line that separates headers from content
                content_start_idx = -1
                for i, line in enumerate(lines):
                    if line.strip() == "":
                        content_start_idx = i + 1
                        break

                if content_start_idx > 0 and content_start_idx < len(lines):
                    # Extract file content (everything after the empty line)
                    content_lines = lines[content_start_idx:]

                    # Remove the final boundary marker if present
                    if content_lines and content_lines[-1].startswith("--"):
                        content_lines = content_lines[:-1]

                    # Join content and convert to bytes
                    file_content = "\r\n".join(content_lines)
                    file_bytes = file_content.encode("latin-1")

                    # Return as base64
                    return base64.b64encode(file_bytes).decode("utf-8")

        logger.error("No file found in multipart data")
        return None

    except Exception as e:
        logger.error(f"Error parsing multipart data: {e}")
        # Don't log the actual body content as it contains binary data
        body_length = len(event.get("body", ""))
        logger.error(f"Event body length: {body_length} characters")
        logger.error(f"Content-Type: {headers.get('content-type', 'Not found')}")
        logger.error(f"Is Base64 Encoded: {event.get('isBase64Encoded', False)}")
        return None


def handle_async_job(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle asynchronous PDF processing job.

    Args:
        event: Async job payload containing jobId, userId, body, and headers

    Returns:
        Job completion status
    """
    job_id = event.get("jobId")
    user_id = event.get("userId")

    # Initialize DynamoDB client
    dynamodb = boto3.resource("dynamodb")
    job_table_name = os.environ.get("PDF_JOB_TABLE")
    # Type hint to help IDE understand this is a DynamoDB Table
    job_table = dynamodb.Table(job_table_name)  # type: ignore

    # Job statuses
    class JobStatus:
        PENDING = "pending"
        PROCESSING = "processing"
        COMPLETED = "completed"
        FAILED = "failed"

    try:
        logger.info(f"Processing async PDF job {job_id} for user {user_id}")

        # Update job status to processing
        job_table.update_item(
            Key={"jobId": job_id},
            UpdateExpression="SET #status = :status, updatedAt = :updated",
            ExpressionAttributeNames={"#status": "status"},
            ExpressionAttributeValues={
                ":status": JobStatus.PROCESSING,
                ":updated": datetime.now(timezone.utc).isoformat(),
            },
        )

        # Parse PDF from the event body
        pdf_base64 = parse_multipart_data(event)
        if not pdf_base64:
            raise ValueError("No PDF file found in async job payload")

        # Initialize extractor and process PDF
        extractor = PDFFundExtractor()
        fund = extractor.extract_fund_from_pdf_direct(pdf_base64)

        # Convert fund to response format
        fund_data = fund.model_dump(mode="json")

        # Save to database
        try:
            fund_repository = get_fund_repository()
            saved_fund = fund_repository.create(fund)
            fund_data["saved_to_database"] = True
            fund_data["database_fund_id"] = saved_fund.fund_id
            logger.info(f"Saved extracted fund to database: {saved_fund.fund_id}")
        except Exception as e:
            logger.error(f"Failed to save fund to database: {e}")
            fund_data["saved_to_database"] = False
            fund_data["database_error"] = str(e)

        # Update job with successful completion
        job_table.update_item(
            Key={"jobId": job_id},
            UpdateExpression="SET #status = :status, updatedAt = :updated, result = :result, completedAt = :completed",
            ExpressionAttributeNames={"#status": "status"},
            ExpressionAttributeValues={
                ":status": JobStatus.COMPLETED,
                ":updated": datetime.now(timezone.utc).isoformat(),
                ":completed": datetime.now(timezone.utc).isoformat(),
                ":result": fund_data,
            },
        )

        logger.info(f"Successfully completed async PDF job {job_id}")
        metrics.add_metric(name="AsyncPDFJobSuccess", unit=MetricUnit.Count, value=1)

        return {
            "statusCode": 200,
            "body": json.dumps({"status": "completed", "jobId": job_id}),
        }

    except Exception as e:
        logger.error(f"Error processing async PDF job {job_id}: {str(e)}")

        # Provide more specific error messages for common issues
        error_message = str(e)
        if isinstance(e, OSError) and "File name too long" in str(e):
            error_message = (
                "PDF file processing failed due to internal file handling error"
            )
        elif isinstance(e, OSError):
            error_message = "PDF file could not be processed due to a system error"

        # Update job with failure status
        try:
            job_table.update_item(
                Key={"jobId": job_id},
                UpdateExpression="SET #status = :status, updatedAt = :updated, error = :error, completedAt = :completed",
                ExpressionAttributeNames={"#status": "status"},
                ExpressionAttributeValues={
                    ":status": JobStatus.FAILED,
                    ":updated": datetime.now(timezone.utc).isoformat(),
                    ":completed": datetime.now(timezone.utc).isoformat(),
                    ":error": error_message,
                },
            )
        except Exception as update_error:
            logger.error(
                f"Failed to update job status for failed job {job_id}: {update_error}"
            )

        metrics.add_metric(name="AsyncPDFJobFailure", unit=MetricUnit.Count, value=1)
        return {
            "statusCode": 500,
            "body": json.dumps(
                {"status": "failed", "jobId": job_id, "error": error_message}
            ),
        }


@logger.inject_lambda_context(
    log_event=False
)  # Disable event logging to avoid binary data in logs
@tracer.capture_lambda_handler
@metrics.log_metrics(capture_cold_start_metric=True)
def handler(
    event: Dict[str, Any], context: LambdaContext
) -> Dict[str, Any]:  # noqa: ARG001
    """
    Fund Extractor Lambda handler.

    Accepts PDF files via API Gateway and extracts fund information using AI.
    Also supports asynchronous job processing for long-running operations.

    Args:
        event: API Gateway event or async job payload
        context: Lambda context

    Returns:
        API Gateway response with extracted fund data or job completion status
    """
    # Context is used by AWS Lambda Powertools decorators
    try:
        # Check if this is an async job invocation
        if event.get("isAsyncJob"):
            return handle_async_job(event)

        # Handle CORS preflight requests
        if CORSHandler.is_options_request(event):
            return CORSHandler.handle_options()

        # Extract request info
        method = event.get("httpMethod", "")
        path = event.get("path", "")

        logger.info(f"Processing {method} request to {path}")

        # Only support POST method for PDF upload
        if method != "POST":
            return APIResponse.error(
                "METHOD_NOT_ALLOWED",
                f"Method {method} not allowed. Only POST is supported.",
                405,
            )

        # Validate authentication
        try:
            user_context = create_user_context(
                event.get("requestContext", {}).get("authorizer", {})
            )
            if not user_context:
                return APIResponse.unauthorized("Authentication required")

            logger.info(
                f"Request authenticated for user: {user_context.get('user_id')}"
            )

        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return APIResponse.unauthorized("Invalid authentication")

        # Log request details for debugging (without binary data)
        logger.info(
            "PDF extraction request received",
            extra={
                "event_keys": list(event.keys()),
                "content_type": event.get("headers", {}).get(
                    "content-type", "Not found"
                ),
                "is_base64_encoded": event.get("isBase64Encoded", False),
                "body_length": len(event.get("body", "")),
                "method": method,
                "path": path,
            },
        )

        # Parse PDF file from multipart form data
        pdf_base64 = parse_multipart_data(event)
        if not pdf_base64:
            return APIResponse.bad_request(
                "No PDF file found in request. Please upload a PDF file.",
                {
                    "expected_content_type": "multipart/form-data",
                    "expected_field": "file",
                },
            )

        # Initialize extractor
        try:
            extractor = PDFFundExtractor()
        except ValueError as e:
            logger.error(f"Failed to initialize PDF extractor: {e}")
            return APIResponse.error(
                "CONFIGURATION_ERROR",
                "PDF extraction service is not properly configured",
                500,
            )

        # Extract fund data from PDF
        try:
            logger.info(
                "Starting PDF extraction process",
                extra={
                    "pdf_size_chars": len(pdf_base64),
                    "user_id": user_context.get("user_id"),
                },
            )
            metrics.add_metric(
                name="PDFExtractionAttempt", unit=MetricUnit.Count, value=1
            )

            fund = extractor.extract_fund_from_pdf_direct(pdf_base64)

            logger.info(
                "PDF extraction completed successfully",
                extra={
                    "fund_name": fund.name,
                    "fund_id": fund.fund_id,
                    "fund_type": fund.fund_type,
                },
            )
            metrics.add_metric(
                name="PDFExtractionSuccess", unit=MetricUnit.Count, value=1
            )

            # Convert fund to response format
            fund_data = fund.model_dump(mode="json")

            # Optionally save to database
            save_to_db = (
                event.get("queryStringParameters", {}).get("save", "false").lower()
                == "true"
            )
            if save_to_db:
                try:
                    fund_repository = get_fund_repository()
                    saved_fund = fund_repository.create(fund)
                    logger.info(
                        f"Saved extracted fund to database: {saved_fund.fund_id}"
                    )
                    fund_data["saved_to_database"] = True
                    fund_data["database_fund_id"] = saved_fund.fund_id
                except Exception as e:
                    logger.error(f"Failed to save fund to database: {e}")
                    fund_data["saved_to_database"] = False
                    fund_data["database_error"] = str(e)

            return APIResponse.success(
                data=fund_data,
                message="Fund information successfully extracted from PDF",
            )

        except requests.RequestException as e:
            logger.error(
                "AI API error during PDF extraction",
                extra={
                    "error_type": "RequestException",
                    "error_message": str(e),
                    "user_id": user_context.get("user_id"),
                },
            )
            metrics.add_metric(
                name="PDFExtractionAPIError", unit=MetricUnit.Count, value=1
            )
            return APIResponse.error(
                "AI_API_ERROR",
                "Failed to process PDF with AI service. Please try again.",
                503,
            )
        except json.JSONDecodeError as e:
            logger.error(
                "AI response parsing error during PDF extraction",
                extra={
                    "error_type": "JSONDecodeError",
                    "error_message": str(e),
                    "user_id": user_context.get("user_id"),
                },
            )
            metrics.add_metric(
                name="PDFExtractionParseError", unit=MetricUnit.Count, value=1
            )
            return APIResponse.error(
                "AI_RESPONSE_ERROR",
                "AI service returned invalid response. Please try again.",
                502,
            )
        except ValidationError as e:
            logger.error(
                "Fund model validation error during PDF extraction",
                extra={
                    "error_type": "ValidationError",
                    "error_message": str(e),
                    "user_id": user_context.get("user_id"),
                },
            )
            metrics.add_metric(
                name="PDFExtractionValidationError", unit=MetricUnit.Count, value=1
            )
            return APIResponse.error(
                "VALIDATION_ERROR",
                "Extracted fund data failed validation",
                400,
                {"validation_errors": str(e)},
            )
        except Exception as e:
            logger.error(
                "Unexpected error during PDF extraction",
                extra={
                    "error_type": type(e).__name__,
                    "error_message": str(e),
                    "user_id": user_context.get("user_id"),
                },
            )
            metrics.add_metric(
                name="PDFExtractionUnexpectedError", unit=MetricUnit.Count, value=1
            )

            # Provide more specific error messages for common issues
            error_message = "An unexpected error occurred during PDF processing"
            if isinstance(e, OSError) and "File name too long" in str(e):
                error_message = "PDF file processing failed due to internal file handling error. Please try again."
            elif isinstance(e, OSError):
                error_message = "PDF file could not be processed due to a system error. Please try again."

            return APIResponse.error(
                "EXTRACTION_ERROR",
                error_message,
                500,
            )

    except Exception as e:
        logger.error(
            "Unexpected error in handler",
            extra={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "method": event.get("httpMethod", "Unknown"),
                "path": event.get("path", "Unknown"),
            },
        )
        metrics.add_metric(
            name="HandlerUnexpectedError", unit=MetricUnit.Count, value=1
        )
        return APIResponse.error("INTERNAL_ERROR", "An internal error occurred", 500)
