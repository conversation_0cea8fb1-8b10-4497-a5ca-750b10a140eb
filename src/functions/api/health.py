"""
Health check Lambda function.
Provides API health status and basic connectivity tests.
"""

import json
import os
from datetime import datetime, timezone
from typing import Any, Dict

from aws_lambda_powertools import Logger, Tracer, Metrics
from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_powertools.metrics import MetricUnit

from shared.api.responses import APIResponse, CORSHandler

# Initialize AWS Lambda Powertools
logger = Logger()
tracer = Tracer()
metrics = Metrics()


@logger.inject_lambda_context(log_event=True)
@tracer.capture_lambda_handler
@metrics.log_metrics(capture_cold_start_metric=True)
def handler(event: Dict[str, Any], context: LambdaContext) -> Dict[str, Any]:
    """
    Health check endpoint handler.

    Returns:
        API Gateway response with health status
    """

    # Handle CORS preflight requests
    if CORSHandler.is_options_request(event):
        return CORSHandler.handle_options()

    try:
        # Log the health check request
        logger.info(
            "Health check requested",
            extra={
                "user_agent": event.get("headers", {}).get("User-Agent", "Unknown"),
                "source_ip": event.get("requestContext", {})
                .get("identity", {})
                .get("sourceIp"),
            },
        )

        # Add custom metric for health checks
        metrics.add_metric(name="HealthCheckRequests", unit=MetricUnit.Count, value=1)

        # Perform basic connectivity checks
        health_status = perform_health_checks()

        # Determine overall status
        overall_status = "healthy" if health_status["all_checks_passed"] else "degraded"

        response_data = {
            "status": overall_status,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "version": os.getenv("API_VERSION", "1.0.0"),
            "environment": os.getenv("ENVIRONMENT", "unknown"),
            "region": os.getenv("AWS_REGION", "unknown"),
            "checks": health_status["checks"],
        }

        # Set status code based on health
        status_code = 200 if overall_status == "healthy" else 503

        logger.info(
            f"Health check completed",
            extra={
                "overall_status": overall_status,
                "checks_passed": health_status["all_checks_passed"],
            },
        )

        return APIResponse.success(data=response_data, status_code=status_code)

    except Exception as e:
        logger.exception("Health check failed", extra={"error": str(e)})

        # Add error metric
        metrics.add_metric(name="HealthCheckErrors", unit=MetricUnit.Count, value=1)

        # Create simple error response to avoid logging conflicts
        return {
            "statusCode": 500,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
            },
            "body": json.dumps(
                {
                    "error": "INTERNAL_SERVER_ERROR",
                    "message": "Health check failed",
                    "error_id": context.aws_request_id,
                }
            ),
        }


def perform_health_checks() -> Dict[str, Any]:
    """
    Perform various health checks.

    Returns:
        Dictionary with check results
    """
    checks = {}
    all_passed = True

    # Basic Lambda runtime check
    try:
        checks["lambda_runtime"] = {
            "status": "healthy",
            "message": "Lambda runtime is operational",
        }
    except Exception as e:
        checks["lambda_runtime"] = {
            "status": "unhealthy",
            "message": f"Lambda runtime issue: {str(e)}",
        }
        all_passed = False

    # Environment variables check
    try:
        required_env_vars = ["AWS_REGION"]
        missing_vars = [var for var in required_env_vars if not os.getenv(var)]

        if missing_vars:
            checks["environment"] = {
                "status": "degraded",
                "message": f'Missing environment variables: {", ".join(missing_vars)}',
            }
        else:
            checks["environment"] = {
                "status": "healthy",
                "message": "All required environment variables present",
            }
    except Exception as e:
        checks["environment"] = {
            "status": "unhealthy",
            "message": f"Environment check failed: {str(e)}",
        }
        all_passed = False

    # DynamoDB connectivity check (basic)
    try:
        # This is a basic check - in production, you might want to perform a simple DynamoDB operation
        import boto3

        dynamodb = boto3.client("dynamodb")

        # Just check if we can create the client - actual table operations would be in real health checks
        checks["dynamodb"] = {
            "status": "healthy",
            "message": "DynamoDB client initialized successfully",
        }
    except Exception as e:
        logger.warning(f"DynamoDB health check failed: {str(e)}")
        checks["dynamodb"] = {
            "status": "degraded",
            "message": f"DynamoDB connectivity issue: {str(e)}",
        }
        # Don't fail overall health for DynamoDB issues in this basic check

    # Memory and execution context check
    try:
        import psutil

        memory_info = psutil.virtual_memory()

        checks["resources"] = {
            "status": "healthy",
            "message": f"Memory usage: {memory_info.percent}%",
        }
    except ImportError:
        # psutil not available - that's fine for basic health check
        checks["resources"] = {
            "status": "healthy",
            "message": "Resource monitoring not available",
        }
    except Exception as e:
        checks["resources"] = {
            "status": "degraded",
            "message": f"Resource check failed: {str(e)}",
        }

    return {"checks": checks, "all_checks_passed": all_passed}
