"""
PDF Fund Information Extractor

This module provides functionality to extract fund information from PDF files
and convert it to the standardized Fund data model using AI models.
Supports two extraction modes:
1. Vision mode: Converts PDF to images and uses GPT-4o vision model
2. Direct mode: Sends entire PDF file directly to deepseek/deepseek-r1-0528:free model
"""

import os
import json
import logging
import base64
from typing import Optional, Dict, Any, Union, List
from pathlib import Path
from decimal import Decimal
from datetime import datetime
from pydantic import ValidationError
from aws_lambda_powertools import Logger, Tracer, Metrics
import requests


# Add the src directory to Python path to import our models
import sys

sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), "src"))

from shared.models.fund import (
    Fund,
    PerformanceMetrics,
    Holdings,
)

# Configure logging for both Lambda and local execution
# Check if we're running in Lambda environment
_is_lambda = os.getenv("AWS_LAMBDA_FUNCTION_NAME") is not None

if _is_lambda:
    # Running in Lambda - use Powertools Logger with proper service name
    _powertools_logger = Logger(service="pdf-fund-extractor")
else:
    # Running locally - use standard logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    _standard_logger = logging.getLogger(__name__)


class LoggerWrapper:
    """Wrapper to handle both Powertools Logger and standard logging."""

    def __init__(self):
        self.is_lambda = _is_lambda
        if self.is_lambda:
            self.powertools_logger = _powertools_logger
        else:
            self.standard_logger = _standard_logger

    def info(self, message: str, **kwargs):
        if self.is_lambda:
            # Powertools Logger supports structured logging
            if kwargs:
                self.powertools_logger.info(message, extra=kwargs)
            else:
                self.powertools_logger.info(message)
        else:
            # Standard logger - format kwargs into message
            if kwargs:
                extra_info = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
                self.standard_logger.info(f"{message} [{extra_info}]")
            else:
                self.standard_logger.info(message)

    def error(self, message: str, **kwargs):
        if self.is_lambda:
            if kwargs:
                self.powertools_logger.error(message, extra=kwargs)
            else:
                self.powertools_logger.error(message)
        else:
            if kwargs:
                extra_info = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
                self.standard_logger.error(f"{message} [{extra_info}]")
            else:
                self.standard_logger.error(message)

    def debug(self, message: str, **kwargs):
        if self.is_lambda:
            if kwargs:
                self.powertools_logger.debug(message, extra=kwargs)
            else:
                self.powertools_logger.debug(message)
        else:
            if kwargs:
                extra_info = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
                self.standard_logger.debug(f"{message} [{extra_info}]")
            else:
                self.standard_logger.debug(message)

    def warning(self, message: str, **kwargs):
        if self.is_lambda:
            if kwargs:
                self.powertools_logger.warning(message, extra=kwargs)
            else:
                self.powertools_logger.warning(message)
        else:
            if kwargs:
                extra_info = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
                self.standard_logger.warning(f"{message} [{extra_info}]")
            else:
                self.standard_logger.warning(message)


# Create the logger instance
logger = LoggerWrapper()


class PDFFundExtractor:
    """
    Extracts fund information from PDF files using AI models.

    Supports extraction mode:
    - Direct mode: Sends entire PDF file directly to deepseek/deepseek-r1-0528:free model
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the PDF Fund Extractor.

        Args:
            api_key: OpenRouter API key. If not provided, will read from OPENROUTER_API_KEY env var.
        """
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        if not self.api_key:
            raise ValueError(
                "OpenRouter API key is required. Set OPENROUTER_API_KEY environment variable."
            )

        self.openrouter_base_url = "https://openrouter.ai/api/v1"
        # Use a proven model for PDF processing
        # Gemini 2.5 Flash has excellent multimodal support and is well-tested for PDFs
        self.text_model = "google/gemini-2.5-flash"

        # Fallback models if the primary one fails
        self.fallback_models = [
            "google/gemini-flash-1.5",
        ]

        # Verify API key on initialization
        self.verify_api_key()

    def verify_api_key(self) -> bool:
        """
        Verify that the API key is valid by making a test request.

        Returns:
            True if API key is valid, raises exception otherwise
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        try:
            logger.info("Verifying OpenRouter API key")
            response = requests.get(
                f"{self.openrouter_base_url}/models",
                headers=headers,
                timeout=30,
            )

            if response.status_code == 401:
                raise ValueError(
                    "Invalid OpenRouter API key. Please check your OPENROUTER_API_KEY environment variable."
                )
            elif response.status_code != 200:
                logger.warning(
                    f"API key verification returned status {response.status_code}, but proceeding"
                )
            else:
                logger.info("API key verified successfully")

            return True

        except requests.RequestException as e:
            logger.warning(f"Could not verify API key (network issue): {e}")
            return True  # Assume it's valid and let the actual API call handle authentication

    def read_pdf_as_base64(self, pdf_path: Union[str, Path]) -> str:
        """
        Read PDF file and encode it as base64 for direct AI model processing.

        Args:
            pdf_path: Path to the PDF file

        Returns:
            Base64-encoded PDF content
        """
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")

        logger.info("Reading PDF file as base64", pdf_path=str(pdf_path))

        try:
            with open(pdf_path, "rb") as pdf_file:
                pdf_bytes = pdf_file.read()
                pdf_base64 = base64.b64encode(pdf_bytes).decode("utf-8")

            logger.info(
                "Successfully encoded PDF as base64",
                pdf_size_bytes=len(pdf_bytes),
                base64_size_chars=len(pdf_base64),
            )
            return pdf_base64

        except Exception as e:
            logger.error("Error reading PDF file", error=str(e), pdf_path=str(pdf_path))
            raise

    def create_extraction_prompt(self) -> str:
        """
        Create a comprehensive prompt for the AI model to extract fund information from PDF.

        Returns:
            Formatted prompt for AI processing
        """
        return """You are a financial data extraction expert. Extract fund information from this PDF document and return it as a valid JSON object that exactly matches this structure.

CRITICAL INSTRUCTIONS:
1. Return ONLY valid JSON - no markdown, no comments, no extra text
2. Use null for any missing fields (never omit required fields)
3. Generate a unique fund_id if not found (format: extracted-YYYY-fundname-hash)
4. Convert all percentages to decimal format (e.g., 5.5% becomes "5.5")
5. Ensure all financial amounts are numeric strings without currency symbols
6. Use ISO date format (YYYY-MM-DD) for dates

REQUIRED JSON STRUCTURE:
{
  "fund_id": "string (auto-generate if missing: 'extracted-2025-fundname-001')",
  "name": "string (fund name - REQUIRED)",
  "fund_type": "mutual_fund|etf|index_fund|bond_fund|money_market|equity|bond|mixed|alternative|index",
  "status": "active",
  "currency": "USD|EUR|GBP|JPY|CAD|AUD|CHF|CNY|HKD|SGD|other",
  "nav": "number_as_string|null",
  "total_assets": "number_as_string|null",
  "inception_date": "YYYY-MM-DD|null",
  "risk_level": "very_low|low|moderate|high|very_high|null",
  "fund_manager": "string|null",
  "fund_manager_photo": "string|null",
  "fund_manager_introduction": "string|null",
  "fund_manager_experience": "string|null",
  "management_company": "string|null",
  "expense_ratio": "decimal_as_string|null",
  "minimum_investment": "number_as_string|null",
  "isin": "string|null",
  "cusip": "string|null",
  "bloomberg_ticker": "string|null",
  "description": "string|null",
  "investment_objective": "string|null",
  "benchmark": "string|null",
  "performance_metrics": {
    "ytd_return": "decimal_as_string|null",
    "one_day_return": "decimal_as_string|null",
    "one_week_return": "decimal_as_string|null",
    "one_month_return": "decimal_as_string|null",
    "three_month_return": "decimal_as_string|null",
    "six_month_return": "decimal_as_string|null",
    "one_year_return": "decimal_as_string|null",
    "three_year_return": "decimal_as_string|null",
    "five_year_return": "decimal_as_string|null",
    "inception_return": "decimal_as_string|null",
    "volatility": "decimal_as_string|null",
    "sharpe_ratio": "decimal_as_string|null",
    "sortino_ratio": "decimal_as_string|null",
    "calmar_ratio": "decimal_as_string|null",
    "information_ratio": "decimal_as_string|null",
    "treynor_ratio": "decimal_as_string|null",
    "max_drawdown": "negative_decimal_as_string|null",
    "downside_deviation": "decimal_as_string|null",
    "tracking_error": "decimal_as_string|null",
    "alpha": "decimal_as_string|null",
    "beta": "decimal_as_string|null",
    "correlation": "decimal_as_string|null",
    "var_1d_95": "decimal_as_string|null",
    "var_1d_99": "decimal_as_string|null"
  },
  "holdings": {
    "top_holdings": [
      {
        "name": "string",
        "percentage": "decimal_as_string",
        "ticker": "string|null",
        "sector": "string|null"
      }
    ],
    "sector_allocation": {
      "Technology": "decimal_as_string",
      "Healthcare": "decimal_as_string",
      "Financial Services": "decimal_as_string"
    },
    "geographic_allocation": {
      "United States": "decimal_as_string",
      "Europe": "decimal_as_string",
      "Asia": "decimal_as_string"
    },
    "asset_allocation": {
      "Stocks": "decimal_as_string",
      "Bonds": "decimal_as_string",
      "Cash": "decimal_as_string"
    },
    "market_cap_allocation": {
      "Large Cap": "decimal_as_string",
      "Mid Cap": "decimal_as_string",
      "Small Cap": "decimal_as_string"
    },
    "currency_allocation": {
      "USD": "decimal_as_string",
      "EUR": "decimal_as_string"
    },
    "total_holdings_count": "integer|null",
    "holdings_concentration": "decimal_as_string|null"
  },
  "tags": ["array_of_strings"],
  "custom_fields": {}
}

EXTRACTION GUIDELINES:
- Fund Type: Classify based on investment focus (equity=stocks, bond=fixed income, mixed=balanced, etc.)
- Risk Level: Assess from investment objectives and portfolio composition
- Returns: Extract from performance tables, fact sheets, or performance sections
- Holdings: Look for top holdings tables, sector breakdowns, geographic allocations
- Expense Ratio: Find in fee structure or fund facts (as annual percentage)
- Minimum Investment: Look for investment minimums or subscription amounts
- Fund Manager: Extract manager name, photo URL (if available), introduction/bio text, and experience details
- Fund Manager Photo: Look for headshot images or manager photos - provide URL if available
- Fund Manager Introduction: Extract biographical information, education, career highlights
- Fund Manager Experience: Extract years of experience, previous roles, track record details
- Dates: Convert any date format to YYYY-MM-DD
- Financial Amounts: Remove currency symbols, use numeric strings
- Percentages: Convert to decimal format (5.25% → "5.25")

VALIDATION REQUIREMENTS:
- Ensure fund_id is unique and follows pattern if generated
- Verify fund_type matches one of the allowed values
- Check that percentage allocations don't exceed 100%
- Ensure expense_ratio is reasonable (typically 0.1% to 5%)
- Validate ISIN format if present (2 letters + 9 characters + 1 digit)
- Make sure all required fields have values (use generated/default if needed)

Return ONLY the JSON object. No additional text, explanations, or formatting."""

    def call_pdf_model_api(self, prompt: str, pdf_base64: str) -> Dict[str, Any]:
        """
        Call OpenRouter API with model to process the extraction prompt and PDF file directly.

        Args:
            prompt: The extraction prompt
            pdf_base64: Base64-encoded PDF content

        Returns:
            API response containing extracted fund data
        """
        # Try primary model first, then fallbacks
        models_to_try = [self.text_model] + self.fallback_models

        for model in models_to_try:
            try:
                logger.info(f"Trying model: {model}")
                return self._call_single_model(prompt, pdf_base64, model)
            except Exception as e:
                logger.warning(f"Model {model} failed: {e}")
                if model == models_to_try[-1]:  # Last model
                    raise e
                logger.info(f"Falling back to next model...")

        raise RuntimeError("All models failed")

    def _call_single_model(
        self, prompt: str, pdf_base64: str, model: str
    ) -> Dict[str, Any]:
        """
        Call OpenRouter API with a specific model to process the extraction prompt and PDF.

        Args:
            prompt: The extraction prompt
            pdf_base64: Base64-encoded PDF content
            model: Model to use

        Returns:
            API response containing extracted fund data
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        # Create content array with text prompt and PDF file
        # Use the correct OpenRouter PDF format
        data_url = f"data:application/pdf;base64,{pdf_base64}"

        content = [
            {"type": "text", "text": prompt},
            {
                "type": "file",
                "file": {"filename": "fund_document.pdf", "file_data": data_url},
            },
        ]

        # Add PDF processing plugins for better accuracy
        plugins = [
            {
                "id": "file-parser",
                "pdf": {
                    "engine": "pdf-text"  # Free option, fallback to mistral-ocr if needed
                },
            }
        ]

        payload = {
            "model": model,
            "messages": [{"role": "user", "content": content}],
            "plugins": plugins,
            "max_tokens": 4000,
            "temperature": 0.1,  # Low temperature for consistent extraction
        }

        try:
            logger.info(
                "Calling OpenRouter API with PDF model",
                model=model,
                pdf_size_chars=len(pdf_base64),
            )
            response = requests.post(
                f"{self.openrouter_base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=180,
            )

            # Log response status and content for debugging
            logger.info(f"Response status code: {response.status_code}")
            if response.status_code != 200:
                logger.error(f"Response content: {response.text}")

            response.raise_for_status()

            api_response = response.json()

            # Log the full API response for debugging
            logger.info(
                "OpenRouter API response received",
                status_code=response.status_code,
                response_keys=(
                    list(api_response.keys())
                    if isinstance(api_response, dict)
                    else "not_dict"
                ),
            )

            # Check for error response from OpenRouter (but only if no choices)
            if (
                isinstance(api_response, dict)
                and "error" in api_response
                and "choices" not in api_response
            ):
                error_info = api_response.get("error", {})
                error_msg = error_info.get("message", "Unknown API error")
                error_code = error_info.get("code", "unknown")
                logger.error(f"OpenRouter API error: {error_msg} (code: {error_code})")

                # Check for specific error types
                if "rate limit" in error_msg.lower():
                    raise ValueError(
                        f"OpenRouter rate limit exceeded. Please try again later."
                    )
                elif (
                    "invalid api key" in error_msg.lower()
                    or "unauthorized" in error_msg.lower()
                ):
                    raise ValueError(
                        f"Invalid OpenRouter API key. Please check your OPENROUTER_API_KEY."
                    )
                elif "model not found" in error_msg.lower():
                    raise ValueError(
                        f"Model '{model}' not available. Please check model name."
                    )
                else:
                    raise ValueError(f"OpenRouter API error: {error_msg}")

            # Check if the response has the expected structure
            if not isinstance(api_response, dict):
                logger.error(
                    "API response is not a dictionary",
                    response_type=type(api_response).__name__,
                )
                raise ValueError(
                    f"Invalid API response format: expected dict, got {type(api_response).__name__}"
                )

            if "choices" not in api_response:
                logger.error(
                    "API response missing 'choices' field",
                    available_fields=list(api_response.keys()),
                    full_response=api_response,
                )
                raise ValueError(
                    f"API response missing 'choices' field. Available fields: {list(api_response.keys())}"
                )

            if not api_response["choices"]:
                logger.error("API response 'choices' field is empty")
                raise ValueError("API response 'choices' field is empty")

            if "message" not in api_response["choices"][0]:
                logger.error(
                    "API response choice missing 'message' field",
                    choice_fields=list(api_response["choices"][0].keys()),
                )
                raise ValueError("API response choice missing 'message' field")

            if "content" not in api_response["choices"][0]["message"]:
                logger.error(
                    "API response message missing 'content' field",
                    message_fields=list(api_response["choices"][0]["message"].keys()),
                )
                raise ValueError("API response message missing 'content' field")

            response_content = api_response["choices"][0]["message"]["content"]

            # Log the response content for debugging
            logger.info(f"AI Response content length: {len(response_content)}")
            logger.debug(f"AI Response content: {response_content}")

            # Clean the response content to extract JSON
            # Remove markdown code blocks if present
            if "```json" in response_content:
                # Extract JSON from markdown code block
                start = response_content.find("```json") + 7
                end = response_content.find("```", start)
                if end != -1:
                    response_content = response_content[start:end].strip()
            elif "```" in response_content:
                # Handle generic code blocks
                start = response_content.find("```") + 3
                end = response_content.find("```", start)
                if end != -1:
                    response_content = response_content[start:end].strip()

            # Clean any leading/trailing text that might not be JSON
            response_content = response_content.strip()

            # Find the first { and last } to isolate JSON object
            start_brace = response_content.find("{")
            end_brace = response_content.rfind("}")

            if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
                response_content = response_content[start_brace : end_brace + 1]

            # Parse the JSON response
            try:
                fund_data = json.loads(response_content)
            except json.JSONDecodeError as json_error:
                # Try to fix common JSON truncation issues
                logger.warning(
                    "Initial JSON parsing failed, attempting to fix truncated JSON"
                )
                fixed_content = self._attempt_json_fix(response_content)
                if fixed_content:
                    try:
                        fund_data = json.loads(fixed_content)
                        logger.info("Successfully parsed JSON after fixing truncation")
                    except json.JSONDecodeError:
                        logger.error("Failed to fix truncated JSON")
                        raise json_error
                else:
                    raise json_error

            logger.info(
                "Successfully extracted fund data from AI PDF response",
                response_length=len(response_content),
                fund_name=fund_data.get("name", "Unknown"),
                fund_id=fund_data.get("fund_id", "Unknown"),
                model=model,
            )
            return fund_data

        except requests.RequestException as e:
            logger.error("Error calling OpenRouter API", error=str(e), model=model)
            raise
        except json.JSONDecodeError as e:
            logger.error("Error parsing AI response as JSON", error=str(e))
            logger.error(
                "JSON parsing error details",
                error_msg=str(e),
                error_pos=getattr(e, "pos", "unknown"),
                response_length=len(response_content),
            )

            # Log the full response content for debugging (temporarily)
            logger.error(f"Full response content: {response_content}")
            raise

    def _attempt_json_fix(self, content: str) -> Optional[str]:
        """
        Attempt to fix common JSON truncation issues.

        Args:
            content: The potentially truncated JSON content

        Returns:
            Fixed JSON string if successful, None otherwise
        """
        try:
            # Common fixes for truncated JSON
            fixed_content = content.strip()

            # Remove any trailing text that's not part of JSON
            # Find the main JSON object boundaries
            first_brace = fixed_content.find("{")
            if first_brace == -1:
                return None

            # Find the last valid closing brace position
            last_brace = fixed_content.rfind("}")
            if last_brace != -1 and last_brace > first_brace:
                # Extract only the JSON part
                fixed_content = fixed_content[first_brace : last_brace + 1]
            else:
                # No closing brace found, work with what we have
                fixed_content = fixed_content[first_brace:]

            # Handle incomplete strings - look for unclosed quotes
            quote_count = fixed_content.count('"')
            # If odd number of quotes, we have an unclosed string
            if quote_count % 2 == 1:
                # Find the last quote and see if it's opening a value
                last_quote_idx = fixed_content.rfind('"')
                if last_quote_idx != -1:
                    # Check what comes before the quote
                    before_quote = fixed_content[:last_quote_idx]
                    # If it ends with ": " it's opening a value, close it
                    if before_quote.endswith(": "):
                        fixed_content += '"'
                    # If it's in the middle of a string value, try to close it properly
                    elif before_quote.endswith('"') is False:
                        # Remove the incomplete part after the last complete quote
                        prev_quote = before_quote.rfind('"')
                        if prev_quote != -1:
                            # Find the colon before this quote pair to see if we're in a value
                            colon_pos = before_quote.rfind(":", 0, prev_quote)
                            if colon_pos != -1:
                                # We're in a value, close the string at the current position
                                fixed_content += '"'

            # Count and balance braces and brackets
            open_braces = fixed_content.count("{")
            close_braces = fixed_content.count("}")
            open_brackets = fixed_content.count("[")
            close_brackets = fixed_content.count("]")

            # Add missing closing brackets first (they might be inside object)
            missing_brackets = open_brackets - close_brackets
            if missing_brackets > 0:
                fixed_content += "]" * missing_brackets

            # Add missing closing braces
            missing_braces = open_braces - close_braces
            if missing_braces > 0:
                fixed_content += "}" * missing_braces

            # Handle incomplete last field - remove trailing comma and incomplete fields
            # This is a common issue when JSON is truncated mid-field
            while fixed_content.endswith(",}") or fixed_content.endswith(", }"):
                # Remove trailing comma before closing brace
                if fixed_content.endswith(",}"):
                    fixed_content = fixed_content[:-2] + "}"
                elif fixed_content.endswith(", }"):
                    fixed_content = fixed_content[:-3] + "}"

            # Try to parse the fixed content
            json.loads(fixed_content)
            logger.info("Successfully fixed truncated JSON")
            return fixed_content

        except (json.JSONDecodeError, Exception) as e:
            logger.debug(f"JSON fix attempt failed: {e}")
            return None

    def convert_floats_to_decimal(self, obj: Any) -> Any:
        """
        Recursively convert all float values to Decimal in a nested data structure.

        Args:
            obj: The object to convert (dict, list, or primitive)

        Returns:
            The object with all floats converted to Decimal
        """
        if isinstance(obj, float):
            return Decimal(str(obj))
        elif isinstance(obj, dict):
            return {k: self.convert_floats_to_decimal(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.convert_floats_to_decimal(item) for item in obj]
        else:
            return obj

    def convert_to_fund_model(self, fund_data: Dict[str, Any]) -> Fund:
        """
        Convert extracted fund data to Fund model.

        Args:
            fund_data: Dictionary containing extracted fund data

        Returns:
            Fund model instance
        """
        try:
            logger.info(
                "Converting extracted data to Fund model",
                fund_name=fund_data.get("name", "Unknown"),
            )

            # First, recursively convert all floats to Decimal
            fund_data = self.convert_floats_to_decimal(fund_data)

            # Generate fund_id if missing or empty
            if not fund_data.get("fund_id"):
                import hashlib

                fund_name = fund_data.get("name") or "unknown-fund"
                # Create a hash from fund name with more characters for uniqueness
                name_hash = hashlib.md5(fund_name.lower().encode()).hexdigest()[:8]
                # Clean fund name for ID generation - allow more characters
                clean_name = "".join(c for c in fund_name.lower() if c.isalnum())[:20]
                # Format: ext-{clean_name}-{hash} to fit within 40 character limit
                fund_data["fund_id"] = f"ext-{clean_name}-{name_hash}"

                # Ensure the generated ID doesn't exceed 40 characters
                if len(fund_data["fund_id"]) > 40:
                    # Calculate available space for clean_name
                    available_name_chars = 40 - len(f"ext--{name_hash}")
                    clean_name = clean_name[:available_name_chars]
                    fund_data["fund_id"] = f"ext-{clean_name}-{name_hash}"

                logger.info("Generated fund_id", fund_id=fund_data["fund_id"])

            # Handle decimal conversions for specific fields (in case they're strings)
            decimal_fields = [
                "nav",
                "total_assets",
                "expense_ratio",
                "minimum_investment",
            ]
            for field in decimal_fields:
                if fund_data.get(field) is not None and not isinstance(
                    fund_data[field], Decimal
                ):
                    try:
                        # Handle string numbers (remove any currency symbols or commas)
                        value_str = (
                            str(fund_data[field])
                            .replace(",", "")
                            .replace("$", "")
                            .replace("€", "")
                            .replace("£", "")
                            .strip()
                        )
                        if value_str and value_str != "null":
                            fund_data[field] = Decimal(value_str)
                        else:
                            fund_data[field] = None
                    except (ValueError, TypeError):
                        logger.warning(
                            f"Could not convert {field} to Decimal",
                            value=fund_data[field],
                        )
                        fund_data[field] = None

            # Handle performance metrics - get all expected fields
            perf_fields = [
                "ytd_return",
                "one_day_return",
                "one_week_return",
                "one_month_return",
                "three_month_return",
                "six_month_return",
                "one_year_return",
                "three_year_return",
                "five_year_return",
                "inception_return",
                "volatility",
                "sharpe_ratio",
                "sortino_ratio",
                "calmar_ratio",
                "information_ratio",
                "treynor_ratio",
                "max_drawdown",
                "downside_deviation",
                "tracking_error",
                "alpha",
                "beta",
                "correlation",
                "var_1d_95",
                "var_1d_99",
            ]

            # Check if performance metrics are provided as flat fields (legacy support)
            perf_data = {}
            for field in perf_fields:
                if fund_data.get(field) is not None:
                    value = fund_data.pop(field)
                    try:
                        if value is not None and str(value) != "null":
                            perf_data[field] = (
                                value
                                if isinstance(value, Decimal)
                                else Decimal(str(value))
                            )
                    except (ValueError, TypeError):
                        logger.warning(
                            f"Could not convert performance metric {field} to Decimal",
                            value=value,
                        )

            # Handle nested performance metrics (preferred structure)
            if fund_data.get("performance_metrics"):
                nested_perf = fund_data["performance_metrics"]
                if isinstance(nested_perf, dict):
                    for field in perf_fields:
                        if nested_perf.get(field) is not None:
                            value = nested_perf[field]
                            try:
                                if value is not None and str(value) != "null":
                                    perf_data[field] = (
                                        value
                                        if isinstance(value, Decimal)
                                        else Decimal(str(value))
                                    )
                            except (ValueError, TypeError):
                                logger.warning(
                                    f"Could not convert nested performance metric {field} to Decimal",
                                    value=value,
                                )

            # Create PerformanceMetrics object if we have any performance data
            if perf_data:
                fund_data["performance_metrics"] = PerformanceMetrics(**perf_data)
            else:
                fund_data["performance_metrics"] = None

            # Handle holdings - get all expected fields
            holdings_fields = [
                "top_holdings",
                "sector_allocation",
                "geographic_allocation",
                "asset_allocation",
                "market_cap_allocation",
                "currency_allocation",
                "total_holdings_count",
                "holdings_concentration",
            ]
            holdings_data = {}

            # Check if holdings data is provided as flat fields (legacy support)
            for field in holdings_fields:
                if fund_data.get(field) is not None:
                    holdings_data[field] = fund_data.pop(field)

            # Handle nested holdings (preferred structure)
            if fund_data.get("holdings"):
                nested_holdings = fund_data["holdings"]
                if isinstance(nested_holdings, dict):
                    for field in holdings_fields:
                        if nested_holdings.get(field) is not None:
                            holdings_data[field] = nested_holdings[field]

            # Process holdings data if we have any
            if holdings_data:
                # Handle top_holdings list - clean up percentage values
                if holdings_data.get("top_holdings"):
                    top_holdings = holdings_data["top_holdings"]
                    if isinstance(top_holdings, list):
                        cleaned_holdings = []
                        for holding in top_holdings:
                            if isinstance(holding, dict):
                                cleaned_holding = holding.copy()
                                # Handle percentage field conversion
                                if "percentage" in cleaned_holding:
                                    pct_value = cleaned_holding["percentage"]
                                    if (
                                        pct_value is not None
                                        and str(pct_value) != "null"
                                    ):
                                        try:
                                            # Convert to Decimal for validation, but keep as string in dict
                                            decimal_val = Decimal(str(pct_value))
                                            cleaned_holding["percentage"] = str(
                                                decimal_val
                                            )
                                        except (ValueError, TypeError):
                                            logger.warning(
                                                "Could not convert holding percentage to Decimal",
                                                holding_name=holding.get(
                                                    "name", "Unknown"
                                                ),
                                                percentage=pct_value,
                                            )
                                            # Remove invalid percentage rather than keeping null
                                            cleaned_holding.pop("percentage", None)
                                    else:
                                        # Remove null/None percentage values
                                        cleaned_holding.pop("percentage", None)
                                cleaned_holdings.append(cleaned_holding)
                        holdings_data["top_holdings"] = cleaned_holdings

                # Convert allocation fields to dictionaries with Decimal values
                allocation_fields = [
                    "sector_allocation",
                    "geographic_allocation",
                    "asset_allocation",
                    "market_cap_allocation",
                    "currency_allocation",
                ]

                for alloc_field in allocation_fields:
                    if holdings_data.get(alloc_field):
                        alloc_data = holdings_data[alloc_field]
                        if isinstance(alloc_data, list):
                            # Convert list of {sector/region: name, percentage: value} to dict
                            converted_dict = {}
                            for item in alloc_data:
                                if isinstance(item, dict):
                                    key = item.get(
                                        "sector",
                                        item.get("region", item.get("name", "Unknown")),
                                    )
                                    percentage = item.get("percentage")
                                    if key and percentage is not None:
                                        try:
                                            converted_dict[key] = (
                                                Decimal(str(percentage))
                                                if not isinstance(percentage, Decimal)
                                                else percentage
                                            )
                                        except (ValueError, TypeError):
                                            logger.warning(
                                                f"Could not convert {alloc_field} percentage to Decimal",
                                                key=key,
                                                percentage=percentage,
                                            )
                            holdings_data[alloc_field] = converted_dict
                        elif isinstance(alloc_data, dict):
                            # Convert dict values to Decimal if not already
                            converted_dict = {}
                            for k, v in alloc_data.items():
                                try:
                                    if v is not None and str(v) != "null":
                                        converted_dict[k] = (
                                            v
                                            if isinstance(v, Decimal)
                                            else Decimal(str(v))
                                        )
                                except (ValueError, TypeError):
                                    logger.warning(
                                        f"Could not convert {alloc_field} value to Decimal",
                                        key=k,
                                        value=v,
                                    )
                            holdings_data[alloc_field] = converted_dict

                # Handle holdings concentration as Decimal
                if holdings_data.get("holdings_concentration") is not None:
                    try:
                        value = holdings_data["holdings_concentration"]
                        if value is not None and str(value) != "null":
                            holdings_data["holdings_concentration"] = (
                                value
                                if isinstance(value, Decimal)
                                else Decimal(str(value))
                            )
                    except (ValueError, TypeError):
                        logger.warning(
                            "Could not convert holdings_concentration to Decimal",
                            value=holdings_data["holdings_concentration"],
                        )
                        holdings_data["holdings_concentration"] = None

                # Handle total_holdings_count as integer
                if holdings_data.get("total_holdings_count") is not None:
                    try:
                        value = holdings_data["total_holdings_count"]
                        if value is not None and str(value) != "null":
                            holdings_data["total_holdings_count"] = int(value)
                    except (ValueError, TypeError):
                        logger.warning(
                            "Could not convert total_holdings_count to integer",
                            value=holdings_data["total_holdings_count"],
                        )
                        holdings_data["total_holdings_count"] = None

                fund_data["holdings"] = Holdings(**holdings_data)
            else:
                fund_data["holdings"] = None

            # Handle date conversion
            if fund_data.get("inception_date"):
                if isinstance(fund_data["inception_date"], str):
                    date_str = fund_data["inception_date"].strip()
                    if date_str and date_str != "null":
                        try:
                            # Handle different date formats
                            if "T" in date_str:
                                # Full datetime string
                                fund_data["inception_date"] = datetime.fromisoformat(
                                    date_str.replace("Z", "+00:00")
                                )
                            else:
                                # Simple date string (YYYY-MM-DD)
                                # Parse as date and convert to timezone-aware datetime
                                import datetime as dt

                                parsed_date = dt.datetime.fromisoformat(date_str)
                                fund_data["inception_date"] = parsed_date.replace(
                                    tzinfo=dt.timezone.utc
                                )
                        except (ValueError, TypeError) as e:
                            logger.warning(
                                "Could not parse inception_date",
                                date_str=date_str,
                                error=str(e),
                            )
                            fund_data["inception_date"] = None
                    else:
                        fund_data["inception_date"] = None

            # Ensure required fields have reasonable defaults
            if not fund_data.get("name"):
                fund_data["name"] = "Extracted Fund"
                logger.warning("Fund name was missing, using default")

            if not fund_data.get("fund_type"):
                fund_data["fund_type"] = "mutual_fund"  # Safe default
                logger.warning("Fund type was missing, using 'mutual_fund' as default")

            # Ensure fund_type is valid
            valid_fund_types = [
                # Primary frontend types
                "mutual_fund",
                "etf",
                "index_fund",
                "bond_fund",
                "money_market",
                # Legacy backend types (for compatibility)
                "equity",
                "bond",
                "mixed",
                "alternative",
                "index",
            ]
            if fund_data.get("fund_type") not in valid_fund_types:
                logger.warning(
                    "Invalid fund_type, using 'mutual_fund' as fallback",
                    fund_type=fund_data.get("fund_type"),
                )
                fund_data["fund_type"] = "mutual_fund"

            # Handle currency validation - ensure it's a valid currency or use USD as default
            valid_currencies = [
                "USD",
                "EUR",
                "GBP",
                "JPY",
                "CAD",
                "AUD",
                "CHF",
                "CNY",
                "HKD",
                "SGD",
                "KRW",
                "TWD",
                "INR",
                "IDR",
                "MYR",
                "PHP",
                "THB",
                "VND",
                "ZAR",
                "RUB",
                "TRY",
                "MXN",
                "PEN",
                "BRL",
                "CLP",
                "COP",
                "KZT",
                "UAH",
                "PLN",
                "HUF",
                "CZK",
                "NOK",
                "SEK",
                "ILS",
                "AED",
                "SAR",
                "QAR",
                "OMR",
                "KWD",
                "BHD",
                "JOD",
                "LYD",
                "DZD",
                "TND",
            ]
            current_currency = fund_data.get("currency")
            if current_currency and current_currency.upper() in valid_currencies:
                fund_data["currency"] = current_currency.upper()
            elif current_currency and current_currency.upper() not in valid_currencies:
                logger.warning(
                    "Invalid currency, using USD as fallback", currency=current_currency
                )
                fund_data["currency"] = "USD"
            elif not current_currency:
                fund_data["currency"] = "USD"  # Default to USD

            # Ensure status is set
            if not fund_data.get("status"):
                fund_data["status"] = "active"

            # Handle tags field
            if not fund_data.get("tags"):
                fund_data["tags"] = []
            elif isinstance(fund_data["tags"], str):
                # If tags is a string, convert to list
                fund_data["tags"] = [
                    tag.strip() for tag in fund_data["tags"].split(",") if tag.strip()
                ]

            # Ensure description is within 900 character limit
            if fund_data.get("description") and len(fund_data["description"]) > 900:
                fund_data["description"] = fund_data["description"][:900]
                logger.info("Truncated description to 900 characters")

            # Handle custom_fields
            if not fund_data.get("custom_fields"):
                fund_data["custom_fields"] = {}

            # Create Fund instance
            fund = Fund(**fund_data)
            logger.info(
                "Successfully created Fund model",
                fund_name=fund.name,
                fund_id=fund.fund_id,
                fund_type=fund.fund_type,
            )
            return fund

        except ValidationError as e:
            logger.error("Validation error creating Fund model", error=str(e))
            raise
        except Exception as e:
            logger.error("Error converting to Fund model", error=str(e))
            raise

    def extract_fund_from_pdf_direct(
        self,
        pdf_input: Union[str, Path],
        output_path: Optional[Union[str, Path]] = None,
    ) -> Fund:
        """
        Extract fund information from a PDF file by sending the entire PDF directly to AI model.
        This method sends the PDF file as binary data to the AI model without any preprocessing.

        Args:
            pdf_input: Path to the PDF file or base64-encoded PDF content
            output_path: Optional path to save the extracted data as JSON

        Returns:
            Fund model instance with extracted data
        """
        # Check if input is a file path or base64 content
        # Base64 strings are typically very long and don't contain path separators
        # File paths are typically short and contain path separators or end with .pdf
        is_file_path = False

        if isinstance(pdf_input, (str, Path)):
            pdf_input_str = str(pdf_input)
            # Check if it looks like a file path (short, contains path separators, or ends with .pdf)
            if len(pdf_input_str) < 500 and (  # Reasonable path length
                pdf_input_str.endswith(".pdf")
                or "/" in pdf_input_str
                or "\\" in pdf_input_str
            ):
                # Additional check: only call exists() if it looks like a reasonable path
                try:
                    is_file_path = Path(pdf_input_str).exists()
                except (OSError, ValueError):
                    # If Path creation fails (e.g., filename too long), it's not a valid path
                    is_file_path = False

        if is_file_path:
            # Input is a file path
            logger.info(
                "Starting direct PDF fund extraction from file", pdf_path=str(pdf_input)
            )
            pdf_base64 = self.read_pdf_as_base64(pdf_input)
        else:
            # Input is base64-encoded PDF content
            logger.info(
                "Starting direct PDF fund extraction from base64 data",
                data_size_chars=len(str(pdf_input)),
            )
            pdf_base64 = str(pdf_input)

        # Create extraction prompt
        prompt = self.create_extraction_prompt()

        # Call AI API with PDF file directly
        fund_data = self.call_pdf_model_api(prompt, pdf_base64)

        # Convert to Fund model
        fund = self.convert_to_fund_model(fund_data)

        # Save to file if requested
        if output_path:
            output_path = Path(output_path)
            output_data = fund.model_dump(mode="json")

            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)

            logger.info(
                "Saved extracted fund data to file", output_path=str(output_path)
            )

        logger.info(
            "Direct PDF fund extraction completed successfully",
            fund_name=fund.name,
            fund_id=fund.fund_id,
        )
        return fund


def main():
    """
    Extract fund information from hardcoded PDF file.
    """
    # Hardcoded parameters
    pdf_path = "/Users/<USER>/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"
    pdf_path = "/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"
    output_path = "extracted_fund_data.json"
    #  use environment variable OPENROUTER_API_KEY
    api_key = os.getenv("OPENROUTER_API_KEY")

    verbose = True  # Enable verbose logging

    # Configure logging
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Initialize extractor (API key from environment variable)
        extractor = PDFFundExtractor(api_key=api_key)

        # Always use direct PDF mode (deepseek model)
        fund = extractor.extract_fund_from_pdf_direct(pdf_path, output_path)

        # Print summary
        print(f"\n✅ Successfully extracted fund information:")
        print(f"   Fund ID: {fund.fund_id}")
        print(f"   Name: {fund.name}")
        print(f"   Type: {fund.fund_type}")
        print(f"   Currency: {fund.currency}")
        if fund.total_assets:
            print(f"   Total Assets: {fund.total_assets:,.2f} {fund.currency}")
        if fund.nav:
            print(f"   NAV: {fund.nav}")

        print(f"   Saved to: {output_path}")

    except Exception as e:
        logger.error("Extraction failed", error=str(e))
        return 1

    return 0


if __name__ == "__main__":
    main()
