"""
Portfolio Management API endpoints.
Provides CRUD operations for portfolio entities with JWT authentication.
"""

import json
from typing import Dict, Any

from aws_lambda_powertools import Logger, Tracer, Metrics
from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_powertools.metrics import MetricUnit
from pydantic import ValidationError

from shared.models.portfolio import (
    PortfolioCreateRequest,
    PortfolioUpdateRequest,
    AddHoldingRequest,
    AddTransactionRequest,
)
from shared.api.responses import APIResponse, CORSHandler
from shared.database import get_portfolio_repository
from shared.api.auth_dependencies import create_user_context
from shared.security.session_manager import SessionManager

# Initialize AWS Lambda Powertools
logger = Logger()
tracer = Tracer()
metrics = Metrics()


@logger.inject_lambda_context(log_event=True)
@tracer.capture_lambda_handler
@metrics.log_metrics
def handler(event: Dict[str, Any], context: LambdaContext) -> Dict[str, Any]:
    """
    Lambda handler for portfolio management operations.

    Args:
        event: API Gateway event
        context: Lambda context (used by decorators)

    Returns:
        API Gateway response
    """
    # Context is used by AWS Lambda Powertools decorators
    _ = context  # Used by decorators

    # Handle CORS preflight requests
    if CORSHandler.is_options_request(event):
        return CORSHandler.handle_options()

    try:
        # Extract request info
        method = event.get("httpMethod", "")
        path = event.get("path", "")

        logger.info(f"Processing {method} request to {path}")

        # Route requests based on method and path
        if method == "GET":
            if path.endswith("/portfolios") or "/portfolios?" in path:
                # GET /portfolios - List portfolios with optional filters
                return handle_list_portfolios(event)
            elif "/portfolios/" in path:
                # GET /portfolios/{portfolio_id} - Get specific portfolio
                return handle_get_portfolio(event)
            else:
                logger.warning(f"Unsupported GET path: {path}")
                return APIResponse.not_found("Endpoint not found")

        elif method == "POST":
            if path.endswith("/portfolios"):
                # POST /portfolios - Create new portfolio
                return handle_create_portfolio(event)
            elif "/holdings" in path:
                # POST /portfolios/{portfolio_id}/holdings - Add holding
                return handle_add_holding(event)
            elif "/transactions" in path:
                # POST /portfolios/{portfolio_id}/transactions - Add transaction
                return handle_add_transaction(event)
            else:
                logger.warning(f"Unsupported POST path: {path}")
                return APIResponse.not_found("Endpoint not found")

        elif method == "PUT":
            if "/portfolios/" in path:
                # PUT /portfolios/{portfolio_id} - Update portfolio
                return handle_update_portfolio(event)
            else:
                logger.warning(f"Unsupported PUT path: {path}")
                return APIResponse.not_found("Endpoint not found")

        elif method == "DELETE":
            if "/portfolios/" in path:
                # DELETE /portfolios/{portfolio_id} - Delete portfolio
                return handle_delete_portfolio(event)
            else:
                logger.warning(f"Unsupported DELETE path: {path}")
                return APIResponse.not_found("Endpoint not found")

        else:
            logger.warning(f"Unsupported HTTP method: {method}")
            return APIResponse.error("METHOD_NOT_ALLOWED", f"Method {method} not allowed", 405)

    except Exception:
        logger.exception("Error in portfolio management handler")
        metrics.add_metric(name="PortfolioAPIErrors", unit=MetricUnit.Count, value=1)
        # Create simple error response to avoid logging conflicts
        return {
            "statusCode": 500,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
            },
            "body": json.dumps(
                {"error": "INTERNAL_SERVER_ERROR", "message": "Internal server error"}
            ),
        }


@tracer.capture_method
def handle_list_portfolios(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle GET /portfolios - List portfolios with optional filters and pagination."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Validate user_id is present
        user_id = user_context.get("user_id")
        if not user_id:
            return APIResponse.unauthorized("User ID not found in session")

        logger.info(
            "Processing list portfolios request",
            extra={"user_id": user_id},
        )

        # Parse query parameters
        query_params = event.get("queryStringParameters") or {}

        # Get portfolio repository
        portfolio_repo = get_portfolio_repository()

        # List portfolios for the user
        portfolios_result = portfolio_repo.list_portfolios(
            user_id=user_id,
            limit=int(query_params.get("page_size", 20)),
            status=query_params.get("status"),
            portfolio_type=query_params.get("portfolio_type"),
            search=query_params.get("search"),
        )

        # Convert portfolios to response format
        portfolio_responses = portfolios_result["portfolios"]

        response_data = {
            "portfolios": [portfolio.dict() for portfolio in portfolio_responses],
            "pagination": {
                "page": int(query_params.get("page", 1)),
                "page_size": int(query_params.get("page_size", 20)),
                "total_count": portfolios_result["count"],
                "has_more": portfolios_result.get("has_more", False),
            },
            "filters_applied": {
                "status": query_params.get("status"),
                "portfolio_type": query_params.get("portfolio_type"),
                "search": query_params.get("search"),
            },
        }

        metrics.add_metric(name="PortfoliosListed", unit=MetricUnit.Count, value=len(portfolio_responses))

        return APIResponse.success(
            data=response_data,
            message=f"Retrieved {len(portfolio_responses)} portfolios successfully",
        )

    except Exception:
        logger.exception("Error listing portfolios")
        metrics.add_metric(name="PortfolioListErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.error("INTERNAL_SERVER_ERROR", "Failed to retrieve portfolios", 500)


@tracer.capture_method
def handle_get_portfolio(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle GET /portfolios/{portfolio_id} - Get specific portfolio."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Validate user_id is present
        user_id = user_context.get("user_id")
        if not user_id:
            return APIResponse.unauthorized("User ID not found in session")

        # Extract portfolio ID from path
        path_params = event.get("pathParameters") or {}
        portfolio_id = path_params.get("portfolio_id")

        if not portfolio_id:
            return APIResponse.validation_error("Portfolio ID is required")

        logger.info(
            "Processing get portfolio request",
            extra={"user_id": user_id, "portfolio_id": portfolio_id},
        )

        # Get portfolio repository
        portfolio_repo = get_portfolio_repository()

        # Get the portfolio
        portfolio = portfolio_repo.get_portfolio(portfolio_id)

        if not portfolio:
            return APIResponse.not_found("Portfolio not found")

        # Check if user has access to this portfolio
        if portfolio.user_id != user_id:
            return APIResponse.forbidden("Access denied to this portfolio")

        metrics.add_metric(name="PortfolioRetrieved", unit=MetricUnit.Count, value=1)

        return APIResponse.success(
            data=portfolio.model_dump(),
            message="Portfolio retrieved successfully",
        )

    except Exception:
        logger.exception("Error retrieving portfolio")
        metrics.add_metric(name="PortfolioGetErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.error("INTERNAL_SERVER_ERROR", "Failed to retrieve portfolio", 500)


@tracer.capture_method
def handle_create_portfolio(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST /portfolios - Create new portfolio."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Validate user_id is present
        user_id = user_context.get("user_id")
        if not user_id:
            return APIResponse.unauthorized("User ID not found in session")

        logger.info(
            "Processing create portfolio request",
            extra={"user_id": user_id},
        )

        # Parse request body
        try:
            body = json.loads(event.get("body", "{}"))
            create_request = PortfolioCreateRequest(**body)
        except (json.JSONDecodeError, ValidationError) as e:
            logger.warning(f"Invalid request body: {e}")
            return APIResponse.validation_error("Invalid request body", [{"field": "body", "message": str(e)}])

        # Get portfolio repository
        portfolio_repo = get_portfolio_repository()

        # Create the portfolio
        portfolio = portfolio_repo.create_portfolio(create_request, user_id)

        metrics.add_metric(name="PortfolioCreated", unit=MetricUnit.Count, value=1)

        return APIResponse.success(
            data=portfolio.model_dump(),
            message="Portfolio created successfully",
            status_code=201,
        )

    except Exception:
        logger.exception("Error creating portfolio")
        metrics.add_metric(name="PortfolioCreateErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.error("INTERNAL_SERVER_ERROR", "Failed to create portfolio", 500)


@tracer.capture_method
def handle_update_portfolio(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle PUT /portfolios/{portfolio_id} - Update portfolio."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Validate user_id is present
        user_id = user_context.get("user_id")
        if not user_id:
            return APIResponse.unauthorized("User ID not found in session")

        # Extract portfolio ID from path
        path_params = event.get("pathParameters") or {}
        portfolio_id = path_params.get("portfolio_id")

        if not portfolio_id:
            return APIResponse.validation_error("Portfolio ID is required")

        logger.info(
            "Processing update portfolio request",
            extra={"user_id": user_id, "portfolio_id": portfolio_id},
        )

        # Parse request body
        try:
            body = json.loads(event.get("body", "{}"))
            update_request = PortfolioUpdateRequest(**body)
        except (json.JSONDecodeError, ValidationError) as e:
            logger.warning(f"Invalid request body: {e}")
            return APIResponse.validation_error("Invalid request body", [{"field": "body", "message": str(e)}])

        # Get portfolio repository
        portfolio_repo = get_portfolio_repository()

        # Check if portfolio exists and user has access
        existing_portfolio = portfolio_repo.get_portfolio(portfolio_id)
        if not existing_portfolio:
            return APIResponse.not_found("Portfolio not found")

        if existing_portfolio.user_id != user_id:
            return APIResponse.forbidden("Access denied to this portfolio")

        # Update the portfolio
        updated_portfolio = portfolio_repo.update_portfolio(portfolio_id, update_request, user_id)

        metrics.add_metric(name="PortfolioUpdated", unit=MetricUnit.Count, value=1)

        return APIResponse.success(
            data=updated_portfolio.model_dump(),
            message="Portfolio updated successfully",
        )

    except Exception:
        logger.exception("Error updating portfolio")
        metrics.add_metric(name="PortfolioUpdateErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.error("INTERNAL_SERVER_ERROR", "Failed to update portfolio", 500)


@tracer.capture_method
def handle_delete_portfolio(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle DELETE /portfolios/{portfolio_id} - Delete portfolio."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Validate user_id is present
        user_id = user_context.get("user_id")
        if not user_id:
            return APIResponse.unauthorized("User ID not found in session")

        # Extract portfolio ID from path
        path_params = event.get("pathParameters") or {}
        portfolio_id = path_params.get("portfolio_id")

        if not portfolio_id:
            return APIResponse.validation_error("Portfolio ID is required")

        logger.info(
            "Processing delete portfolio request",
            extra={"user_id": user_id, "portfolio_id": portfolio_id},
        )

        # Get portfolio repository
        portfolio_repo = get_portfolio_repository()

        # Check if portfolio exists and user has access
        existing_portfolio = portfolio_repo.get_portfolio(portfolio_id)
        if not existing_portfolio:
            return APIResponse.not_found("Portfolio not found")

        if existing_portfolio.user_id != user_id:
            return APIResponse.forbidden("Access denied to this portfolio")

        # Delete the portfolio
        success = portfolio_repo.delete_portfolio(portfolio_id)

        if success:
            metrics.add_metric(name="PortfolioDeleted", unit=MetricUnit.Count, value=1)
            return APIResponse.success(
                data={"deleted": True},
                message="Portfolio deleted successfully",
            )
        else:
            return APIResponse.error("INTERNAL_SERVER_ERROR", "Failed to delete portfolio", 500)

    except Exception:
        logger.exception("Error deleting portfolio")
        metrics.add_metric(name="PortfolioDeleteErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.error("INTERNAL_SERVER_ERROR", "Failed to delete portfolio", 500)


@tracer.capture_method
def handle_add_holding(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST /portfolios/{portfolio_id}/holdings - Add holding to portfolio."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Validate user_id is present
        user_id = user_context.get("user_id")
        if not user_id:
            return APIResponse.unauthorized("User ID not found in session")

        # Extract portfolio ID from path
        path_params = event.get("pathParameters") or {}
        portfolio_id = path_params.get("portfolio_id")

        if not portfolio_id:
            return APIResponse.validation_error("Portfolio ID is required")

        logger.info(
            "Processing add holding request",
            extra={"user_id": user_id, "portfolio_id": portfolio_id},
        )

        # Parse request body
        try:
            body = json.loads(event.get("body", "{}"))
            holding_request = AddHoldingRequest(**body)
        except (json.JSONDecodeError, ValidationError) as e:
            logger.warning(f"Invalid request body: {e}")
            return APIResponse.validation_error("Invalid request body", [{"field": "body", "message": str(e)}])

        # Get portfolio repository
        portfolio_repo = get_portfolio_repository()

        # Check if portfolio exists and user has access
        existing_portfolio = portfolio_repo.get_portfolio(portfolio_id)
        if not existing_portfolio:
            return APIResponse.not_found("Portfolio not found")

        if existing_portfolio.user_id != user_id:
            return APIResponse.forbidden("Access denied to this portfolio")

        # Add the holding
        updated_portfolio = portfolio_repo.add_holding(portfolio_id, holding_request)

        metrics.add_metric(name="HoldingAdded", unit=MetricUnit.Count, value=1)

        return APIResponse.success(
            data=updated_portfolio.model_dump(),
            message="Holding added successfully",
        )

    except Exception:
        logger.exception("Error adding holding")
        metrics.add_metric(name="HoldingAddErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.error("INTERNAL_SERVER_ERROR", "Failed to add holding", 500)


@tracer.capture_method
def handle_add_transaction(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST /portfolios/{portfolio_id}/transactions - Add transaction to portfolio."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Validate user_id is present
        user_id = user_context.get("user_id")
        if not user_id:
            return APIResponse.unauthorized("User ID not found in session")

        # Extract portfolio ID from path
        path_params = event.get("pathParameters") or {}
        portfolio_id = path_params.get("portfolio_id")

        if not portfolio_id:
            return APIResponse.validation_error("Portfolio ID is required")

        logger.info(
            "Processing add transaction request",
            extra={"user_id": user_id, "portfolio_id": portfolio_id},
        )

        # Parse request body
        try:
            body = json.loads(event.get("body", "{}"))
            transaction_request = AddTransactionRequest(**body)
        except (json.JSONDecodeError, ValidationError) as e:
            logger.warning(f"Invalid request body: {e}")
            return APIResponse.validation_error("Invalid request body", [{"field": "body", "message": str(e)}])

        # Get portfolio repository
        portfolio_repo = get_portfolio_repository()

        # Check if portfolio exists and user has access
        existing_portfolio = portfolio_repo.get_portfolio(portfolio_id)
        if not existing_portfolio:
            return APIResponse.not_found("Portfolio not found")

        if existing_portfolio.user_id != user_id:
            return APIResponse.forbidden("Access denied to this portfolio")

        # Add the transaction
        updated_portfolio = portfolio_repo.add_transaction(portfolio_id, transaction_request)

        metrics.add_metric(name="TransactionAdded", unit=MetricUnit.Count, value=1)

        return APIResponse.success(
            data=updated_portfolio.model_dump(),
            message="Transaction added successfully",
        )

    except Exception:
        logger.exception("Error adding transaction")
        metrics.add_metric(name="TransactionAddErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.error("INTERNAL_SERVER_ERROR", "Failed to add transaction", 500)
