"""
Reports management Lambda function.
Handles CRUD operations for report data via API Gateway.
"""

import json
from typing import Any, Dict

from aws_lambda_powertools import Logger, Tracer, Metrics
from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_powertools.metrics import MetricUnit
from pydantic import ValidationError, field_serializer, ConfigDict

from shared.api.responses import APIResponse, RequestValidator, CORSHandler
from shared.models.report import Report, ReportCreate, ReportUpdate
from shared.repositories.report_repository import ReportRepository

# Initialize AWS Lambda Powertools
logger = Logger()
tracer = Tracer()
metrics = Metrics()

# Initialize repository
report_repository = ReportRepository()


@logger.inject_lambda_context(log_event=True)
@tracer.capture_lambda_handler
@metrics.log_metrics(capture_cold_start_metric=True)
def handler(event: Dict[str, Any], context: LambdaContext) -> Dict[str, Any]:
    """
    Reports API endpoint handler.

    Handles all report-related operations based on HTTP method and path.

    Returns:
        API Gateway response
    """

    # Handle CORS preflight requests
    if CORSHandler.is_options_request(event):
        return CORSHandler.handle_options()

    try:
        # Extract request information
        http_method = event.get("httpMethod")
        path = event.get("path", "")

        # Get user context from Cognito authorizer
        try:
            user_context = RequestValidator.get_user_context(event)
            logger.info("User context extracted", extra=user_context)
        except Exception as e:
            logger.error(f"Failed to extract user context: {str(e)}")
            return APIResponse.unauthorized("Invalid authentication")

        # Route to appropriate handler based on HTTP method and path
        if http_method == "GET":
            if (
                path.endswith("/reports")
                or "/reports" in path
                and not path.split("/reports")[1].strip("/")
            ):
                # GET /reports - List reports
                return handle_list_reports(event, user_context)
            else:
                # GET /reports/{reportId} - Get specific report
                return handle_get_report(event, user_context)

        elif http_method == "POST" and (
            path.endswith("/reports") or "/reports" in path
        ):
            # POST /reports - Create new report
            return handle_create_report(event, user_context)

        elif http_method == "PUT":
            # PUT /reports/{reportId} - Update report
            return handle_update_report(event, user_context)

        elif http_method == "DELETE":
            # DELETE /reports/{reportId} - Delete report
            return handle_delete_report(event, user_context)

        else:
            logger.warning(f"Unsupported method: {http_method}")
            return APIResponse.error(
                "METHOD_NOT_ALLOWED", f"Method {http_method} not allowed", 405
            )

    except Exception as e:
        logger.exception(
            "Unhandled error in reports handler",
            extra={"error": str(e), "request_id": context.aws_request_id},
        )

        metrics.add_metric(name="ReportsAPIErrors", unit=MetricUnit.Count, value=1)

        return APIResponse.internal_server_error(
            message="An unexpected error occurred", error_id=context.aws_request_id
        )


@tracer.capture_method
def handle_list_reports(
    event: Dict[str, Any], user_context: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle GET /reports - List reports with optional filtering and pagination."""

    try:
        # Parse pagination parameters
        pagination_params = RequestValidator.validate_pagination_params(event)

        # Parse filter parameters
        status_filter = RequestValidator.get_query_parameter(event, "status")
        type_filter = RequestValidator.get_query_parameter(event, "type")
        user_filter = RequestValidator.get_query_parameter(event, "user_id")

        # Check permissions - users can only see their own reports unless they're admins
        if not _can_view_all_reports(user_context):
            user_filter = user_context.get("user_id")

        logger.info(
            "Listing reports",
            extra={
                "user_id": user_context.get("user_id"),
                "filters": {
                    "status": status_filter,
                    "type": type_filter,
                    "user_id": user_filter,
                },
                "pagination": pagination_params,
            },
        )

        # Build query parameters
        query_params = {
            "limit": pagination_params["limit"],
            "last_key": pagination_params["last_key"],
        }

        if status_filter:
            query_params["status"] = status_filter
        if type_filter:
            query_params["report_type"] = type_filter
        if user_filter:
            query_params["user_id"] = user_filter

        # Query reports
        result = report_repository.list_reports(**query_params)

        metrics.add_metric(name="ReportsListRequests", unit=MetricUnit.Count, value=1)
        metrics.add_metric(
            name="ReportsReturned", unit=MetricUnit.Count, value=len(result["reports"])
        )

        # Convert reports to response format
        reports_data = [report.dict() for report in result["reports"]]

        return APIResponse.paginated_response(
            data=reports_data,
            count=len(reports_data),
            has_more=result.get("has_more", False),
            last_key=result.get("last_key"),
        )

    except ValueError as e:
        logger.warning(f"Invalid request parameters: {str(e)}")
        return APIResponse.bad_request(str(e))
    except Exception as e:
        logger.exception("Error listing reports")
        raise


@tracer.capture_method
def handle_get_report(
    event: Dict[str, Any], user_context: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle GET /reports/{reportId} - Get specific report."""

    try:
        # Extract report ID from path
        report_id = RequestValidator.get_path_parameter(event, "reportId")

        logger.info(
            "Getting report",
            extra={"report_id": report_id, "user_id": user_context.get("user_id")},
        )

        # Retrieve report
        report = report_repository.get_report(report_id)

        if not report:
            return APIResponse.not_found("Report", report_id)

        # Check permissions - users can only view their own reports unless they're admins
        if not _can_view_report(user_context, report):
            return APIResponse.forbidden("Insufficient permissions to view this report")

        metrics.add_metric(name="ReportGetRequests", unit=MetricUnit.Count, value=1)

        return APIResponse.success(report.dict())

    except ValueError as e:
        logger.warning(f"Invalid request: {str(e)}")
        return APIResponse.bad_request(str(e))
    except Exception as e:
        logger.exception("Error retrieving report")
        raise


@tracer.capture_method
def handle_create_report(
    event: Dict[str, Any], user_context: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle POST /reports - Create new report."""

    try:
        # Check permissions - users need report generation permissions
        if not _can_generate_reports(user_context):
            return APIResponse.forbidden("Insufficient permissions to create reports")

        # Parse and validate request body
        try:
            body = RequestValidator.parse_json_body(event)
            report_data = ReportCreate(**body)
        except ValidationError as e:
            logger.warning(f"Invalid report data: {e}")
            return APIResponse.validation_error("Invalid report data", e.errors())
        except ValueError as e:
            logger.warning(f"Invalid JSON body: {str(e)}")
            return APIResponse.bad_request("Invalid JSON in request body")

        user_id = user_context.get("user_id")

        logger.info(
            "Creating report",
            extra={
                "name": report_data.name,
                "type": report_data.report_type,
                "format": report_data.format,
                "user_id": user_id,
            },
        )

        # Create report
        report = report_repository.create_report(user_id, report_data)

        metrics.add_metric(name="ReportCreateRequests", unit=MetricUnit.Count, value=1)

        return APIResponse.success(report.dict(), status_code=201)

    except ValidationError as e:
        logger.warning(f"Validation error: {e}")
        return APIResponse.validation_error("Validation error", e.errors())
    except ValueError as e:
        logger.warning(f"Invalid request: {str(e)}")
        return APIResponse.bad_request(str(e))
    except Exception as e:
        logger.exception("Error creating report")
        raise


@tracer.capture_method
def handle_update_report(
    event: Dict[str, Any], user_context: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle PUT /reports/{reportId} - Update report."""

    try:
        # Extract report ID from path
        report_id = RequestValidator.get_path_parameter(event, "reportId")

        # Get existing report to check permissions
        existing_report = report_repository.get_report(report_id)
        if not existing_report:
            return APIResponse.not_found("Report", report_id)

        # Check permissions - users can only update their own reports unless they're admins
        if not _can_edit_report(user_context, existing_report):
            return APIResponse.forbidden(
                "Insufficient permissions to update this report"
            )

        # Parse and validate request body
        try:
            body = RequestValidator.parse_json_body(event)
            report_data = ReportUpdate(**body)
        except ValidationError as e:
            logger.warning(f"Invalid report data: {e}")
            return APIResponse.validation_error("Invalid report data", e.errors())
        except ValueError as e:
            logger.warning(f"Invalid JSON body: {str(e)}")
            return APIResponse.bad_request("Invalid JSON in request body")

        logger.info(
            "Updating report",
            extra={
                "report_id": report_id,
                "user_id": user_context.get("user_id"),
                "fields": list(report_data.dict(exclude_unset=True).keys()),
            },
        )

        # Update report
        updated_report = report_repository.update_report(report_id, report_data)

        if not updated_report:
            return APIResponse.not_found("Report", report_id)

        metrics.add_metric(name="ReportUpdateRequests", unit=MetricUnit.Count, value=1)

        return APIResponse.success(updated_report.dict())

    except ValidationError as e:
        logger.warning(f"Validation error: {e}")
        return APIResponse.validation_error(e.errors())
    except ValueError as e:
        logger.warning(f"Invalid request: {str(e)}")
        return APIResponse.bad_request(str(e))
    except Exception as e:
        logger.exception("Error updating report")
        raise


@tracer.capture_method
def handle_delete_report(
    event: Dict[str, Any], user_context: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle DELETE /reports/{reportId} - Delete report."""

    try:
        # Extract report ID from path
        report_id = RequestValidator.get_path_parameter(event, "reportId")

        # Get existing report to check permissions
        existing_report = report_repository.get_report(report_id)
        if not existing_report:
            return APIResponse.not_found("Report", report_id)

        # Check permissions - users can only delete their own reports unless they're admins
        if not _can_edit_report(user_context, existing_report):
            return APIResponse.forbidden(
                "Insufficient permissions to delete this report"
            )

        logger.info(
            "Deleting report",
            extra={"report_id": report_id, "user_id": user_context.get("user_id")},
        )

        # Delete report
        deleted = report_repository.delete_report(report_id)

        if not deleted:
            return APIResponse.not_found("Report", report_id)

        metrics.add_metric(name="ReportDeleteRequests", unit=MetricUnit.Count, value=1)

        return APIResponse.success(
            {"message": f"Report {report_id} deleted successfully"}
        )

    except ValueError as e:
        logger.warning(f"Invalid request: {str(e)}")
        return APIResponse.bad_request(str(e))
    except Exception as e:
        logger.exception("Error deleting report")
        raise


def _can_view_all_reports(user_context: Dict[str, Any]) -> bool:
    """Check if user can view all reports."""
    user_role = user_context.get("role", "").lower()
    return user_role in ["admin", "fund_manager", "compliance_officer"]


def _can_view_report(user_context: Dict[str, Any], report: Report) -> bool:
    """Check if user can view a specific report."""
    # Users can always view their own reports
    if user_context.get("user_id") == report.user_id:
        return True

    # Admins and certain roles can view any report
    return _can_view_all_reports(user_context)


def _can_generate_reports(user_context: Dict[str, Any]) -> bool:
    """Check if user can generate reports."""
    user_role = user_context.get("role", "").lower()
    # Most roles can generate reports except viewers
    return user_role in [
        "admin",
        "fund_manager",
        "analyst",
        "compliance_officer",
        "portfolio_manager",
    ]


def _can_edit_report(user_context: Dict[str, Any], report: Report) -> bool:
    """Check if user can edit a specific report."""
    # Users can edit their own reports
    if user_context.get("user_id") == report.user_id:
        return True

    # Admins can edit any report
    user_role = user_context.get("role", "").lower()
    return user_role == "admin"
