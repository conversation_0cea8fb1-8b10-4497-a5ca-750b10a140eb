"""
Users management Lambda function.
Handles CRUD operations for user data via API Gateway.
"""

import json
from typing import Any, Dict

from aws_lambda_powertools import Logger, Tracer, Metrics
from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_powertools.metrics import MetricUnit
from pydantic import ValidationError, field_serializer, ConfigDict

from shared.api.responses import APIResponse, RequestValidator, CORSHandler
from shared.models.user import User, UserCreate, UserUpdate
from shared.repositories.user_repository import UserRepository

# Initialize AWS Lambda Powertools
logger = Logger()
tracer = Tracer()
metrics = Metrics()

# Initialize repository
user_repository = UserRepository()


@logger.inject_lambda_context(log_event=True)
@tracer.capture_lambda_handler
@metrics.log_metrics(capture_cold_start_metric=True)
def handler(event: Dict[str, Any], context: LambdaContext) -> Dict[str, Any]:
    """
    Users API endpoint handler.

    Handles all user-related operations based on HTTP method and path.

    Returns:
        API Gateway response
    """

    # Handle CORS preflight requests
    if CORSHandler.is_options_request(event):
        return CORSHandler.handle_options()

    try:
        # Extract request information
        http_method = event.get("httpMethod")
        path = event.get("path", "")

        # Get user context from Cognito authorizer
        try:
            user_context = RequestValidator.get_user_context(event)
            logger.info("User context extracted", extra=user_context)
        except Exception as e:
            logger.error(f"Failed to extract user context: {str(e)}")
            return APIResponse.unauthorized("Invalid authentication")

        # Route to appropriate handler based on HTTP method and path
        if http_method == "GET":
            if (
                path.endswith("/users")
                or "/users" in path
                and not path.split("/users")[1].strip("/")
            ):
                # GET /users - List users
                return handle_list_users(event, user_context)
            else:
                # GET /users/{userId} - Get specific user
                return handle_get_user(event, user_context)

        elif http_method == "POST" and (path.endswith("/users") or "/users" in path):
            # POST /users - Create new user
            return handle_create_user(event, user_context)

        elif http_method == "PUT":
            # PUT /users/{userId} - Update user
            return handle_update_user(event, user_context)

        elif http_method == "DELETE":
            # DELETE /users/{userId} - Delete user
            return handle_delete_user(event, user_context)

        else:
            logger.warning(f"Unsupported method: {http_method}")
            return APIResponse.error(
                "METHOD_NOT_ALLOWED", f"Method {http_method} not allowed", 405
            )

    except Exception as e:
        logger.exception(
            "Unhandled error in users handler",
            extra={"error": str(e), "request_id": context.aws_request_id},
        )

        metrics.add_metric(name="UsersAPIErrors", unit=MetricUnit.Count, value=1)

        return APIResponse.internal_server_error(
            message="An unexpected error occurred", error_id=context.aws_request_id
        )


@tracer.capture_method
def handle_list_users(
    event: Dict[str, Any], user_context: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle GET /users - List users with optional filtering and pagination."""

    try:
        # Check permissions - only admins and managers can list users
        if not _can_manage_users(user_context):
            return APIResponse.forbidden("Insufficient permissions to list users")

        # Parse pagination parameters
        pagination_params = RequestValidator.validate_pagination_params(event)

        # Parse filter parameters
        role_filter = RequestValidator.get_query_parameter(event, "role")
        status_filter = RequestValidator.get_query_parameter(event, "status")
        department_filter = RequestValidator.get_query_parameter(event, "department")
        search_query = RequestValidator.get_query_parameter(event, "search")

        logger.info(
            "Listing users",
            extra={
                "user_id": user_context.get("user_id"),
                "filters": {
                    "role": role_filter,
                    "status": status_filter,
                    "department": department_filter,
                    "search": search_query,
                },
                "pagination": pagination_params,
            },
        )

        # Handle search query
        if search_query:
            users = user_repository.search_users(
                search_query, pagination_params["limit"]
            )
            result = {
                "users": users,
                "has_more": False,  # Search doesn't support pagination in this implementation
                "last_key": None,
            }
        else:
            # Build query parameters
            query_params = {
                "limit": pagination_params["limit"],
                "last_key": pagination_params["last_key"],
            }

            if role_filter:
                query_params["role"] = role_filter
            if status_filter:
                query_params["status"] = status_filter
            if department_filter:
                query_params["department"] = department_filter

            # Query users
            result = user_repository.list_users(**query_params)

        metrics.add_metric(name="UsersListRequests", unit=MetricUnit.Count, value=1)
        metrics.add_metric(
            name="UsersReturned", unit=MetricUnit.Count, value=len(result["users"])
        )

        # Convert users to response format (excluding sensitive fields)
        users_data = [_sanitize_user_response(user.dict()) for user in result["users"]]

        return APIResponse.paginated_response(
            data=users_data,
            count=len(users_data),
            has_more=result.get("has_more", False),
            last_key=result.get("last_key"),
        )

    except ValueError as e:
        logger.warning(f"Invalid request parameters: {str(e)}")
        return APIResponse.bad_request(str(e))
    except Exception as e:
        logger.exception("Error listing users")
        raise


@tracer.capture_method
def handle_get_user(
    event: Dict[str, Any], user_context: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle GET /users/{userId} - Get specific user."""

    try:
        # Extract user ID from path
        user_id = RequestValidator.get_path_parameter(event, "userId")

        # Check permissions - users can view their own profile, admins can view any
        if not _can_view_user(user_context, user_id):
            return APIResponse.forbidden("Insufficient permissions to view this user")

        logger.info(
            "Getting user",
            extra={"user_id": user_id, "requester_id": user_context.get("user_id")},
        )

        # Retrieve user
        user = user_repository.get_user(user_id)

        if not user:
            return APIResponse.not_found("User", user_id)

        metrics.add_metric(name="UserGetRequests", unit=MetricUnit.Count, value=1)

        # Sanitize response based on permissions
        user_data = _sanitize_user_response(user.dict(), user_context, user_id)

        return APIResponse.success(user_data)

    except ValueError as e:
        logger.warning(f"Invalid request: {str(e)}")
        return APIResponse.bad_request(str(e))
    except Exception as e:
        logger.exception("Error retrieving user")
        raise


@tracer.capture_method
def handle_create_user(
    event: Dict[str, Any], user_context: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle POST /users - Create new user."""

    try:
        # Check permissions - only admins can create users
        if not _can_manage_users(user_context):
            return APIResponse.forbidden("Insufficient permissions to create users")

        # Parse and validate request body
        try:
            body = RequestValidator.parse_json_body(event)
            user_data = UserCreate(**body)
        except ValidationError as e:
            logger.warning(f"Invalid user data: {e}")
            return APIResponse.validation_error("Invalid user data", e.errors())
        except ValueError as e:
            logger.warning(f"Invalid JSON body: {str(e)}")
            return APIResponse.bad_request("Invalid JSON in request body")

        logger.info(
            "Creating user",
            extra={
                "email": user_data.email,
                "role": user_data.role,
                "creator_id": user_context.get("user_id"),
            },
        )

        # Check if user with email already exists
        existing_user = user_repository.get_user_by_email(user_data.email)
        if existing_user:
            return APIResponse.error(
                "USER_EXISTS", f"User with email {user_data.email} already exists", 409
            )

        # Create user
        user = user_repository.create_user(user_data)

        metrics.add_metric(name="UserCreateRequests", unit=MetricUnit.Count, value=1)

        # Return sanitized user data
        user_response = _sanitize_user_response(user.dict())

        return APIResponse.success(user_response, status_code=201)

    except ValidationError as e:
        logger.warning(f"Validation error: {e}")
        return APIResponse.validation_error("Validation error", e.errors())
    except ValueError as e:
        logger.warning(f"Invalid request: {str(e)}")
        return APIResponse.bad_request(str(e))
    except Exception as e:
        logger.exception("Error creating user")
        raise


@tracer.capture_method
def handle_update_user(
    event: Dict[str, Any], user_context: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle PUT /users/{userId} - Update user."""

    try:
        # Extract user ID from path
        user_id = RequestValidator.get_path_parameter(event, "userId")

        # Check permissions - users can update their own profile, admins can update any
        if not _can_edit_user(user_context, user_id):
            return APIResponse.forbidden("Insufficient permissions to update this user")

        # Parse and validate request body
        try:
            body = RequestValidator.parse_json_body(event)
            user_data = UserUpdate(**body)
        except ValidationError as e:
            logger.warning(f"Invalid user data: {e}")
            return APIResponse.validation_error("Invalid user data", e.errors())
        except ValueError as e:
            logger.warning(f"Invalid JSON body: {str(e)}")
            return APIResponse.bad_request("Invalid JSON in request body")

        logger.info(
            "Updating user",
            extra={
                "user_id": user_id,
                "updater_id": user_context.get("user_id"),
                "fields": list(user_data.dict(exclude_unset=True).keys()),
            },
        )

        # Update user
        updated_user = user_repository.update_user(user_id, user_data)

        if not updated_user:
            return APIResponse.not_found("User", user_id)

        metrics.add_metric(name="UserUpdateRequests", unit=MetricUnit.Count, value=1)

        # Return sanitized user data
        user_response = _sanitize_user_response(
            updated_user.dict(), user_context, user_id
        )

        return APIResponse.success(user_response)

    except ValidationError as e:
        logger.warning(f"Validation error: {e}")
        return APIResponse.validation_error(e.errors())
    except ValueError as e:
        logger.warning(f"Invalid request: {str(e)}")
        return APIResponse.bad_request(str(e))
    except Exception as e:
        logger.exception("Error updating user")
        raise


@tracer.capture_method
def handle_delete_user(
    event: Dict[str, Any], user_context: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle DELETE /users/{userId} - Delete user."""

    try:
        # Extract user ID from path
        user_id = RequestValidator.get_path_parameter(event, "userId")

        # Check permissions - only admins can delete users
        if not _can_manage_users(user_context):
            return APIResponse.forbidden("Insufficient permissions to delete users")

        # Prevent self-deletion
        if user_context.get("user_id") == user_id:
            return APIResponse.bad_request("Cannot delete your own account")

        logger.info(
            "Deleting user",
            extra={"user_id": user_id, "deleter_id": user_context.get("user_id")},
        )

        # Delete user
        deleted = user_repository.delete_user(user_id)

        if not deleted:
            return APIResponse.not_found("User", user_id)

        metrics.add_metric(name="UserDeleteRequests", unit=MetricUnit.Count, value=1)

        return APIResponse.success({"message": f"User {user_id} deleted successfully"})

    except ValueError as e:
        logger.warning(f"Invalid request: {str(e)}")
        return APIResponse.bad_request(str(e))
    except Exception as e:
        logger.exception("Error deleting user")
        raise


def _can_manage_users(user_context: Dict[str, Any]) -> bool:
    """Check if user can manage other users."""
    user_role = user_context.get("role", "").lower()
    return user_role in ["admin", "fund_manager"]


def _can_view_user(user_context: Dict[str, Any], target_user_id: str) -> bool:
    """Check if user can view another user's profile."""
    # Users can always view their own profile
    if user_context.get("user_id") == target_user_id:
        return True

    # Admins and managers can view any user
    return _can_manage_users(user_context)


def _can_edit_user(user_context: Dict[str, Any], target_user_id: str) -> bool:
    """Check if user can edit another user's profile."""
    # Users can edit their own profile
    if user_context.get("user_id") == target_user_id:
        return True

    # Admins can edit any user
    return _can_manage_users(user_context)


def _sanitize_user_response(
    user_data: Dict[str, Any],
    user_context: Dict[str, Any] = None,
    target_user_id: str = None,
) -> Dict[str, Any]:
    """Remove sensitive fields from user response based on permissions."""

    # Fields that should never be exposed
    sensitive_fields = ["cognito_sub"]

    # Fields that only admins or the user themselves can see
    admin_only_fields = ["session_info", "notes", "custom_fields"]

    # Create a copy to avoid modifying the original
    sanitized = user_data.copy()

    # Always remove sensitive fields
    for field in sensitive_fields:
        sanitized.pop(field, None)

    # Remove admin-only fields if not authorized
    if user_context and target_user_id:
        is_self = user_context.get("user_id") == target_user_id
        is_admin = _can_manage_users(user_context)

        if not (is_self or is_admin):
            for field in admin_only_fields:
                sanitized.pop(field, None)

    return sanitized
