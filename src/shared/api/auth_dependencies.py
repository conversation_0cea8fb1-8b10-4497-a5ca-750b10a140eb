"""
Lambda authentication dependencies.
Provides JWT validation and user authentication for Lambda functions.
"""

from typing import Dict, Any, Optional

from shared.security.jwt_utils import jwt_manager
from shared.security.session_manager import (
    SessionManager,
    InvalidSessionError,
    SessionExpiredError,
)

# Initialize security components
session_manager = SessionManager()


def create_user_context(user_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a standardized user context from user info.

    Args:
        user_info: Raw user information from session/token

    Returns:
        Standardized user context dictionary
    """
    # Extract user ID from various possible fields
    user_id = (
        user_info.get("sub") or
        user_info.get("user_sub") or
        user_info.get("user_id") or
        user_info.get("username")
    )

    # Extract roles from various possible fields
    roles = user_info.get("roles", [])
    groups = user_info.get("cognito:groups", [])

    # Determine primary role - default to FUND_MANAGER for authenticated users
    # This allows fund creation while maintaining security
    primary_role = "FUND_MANAGER"  # Default role for authenticated users

    if roles:
        primary_role = roles[0] if isinstance(roles, list) else str(roles)
    elif groups:
        # Map Cognito groups to roles if needed
        group_to_role_mapping = {
            "Admins": "ADMIN",
            "FundManagers": "FUND_MANAGER",
            "Analysts": "SENIOR_ANALYST",
            "Users": "FUND_MANAGER"  # Default for regular users
        }
        primary_role = group_to_role_mapping.get(groups[0], "FUND_MANAGER")

    return {
        "user_id": user_id,
        "email": user_info.get("email"),
        "username": user_info.get("username") or user_info.get("preferred_username"),
        "role": primary_role,  # Single role for validation
        "roles": roles,  # Keep plural for backward compatibility
        "groups": groups,
        "attributes": user_info.get("attributes", {}),
        "session_info": user_info,
    }


def validate_lambda_session(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate user session from Lambda event context.

    Args:
        event: Lambda event containing authorization context

    Returns:
        Dict containing session validation results

    Raises:
        ValueError: If session validation fails
    """
    try:
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            error_type = session_result.get("error_type", "InvalidSession")
            error_message = session_result.get("error", "Session validation failed")

            if error_type == "SessionExpiredError":
                raise ValueError("Session has expired. Please log in again.")
            else:
                raise ValueError(error_message)

        return session_result

    except (SessionExpiredError, InvalidSessionError) as e:
        raise ValueError(str(e))
    except Exception as e:
        raise ValueError("Session validation failed")


def validate_token_from_event(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate JWT token from Lambda event with blacklist check.

    Args:
        event: Lambda event containing authorization header

    Returns:
        Dict containing validation results

    Raises:
        ValueError: If token validation fails
    """
    # Extract token from Authorization header
    headers = event.get("headers", {})
    auth_header = headers.get("Authorization") or headers.get("authorization")

    if not auth_header:
        raise ValueError("Authorization header missing")

    if not auth_header.startswith("Bearer "):
        raise ValueError("Invalid authorization header format")

    token = auth_header[7:]  # Remove "Bearer " prefix

    try:
        # Validate token with blacklist check
        validation_result = jwt_manager.validate_token_with_blacklist(token)

        if not validation_result.is_valid:
            if validation_result.is_blacklisted:
                raise ValueError("Token has been revoked. Please log in again.")
            else:
                raise ValueError(validation_result.error_message or "Invalid token")

        return validation_result.user_info

    except ValueError:
        # Re-raise ValueError as-is
        raise
    except Exception as e:
        raise ValueError("Token validation failed")


def get_user_from_lambda_event(event: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Get user information from Lambda event (either from Cognito authorizer or token).

    Args:
        event: Lambda event

    Returns:
        User context dictionary or None if not authenticated
    """
    try:
        # First try to get user from Cognito authorizer context
        request_context = event.get("requestContext", {})
        authorizer = request_context.get("authorizer", {})

        if authorizer and authorizer.get("sub"):
            # User authenticated via Cognito authorizer
            return create_user_context(authorizer)

        # Try to validate token directly
        user_info = validate_token_from_event(event)
        return create_user_context(user_info)

    except Exception:
        return None


def get_user_from_event(event: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Extract user information from Lambda event.

    Args:
        event: Lambda event containing request context

    Returns:
        User context dictionary or None if not authenticated
    """
    # Try to get user from request context authorizer
    request_context = event.get('requestContext', {})
    authorizer = request_context.get('authorizer', {})

    if authorizer:
        return create_user_context(authorizer)

    # Try to get user from claims (for direct Lambda invocation)
    claims = event.get('claims')
    if claims:
        return create_user_context(claims)

    # For async job processing, user info might be in the event directly
    user_id = event.get('userId')
    if user_id:
        return {
            'user_id': user_id,
            'role': 'FUND_MANAGER',  # Default role for async jobs
            'permissions': ['fund:create', 'fund:read', 'fund:update']
        }

    return None
