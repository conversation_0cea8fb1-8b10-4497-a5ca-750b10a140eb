"""
API response utilities for Lambda functions.
Standardized response formatting and error handling.
"""

import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum

from aws_lambda_powertools import Logger

logger = Logger()


class APIResponse:
    """Utility class for creating standardized API responses."""

    @staticmethod
    def _serialize_data(data: Any) -> Any:
        """Serialize data for JSON response, handling special types."""
        if isinstance(data, dict):
            return {k: APIResponse._serialize_data(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [APIResponse._serialize_data(item) for item in data]
        elif isinstance(data, Decimal):
            return float(data)
        elif isinstance(data, datetime):
            return data.isoformat()
        elif isinstance(data, Enum):
            return data.value
        elif hasattr(data, "dict"):  # Pydantic models
            return APIResponse._serialize_data(data.dict())
        else:
            return data

    @staticmethod
    def _create_response(
        status_code: int, body: Dict[str, Any], headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Create a Lambda response object."""
        default_headers = {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",  # Will be overridden by API Gateway CORS
            "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
            "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS",
        }

        if headers:
            default_headers.update(headers)

        # Add timestamp to response
        if "timestamp" not in body:
            body["timestamp"] = datetime.now(timezone.utc).isoformat()

        # Serialize the body data
        serialized_body = APIResponse._serialize_data(body)

        return {
            "statusCode": status_code,
            "headers": default_headers,
            "body": json.dumps(serialized_body, default=str),
        }

    @staticmethod
    def success(
        data: Any = None,
        message: Optional[str] = None,
        status_code: int = 200,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """Create a successful response."""
        body = {}

        if data is not None:
            if isinstance(data, dict) and "data" not in data:
                body["data"] = data
            else:
                body["data"] = data

        if message:
            body["message"] = message

        logger.info(
            f"API Success Response - Status: {status_code}, Message: {message}",
            extra={
                "status_code": status_code,
                "has_data": data is not None,
            },
        )

        return APIResponse._create_response(status_code, body, headers)

    @staticmethod
    def error(
        error: str,
        message: str,
        status_code: int = 400,
        details: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """Create an error response."""
        body = {"error": error, "message": message}

        if details:
            body["details"] = details

        logger.error(
            f"API Error Response - {error}: {message}",
            extra={
                "status_code": status_code,
                "error": error,
                "details": details,
            },
        )

        return APIResponse._create_response(status_code, body, headers)

    @staticmethod
    def created(
        data: Any,
        message: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """Create a 201 Created response."""
        return APIResponse.success(data, message, 201, headers)

    @staticmethod
    def no_content(headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Create a 204 No Content response."""
        return APIResponse._create_response(204, {}, headers)

    @staticmethod
    def bad_request(
        message: str, details: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a 400 Bad Request response."""
        return APIResponse.error("BAD_REQUEST", message, 400, details)

    @staticmethod
    def unauthorized(message: str = "Unauthorized") -> Dict[str, Any]:
        """Create a 401 Unauthorized response."""
        return APIResponse.error("UNAUTHORIZED", message, 401)

    @staticmethod
    def forbidden(message: str = "Forbidden") -> Dict[str, Any]:
        """Create a 403 Forbidden response."""
        return APIResponse.error("FORBIDDEN", message, 403)

    @staticmethod
    def not_found(
        resource: str = "Resource", resource_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a 404 Not Found response."""
        if resource_id:
            message = f"{resource} with ID '{resource_id}' not found"
        else:
            message = f"{resource} not found"

        return APIResponse.error("NOT_FOUND", message, 404)

    @staticmethod
    def conflict(
        message: str, details: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a 409 Conflict response."""
        return APIResponse.error("CONFLICT", message, 409, details)

    @staticmethod
    def validation_error(
        message: str,
        validation_errors: Optional[List[Dict[str, Any]]] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a 422 Validation Error response."""
        error_details = {}

        if validation_errors:
            error_details["validation_errors"] = validation_errors

        if details:
            error_details.update(details)

        return APIResponse.error(
            "VALIDATION_ERROR", message, 422, error_details if error_details else None
        )

    @staticmethod
    def internal_server_error(
        message: str = "Internal server error", error_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a 500 Internal Server Error response."""
        details = {}
        if error_id:
            details["error_id"] = error_id

        return APIResponse.error(
            "INTERNAL_SERVER_ERROR", message, 500, details if details else None
        )

    @staticmethod
    def paginated_response(
        data: list,
        count: int,
        has_more: bool = False,
        last_key: Optional[str] = None,
        total_count: Optional[int] = None,
    ) -> Dict[str, Any]:
        """Create a paginated response."""
        response_data = {
            "data": data,
            "pagination": {"count": count, "has_more": has_more},
        }

        if last_key:
            response_data["pagination"]["last_key"] = last_key

        if total_count is not None:
            response_data["pagination"]["total_count"] = total_count

        return APIResponse.success(response_data)


class RequestValidator:
    """Utility class for validating API requests."""

    @staticmethod
    def validate_json_body(event: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and parse JSON body from Lambda event."""
        try:
            body = event.get("body")
            if not body:
                raise ValueError("Request body is required")

            if isinstance(body, str):
                return json.loads(body)
            return body

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in request body: {str(e)}")
            raise ValueError("Invalid JSON in request body")
        except Exception as e:
            logger.error(f"Error parsing request body: {str(e)}")
            raise ValueError("Error parsing request body")

    @staticmethod
    def get_path_parameter(event: Dict[str, Any], param_name: str) -> str:
        """Get a required path parameter from Lambda event."""
        path_params = event.get("pathParameters") or {}
        param_value = path_params.get(param_name)

        if not param_value:
            raise ValueError(f"Missing required path parameter: {param_name}")

        return param_value

    @staticmethod
    def get_query_parameter(
        event: Dict[str, Any],
        param_name: str,
        default: Optional[str] = None,
        required: bool = False,
    ) -> Optional[str]:
        """Get a query parameter from Lambda event."""
        query_params = event.get("queryStringParameters") or {}
        param_value = query_params.get(param_name, default)

        if required and not param_value:
            raise ValueError(f"Missing required query parameter: {param_name}")

        return param_value

    @staticmethod
    def get_user_context(event: Dict[str, Any]) -> Dict[str, Any]:
        """Extract user context from Lambda event (from Cognito authorizer)."""
        request_context = event.get("requestContext", {})
        authorizer = request_context.get("authorizer", {})

        # Extract user information from Cognito authorizer
        user_context = {
            "user_id": authorizer.get("sub"),
            "email": authorizer.get("email"),
            "cognito_sub": authorizer.get("sub"),
            "roles": (
                authorizer.get("custom:role", "").split(",")
                if authorizer.get("custom:role")
                else []
            ),
            "groups": (
                authorizer.get("cognito:groups", [])
                if authorizer.get("cognito:groups")
                else []
            ),
        }

        return user_context

    @staticmethod
    def validate_pagination_params(event: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and parse pagination parameters."""
        try:
            limit_str = RequestValidator.get_query_parameter(event, "limit", "20")
            limit = int(limit_str)

            if limit < 1 or limit > 100:
                raise ValueError("Limit must be between 1 and 100")

            last_key = RequestValidator.get_query_parameter(event, "lastKey")

            return {"limit": limit, "last_key": last_key}

        except ValueError as e:
            if "Limit must be" in str(e):
                raise e
            raise ValueError("Invalid pagination parameters")


class CORSHandler:
    """Handle CORS preflight requests."""

    @staticmethod
    def handle_options() -> Dict[str, Any]:
        """Handle CORS preflight OPTIONS request."""
        return {
            "statusCode": 200,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
                "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS",
                "Access-Control-Max-Age": "86400",
            },
            "body": "",
        }

    @staticmethod
    def is_options_request(event: Dict[str, Any]) -> bool:
        """Check if the request is a CORS preflight OPTIONS request."""
        return event.get("httpMethod") == "OPTIONS"


def create_response(
    data: Any = None,
    message: Optional[str] = None,
    success: bool = True,
    status_code: int = 200,
    headers: Optional[Dict[str, str]] = None,
) -> Dict[str, Any]:
    """
    Create a standardized API response.

    Args:
        data: Response data
        message: Response message
        success: Whether the operation was successful
        status_code: HTTP status code
        headers: Additional headers

    Returns:
        Standardized API response
    """
    if success:
        return APIResponse.success(data, message, status_code, headers)
    else:
        return APIResponse.error(
            error="OPERATION_FAILED",
            message=message or "Operation failed",
            status_code=status_code,
            headers=headers
        )
