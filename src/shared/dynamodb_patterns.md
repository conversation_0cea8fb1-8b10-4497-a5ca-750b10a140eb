# DynamoDB Access Patterns

This document outlines the data access patterns for the FundFlow application's DynamoDB tables.

## Table Structure Overview

### Fund Table (`fundflow-{env}-funds`)

- **Primary Key**: `fund_id` (String)
- **GSI 1**: `fund_type_index` - `fund_type` (HASH), `created_at` (RANGE)
- **GSI 2**: `status_index` - `status` (HASH), `created_at` (RANGE)

### User Table (`fundflow-{env}-users`)

- **Primary Key**: `user_id` (String)
- **GSI 1**: `email_index` - `email` (HASH)
- **GSI 2**: `role_index` - `role` (HASH), `created_at` (RANGE)

### Report Table (`fundflow-{env}-reports`)

- **Primary Key**: `report_id` (String)
- **GSI 1**: `user_reports_index` - `user_id` (HASH), `created_at` (RANGE)
- **GSI 2**: `report_type_index` - `report_type` (HASH), `created_at` (RANGE)

## Access Patterns

### Fund Access Patterns

#### 1. Get Fund by ID

- **Operation**: GetItem
- **Key**: `fund_id`
- **Usage**: View fund details, edit fund
- **Performance**: O(1) - Direct access

#### 2. List All Funds

- **Operation**: Scan
- **Filter**: Optional status filter
- **Usage**: Fund listing page, search results
- **Performance**: O(n) - Full table scan
- **Optimization**: Consider using status GSI with pagination

#### 3. Get Funds by Type

- **Operation**: Query
- **Index**: `fund_type_index`
- **Key**: `fund_type = :type`
- **Sort**: `created_at` (ascending/descending)
- **Usage**: Filter funds by category
- **Performance**: O(log n) + O(m) where m = results

#### 4. Get Funds by Status

- **Operation**: Query
- **Index**: `status_index`
- **Key**: `status = :status`
- **Sort**: `created_at` (ascending/descending)
- **Usage**: Show active/inactive funds
- **Performance**: O(log n) + O(m)

#### 5. Search Funds by Name

- **Operation**: Scan with FilterExpression
- **Filter**: `contains(name, :search_term)`
- **Usage**: Fund search functionality
- **Performance**: O(n) - Consider using OpenSearch for better performance

### User Access Patterns

#### 1. Get User by ID

- **Operation**: GetItem
- **Key**: `user_id`
- **Usage**: User profile, authentication
- **Performance**: O(1)

#### 2. Get User by Email

- **Operation**: Query
- **Index**: `email_index`
- **Key**: `email = :email`
- **Usage**: Login, user lookup
- **Performance**: O(log n) + O(1)

#### 3. List Users by Role

- **Operation**: Query
- **Index**: `role_index`
- **Key**: `role = :role`
- **Sort**: `created_at`
- **Usage**: Admin user management
- **Performance**: O(log n) + O(m)

#### 4. List All Users

- **Operation**: Scan
- **Filter**: Optional status filter
- **Usage**: User management interface
- **Performance**: O(n)

### Report Access Patterns

#### 1. Get Report by ID

- **Operation**: GetItem
- **Key**: `report_id`
- **Usage**: View/download report
- **Performance**: O(1)

#### 2. Get User's Reports

- **Operation**: Query
- **Index**: `user_reports_index`
- **Key**: `user_id = :user_id`
- **Sort**: `created_at` (descending for recent first)
- **Usage**: User's report history
- **Performance**: O(log n) + O(m)

#### 3. Get Reports by Type

- **Operation**: Query
- **Index**: `report_type_index`
- **Key**: `report_type = :type`
- **Sort**: `created_at`
- **Usage**: Filter reports by category
- **Performance**: O(log n) + O(m)

#### 4. List All Reports

- **Operation**: Scan
- **Filter**: Optional status filter
- **Usage**: Admin report management
- **Performance**: O(n)

## Query Examples

### Fund Queries

```python
# Get fund by ID
response = dynamodb.get_item(
    TableName='fundflow-dev-funds',
    Key={'fund_id': {'S': 'fund-123'}}
)

# Get equity funds, newest first
response = dynamodb.query(
    TableName='fundflow-dev-funds',
    IndexName='fund_type_index',
    KeyConditionExpression='fund_type = :type',
    ExpressionAttributeValues={':type': {'S': 'equity'}},
    ScanIndexForward=False  # Descending order
)

# Get active funds
response = dynamodb.query(
    TableName='fundflow-dev-funds',
    IndexName='status_index',
    KeyConditionExpression='#status = :status',
    ExpressionAttributeNames={'#status': 'status'},
    ExpressionAttributeValues={':status': {'S': 'active'}}
)
```

### User Queries

```python
# Get user by email
response = dynamodb.query(
    TableName='fundflow-dev-users',
    IndexName='email_index',
    KeyConditionExpression='email = :email',
    ExpressionAttributeValues={':email': {'S': '<EMAIL>'}}
)

# Get admins, newest first
response = dynamodb.query(
    TableName='fundflow-dev-users',
    IndexName='role_index',
    KeyConditionExpression='#role = :role',
    ExpressionAttributeNames={'#role': 'role'},
    ExpressionAttributeValues={':role': {'S': 'admin'}},
    ScanIndexForward=False
)
```

### Report Queries

```python
# Get user's reports, newest first
response = dynamodb.query(
    TableName='fundflow-dev-reports',
    IndexName='user_reports_index',
    KeyConditionExpression='user_id = :user_id',
    ExpressionAttributeValues={':user_id': {'S': 'user-123'}},
    ScanIndexForward=False
)

# Get performance reports
response = dynamodb.query(
    TableName='fundflow-dev-reports',
    IndexName='report_type_index',
    KeyConditionExpression='report_type = :type',
    ExpressionAttributeValues={':type': {'S': 'fund_performance'}}
)
```

## Performance Considerations

### Read Patterns

- Use GetItem for single item retrieval when you have the primary key
- Use Query operations with GSIs for filtered access
- Avoid Scan operations in production when possible
- Implement pagination for large result sets

### Write Patterns

- Use PutItem for creates and full updates
- Use UpdateItem for partial updates
- Consider using TransactWriteItems for multi-table operations
- Implement optimistic locking with conditional updates

### Cost Optimization

- Use PAY_PER_REQUEST billing mode for variable workloads
- Monitor read/write capacity metrics
- Design GSIs to support common query patterns
- Use projection types appropriately (ALL, KEYS_ONLY, INCLUDE)

### Hot Partitions

- Distribute writes across partition keys
- Avoid sequential IDs for high-write scenarios
- Use random prefixes if needed for load distribution
- Monitor CloudWatch metrics for throttling

## Best Practices

1. **Single Table Design**: Consider if entities can be modeled in a single table
2. **Access Pattern First**: Design tables based on access patterns, not entities
3. **Minimize GSIs**: Only create GSIs for required access patterns
4. **Consistent Naming**: Use consistent attribute naming across tables
5. **Error Handling**: Implement proper retry logic with exponential backoff
6. **Monitoring**: Set up CloudWatch alarms for throttling and errors
