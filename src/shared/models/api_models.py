"""
API model utilities and specialized models for different API contexts.
This module provides utilities for model conversion, validation, and context-specific models.
"""

from typing import Type, TypeVar, Dict, Any, List, Optional, Union
from datetime import datetime, timezone
from decimal import Decimal

from pydantic import BaseModel, field_serializer, Field, validator, ConfigDict
from .fund import Fund
from .user import User
from .report import Report
from .requests import (
    FundCreateRequest,
    FundUpdateRequest,
    FundQueryRequest,
    UserCreateRequest,
    UserUpdateRequest,
    UserQueryRequest,
    ReportCreateRequest,
    ReportUpdateRequest,
    ReportQueryRequest,
)
from .responses import (
    FundSummaryResponse,
    FundDetailResponse,
    UserSummaryResponse,
    UserDetailResponse,
    UserProfileResponse,
    ReportSummaryResponse,
    ReportDetailResponse,
)

T = TypeVar("T", bound=BaseModel)


# ============================================================================
# Model Conversion Utilities
# ============================================================================


class ModelConverter:
    """Utility class for converting between different model types."""

    @staticmethod
    def fund_to_summary_response(fund: Fund) -> FundSummaryResponse:
        """Convert Fund model to FundSummaryResponse."""
        return FundSummaryResponse(
            fund_id=fund.fund_id,
            name=fund.name,
            fund_type=fund.fund_type,
            status=fund.status,
            nav=fund.nav,
            currency=fund.currency,
            inception_date=fund.inception_date,
            total_assets=fund.total_assets,
            risk_level=fund.risk_level,
            fund_manager=fund.fund_manager,
            management_company=fund.management_company,
            expense_ratio=fund.expense_ratio,
            minimum_investment=fund.minimum_investment,
            isin=fund.isin,
            bloomberg_ticker=fund.bloomberg_ticker,
            description=fund.description,
            investment_objective=fund.investment_objective,
            benchmark=fund.benchmark,
            tags=fund.tags or [],
            created_at=fund.created_at,
            updated_at=fund.updated_at,
        )

    @staticmethod
    def fund_to_detail_response(fund: Fund) -> FundDetailResponse:
        """Convert Fund model to FundDetailResponse."""
        summary = ModelConverter.fund_to_summary_response(fund)
        return FundDetailResponse(
            **summary.dict(),
            performance_metrics=fund.performance_metrics,
            holdings=fund.holdings,
            cusip=fund.cusip,
            custom_fields=fund.custom_fields or {},
        )

    @staticmethod
    def fund_create_request_to_fund(request: FundCreateRequest, user_id: str) -> Fund:
        """Convert FundCreateRequest to Fund model."""
        return Fund(
            fund_id=request.fund_id,
            name=request.name,
            fund_type=request.fund_type,
            status=request.status,
            nav=request.nav,
            currency=request.currency,
            inception_date=request.inception_date,
            total_assets=request.total_assets,
            risk_level=request.risk_level,
            performance_metrics=request.performance_metrics,
            holdings=request.holdings,
            fund_manager=request.fund_manager,
            management_company=request.management_company,
            expense_ratio=request.expense_ratio,
            minimum_investment=request.minimum_investment,
            isin=request.isin,
            cusip=request.cusip,
            bloomberg_ticker=request.bloomberg_ticker,
            description=request.description,
            investment_objective=request.investment_objective,
            benchmark=request.benchmark,
            tags=request.tags,
            custom_fields=request.custom_fields,
        )

    @staticmethod
    def user_to_summary_response(user: User) -> UserSummaryResponse:
        """Convert User model to UserSummaryResponse."""
        return UserSummaryResponse(
            user_id=user.user_id,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            full_name=user.full_name,
            role=user.role,
            department=user.department,
            job_title=user.job_title,
            status=user.status,
            is_verified=user.is_verified,
            created_at=user.created_at,
            updated_at=user.updated_at,
        )

    @staticmethod
    def user_to_detail_response(user: User) -> UserDetailResponse:
        """Convert User model to UserDetailResponse."""
        summary = ModelConverter.user_to_summary_response(user)
        return UserDetailResponse(
            **summary.dict(),
            employee_id=user.employee_id,
            manager_id=user.manager_id,
            is_mfa_enabled=user.is_mfa_enabled,
            access_groups=user.access_groups or [],
            tags=user.tags or [],
            last_password_change=user.last_password_change,
            email_verified_at=user.email_verified_at,
        )

    @staticmethod
    def user_to_profile_response(user: User) -> UserProfileResponse:
        """Convert User model to UserProfileResponse."""
        detail = ModelConverter.user_to_detail_response(user)
        return UserProfileResponse(
            **detail.dict(),
            phone_number=user.phone_number,
            permissions=user.permissions,
            preferences=user.preferences,
            last_login=user.session_info.last_login if user.session_info else None,
            login_count=user.session_info.login_count if user.session_info else 0,
            notes=user.notes,
            custom_fields=user.custom_fields or {},
        )

    @staticmethod
    def user_create_request_to_user(request: UserCreateRequest) -> User:
        """Convert UserCreateRequest to User model."""
        return User(
            user_id=request.user_id,
            email=request.email,
            first_name=request.first_name,
            last_name=request.last_name,
            phone_number=request.phone_number,
            role=request.role,
            department=request.department,
            job_title=request.job_title,
            employee_id=request.employee_id,
            manager_id=request.manager_id,
            status=request.status,
            is_mfa_enabled=request.is_mfa_enabled,
            permissions=request.permissions,
            access_groups=request.access_groups,
            preferences=request.preferences,
            notes=request.notes,
            tags=request.tags,
            custom_fields=request.custom_fields,
        )

    @staticmethod
    def report_to_summary_response(report: Report) -> ReportSummaryResponse:
        """Convert Report model to ReportSummaryResponse."""
        return ReportSummaryResponse(
            report_id=report.report_id,
            name=report.name,
            description=report.description,
            report_type=report.report_type,
            user_id=report.user_id,
            status=report.status,
            format=report.format,
            created_at=report.created_at,
            updated_at=report.updated_at,
            completed_at=report.completed_at,
        )

    @staticmethod
    def report_to_detail_response(
        report: Report,
        download_url: Optional[str] = None,
        file_size: Optional[int] = None,
    ) -> ReportDetailResponse:
        """Convert Report model to ReportDetailResponse."""
        summary = ModelConverter.report_to_summary_response(report)

        # Calculate generation time if completed
        generation_time = None
        if report.completed_at and report.created_at:
            generation_time = (report.completed_at - report.created_at).total_seconds()

        return ReportDetailResponse(
            **summary.dict(),
            file_path=report.file_path,
            file_size=file_size,
            download_url=download_url,
            parameters=report.parameters or {},
            generation_time_seconds=generation_time,
        )

    @staticmethod
    def report_create_request_to_report(
        request: ReportCreateRequest, user_id: str
    ) -> Report:
        """Convert ReportCreateRequest to Report model."""
        return Report(
            report_id=request.report_id,
            name=request.name,
            description=request.description,
            report_type=request.report_type,
            user_id=user_id,
            format=request.format,
            parameters=request.parameters,
        )


# ============================================================================
# Context-Specific Models
# ============================================================================


class PublicFundResponse(BaseModel):
    """Public fund response model (minimal data for unauthenticated users)."""

    fund_id: str = Field(..., description="Unique fund identifier")
    name: str = Field(..., description="Fund name")
    fund_type: str = Field(..., description="Type of fund")
    nav: Optional[Decimal] = Field(None, description="Net Asset Value")
    currency: str = Field(..., description="Base currency")
    inception_date: Optional[datetime] = Field(None, description="Fund inception date")
    risk_level: Optional[str] = Field(None, description="Risk level")
    minimum_investment: Optional[Decimal] = Field(
        None, description="Minimum investment amount"
    )
    description: Optional[str] = Field(None, description="Fund description")

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("nav", "minimum_investment", when_used="json")
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON serialization."""
        return str(value) if value is not None else None

    @field_serializer("inception_date", when_used="json")
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        """Serialize datetime fields to ISO format for JSON serialization."""
        return value.isoformat() if value is not None else None


class AdminUserResponse(UserProfileResponse):
    """Admin user response model (includes all fields for admin access)."""

    # Additional admin-only fields
    cognito_sub: Optional[str] = Field(None, description="AWS Cognito subject")
    session_info: Optional[Dict[str, Any]] = Field(
        None, description="Full session information"
    )
    failed_login_attempts: int = Field(default=0, description="Failed login attempts")
    ip_address: Optional[str] = Field(None, description="Last known IP address")


class AuditLogEntry(BaseModel):
    """Audit log entry model."""

    id: str = Field(..., description="Unique log entry ID")
    user_id: str = Field(..., description="User who performed the action")
    action: str = Field(..., description="Action performed")
    resource_type: str = Field(..., description="Type of resource affected")
    resource_id: str = Field(..., description="ID of the resource affected")
    timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Action timestamp",
    )
    ip_address: Optional[str] = Field(None, description="IP address of the user")
    user_agent: Optional[str] = Field(None, description="User agent string")
    changes: Optional[Dict[str, Any]] = Field(None, description="Changes made")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("timestamp", when_used="json")
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        """Serialize datetime fields to ISO format for JSON serialization."""
        return value.isoformat() if value is not None else None


# ============================================================================
# Validation Utilities
# ============================================================================


class ValidationUtils:
    """Utility class for common validation operations."""

    @staticmethod
    def validate_fund_id_unique(fund_id: str, existing_funds: List[Fund]) -> bool:
        """Validate that fund ID is unique."""
        return not any(fund.fund_id == fund_id for fund in existing_funds)

    @staticmethod
    def validate_user_email_unique(email: str, existing_users: List[User]) -> bool:
        """Validate that user email is unique."""
        return not any(user.email.lower() == email.lower() for user in existing_users)

    @staticmethod
    def validate_manager_hierarchy(
        user_id: str, manager_id: str, all_users: List[User]
    ) -> bool:
        """Validate that manager hierarchy doesn't create cycles."""
        if user_id == manager_id:
            return False

        # Check for circular references
        current_manager = manager_id
        visited = set()

        while current_manager:
            if current_manager in visited:
                return False  # Circular reference detected

            visited.add(current_manager)

            # Find the manager's manager
            manager_user = next(
                (u for u in all_users if u.user_id == current_manager), None
            )
            if not manager_user:
                break

            current_manager = manager_user.manager_id

        return True

    @staticmethod
    def validate_permission_consistency(permissions: Dict[str, bool]) -> List[str]:
        """Validate permission consistency and return any errors."""
        errors = []

        # Users who can delete should also be able to edit
        if permissions.get("can_delete_funds") and not permissions.get(
            "can_edit_funds"
        ):
            errors.append("Users who can delete funds must also be able to edit them")

        # Users who can edit should also be able to view
        if permissions.get("can_edit_funds") and not permissions.get("can_view_funds"):
            errors.append("Users who can edit funds must also be able to view them")

        # Users who can create should also be able to view
        if permissions.get("can_create_funds") and not permissions.get(
            "can_view_funds"
        ):
            errors.append("Users who can create funds must also be able to view them")

        return errors


# ============================================================================
# Query Builder Utilities
# ============================================================================


class QueryBuilder:
    """Utility class for building database queries from request models."""

    @staticmethod
    def build_fund_query_filters(request: FundQueryRequest) -> Dict[str, Any]:
        """Build DynamoDB query filters from FundQueryRequest."""
        filters = {}

        # Direct field filters
        if request.fund_type:
            filters["fund_type"] = request.fund_type
        if request.status:
            filters["status"] = request.status
        if request.risk_level:
            filters["risk_level"] = request.risk_level
        if request.currency:
            filters["currency"] = request.currency
        if request.fund_manager:
            filters["fund_manager"] = request.fund_manager
        if request.management_company:
            filters["management_company"] = request.management_company

        # Range filters
        if request.nav_min is not None or request.nav_max is not None:
            nav_filter = {}
            if request.nav_min is not None:
                nav_filter["gte"] = request.nav_min
            if request.nav_max is not None:
                nav_filter["lte"] = request.nav_max
            filters["nav"] = nav_filter

        # Date filters
        if request.inception_date_from or request.inception_date_to:
            date_filter = {}
            if request.inception_date_from:
                date_filter["gte"] = request.inception_date_from
            if request.inception_date_to:
                date_filter["lte"] = request.inception_date_to
            filters["inception_date"] = date_filter

        # Tags filter (any match)
        if request.tags:
            filters["tags"] = {"contains_any": request.tags}

        return filters

    @staticmethod
    def build_user_query_filters(request: UserQueryRequest) -> Dict[str, Any]:
        """Build DynamoDB query filters from UserQueryRequest."""
        filters = {}

        # Direct field filters
        if request.role:
            filters["role"] = request.role
        if request.status:
            filters["status"] = request.status
        if request.department:
            filters["department"] = request.department
        if request.manager_id:
            filters["manager_id"] = request.manager_id
        if request.is_verified is not None:
            filters["is_verified"] = request.is_verified
        if request.is_mfa_enabled is not None:
            filters["is_mfa_enabled"] = request.is_mfa_enabled

        # Date filters
        if request.created_at_from or request.created_at_to:
            date_filter = {}
            if request.created_at_from:
                date_filter["gte"] = request.created_at_from
            if request.created_at_to:
                date_filter["lte"] = request.created_at_to
            filters["created_at"] = date_filter

        # Access groups filter (any match)
        if request.access_groups:
            filters["access_groups"] = {"contains_any": request.access_groups}

        # Tags filter (any match)
        if request.tags:
            filters["tags"] = {"contains_any": request.tags}

        return filters

    @staticmethod
    def build_report_query_filters(request: ReportQueryRequest) -> Dict[str, Any]:
        """Build DynamoDB query filters from ReportQueryRequest."""
        filters = {}

        # Direct field filters
        if request.report_type:
            filters["report_type"] = request.report_type
        if request.status:
            filters["status"] = request.status
        if request.format:
            filters["format"] = request.format
        if request.user_id:
            filters["user_id"] = request.user_id

        # Date filters
        if request.created_at_from or request.created_at_to:
            date_filter = {}
            if request.created_at_from:
                date_filter["gte"] = request.created_at_from
            if request.created_at_to:
                date_filter["lte"] = request.created_at_to
            filters["created_at"] = date_filter

        if request.completed_at_from or request.completed_at_to:
            date_filter = {}
            if request.completed_at_from:
                date_filter["gte"] = request.completed_at_from
            if request.completed_at_to:
                date_filter["lte"] = request.completed_at_to
            filters["completed_at"] = date_filter

        return filters


# ============================================================================
# Response Formatting Utilities
# ============================================================================


class ResponseFormatter:
    """Utility class for formatting API responses."""

    @staticmethod
    def format_error_response(
        error: Exception, error_code: Optional[str] = None
    ) -> Dict[str, Any]:
        """Format an error response with consistent structure."""
        return {
            "success": False,
            "error_code": error_code or "UNKNOWN_ERROR",
            "message": str(error),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    @staticmethod
    def format_validation_error_response(
        validation_errors: List[Dict[str, str]],
    ) -> Dict[str, Any]:
        """Format validation error response."""
        return {
            "success": False,
            "error_code": "VALIDATION_ERROR",
            "message": "Request validation failed",
            "validation_errors": validation_errors,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    @staticmethod
    def format_success_response(
        data: Any, message: Optional[str] = None
    ) -> Dict[str, Any]:
        """Format a success response with consistent structure."""
        return {
            "success": True,
            "data": data,
            "message": message,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


# ============================================================================
# Enhanced Model Conversion and Validation Utilities
# ============================================================================


class AdvancedModelConverter(ModelConverter):
    """Extended model converter with additional specialized conversions."""

    @staticmethod
    def create_fund_comparison_response(
        funds: List[Fund], comparison_data: Dict[str, Any]
    ) -> "FundComparisonResponse":
        """Create a fund comparison response from fund objects and comparison data."""
        from .responses import FundComparisonResponse

        fund_summaries = [
            ModelConverter.fund_to_summary_response(fund) for fund in funds
        ]

        return FundComparisonResponse(
            funds=fund_summaries,
            comparison_metrics=comparison_data.get("metrics", {}),
            analysis_date=datetime.now(timezone.utc),
            relative_performance=comparison_data.get("relative_performance"),
        )

    @staticmethod
    def create_performance_response(
        fund: Fund, performance_data: Dict[str, Any]
    ) -> "FundPerformanceResponse":
        """Create a performance analysis response."""
        from .responses import FundPerformanceResponse

        return FundPerformanceResponse(
            fund_id=fund.fund_id,
            fund_name=fund.name,
            analysis_period=performance_data.get("period", {}),
            performance_metrics=performance_data.get("metrics", {}),
            benchmark_comparison=performance_data.get("benchmark_comparison"),
            risk_analysis=performance_data.get("risk_analysis"),
        )

    @staticmethod
    def create_bulk_update_response(
        total_count: int,
        successful_updates: List[str],
        failed_updates: Dict[str, List[str]],
    ) -> "UserBulkUpdateResponse":
        """Create a bulk user update response."""
        from .responses import UserBulkUpdateResponse

        return UserBulkUpdateResponse(
            total_processed=total_count,
            successful=len(successful_updates),
            failed=len(failed_updates),
            updated_users=successful_updates,
            validation_errors=failed_updates,
            errors=[
                {"user_id": user_id, "errors": errors}
                for user_id, errors in failed_updates.items()
            ],
        )

    @staticmethod
    def create_batch_operation_response(
        batch_id: str,
        operation_results: List[Dict[str, Any]],
        execution_time: Optional[float] = None,
        rollback_performed: bool = False,
    ) -> "BatchOperationResponse":
        """Create a batch operation response."""
        from .responses import BatchOperationResponse

        total_ops = len(operation_results)
        completed_ops = len([r for r in operation_results if r.get("success", False)])
        failed_ops = total_ops - completed_ops

        return BatchOperationResponse(
            batch_id=batch_id,
            total_operations=total_ops,
            completed_operations=completed_ops,
            failed_operations=failed_ops,
            operation_results=operation_results,
            rollback_performed=rollback_performed,
            execution_time_seconds=execution_time,
        )


class EnhancedValidationUtils(ValidationUtils):
    """Enhanced validation utilities with additional business logic checks."""

    @staticmethod
    def validate_fund_comparison_request(
        fund_ids: List[str], available_funds: List[Fund]
    ) -> List[str]:
        """Validate fund comparison request parameters."""
        errors = []
        available_fund_ids = {fund.fund_id for fund in available_funds}

        for fund_id in fund_ids:
            if fund_id not in available_fund_ids:
                errors.append(f"Fund with ID '{fund_id}' not found")

        # Check for duplicate fund IDs
        if len(fund_ids) != len(set(fund_ids)):
            errors.append("Duplicate fund IDs are not allowed in comparison")

        return errors

    @staticmethod
    def validate_performance_request_dates(
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        fund_inception_date: Optional[datetime],
    ) -> List[str]:
        """Validate performance analysis date parameters."""
        errors = []

        if start_date and end_date and start_date >= end_date:
            errors.append("Start date must be before end date")

        if start_date and fund_inception_date and start_date < fund_inception_date:
            errors.append("Start date cannot be before fund inception date")

        if end_date and end_date > datetime.now(timezone.utc):
            errors.append("End date cannot be in the future")

        return errors

    @staticmethod
    def validate_bulk_operation_permissions(
        user_permissions: Dict[str, bool], operation_type: str, resource_count: int
    ) -> List[str]:
        """Validate permissions for bulk operations."""
        errors = []

        # Check if user has bulk operation permissions
        if not user_permissions.get("bulk_operations", False):
            errors.append("User does not have permission for bulk operations")

        # Check resource-specific permissions
        if operation_type == "update_users" and not user_permissions.get(
            "manage_users", False
        ):
            errors.append("User does not have permission to manage users")

        # Check operation limits
        max_bulk_size = 50 if user_permissions.get("admin", False) else 20
        if resource_count > max_bulk_size:
            errors.append(
                f"Bulk operation size ({resource_count}) exceeds limit ({max_bulk_size})"
            )

        return errors


class RequestSanitizer:
    """Utility class for sanitizing and normalizing request data."""

    @staticmethod
    def sanitize_search_query(query: str) -> str:
        """Sanitize search query to prevent injection attacks."""
        import re

        # Remove potentially dangerous characters
        sanitized = re.sub(r'[<>"\';\\]', "", query)
        # Normalize whitespace
        sanitized = " ".join(sanitized.split())
        return sanitized[:200]  # Limit length

    @staticmethod
    def normalize_filter_values(filters: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize filter values for consistent querying."""
        normalized = {}
        for key, value in filters.items():
            if isinstance(value, str):
                # Normalize string values
                normalized[key] = value.strip().lower()
            elif isinstance(value, list):
                # Normalize list values
                normalized[key] = [
                    v.strip().lower() if isinstance(v, str) else v for v in value
                ]
            else:
                normalized[key] = value
        return normalized

    @staticmethod
    def sanitize_email_list(emails: List[str]) -> List[str]:
        """Sanitize and validate email list."""
        import re

        email_pattern = r"^[^\s@]+@[^\s@]+\.[^\s@]+$"
        sanitized = []

        for email in emails:
            email = email.strip().lower()
            if re.match(email_pattern, email):
                sanitized.append(email)

        return list(set(sanitized))  # Remove duplicates
