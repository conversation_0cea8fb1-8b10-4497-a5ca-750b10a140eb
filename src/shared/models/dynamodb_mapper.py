"""
Enhanced DynamoDB mapping utilities for all model types.
Provides type-safe conversion between Pydantic models and DynamoDB items.
"""

from datetime import datetime, timezone
from decimal import Decimal
import decimal
from typing import Any, Dict, List, Optional, Type, TypeVar, Union
import json

from pydantic import BaseModel, field_serializer, ConfigDict

# Import all model types
from .fund import Fund, FundType, FundStatus, RiskLevel, Currency
from .user import User, UserRole, UserStatus, Department
from .report import Report, ReportType, ReportStatus, ReportFormat
from .relationships import (
    FundReference,
    UserReference,
    ReportReference,
    FundSubscription,
    UserPortfolio,
    AuditLog,
    FundHierarchy,
    InvestmentStrategy,
    FundManager,
    ReportSchedule,
    UserFundAccess,
    FundBenchmark,
    FundComparison,
    Notification,
    FundDataSource,
)

T = TypeVar("T", bound=BaseModel)


class DynamoDBTypeConverter:
    """Handles conversion between Python types and DynamoDB types."""

    @staticmethod
    def to_dynamodb_value(value: Any) -> Any:
        """Convert Python value to DynamoDB-compatible value."""
        if value is None:
            return None
        elif isinstance(value, datetime):
            return value.isoformat()
        elif isinstance(value, Decimal):
            return str(value)
        elif isinstance(value, (int, float, str, bool)):
            return value
        elif isinstance(value, list):
            return [DynamoDBTypeConverter.to_dynamodb_value(item) for item in value]
        elif isinstance(value, dict):
            return {
                k: DynamoDBTypeConverter.to_dynamodb_value(v) for k, v in value.items()
            }
        elif isinstance(value, BaseModel):
            return DynamoDBTypeConverter.to_dynamodb_value(value.dict())
        elif hasattr(value, "value"):  # Enum
            return value.value
        else:
            # Fallback to string representation
            return str(value)

    @staticmethod
    def from_dynamodb_value(value: Any, target_type: Optional[Type] = None) -> Any:
        """Convert DynamoDB value back to Python value."""
        if value is None:
            return None
        elif isinstance(value, str) and target_type:
            if target_type == datetime:
                try:
                    return datetime.fromisoformat(value)
                except ValueError:
                    return value
            elif target_type == Decimal:
                try:
                    return Decimal(value)
                except (ValueError, TypeError):
                    return value
        elif isinstance(value, list):
            return [
                DynamoDBTypeConverter.from_dynamodb_value(item, target_type)
                for item in value
            ]
        elif isinstance(value, dict):
            return {
                k: DynamoDBTypeConverter.from_dynamodb_value(v, target_type)
                for k, v in value.items()
            }

        return value


class DynamoDBMapper:
    """Enhanced DynamoDB mapping utility for all model types."""

    # Define fields that contain Decimal values for each model type
    DECIMAL_FIELDS = {
        "Fund": [
            "nav",
            "total_assets",
            "expense_ratio",
            "minimum_investment",
            "performance_metrics.ytd_return",
            "performance_metrics.one_year_return",
            "performance_metrics.three_year_return",
            "performance_metrics.five_year_return",
            "performance_metrics.inception_return",
            "performance_metrics.volatility",
            "performance_metrics.sharpe_ratio",
            "performance_metrics.max_drawdown",
            "holdings.sector_allocation",
            "holdings.geographic_allocation",
        ],
        "User": [],  # Users don't typically have decimal fields
        "Report": [],  # Reports don't typically have decimal fields
    }

    # Define fields that contain datetime values for each model type
    DATETIME_FIELDS = {
        "Fund": ["created_at", "updated_at", "inception_date"],
        "User": [
            "created_at",
            "updated_at",
            "last_password_change",
            "email_verified_at",
            "session_info.last_login",
            "session_info.last_activity",
        ],
        "Report": ["created_at", "updated_at", "completed_at"],
    }

    @classmethod
    def to_dynamodb_item(cls, model: BaseModel) -> Dict[str, Any]:
        """
        Convert any Pydantic model to DynamoDB item format.

        Args:
            model: Pydantic model instance

        Returns:
            Dictionary suitable for DynamoDB operations
        """
        # Get the model data as a dictionary
        item = model.dict(exclude_none=True)  # Exclude None values to save space

        # Convert all values to DynamoDB-compatible types
        converted_item = cls._convert_to_dynamodb_types(item)

        # Add metadata for reconstruction
        converted_item["_model_type"] = model.__class__.__name__
        converted_item["_model_version"] = getattr(model, "__version__", "1.0")

        return converted_item

    @classmethod
    def from_dynamodb_item(cls, item: Dict[str, Any], model_class: Type[T]) -> T:
        """
        Convert DynamoDB item back to Pydantic model.

        Args:
            item: DynamoDB item dictionary
            model_class: Target Pydantic model class

        Returns:
            Pydantic model instance
        """
        # Remove metadata fields
        clean_item = {k: v for k, v in item.items() if not k.startswith("_")}

        # Convert DynamoDB types back to Python types
        converted_item = cls._convert_from_dynamodb_types(
            clean_item, model_class.__name__
        )

        # Create and return the model instance
        return model_class(**converted_item)

    @classmethod
    def _convert_to_dynamodb_types(cls, obj: Any) -> Any:
        """Recursively convert object to DynamoDB-compatible types."""
        if obj is None:
            return None
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return str(obj)
        elif isinstance(obj, (int, float, str, bool)):
            return obj
        elif isinstance(obj, list):
            return [cls._convert_to_dynamodb_types(item) for item in obj]
        elif isinstance(obj, dict):
            return {k: cls._convert_to_dynamodb_types(v) for k, v in obj.items()}
        elif isinstance(obj, BaseModel):
            return cls._convert_to_dynamodb_types(obj.dict())
        elif hasattr(obj, "value"):  # Enum
            return obj.value
        else:
            # Fallback to string representation
            return str(obj)

    @classmethod
    def _convert_from_dynamodb_types(cls, obj: Any, model_type: str) -> Any:
        """Recursively convert DynamoDB types back to Python types."""
        if obj is None:
            return None
        elif isinstance(obj, list):
            return [cls._convert_from_dynamodb_types(item, model_type) for item in obj]
        elif isinstance(obj, dict):
            return {
                k: cls._convert_from_dynamodb_types(v, model_type)
                for k, v in obj.items()
            }
        elif isinstance(obj, str):
            # Try to convert string values back to appropriate types
            # This is a simplified approach - in production, you might want more sophisticated type detection

            # Try datetime conversion
            if cls._looks_like_datetime(obj):
                try:
                    return datetime.fromisoformat(obj)
                except ValueError:
                    pass

            # Try decimal conversion for known decimal fields
            if cls._should_be_decimal(obj, model_type):
                try:
                    return Decimal(obj)
                except (ValueError, TypeError):
                    pass

            return obj
        else:
            return obj

    @classmethod
    def _looks_like_datetime(cls, value: str) -> bool:
        """Check if a string looks like an ISO datetime."""
        if not isinstance(value, str):
            return False

        # Simple heuristic: contains 'T' and has reasonable length
        return "T" in value and len(value) >= 19 and len(value) <= 32

    @classmethod
    def _should_be_decimal(cls, value: str, model_type: str) -> bool:
        """Check if a string value should be converted to Decimal."""
        if not isinstance(value, str):
            return False

        # Only convert if it looks like a number (contains digits and decimal point)
        import re

        if not re.match(r"^-?\d+(\.\d+)?$", value):
            return False

        # Try to parse as decimal
        try:
            Decimal(value)
            return True
        except (ValueError, TypeError, decimal.InvalidOperation):
            return False

    @classmethod
    def create_partition_key(cls, model: BaseModel) -> str:
        """
        Create a partition key for the model.

        Args:
            model: Pydantic model instance

        Returns:
            Partition key string
        """
        if isinstance(model, Fund):
            return f"FUND#{model.fund_id}"
        elif isinstance(model, User):
            return f"USER#{model.user_id}"
        elif isinstance(model, Report):
            return f"REPORT#{model.report_id}"
        else:
            # Generic approach for other models
            model_type = model.__class__.__name__.upper()
            # Try to find an ID field
            for field_name in ["id", f"{model_type.lower()}_id", "pk", "key"]:
                if hasattr(model, field_name):
                    return f"{model_type}#{getattr(model, field_name)}"

            # Fallback to model type only
            return model_type

    @classmethod
    def create_sort_key(cls, model: BaseModel, sort_type: Optional[str] = None) -> str:
        """
        Create a sort key for the model.

        Args:
            model: Pydantic model instance
            sort_type: Optional sort type for different access patterns

        Returns:
            Sort key string
        """
        if sort_type:
            return f"{sort_type}#{model.created_at.isoformat()}"
        else:
            return f"METADATA#{model.created_at.isoformat()}"

    @classmethod
    def create_gsi_keys(cls, model: BaseModel) -> Dict[str, Dict[str, str]]:
        """
        Create Global Secondary Index keys for the model.

        Args:
            model: Pydantic model instance

        Returns:
            Dictionary of GSI names to key dictionaries
        """
        gsi_keys = {}

        if isinstance(model, Fund):
            # Fund type index
            gsi_keys["fund_type_index"] = {
                "pk": f"FUND_TYPE#{model.fund_type}",
                "sk": model.created_at.isoformat(),
            }

            # Status index
            gsi_keys["status_index"] = {
                "pk": f"FUND_STATUS#{model.status}",
                "sk": model.created_at.isoformat(),
            }

        elif isinstance(model, User):
            # Email index
            gsi_keys["email_index"] = {
                "pk": f"EMAIL#{model.email}",
                "sk": model.user_id,
            }

            # Role index
            gsi_keys["role_index"] = {
                "pk": f"USER_ROLE#{model.role}",
                "sk": model.created_at.isoformat(),
            }

        elif isinstance(model, Report):
            # User reports index
            gsi_keys["user_reports_index"] = {
                "pk": f"USER#{model.user_id}",
                "sk": model.created_at.isoformat(),
            }

            # Report type index
            gsi_keys["report_type_index"] = {
                "pk": f"REPORT_TYPE#{model.report_type}",
                "sk": model.created_at.isoformat(),
            }

        return gsi_keys

    @classmethod
    def create_full_dynamodb_item(cls, model: BaseModel) -> Dict[str, Any]:
        """
        Create a complete DynamoDB item with all keys and metadata.

        Args:
            model: Pydantic model instance

        Returns:
            Complete DynamoDB item with partition key, sort key, and GSI keys
        """
        # Start with the basic item
        item = cls.to_dynamodb_item(model)

        # Add primary keys
        item["pk"] = cls.create_partition_key(model)
        item["sk"] = cls.create_sort_key(model)

        # Add GSI keys
        gsi_keys = cls.create_gsi_keys(model)
        for gsi_name, keys in gsi_keys.items():
            item[f"{gsi_name}_pk"] = keys["pk"]
            item[f"{gsi_name}_sk"] = keys["sk"]

        # Add timestamps for TTL and tracking
        item["_created_at"] = model.created_at.isoformat()
        item["_updated_at"] = model.updated_at.isoformat()

        return item


# Convenience functions for specific model types
class FundDynamoDBMapper(DynamoDBMapper):
    """Specialized mapper for Fund models."""

    @classmethod
    def to_item(cls, fund: Fund) -> Dict[str, Any]:
        """Convert Fund to DynamoDB item."""
        return cls.create_full_dynamodb_item(fund)

    @classmethod
    def from_item(cls, item: Dict[str, Any]) -> Fund:
        """Convert DynamoDB item to Fund."""
        return cls.from_dynamodb_item(item, Fund)


class UserDynamoDBMapper(DynamoDBMapper):
    """Specialized mapper for User models."""

    @classmethod
    def to_item(cls, user: User) -> Dict[str, Any]:
        """Convert User to DynamoDB item."""
        return cls.create_full_dynamodb_item(user)

    @classmethod
    def from_item(cls, item: Dict[str, Any]) -> User:
        """Convert DynamoDB item to User."""
        return cls.from_dynamodb_item(item, User)


class ReportDynamoDBMapper(DynamoDBMapper):
    """Specialized mapper for Report models."""

    @classmethod
    def to_item(cls, report: Report) -> Dict[str, Any]:
        """Convert Report to DynamoDB item."""
        return cls.create_full_dynamodb_item(report)

    @classmethod
    def from_item(cls, item: Dict[str, Any]) -> Report:
        """Convert DynamoDB item to Report."""
        return cls.from_dynamodb_item(item, Report)


# Utility functions for batch operations
class BatchDynamoDBMapper:
    """Utility for batch DynamoDB operations."""

    @classmethod
    def to_batch_write_items(cls, models: List[BaseModel]) -> List[Dict[str, Any]]:
        """Convert list of models to batch write format."""
        return [DynamoDBMapper.create_full_dynamodb_item(model) for model in models]

    @classmethod
    def from_batch_read_items(
        cls, items: List[Dict[str, Any]], model_class: Type[T]
    ) -> List[T]:
        """Convert list of DynamoDB items to models."""
        return [DynamoDBMapper.from_dynamodb_item(item, model_class) for item in items]
