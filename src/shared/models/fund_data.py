"""
Fund data models for DynamoDB single-table design.
Defines detailed fund information, statistics, returns, and exposure data.
"""

from datetime import date, timezone
from decimal import Decimal
from typing import Dict, List, Literal, Optional, Any
from pydantic import BaseModel, field_serializer, Field, field_validator, ConfigDict
from aws_lambda_powertools.utilities.parser import BaseModel as PowertoolsBaseModel


class BaseItem(BaseModel):
    """Base model for DynamoDB single-table design items."""

    PK: str = Field(..., description="Partition key")
    SK: str = Field(..., description="Sort key")
    entity_type: str = Field(..., description="Entity type identifier")


class FundInfo(BaseItem):
    """
    Fund information entity for single-table design.

    DynamoDB Access Pattern:
    - PK: FUND#{fund_id}
    - SK: INFO#{as_of_date}
    - entity_type: INFO
    """

    entity_type: Literal["INFO"] = "INFO"
    as_of: date = Field(..., description="Data as of date")
    fund_name: str = Field(..., min_length=1, max_length=200, description="Fund name")
    summary: str = Field(..., max_length=1000, description="Fund summary description")
    structure: str = Field(..., description="Fund structure (e.g., UCITS, SICAV)")
    base_currency: str = Field(
        ..., pattern=r"^[A-Z]{3}$", description="Base currency code"
    )
    fund_managers: List[str] = Field(..., description="List of fund manager names")
    share_classes: List[str] = Field(..., description="Available share classes")
    subscription_freq: str = Field(..., description="Subscription frequency")
    redemption_freq: str = Field(..., description="Redemption frequency")
    notice_period_days: int = Field(..., ge=0, description="Notice period in days")
    management_fee: Dict[str, Decimal] = Field(
        ..., description="Management fee by share class"
    )
    performance_fee_pct: Decimal = Field(
        ..., ge=0, le=100, description="Performance fee percentage"
    )
    status: Literal["active", "closed", "suspended"] = Field(
        default="active", description="Fund status"
    )

    @field_validator("management_fee")
    def validate_management_fees(cls, v):
        """Validate management fee values are reasonable percentages."""
        for share_class, fee in v.items():
            if fee < 0 or fee > 10:  # 0% to 10% is reasonable range
                raise ValueError(
                    f"Management fee for {share_class} must be between 0% and 10%"
                )
        return v

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("management_fee", when_used="json")
    def serialize_decimal_dict(
        self, value: Optional[Dict[str, Decimal]]
    ) -> Optional[Dict[str, str]]:
        """Serialize Decimal values in dictionaries to strings for JSON serialization."""
        if value is None:
            return None
        return {k: str(v) for k, v in value.items()}

    @field_serializer("performance_fee_pct", when_used="json")
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON serialization."""
        return str(value) if value is not None else None

    @field_serializer("as_of", when_used="json")
    def serialize_date(self, value: Optional[date]) -> Optional[str]:
        """Serialize date fields to ISO format for JSON serialization."""
        return value.isoformat() if value is not None else None


class FundStats(BaseItem):
    """
    Fund statistics entity for single-table design.

    DynamoDB Access Pattern:
    - PK: FUND#{fund_id}
    - SK: STATS#{as_of_date}
    - entity_type: STATS
    """

    entity_type: Literal["STATS"] = "STATS"
    as_of: date = Field(..., description="Statistics as of date")
    aum_usd_bn: Decimal = Field(
        ..., ge=0, description="Assets under management in USD billions"
    )
    sharpe_ratio: float = Field(..., description="Sharpe ratio")
    beta: float = Field(..., description="Beta coefficient")
    correlation: float = Field(..., ge=-1, le=1, description="Correlation coefficient")
    volatility_pct: float = Field(..., ge=0, description="Volatility percentage")
    inception_date: date = Field(..., description="Fund inception date")

    @field_validator("aum_usd_bn")
    def validate_aum(cls, v):
        """Validate AUM is a reasonable value."""
        if v > 1000:  # More than $1 trillion seems unreasonable
            raise ValueError("AUM exceeds reasonable limit")
        return v

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("aum_usd_bn", when_used="json")
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON serialization."""
        return str(value) if value is not None else None

    @field_serializer("as_of", "inception_date", when_used="json")
    def serialize_date(self, value: Optional[date]) -> Optional[str]:
        """Serialize date fields to ISO format for JSON serialization."""
        return value.isoformat() if value is not None else None


class MonthlyReturn(BaseItem):
    """
    Monthly return entity for single-table design.

    DynamoDB Access Pattern:
    - PK: FUND#{fund_id}
    - SK: RETURN#{year}#{month:02d}
    - entity_type: RETURN
    """

    entity_type: Literal["RETURN"] = "RETURN"
    year: int = Field(..., ge=1900, le=2100, description="Return year")
    month: int = Field(..., ge=1, le=12, description="Return month (1-12)")
    net_return_pct: float = Field(..., description="Net return percentage")

    @field_validator("net_return_pct")
    def validate_return(cls, v):
        """Validate return is within reasonable bounds."""
        if v < -100 or v > 1000:  # -100% to 1000% is reasonable range
            raise ValueError("Return percentage is outside reasonable bounds")
        return v


class CalendarYearReturn(BaseItem):
    """
    Calendar year return entity for single-table design.

    DynamoDB Access Pattern:
    - PK: FUND#{fund_id}
    - SK: CAL_YEAR_RET#{year}
    - entity_type: CAL_YEAR_RET
    """

    entity_type: Literal["CAL_YEAR_RET"] = "CAL_YEAR_RET"
    year: int = Field(..., ge=1900, le=2100, description="Calendar year")
    net_return_pct: float = Field(..., description="Net return percentage for the year")
    benchmarks: Dict[str, float] = Field(
        ..., description="Benchmark returns for comparison"
    )

    @field_validator("net_return_pct")
    def validate_return(cls, v):
        """Validate return is within reasonable bounds."""
        if v < -100 or v > 1000:
            raise ValueError("Return percentage is outside reasonable bounds")
        return v

    @field_validator("benchmarks")
    def validate_benchmarks(cls, v):
        """Validate benchmark returns are reasonable."""
        for benchmark, return_val in v.items():
            if return_val < -100 or return_val > 1000:
                raise ValueError(
                    f"Benchmark {benchmark} return is outside reasonable bounds"
                )
        return v


class ExposureSnapshot(BaseItem):
    """
    Exposure snapshot entity for single-table design.

    DynamoDB Access Pattern:
    - PK: FUND#{fund_id}
    - SK: EXPOSURE#{as_of_date}
    - entity_type: EXPOSURE
    """

    entity_type: Literal["EXPOSURE"] = "EXPOSURE"
    as_of: date = Field(..., description="Exposure data as of date")
    long_pct: float = Field(..., ge=0, description="Long exposure percentage")
    short_pct: float = Field(..., le=0, description="Short exposure percentage")
    net_public_pct: float = Field(
        ..., description="Net public equity exposure percentage"
    )
    net_private_pct: float = Field(
        ..., description="Net private equity exposure percentage"
    )
    gross_pct: float = Field(..., ge=0, description="Gross exposure percentage")
    positive_months: int = Field(
        ..., ge=0, description="Number of positive return months"
    )
    negative_months: int = Field(
        ..., ge=0, description="Number of negative return months"
    )

    @field_validator("short_pct")
    def validate_short_exposure(cls, v):
        """Validate short exposure is negative or zero."""
        if v > 0:
            raise ValueError("Short exposure should be negative or zero")
        return v

    @field_validator("gross_pct")
    def validate_gross_exposure(cls, v):
        """Validate gross exposure is reasonable."""
        if v > 1000:  # More than 1000% seems unreasonable
            raise ValueError("Gross exposure exceeds reasonable limit")
        return v

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("as_of", when_used="json")
    def serialize_date(self, value: Optional[date]) -> Optional[str]:
        """Serialize date fields to ISO format for JSON serialization."""
        return value.isoformat() if value is not None else None


class MarketCapExposure(BaseItem):
    """
    Market cap exposure entity for single-table design.

    DynamoDB Access Pattern:
    - PK: FUND#{fund_id}
    - SK: MCAP_EXP#{as_of_date}
    - entity_type: MCAP_EXP
    """

    entity_type: Literal["MCAP_EXP"] = "MCAP_EXP"
    as_of: date = Field(..., description="Market cap exposure data as of date")
    buckets: Dict[str, Dict[str, float]] = Field(
        ...,
        description="Market cap exposure buckets (e.g., large_cap, mid_cap, small_cap)",
    )

    @field_validator("buckets")
    def validate_buckets(cls, v):
        """Validate market cap exposure buckets."""
        for bucket_name, bucket_data in v.items():
            for metric, value in bucket_data.items():
                if not isinstance(value, (int, float)):
                    raise ValueError(
                        f"Invalid value type in bucket {bucket_name}.{metric}"
                    )
                if value < -100 or value > 100:  # Reasonable percentage range
                    raise ValueError(
                        f"Value in bucket {bucket_name}.{metric} is outside reasonable bounds"
                    )
        return v

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("as_of", when_used="json")
    def serialize_date(self, value: Optional[date]) -> Optional[str]:
        """Serialize date fields to ISO format for JSON serialization."""
        return value.isoformat() if value is not None else None


# Helper functions for key generation
def generate_fund_info_pk(fund_id: str) -> str:
    """Generate partition key for fund info."""
    return f"FUND#{fund_id}"


def generate_fund_info_sk(as_of: date) -> str:
    """Generate sort key for fund info."""
    return f"INFO#{as_of.isoformat()}"


def generate_fund_stats_sk(as_of: date) -> str:
    """Generate sort key for fund statistics."""
    return f"STATS#{as_of.isoformat()}"


def generate_monthly_return_sk(year: int, month: int) -> str:
    """Generate sort key for monthly returns."""
    return f"RETURN#{year}#{month:02d}"


def generate_calendar_year_return_sk(year: int) -> str:
    """Generate sort key for calendar year returns."""
    return f"CAL_YEAR_RET#{year}"


def generate_exposure_sk(as_of: date) -> str:
    """Generate sort key for exposure snapshots."""
    return f"EXPOSURE#{as_of.isoformat()}"


def generate_market_cap_exposure_sk(as_of: date) -> str:
    """Generate sort key for market cap exposure."""
    return f"MCAP_EXP#{as_of.isoformat()}"


# DynamoDB conversion utilities
class FundDataDynamoDBItem:
    """Utility class for converting fund data models to/from DynamoDB items."""

    @staticmethod
    def to_dynamodb_item(item: BaseItem) -> Dict[str, Any]:
        """Convert a fund data model to DynamoDB item format."""
        item_dict = item.model_dump()

        # Convert Decimal to string for DynamoDB
        def convert_decimals(obj):
            if isinstance(obj, dict):
                return {k: convert_decimals(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_decimals(item) for item in obj]
            elif isinstance(obj, Decimal):
                return str(obj)
            elif isinstance(obj, date):
                return obj.isoformat()
            return obj

        return convert_decimals(item_dict)

    @staticmethod
    def from_dynamodb_item(item: Dict[str, Any], model_class) -> BaseItem:
        """Convert DynamoDB item to fund data model."""

        # Convert string dates back to date objects where needed
        converted_item = item.copy()

        # Convert date strings for date fields
        date_fields = ["as_of", "inception_date"]
        for field in date_fields:
            if field in converted_item and isinstance(converted_item[field], str):
                try:
                    from datetime import datetime, timezone

                    converted_item[field] = datetime.fromisoformat(
                        converted_item[field]
                    ).date()
                except (ValueError, AttributeError):
                    pass  # Keep original value if conversion fails

        # Convert Decimal string values to Decimal objects for specific fields
        decimal_fields = ["aum_usd_bn", "performance_fee_pct"]
        for field in decimal_fields:
            if field in converted_item and isinstance(converted_item[field], str):
                try:
                    converted_item[field] = Decimal(converted_item[field])
                except (ValueError, TypeError):
                    pass  # Keep original value if conversion fails

        # Convert management_fee values to Decimal
        if "management_fee" in converted_item and isinstance(
            converted_item["management_fee"], dict
        ):
            for key, value in converted_item["management_fee"].items():
                if isinstance(value, str):
                    try:
                        converted_item["management_fee"][key] = Decimal(value)
                    except (ValueError, TypeError):
                        pass

        return model_class(**converted_item)
