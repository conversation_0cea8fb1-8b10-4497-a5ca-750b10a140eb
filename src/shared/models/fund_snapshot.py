"""
Fund snapshot data model for monthly data storage.
Stores monthly snapshots of market data, holdings, performance metrics, and risk analytics.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List, cast
from decimal import Decimal

from pydantic import BaseModel, Field, field_serializer, field_validator, ConfigDict
from aws_lambda_powertools.utilities.parser import BaseModel as PowertoolsBaseModel

from .fund import Holdings, PerformanceMetrics
from .market_data import (
    MarketDataSummary,
    PriceData,
    ValuationMetrics,
    TechnicalIndicators,
    RiskAnalytics,
)


class FundSnapshot(PowertoolsBaseModel):
    """
    Fund snapshot model for monthly data storage.
    
    DynamoDB Table: fundflow-{env}-fund-snapshots
    Primary Key: fund_id (HASH), snapshot_month (RANGE)
    Local Secondary Index: fund_id (HASH), created_at (RANGE)
    """
    
    # Primary Keys
    fund_id: str = Field(..., description="Fund identifier")
    snapshot_month: str = Field(
        ..., 
        pattern=r"^\d{4}-(0[1-9]|1[0-2])$",
        description="Snapshot month in YYYY-MM format"
    )
    
    # Snapshot Data
    market_data: Optional[MarketDataSummary] = Field(
        None, description="Complete market data snapshot"
    )
    holdings: Optional[Holdings] = Field(
        None, description="Holdings information for this month"
    )
    performance_metrics: Optional[PerformanceMetrics] = Field(
        None, description="Performance metrics and KPIs"
    )
    risk_analytics: Optional[RiskAnalytics] = Field(
        None, description="Risk analytics for this month"
    )
    
    # Additional snapshot-specific data
    nav: Optional[Decimal] = Field(None, gt=0, description="Net Asset Value for this month")
    total_assets: Optional[Decimal] = Field(
        None, ge=0, description="Total assets under management for this month"
    )
    
    # Metadata
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Snapshot creation timestamp"
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Last update timestamp"
    )
    created_by: str = Field(..., description="User who created/updated this snapshot")
    
    # Optional metadata
    notes: Optional[str] = Field(
        None, max_length=1000, description="Additional notes about this snapshot"
    )
    data_sources: Optional[Dict[str, str]] = Field(
        default_factory=dict, description="Data sources used for this snapshot"
    )
    
    @field_validator("snapshot_month")
    def validate_snapshot_month(cls, v):
        """Validate snapshot month format and ensure it's not in the future."""
        # Parse the month
        year, month = v.split("-")
        snapshot_date = datetime(int(year), int(month), 1, tzinfo=timezone.utc)
        
        # Ensure it's not in the future
        current_date = datetime.now(timezone.utc)
        if snapshot_date > current_date:
            raise ValueError("Snapshot month cannot be in the future")
        
        return v
    
    @field_validator("updated_at", mode="before")
    def set_updated_at(cls, _v):
        """Always update the updated_at timestamp."""
        return datetime.now(timezone.utc)
    
    @field_validator("nav", "total_assets", mode="before")
    def convert_decimal_fields(cls, v):
        """Convert float values to Decimal for DynamoDB compatibility."""
        if v is not None and isinstance(v, (float, int)):
            return Decimal(str(v))
        return v
    
    @field_serializer("nav", "total_assets", when_used="json")
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON serialization."""
        return str(value) if value is not None else None
    
    @field_serializer("created_at", "updated_at", when_used="json")
    def serialize_datetime(self, value: datetime) -> str:
        """Serialize datetime to ISO format."""
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()
    
    model_config = ConfigDict(
        use_enum_values=True,
        validate_assignment=True,
    )


class FundSnapshotCreate(BaseModel):
    """Model for creating a fund snapshot."""
    
    market_data: Optional[MarketDataSummary] = None
    holdings: Optional[Holdings] = None
    performance_metrics: Optional[PerformanceMetrics] = None
    risk_analytics: Optional[RiskAnalytics] = None
    nav: Optional[Decimal] = Field(None, gt=0)
    total_assets: Optional[Decimal] = Field(None, ge=0)
    notes: Optional[str] = Field(None, max_length=1000)
    data_sources: Optional[Dict[str, str]] = None


class FundSnapshotUpdate(BaseModel):
    """Model for updating a fund snapshot."""
    
    market_data: Optional[MarketDataSummary] = None
    holdings: Optional[Holdings] = None
    performance_metrics: Optional[PerformanceMetrics] = None
    risk_analytics: Optional[RiskAnalytics] = None
    nav: Optional[Decimal] = Field(None, gt=0)
    total_assets: Optional[Decimal] = Field(None, ge=0)
    notes: Optional[str] = Field(None, max_length=1000)
    data_sources: Optional[Dict[str, str]] = None


class FundSnapshotResponse(FundSnapshot):
    """Fund snapshot response model for API responses."""
    pass


# DynamoDB specific utilities
class FundSnapshotDynamoDBItem:
    """Utility class for DynamoDB item transformations."""
    
    @staticmethod
    def to_dynamodb_item(snapshot: FundSnapshot) -> Dict[str, Any]:
        """Convert FundSnapshot model to DynamoDB item format."""
        item = snapshot.model_dump(by_alias=True)
        
        # Convert datetime and Decimal objects for DynamoDB
        def convert_values(obj):
            from datetime import date
            
            if isinstance(obj, dict):
                return {k: convert_values(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_values(v) for v in obj]
            elif isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, date):
                # Convert date to datetime with timezone info
                dt = datetime.combine(obj, datetime.min.time()).replace(
                    tzinfo=timezone.utc
                )
                return dt.isoformat()
            elif isinstance(obj, Decimal):
                return str(obj)
            return obj
        
        # Convert all values recursively
        return cast(Dict[str, Any], convert_values(item))
    
    @staticmethod
    def from_dynamodb_item(item: Dict[str, Any]) -> FundSnapshot:
        """Convert DynamoDB item to FundSnapshot model."""
        
        def convert_values(obj, key=None, parent_key=None):
            if isinstance(obj, dict):
                return {k: convert_values(v, k, key) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_values(v, None, key) for v in obj]
            elif isinstance(obj, str):
                # Convert datetime strings
                if key in ["created_at", "updated_at", "last_updated"]:
                    try:
                        dt = datetime.fromisoformat(obj)
                        # Ensure datetime is timezone-aware
                        if dt.tzinfo is None:
                            dt = dt.replace(tzinfo=timezone.utc)
                        return dt
                    except:
                        return obj
                # Convert Decimal strings
                elif key in [
                    "nav", "total_assets",
                    # Performance metrics fields
                    "ytd_return", "one_day_return", "one_week_return",
                    "one_month_return", "three_month_return", "six_month_return",
                    "one_year_return", "three_year_return", "five_year_return",
                    "inception_return", "volatility", "sharpe_ratio", "sortino_ratio",
                    "calmar_ratio", "information_ratio", "treynor_ratio",
                    "max_drawdown", "downside_deviation", "tracking_error",
                    "alpha", "beta", "correlation", "var_1d_95", "var_1d_99",
                    # Holdings fields
                    "holdings_concentration",
                    # Market data fields
                    "bid_ask_spread", "bid_ask_spread_pct", "market_cap",
                    "price_change_1d", "price_change_1d_pct",
                    "price_change_ytd", "price_change_ytd_pct",
                ] or parent_key in [
                    "sector_allocation", "geographic_allocation",
                    "asset_allocation", "market_cap_allocation",
                    "currency_allocation"
                ]:
                    try:
                        return Decimal(obj)
                    except:
                        return obj
            return obj
        
        converted_item = convert_values(item)
        
        # Ensure converted_item is a dictionary with string keys
        if not isinstance(converted_item, dict):
            raise ValueError("Invalid DynamoDB item format: expected dictionary")
        
        # Cast to the expected type for type checker
        snapshot_data = cast(Dict[str, Any], converted_item)
        
        return FundSnapshot(**snapshot_data)