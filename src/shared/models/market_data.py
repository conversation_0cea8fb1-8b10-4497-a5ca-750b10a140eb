"""
Market data models for comprehensive fund analysis.
Defines price data, valuation metrics, technical indicators, and market data sources.
"""

from datetime import datetime, timezone, date
from typing import Optional, Dict, Any, List, Literal
from decimal import Decimal
from enum import Enum

from pydantic import (
    BaseModel,
    field_serializer,
    Field,
    field_validator,
    ConfigDict,
)
from aws_lambda_powertools.utilities.parser import BaseModel as PowertoolsBaseModel


class MarketDataSource(str, Enum):
    """Market data source enumeration."""
    
    BLOOMBERG = "bloomberg"
    REUTERS = "reuters"
    YAHOO_FINANCE = "yahoo_finance"
    ALPHA_VANTAGE = "alpha_vantage"
    QUANDL = "quandl"
    MANUAL_INPUT = "manual_input"
    FUND_COMPANY = "fund_company"
    REGULATORY_FILING = "regulatory_filing"


class DataQuality(str, Enum):
    """Data quality enumeration."""
    
    EXCELLENT = "excellent"  # Real-time, verified
    GOOD = "good"           # Near real-time, reliable
    FAIR = "fair"           # Delayed, generally reliable
    POOR = "poor"           # Stale or questionable
    UNKNOWN = "unknown"     # Quality not assessed


class PriceType(str, Enum):
    """Price type enumeration."""
    
    NAV = "nav"                    # Net Asset Value
    MARKET_PRICE = "market_price"  # Market trading price
    BID = "bid"                    # Bid price
    ASK = "ask"                    # Ask price
    CLOSE = "close"                # Closing price
    OPEN = "open"                  # Opening price
    HIGH = "high"                  # High price
    LOW = "low"                    # Low price


class MarketDataPoint(BaseModel):
    """Individual market data point with metadata."""
    
    timestamp: datetime = Field(..., description="Data timestamp")
    value: Decimal = Field(..., description="Data value")
    source: MarketDataSource = Field(..., description="Data source")
    quality: DataQuality = Field(default=DataQuality.UNKNOWN, description="Data quality")
    currency: str = Field(..., pattern=r"^[A-Z]{3}$", description="Currency code")
    
    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)
    
    @field_serializer("value", when_used="json")
    def serialize_decimal(self, value: Decimal) -> str:
        """Serialize Decimal to string for JSON."""
        return str(value)
    
    @field_serializer("timestamp", when_used="json")
    def serialize_datetime(self, value: datetime) -> str:
        """Serialize datetime to ISO format."""
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()


class PriceData(BaseModel):
    """Comprehensive price data for a fund."""
    
    fund_id: str = Field(..., description="Fund identifier")
    as_of: datetime = Field(..., description="Price data as of timestamp")
    
    # Core pricing
    nav: Optional[MarketDataPoint] = Field(None, description="Net Asset Value")
    market_price: Optional[MarketDataPoint] = Field(None, description="Market trading price")
    bid: Optional[MarketDataPoint] = Field(None, description="Bid price")
    ask: Optional[MarketDataPoint] = Field(None, description="Ask price")
    
    # Daily OHLC data
    open_price: Optional[MarketDataPoint] = Field(None, description="Opening price")
    high_price: Optional[MarketDataPoint] = Field(None, description="High price")
    low_price: Optional[MarketDataPoint] = Field(None, description="Low price")
    close_price: Optional[MarketDataPoint] = Field(None, description="Closing price")
    
    # Volume and liquidity
    volume: Optional[int] = Field(None, ge=0, description="Trading volume")
    avg_volume_30d: Optional[int] = Field(None, ge=0, description="30-day average volume")
    bid_ask_spread: Optional[Decimal] = Field(None, ge=0, description="Bid-ask spread")
    bid_ask_spread_pct: Optional[Decimal] = Field(None, ge=0, description="Bid-ask spread percentage")
    
    # Market cap and valuation
    market_cap: Optional[Decimal] = Field(None, ge=0, description="Market capitalization")
    shares_outstanding: Optional[int] = Field(None, ge=0, description="Shares outstanding")
    
    # Price changes
    price_change_1d: Optional[Decimal] = Field(None, description="1-day price change")
    price_change_1d_pct: Optional[Decimal] = Field(None, description="1-day price change percentage")
    price_change_ytd: Optional[Decimal] = Field(None, description="Year-to-date price change")
    price_change_ytd_pct: Optional[Decimal] = Field(None, description="YTD price change percentage")
    
    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)
    
    @field_serializer("bid_ask_spread", "bid_ask_spread_pct", "market_cap", 
                     "price_change_1d", "price_change_1d_pct", 
                     "price_change_ytd", "price_change_ytd_pct", when_used="json")
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON."""
        return str(value) if value is not None else None
    
    @field_serializer("as_of", when_used="json")
    def serialize_datetime(self, value: datetime) -> str:
        """Serialize datetime to ISO format."""
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()


class ValuationMetrics(BaseModel):
    """Comprehensive valuation metrics for fundamental analysis."""
    
    fund_id: str = Field(..., description="Fund identifier")
    as_of: date = Field(..., description="Valuation data as of date")
    
    # Price ratios
    price_to_book: Optional[Decimal] = Field(None, ge=0, description="Price-to-book ratio")
    price_to_earnings: Optional[Decimal] = Field(None, ge=0, description="Price-to-earnings ratio")
    price_to_sales: Optional[Decimal] = Field(None, ge=0, description="Price-to-sales ratio")
    price_to_cash_flow: Optional[Decimal] = Field(None, ge=0, description="Price-to-cash-flow ratio")
    
    # Enterprise value metrics
    enterprise_value: Optional[Decimal] = Field(None, ge=0, description="Enterprise value")
    ev_to_revenue: Optional[Decimal] = Field(None, ge=0, description="EV/Revenue ratio")
    ev_to_ebitda: Optional[Decimal] = Field(None, description="EV/EBITDA ratio")
    
    # Profitability metrics
    return_on_equity: Optional[Decimal] = Field(None, description="Return on equity percentage")
    return_on_assets: Optional[Decimal] = Field(None, description="Return on assets percentage")
    return_on_invested_capital: Optional[Decimal] = Field(None, description="ROIC percentage")
    
    # Financial strength
    debt_to_equity: Optional[Decimal] = Field(None, ge=0, description="Debt-to-equity ratio")
    current_ratio: Optional[Decimal] = Field(None, ge=0, description="Current ratio")
    quick_ratio: Optional[Decimal] = Field(None, ge=0, description="Quick ratio")
    
    # Book value metrics
    book_value_per_share: Optional[Decimal] = Field(None, description="Book value per share")
    tangible_book_value: Optional[Decimal] = Field(None, description="Tangible book value")
    
    # Dividend metrics
    dividend_yield: Optional[Decimal] = Field(None, ge=0, description="Dividend yield percentage")
    dividend_payout_ratio: Optional[Decimal] = Field(None, ge=0, description="Dividend payout ratio")
    
    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)
    
    @field_serializer("price_to_book", "price_to_earnings", "price_to_sales", 
                     "price_to_cash_flow", "enterprise_value", "ev_to_revenue", 
                     "ev_to_ebitda", "return_on_equity", "return_on_assets", 
                     "return_on_invested_capital", "debt_to_equity", "current_ratio", 
                     "quick_ratio", "book_value_per_share", "tangible_book_value", 
                     "dividend_yield", "dividend_payout_ratio", when_used="json")
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON."""
        return str(value) if value is not None else None
    
    @field_serializer("as_of", when_used="json")
    def serialize_date(self, value: date) -> str:
        """Serialize date to ISO format."""
        return value.isoformat()


class TechnicalIndicators(BaseModel):
    """Technical analysis indicators for fund price analysis."""

    fund_id: str = Field(..., description="Fund identifier")
    as_of: datetime = Field(..., description="Technical indicators as of timestamp")

    # Moving averages
    sma_20: Optional[Decimal] = Field(None, description="20-day Simple Moving Average")
    sma_50: Optional[Decimal] = Field(None, description="50-day Simple Moving Average")
    sma_200: Optional[Decimal] = Field(None, description="200-day Simple Moving Average")
    ema_12: Optional[Decimal] = Field(None, description="12-day Exponential Moving Average")
    ema_26: Optional[Decimal] = Field(None, description="26-day Exponential Moving Average")

    # Momentum indicators
    rsi_14: Optional[Decimal] = Field(None, ge=0, le=100, description="14-day RSI")
    macd_line: Optional[Decimal] = Field(None, description="MACD line")
    macd_signal: Optional[Decimal] = Field(None, description="MACD signal line")
    macd_histogram: Optional[Decimal] = Field(None, description="MACD histogram")

    # Volatility indicators
    bollinger_upper: Optional[Decimal] = Field(None, description="Bollinger Band upper")
    bollinger_middle: Optional[Decimal] = Field(None, description="Bollinger Band middle")
    bollinger_lower: Optional[Decimal] = Field(None, description="Bollinger Band lower")
    atr_14: Optional[Decimal] = Field(None, ge=0, description="14-day Average True Range")

    # Volume indicators
    vwap: Optional[Decimal] = Field(None, description="Volume Weighted Average Price")
    volume_sma_20: Optional[int] = Field(None, ge=0, description="20-day volume SMA")

    # Support and resistance
    support_level: Optional[Decimal] = Field(None, description="Current support level")
    resistance_level: Optional[Decimal] = Field(None, description="Current resistance level")

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("sma_20", "sma_50", "sma_200", "ema_12", "ema_26",
                     "rsi_14", "macd_line", "macd_signal", "macd_histogram",
                     "bollinger_upper", "bollinger_middle", "bollinger_lower",
                     "atr_14", "vwap", "support_level", "resistance_level", when_used="json")
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON."""
        return str(value) if value is not None else None

    @field_serializer("as_of", when_used="json")
    def serialize_datetime(self, value: datetime) -> str:
        """Serialize datetime to ISO format."""
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()


class RiskAnalytics(BaseModel):
    """Advanced risk analytics and metrics."""

    fund_id: str = Field(..., description="Fund identifier")
    as_of: date = Field(..., description="Risk analytics as of date")

    # Value at Risk
    var_1d_95: Optional[Decimal] = Field(None, description="1-day VaR at 95% confidence")
    var_1d_99: Optional[Decimal] = Field(None, description="1-day VaR at 99% confidence")
    var_10d_95: Optional[Decimal] = Field(None, description="10-day VaR at 95% confidence")
    var_10d_99: Optional[Decimal] = Field(None, description="10-day VaR at 99% confidence")

    # Conditional VaR (Expected Shortfall)
    cvar_1d_95: Optional[Decimal] = Field(None, description="1-day CVaR at 95% confidence")
    cvar_1d_99: Optional[Decimal] = Field(None, description="1-day CVaR at 99% confidence")

    # Risk ratios
    sharpe_ratio: Optional[Decimal] = Field(None, description="Sharpe ratio")
    sortino_ratio: Optional[Decimal] = Field(None, description="Sortino ratio")
    calmar_ratio: Optional[Decimal] = Field(None, description="Calmar ratio")
    information_ratio: Optional[Decimal] = Field(None, description="Information ratio")
    treynor_ratio: Optional[Decimal] = Field(None, description="Treynor ratio")

    # Drawdown metrics
    max_drawdown: Optional[Decimal] = Field(None, le=0, description="Maximum drawdown")
    max_drawdown_duration: Optional[int] = Field(None, ge=0, description="Max drawdown duration in days")
    current_drawdown: Optional[Decimal] = Field(None, le=0, description="Current drawdown")

    # Volatility metrics
    volatility_1m: Optional[Decimal] = Field(None, ge=0, description="1-month volatility")
    volatility_3m: Optional[Decimal] = Field(None, ge=0, description="3-month volatility")
    volatility_1y: Optional[Decimal] = Field(None, ge=0, description="1-year volatility")
    downside_deviation: Optional[Decimal] = Field(None, ge=0, description="Downside deviation")

    # Correlation and beta
    beta_vs_benchmark: Optional[Decimal] = Field(None, description="Beta vs benchmark")
    correlation_vs_benchmark: Optional[Decimal] = Field(None, ge=-1, le=1, description="Correlation vs benchmark")
    tracking_error: Optional[Decimal] = Field(None, ge=0, description="Tracking error vs benchmark")

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("var_1d_95", "var_1d_99", "var_10d_95", "var_10d_99",
                     "cvar_1d_95", "cvar_1d_99", "sharpe_ratio", "sortino_ratio",
                     "calmar_ratio", "information_ratio", "treynor_ratio",
                     "max_drawdown", "current_drawdown", "volatility_1m",
                     "volatility_3m", "volatility_1y", "downside_deviation",
                     "beta_vs_benchmark", "correlation_vs_benchmark", "tracking_error", when_used="json")
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON."""
        return str(value) if value is not None else None

    @field_serializer("as_of", when_used="json")
    def serialize_date(self, value: date) -> str:
        """Serialize date to ISO format."""
        return value.isoformat()


class MarketDataInput(BaseModel):
    """Model for manual market data input."""

    fund_id: str = Field(..., description="Fund identifier")
    input_timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc),
                                    description="When the data was input")
    data_timestamp: datetime = Field(..., description="Timestamp for the market data")
    input_by: str = Field(..., description="User who input the data")

    # Price data inputs
    nav: Optional[Decimal] = Field(None, gt=0, description="Net Asset Value")
    market_price: Optional[Decimal] = Field(None, gt=0, description="Market price")
    volume: Optional[int] = Field(None, ge=0, description="Trading volume")

    # Valuation inputs
    price_to_book: Optional[Decimal] = Field(None, ge=0, description="P/B ratio")
    price_to_earnings: Optional[Decimal] = Field(None, ge=0, description="P/E ratio")
    dividend_yield: Optional[Decimal] = Field(None, ge=0, description="Dividend yield %")

    # Risk inputs
    volatility: Optional[Decimal] = Field(None, ge=0, description="Volatility %")
    beta: Optional[Decimal] = Field(None, description="Beta coefficient")

    # Notes and validation
    notes: Optional[str] = Field(None, max_length=1000, description="Input notes")
    validated: bool = Field(default=False, description="Whether data has been validated")
    validation_notes: Optional[str] = Field(None, max_length=500, description="Validation notes")

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("nav", "market_price", "price_to_book", "price_to_earnings",
                     "dividend_yield", "volatility", "beta", when_used="json")
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON."""
        return str(value) if value is not None else None

    @field_serializer("input_timestamp", "data_timestamp", when_used="json")
    def serialize_datetime(self, value: datetime) -> str:
        """Serialize datetime to ISO format."""
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()


class BenchmarkData(BaseModel):
    """Benchmark data for fund comparison."""

    benchmark_id: str = Field(..., description="Benchmark identifier")
    name: str = Field(..., description="Benchmark name")
    symbol: str = Field(..., description="Benchmark symbol")
    as_of: datetime = Field(..., description="Benchmark data as of timestamp")

    # Price and performance
    current_value: Decimal = Field(..., description="Current benchmark value")

    # Performance periods
    return_1d: Optional[Decimal] = Field(None, description="1-day return %")
    return_1w: Optional[Decimal] = Field(None, description="1-week return %")
    return_1m: Optional[Decimal] = Field(None, description="1-month return %")
    return_3m: Optional[Decimal] = Field(None, description="3-month return %")
    return_6m: Optional[Decimal] = Field(None, description="6-month return %")
    return_1y: Optional[Decimal] = Field(None, description="1-year return %")
    return_3y: Optional[Decimal] = Field(None, description="3-year return %")
    return_5y: Optional[Decimal] = Field(None, description="5-year return %")

    # Risk metrics
    volatility: Optional[Decimal] = Field(None, ge=0, description="Volatility %")
    max_drawdown: Optional[Decimal] = Field(None, le=0, description="Max drawdown %")

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("current_value", "return_1d", "return_1w", "return_1m",
                     "return_3m", "return_6m", "return_1y", "return_3y", "return_5y",
                     "volatility", "max_drawdown", when_used="json")
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON."""
        return str(value) if value is not None else None

    @field_serializer("as_of", when_used="json")
    def serialize_datetime(self, value: datetime) -> str:
        """Serialize datetime to ISO format."""
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()


class MarketDataSummary(BaseModel):
    """Comprehensive market data summary for a fund."""

    fund_id: str = Field(..., description="Fund identifier")
    last_updated: datetime = Field(..., description="Last update timestamp")

    # Current data
    price_data: Optional[PriceData] = Field(None, description="Current price data")
    valuation_metrics: Optional[ValuationMetrics] = Field(None, description="Valuation metrics")
    technical_indicators: Optional[TechnicalIndicators] = Field(None, description="Technical indicators")
    risk_analytics: Optional[RiskAnalytics] = Field(None, description="Risk analytics")

    # Benchmarks
    primary_benchmark: Optional[BenchmarkData] = Field(None, description="Primary benchmark")
    secondary_benchmarks: List[BenchmarkData] = Field(default_factory=list, description="Additional benchmarks")

    # Data quality and sources
    data_sources: Dict[str, MarketDataSource] = Field(default_factory=dict, description="Data sources by field")
    overall_quality: DataQuality = Field(default=DataQuality.UNKNOWN, description="Overall data quality")

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("last_updated", when_used="json")
    def serialize_datetime(self, value: datetime) -> str:
        """Serialize datetime to ISO format."""
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()
