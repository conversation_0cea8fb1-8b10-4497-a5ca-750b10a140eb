"""
Portfolio data model for DynamoDB operations.
Defines the structure and validation for Portfolio entities.
"""

from datetime import datetime, timezone, date
from typing import Optional, Dict, Any, List
from decimal import Decimal
from enum import Enum

from pydantic import (
    BaseModel,
    field_serializer,
    Field,
    field_validator,
    model_validator,
    ConfigDict,
)
from aws_lambda_powertools.utilities.parser import BaseModel as PowertoolsBaseModel

# Import fund models for relationships
from .fund import Currency, RiskLevel


class PortfolioStatus(str, Enum):
    """Portfolio status enumeration."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    CLOSED = "closed"
    LIQUIDATING = "liquidating"


class TransactionType(str, Enum):
    """Transaction type enumeration."""

    BUY = "buy"
    SELL = "sell"
    DIVIDEND = "dividend"
    INTEREST = "interest"
    FEE = "fee"
    TRANSFER_IN = "transfer_in"
    TRANSFER_OUT = "transfer_out"
    SPLIT = "split"
    MERGER = "merger"


class PortfolioType(str, Enum):
    """Portfolio type enumeration."""

    PERSONAL = "personal"
    RETIREMENT = "retirement"
    TAXABLE = "taxable"
    TRUST = "trust"
    CORPORATE = "corporate"
    EDUCATION = "education"


class PortfolioHolding(BaseModel):
    """Individual holding within a portfolio."""

    # Identifiers
    fund_id: str = Field(..., description="Fund identifier")
    fund_name: str = Field(..., description="Fund name for display")
    fund_symbol: Optional[str] = Field(None, description="Fund symbol/ticker")

    # Position details
    shares: Decimal = Field(..., ge=0, description="Number of shares held")
    average_cost: Decimal = Field(..., gt=0, description="Average cost per share")
    current_price: Decimal = Field(..., gt=0, description="Current price per share")
    
    # Calculated values
    market_value: Decimal = Field(..., ge=0, description="Current market value")
    cost_basis: Decimal = Field(..., ge=0, description="Total cost basis")
    unrealized_gain_loss: Decimal = Field(..., description="Unrealized gain/loss")
    unrealized_gain_loss_pct: Decimal = Field(..., description="Unrealized gain/loss percentage")
    
    # Portfolio allocation
    weight: Decimal = Field(..., ge=0, le=100, description="Weight in portfolio as percentage")
    
    # Metadata
    first_purchase_date: datetime = Field(..., description="Date of first purchase")
    last_updated: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Last update timestamp"
    )

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer(
        "shares", "average_cost", "current_price", "market_value", 
        "cost_basis", "unrealized_gain_loss", "unrealized_gain_loss_pct", "weight",
        when_used="json"
    )
    def serialize_decimal(self, value: Decimal) -> str:
        """Serialize Decimal fields to string for JSON serialization."""
        return str(value)

    @field_serializer("first_purchase_date", "last_updated", when_used="json")
    def serialize_datetime(self, value: datetime) -> str:
        """Serialize datetime to ISO format."""
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()


class PortfolioTransaction(BaseModel):
    """Individual transaction within a portfolio."""

    # Identifiers
    transaction_id: str = Field(..., description="Unique transaction identifier")
    fund_id: str = Field(..., description="Fund identifier")
    fund_name: str = Field(..., description="Fund name for display")
    fund_symbol: Optional[str] = Field(None, description="Fund symbol/ticker")

    # Transaction details
    transaction_type: TransactionType = Field(..., description="Type of transaction")
    transaction_date: datetime = Field(..., description="Transaction date")
    settlement_date: Optional[datetime] = Field(None, description="Settlement date")
    
    # Financial details
    shares: Optional[Decimal] = Field(None, description="Number of shares (for buy/sell)")
    price: Optional[Decimal] = Field(None, gt=0, description="Price per share")
    amount: Decimal = Field(..., description="Total transaction amount")
    fees: Decimal = Field(default=Decimal("0"), ge=0, description="Transaction fees")
    net_amount: Decimal = Field(..., description="Net amount after fees")
    
    # Additional details
    description: Optional[str] = Field(None, max_length=500, description="Transaction description")
    reference_number: Optional[str] = Field(None, description="Reference or confirmation number")
    
    # Metadata
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Creation timestamp"
    )

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer(
        "shares", "price", "amount", "fees", "net_amount",
        when_used="json"
    )
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON serialization."""
        return str(value) if value is not None else None

    @field_serializer("transaction_date", "settlement_date", "created_at", when_used="json")
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        """Serialize datetime to ISO format."""
        if value is None:
            return None
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()


class PortfolioPerformance(BaseModel):
    """Portfolio performance metrics."""

    # Return periods
    total_return: Decimal = Field(..., description="Total return amount")
    total_return_pct: Decimal = Field(..., description="Total return percentage")
    
    # Time-based returns
    one_day_return: Optional[Decimal] = Field(None, description="One day return percentage")
    one_week_return: Optional[Decimal] = Field(None, description="One week return percentage")
    one_month_return: Optional[Decimal] = Field(None, description="One month return percentage")
    three_month_return: Optional[Decimal] = Field(None, description="Three month return percentage")
    six_month_return: Optional[Decimal] = Field(None, description="Six month return percentage")
    one_year_return: Optional[Decimal] = Field(None, description="One year return percentage")
    three_year_return: Optional[Decimal] = Field(None, description="Three year return percentage")
    five_year_return: Optional[Decimal] = Field(None, description="Five year return percentage")
    inception_return: Optional[Decimal] = Field(None, description="Return since inception percentage")

    # Risk metrics
    volatility: Optional[Decimal] = Field(None, description="Volatility percentage")
    sharpe_ratio: Optional[Decimal] = Field(None, description="Sharpe ratio")
    max_drawdown: Optional[Decimal] = Field(None, description="Maximum drawdown percentage")
    
    # Benchmark comparison
    benchmark_return: Optional[Decimal] = Field(None, description="Benchmark return percentage")
    alpha: Optional[Decimal] = Field(None, description="Alpha vs benchmark")
    beta: Optional[Decimal] = Field(None, description="Beta vs benchmark")
    
    # Last calculation date
    as_of_date: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Performance calculation date"
    )

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer(
        "total_return", "total_return_pct", "one_day_return", "one_week_return",
        "one_month_return", "three_month_return", "six_month_return", 
        "one_year_return", "three_year_return", "five_year_return", 
        "inception_return", "volatility", "sharpe_ratio", "max_drawdown",
        "benchmark_return", "alpha", "beta",
        when_used="json"
    )
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON serialization."""
        return str(value) if value is not None else None

    @field_serializer("as_of_date", when_used="json")
    def serialize_datetime(self, value: datetime) -> str:
        """Serialize datetime to ISO format."""
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()


class Portfolio(PowertoolsBaseModel):
    """
    Portfolio data model for DynamoDB.

    DynamoDB Table: fundflow-{env}-portfolios
    Primary Key: portfolio_id (HASH)
    Global Secondary Indexes:
    - portfolio_type_index: portfolio_type (HASH), created_at (RANGE)
    - status_index: status (HASH), created_at (RANGE)
    - user_index: user_id (HASH), created_at (RANGE)
    """

    # Primary Key
    portfolio_id: str = Field(..., description="Unique portfolio identifier")

    # Basic Information
    name: str = Field(..., min_length=1, max_length=200, description="Portfolio name")
    description: Optional[str] = Field(
        None, max_length=1000, description="Portfolio description"
    )
    portfolio_type: PortfolioType = Field(..., description="Type of portfolio")
    status: PortfolioStatus = Field(default=PortfolioStatus.ACTIVE, description="Portfolio status")

    # Owner Information
    user_id: str = Field(..., description="Owner user ID")

    # Financial Details
    base_currency: Currency = Field(default=Currency.USD, description="Base currency")
    inception_date: datetime = Field(..., description="Portfolio inception date")

    # Current Values
    total_value: Decimal = Field(default=Decimal("0"), ge=0, description="Total portfolio value")
    total_cost_basis: Decimal = Field(default=Decimal("0"), ge=0, description="Total cost basis")
    cash_balance: Decimal = Field(default=Decimal("0"), ge=0, description="Cash balance")

    # Calculated Fields
    total_gain_loss: Decimal = Field(default=Decimal("0"), description="Total unrealized gain/loss")
    total_gain_loss_pct: Decimal = Field(default=Decimal("0"), description="Total gain/loss percentage")

    # Holdings and Transactions
    holdings: List[PortfolioHolding] = Field(
        default_factory=list, description="Portfolio holdings"
    )
    recent_transactions: List[PortfolioTransaction] = Field(
        default_factory=list, description="Recent transactions (last 10)"
    )

    # Performance
    performance: Optional[PortfolioPerformance] = Field(
        None, description="Portfolio performance metrics"
    )

    # Risk and Analytics
    risk_level: Optional[RiskLevel] = Field(None, description="Overall portfolio risk level")
    benchmark: Optional[str] = Field(
        None, max_length=100, description="Benchmark index for comparison"
    )

    # Metadata
    tags: List[str] = Field(
        default_factory=list, description="Portfolio tags for categorization"
    )
    custom_fields: Dict[str, Any] = Field(
        default_factory=dict, description="Custom fields for extensibility"
    )

    # Timestamps
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Creation timestamp",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Last update timestamp",
    )
    last_rebalanced: Optional[datetime] = Field(
        None, description="Last rebalancing date"
    )

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_validator("updated_at", mode="before")
    def set_updated_at(cls, v):
        """Always update the updated_at timestamp."""
        return datetime.now(timezone.utc)

    @field_validator("tags", mode="before")
    def ensure_tags_list(cls, v):
        """Ensure tags is always a list."""
        if v is None:
            return []
        return v

    @field_validator("custom_fields", mode="before")
    def ensure_custom_fields_dict(cls, v):
        """Ensure custom_fields is always a dict."""
        if v is None:
            return {}
        return v

    @field_validator("inception_date")
    def validate_inception_date(cls, v):
        """Validate inception date is not in the future."""
        if v and v > datetime.now(timezone.utc):
            raise ValueError("Inception date cannot be in the future")
        return v

    @field_validator("total_value", "total_cost_basis", "cash_balance")
    def validate_financial_values(cls, v):
        """Validate financial values are reasonable."""
        if v is not None and v < 0:
            raise ValueError("Financial values cannot be negative")
        return v

    @model_validator(mode="after")
    def validate_portfolio_consistency(self):
        """Validate portfolio data consistency."""
        # Calculate total gain/loss if not provided
        if self.total_value is not None and self.total_cost_basis is not None:
            calculated_gain_loss = self.total_value - self.total_cost_basis
            # Only update if different to avoid recursion
            if self.total_gain_loss != calculated_gain_loss:
                self.total_gain_loss = calculated_gain_loss

            # Calculate percentage if cost basis > 0
            if self.total_cost_basis > 0:
                calculated_pct = (calculated_gain_loss / self.total_cost_basis) * 100
                # Only update if different to avoid recursion
                if self.total_gain_loss_pct != calculated_pct:
                    self.total_gain_loss_pct = calculated_pct

        return self

    @field_serializer(
        "total_value", "total_cost_basis", "cash_balance",
        "total_gain_loss", "total_gain_loss_pct",
        when_used="json"
    )
    def serialize_decimal(self, value: Decimal) -> str:
        """Serialize Decimal fields to string for JSON serialization."""
        return str(value)

    @field_serializer(
        "inception_date", "created_at", "updated_at", "last_rebalanced",
        when_used="json"
    )
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        """Serialize datetime to ISO format."""
        if value is None:
            return None
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()


# Request/Response Models
class PortfolioCreateRequest(BaseModel):
    """Request model for creating a portfolio."""

    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    portfolio_type: PortfolioType
    base_currency: Currency = Currency.USD
    inception_date: Optional[datetime] = None
    cash_balance: Decimal = Field(default=Decimal("0"), ge=0)
    risk_level: Optional[RiskLevel] = None
    benchmark: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(use_enum_values=True)


class PortfolioUpdateRequest(BaseModel):
    """Request model for updating a portfolio."""

    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    portfolio_type: Optional[PortfolioType] = None
    status: Optional[PortfolioStatus] = None
    base_currency: Optional[Currency] = None
    cash_balance: Optional[Decimal] = Field(None, ge=0)
    risk_level: Optional[RiskLevel] = None
    benchmark: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(use_enum_values=True)


class PortfolioResponse(Portfolio):
    """Portfolio response model for API responses."""

    # Add computed fields for API responses
    holdings_count: Optional[int] = Field(None, description="Number of holdings")
    transactions_count: Optional[int] = Field(None, description="Total number of transactions")

    @field_validator("holdings_count", mode="before")
    def calculate_holdings_count(cls, v, info):
        """Calculate holdings count from holdings list."""
        if hasattr(info.data, 'holdings') and info.data['holdings']:
            return len(info.data['holdings'])
        return 0


class AddHoldingRequest(BaseModel):
    """Request model for adding a holding to a portfolio."""

    fund_id: str = Field(..., description="Fund identifier")
    shares: Decimal = Field(..., gt=0, description="Number of shares")
    purchase_price: Decimal = Field(..., gt=0, description="Purchase price per share")
    purchase_date: datetime = Field(..., description="Purchase date")
    fees: Decimal = Field(default=Decimal("0"), ge=0, description="Transaction fees")

    model_config = ConfigDict(use_enum_values=True)


class AddTransactionRequest(BaseModel):
    """Request model for adding a transaction to a portfolio."""

    fund_id: str = Field(..., description="Fund identifier")
    transaction_type: TransactionType = Field(..., description="Type of transaction")
    transaction_date: datetime = Field(..., description="Transaction date")
    shares: Optional[Decimal] = Field(None, description="Number of shares (for buy/sell)")
    price: Optional[Decimal] = Field(None, gt=0, description="Price per share")
    amount: Decimal = Field(..., description="Total transaction amount")
    fees: Decimal = Field(default=Decimal("0"), ge=0, description="Transaction fees")
    description: Optional[str] = Field(None, max_length=500, description="Transaction description")
    reference_number: Optional[str] = Field(None, description="Reference number")

    model_config = ConfigDict(use_enum_values=True)


# DynamoDB specific utilities
class PortfolioDynamoDBItem:
    """Utility class for Portfolio DynamoDB item transformations."""

    @staticmethod
    def to_dynamodb_item(portfolio: Portfolio) -> Dict[str, Any]:
        """Convert Portfolio object to DynamoDB item format."""
        item = portfolio.model_dump()

        # Convert Decimal fields to strings for DynamoDB
        decimal_fields = [
            "total_value", "total_cost_basis", "cash_balance",
            "total_gain_loss", "total_gain_loss_pct"
        ]

        for field in decimal_fields:
            if field in item and item[field] is not None:
                item[field] = str(item[field])

        # Convert datetime fields to ISO strings
        datetime_fields = [
            "inception_date", "created_at", "updated_at", "last_rebalanced"
        ]

        for field in datetime_fields:
            if field in item and item[field] is not None:
                if isinstance(item[field], datetime):
                    if item[field].tzinfo is None:
                        item[field] = item[field].replace(tzinfo=timezone.utc)
                    item[field] = item[field].isoformat()

        # Convert holdings and transactions
        if "holdings" in item and item["holdings"]:
            for holding in item["holdings"]:
                # Convert Decimal fields in holdings
                holding_decimal_fields = [
                    "shares", "average_cost", "current_price", "market_value",
                    "cost_basis", "unrealized_gain_loss", "unrealized_gain_loss_pct", "weight"
                ]
                for field in holding_decimal_fields:
                    if field in holding and holding[field] is not None:
                        holding[field] = str(holding[field])

                # Convert datetime fields in holdings
                holding_datetime_fields = ["first_purchase_date", "last_updated"]
                for field in holding_datetime_fields:
                    if field in holding and holding[field] is not None:
                        if isinstance(holding[field], datetime):
                            if holding[field].tzinfo is None:
                                holding[field] = holding[field].replace(tzinfo=timezone.utc)
                            holding[field] = holding[field].isoformat()

        if "recent_transactions" in item and item["recent_transactions"]:
            for transaction in item["recent_transactions"]:
                # Convert Decimal fields in transactions
                transaction_decimal_fields = ["shares", "price", "amount", "fees", "net_amount"]
                for field in transaction_decimal_fields:
                    if field in transaction and transaction[field] is not None:
                        transaction[field] = str(transaction[field])

                # Convert datetime fields in transactions
                transaction_datetime_fields = ["transaction_date", "settlement_date", "created_at"]
                for field in transaction_datetime_fields:
                    if field in transaction and transaction[field] is not None:
                        if isinstance(transaction[field], datetime):
                            if transaction[field].tzinfo is None:
                                transaction[field] = transaction[field].replace(tzinfo=timezone.utc)
                            transaction[field] = transaction[field].isoformat()

        # Convert performance metrics if present
        if "performance" in item and item["performance"]:
            performance = item["performance"]
            performance_decimal_fields = [
                "total_return", "total_return_pct", "one_day_return", "one_week_return",
                "one_month_return", "three_month_return", "six_month_return",
                "one_year_return", "three_year_return", "five_year_return",
                "inception_return", "volatility", "sharpe_ratio", "max_drawdown",
                "benchmark_return", "alpha", "beta"
            ]
            for field in performance_decimal_fields:
                if field in performance and performance[field] is not None:
                    performance[field] = str(performance[field])

            if "as_of_date" in performance and performance["as_of_date"] is not None:
                if isinstance(performance["as_of_date"], datetime):
                    if performance["as_of_date"].tzinfo is None:
                        performance["as_of_date"] = performance["as_of_date"].replace(tzinfo=timezone.utc)
                    performance["as_of_date"] = performance["as_of_date"].isoformat()

        return item

    @staticmethod
    def from_dynamodb_item(item: Dict[str, Any]) -> Portfolio:
        """Convert DynamoDB item to Portfolio object."""
        # Convert string Decimal fields back to Decimal
        decimal_fields = [
            "total_value", "total_cost_basis", "cash_balance",
            "total_gain_loss", "total_gain_loss_pct"
        ]

        for field in decimal_fields:
            if field in item and item[field] is not None:
                item[field] = Decimal(str(item[field]))

        # Convert ISO string datetime fields back to datetime
        datetime_fields = [
            "inception_date", "created_at", "updated_at", "last_rebalanced"
        ]

        for field in datetime_fields:
            if field in item and item[field] is not None:
                if isinstance(item[field], str):
                    # Handle both timezone-aware and timezone-naive datetime strings
                    datetime_str = item[field].replace('Z', '+00:00')
                    parsed_datetime = datetime.fromisoformat(datetime_str)
                    # Ensure timezone-aware datetime (assume UTC if no timezone)
                    if parsed_datetime.tzinfo is None:
                        parsed_datetime = parsed_datetime.replace(tzinfo=timezone.utc)
                    item[field] = parsed_datetime

        # Convert holdings
        if "holdings" in item and item["holdings"]:
            for holding in item["holdings"]:
                # Convert Decimal fields in holdings
                holding_decimal_fields = [
                    "shares", "average_cost", "current_price", "market_value",
                    "cost_basis", "unrealized_gain_loss", "unrealized_gain_loss_pct", "weight"
                ]
                for field in holding_decimal_fields:
                    if field in holding and holding[field] is not None:
                        holding[field] = Decimal(str(holding[field]))

                # Convert datetime fields in holdings
                holding_datetime_fields = ["first_purchase_date", "last_updated"]
                for field in holding_datetime_fields:
                    if field in holding and holding[field] is not None:
                        if isinstance(holding[field], str):
                            # Handle both timezone-aware and timezone-naive datetime strings
                            datetime_str = holding[field].replace('Z', '+00:00')
                            parsed_datetime = datetime.fromisoformat(datetime_str)
                            # Ensure timezone-aware datetime (assume UTC if no timezone)
                            if parsed_datetime.tzinfo is None:
                                parsed_datetime = parsed_datetime.replace(tzinfo=timezone.utc)
                            holding[field] = parsed_datetime

        # Convert transactions
        if "recent_transactions" in item and item["recent_transactions"]:
            for transaction in item["recent_transactions"]:
                # Convert Decimal fields in transactions
                transaction_decimal_fields = ["shares", "price", "amount", "fees", "net_amount"]
                for field in transaction_decimal_fields:
                    if field in transaction and transaction[field] is not None:
                        transaction[field] = Decimal(str(transaction[field]))

                # Convert datetime fields in transactions
                transaction_datetime_fields = ["transaction_date", "settlement_date", "created_at"]
                for field in transaction_datetime_fields:
                    if field in transaction and transaction[field] is not None:
                        if isinstance(transaction[field], str):
                            # Handle both timezone-aware and timezone-naive datetime strings
                            datetime_str = transaction[field].replace('Z', '+00:00')
                            parsed_datetime = datetime.fromisoformat(datetime_str)
                            # Ensure timezone-aware datetime (assume UTC if no timezone)
                            if parsed_datetime.tzinfo is None:
                                parsed_datetime = parsed_datetime.replace(tzinfo=timezone.utc)
                            transaction[field] = parsed_datetime

        # Convert performance metrics if present
        if "performance" in item and item["performance"]:
            performance = item["performance"]
            performance_decimal_fields = [
                "total_return", "total_return_pct", "one_day_return", "one_week_return",
                "one_month_return", "three_month_return", "six_month_return",
                "one_year_return", "three_year_return", "five_year_return",
                "inception_return", "volatility", "sharpe_ratio", "max_drawdown",
                "benchmark_return", "alpha", "beta"
            ]
            for field in performance_decimal_fields:
                if field in performance and performance[field] is not None:
                    performance[field] = Decimal(str(performance[field]))

            if "as_of_date" in performance and performance["as_of_date"] is not None:
                if isinstance(performance["as_of_date"], str):
                    # Handle both timezone-aware and timezone-naive datetime strings
                    datetime_str = performance["as_of_date"].replace('Z', '+00:00')
                    parsed_datetime = datetime.fromisoformat(datetime_str)
                    # Ensure timezone-aware datetime (assume UTC if no timezone)
                    if parsed_datetime.tzinfo is None:
                        parsed_datetime = parsed_datetime.replace(tzinfo=timezone.utc)
                    performance["as_of_date"] = parsed_datetime

        return Portfolio(**item)
