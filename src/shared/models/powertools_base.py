"""
Base model for AWS Lambda Powertools integration.
"""

from datetime import datetime, timezone
from typing import Any, Dict
from pydantic import BaseModel, ConfigDict, field_serializer


class PowertoolsBaseModel(BaseModel):
    """
    Base model with AWS Lambda Powertools integration for enhanced logging and monitoring.

    Extends Pydantic BaseModel with additional functionality for:
    - Consistent datetime handling
    - Enhanced JSON serialization
    - DynamoDB compatibility
    """

    model_config = ConfigDict(
        # Use enum values instead of enum objects
        use_enum_values=True,
        # Allow population by field name (renamed in V2)
        populate_by_name=True,
        # Validate assignment
        validate_assignment=True,
        # Extra fields behavior
        extra="forbid",
    )

    @field_serializer("*", when_used="json")
    def serialize_datetime(self, value: Any) -> Any:
        """Serialize datetime objects to ISO format when used in JSON."""
        if isinstance(value, datetime):
            return value.isoformat()
        return value

    def dict_for_dynamodb(self) -> Dict[str, Any]:
        """
        Convert model to dictionary format suitable for DynamoDB storage.

        Returns:
            Dictionary with proper type conversion for DynamoDB
        """
        data = self.model_dump()

        # Convert datetime objects to ISO strings for DynamoDB
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()

        return data

    @classmethod
    def from_dynamodb_item(cls, item: Dict[str, Any]) -> "PowertoolsBaseModel":
        """
        Create model instance from DynamoDB item.

        Args:
            item: DynamoDB item dictionary

        Returns:
            Model instance
        """
        # Convert ISO string dates back to datetime objects
        for key, value in item.items():
            if isinstance(value, str):
                # Try to parse as datetime
                try:
                    # Check if it looks like an ISO datetime string
                    if "T" in value and (
                        "Z" in value or "+" in value or value.count("-") >= 2
                    ):
                        item[key] = datetime.fromisoformat(value.replace("Z", "+00:00"))
                except (ValueError, AttributeError):
                    # Not a datetime string, keep as is
                    pass

        return cls(**item)
