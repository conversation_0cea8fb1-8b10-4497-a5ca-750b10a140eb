"""
Model relationships and nested models for Fund, User, and Report entities.
Defines relationships, inheritance hierarchies, and reusable nested components.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List, Union, Literal
from decimal import Decimal
from enum import Enum

from pydantic import (
    BaseModel,
    Field,
    field_validator,
    model_validator,
    ConfigDict,
    field_serializer,
)
from aws_lambda_powertools.utilities.parser import BaseModel as PowertoolsBaseModel

# Import enums from existing models
from .fund import FundType, FundStatus, RiskLevel, Currency
from .user import UserRole, UserStatus, Department
from .report import ReportType, ReportStatus, ReportFormat


class BaseEntity(BaseModel):
    """Base entity with common fields for all models."""

    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    tags: Optional[List[str]] = Field(default_factory=list)
    custom_fields: Optional[Dict[str, Any]] = Field(default_factory=dict)

    @field_validator("updated_at", mode="before")
    def set_updated_at(cls, v):
        return datetime.now(timezone.utc)

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("created_at", "updated_at", when_used="json")
    def serialize_datetime(self, value: datetime) -> str:
        """Serialize datetime to ISO format."""
        return value.isoformat()

    @field_serializer("*", when_used="json")
    def serialize_decimal(self, value: Any) -> Any:
        """Serialize Decimal to string."""
        if isinstance(value, Decimal):
            return str(value)
        return value


class FundReference(BaseModel):
    """Lightweight fund reference for use in other models."""

    fund_id: str = Field(..., description="Fund identifier")
    name: str = Field(..., description="Fund name")
    fund_type: FundType = Field(..., description="Fund type")
    nav: Optional[Decimal] = Field(None, description="Current NAV")
    currency: Currency = Field(default=Currency.USD)

    model_config = ConfigDict()


class UserReference(BaseModel):
    """Lightweight user reference for use in other models."""

    user_id: str = Field(..., description="User identifier")
    full_name: str = Field(..., description="User's full name")
    email: str = Field(..., description="User's email")
    role: UserRole = Field(..., description="User role")
    department: Optional[Department] = Field(None, description="User department")


class ReportReference(BaseModel):
    """Lightweight report reference for use in other models."""

    report_id: str = Field(..., description="Report identifier")
    name: str = Field(..., description="Report name")
    report_type: ReportType = Field(..., description="Report type")
    status: ReportStatus = Field(..., description="Report status")
    created_at: datetime = Field(..., description="Creation timestamp")

    model_config = ConfigDict()


class FundSubscription(BaseModel):
    """Represents a user's subscription to fund updates."""

    fund_id: str = Field(..., description="Subscribed fund ID")
    fund_name: str = Field(..., description="Fund name for display")
    subscription_date: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc)
    )
    notification_preferences: Dict[str, bool] = Field(
        default_factory=lambda: {
            "nav_updates": True,
            "performance_alerts": True,
            "status_changes": True,
            "holdings_updates": False,
        }
    )
    is_active: bool = Field(default=True)

    model_config = ConfigDict()


class UserPortfolio(BaseModel):
    """Represents a user's portfolio tracking specific funds."""

    portfolio_id: str = Field(..., description="Portfolio identifier")
    name: str = Field(..., description="Portfolio name")
    description: Optional[str] = Field(None, description="Portfolio description")
    funds: List[FundReference] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    is_default: bool = Field(
        default=False, description="Is this the user's default portfolio"
    )

    model_config = ConfigDict()


class AuditLog(BaseModel):
    """Audit log entry for tracking changes across entities."""

    entity_type: Literal["fund", "user", "report"] = Field(
        ..., description="Type of entity"
    )
    entity_id: str = Field(..., description="ID of the affected entity")
    action: str = Field(..., description="Action performed (create, update, delete)")
    user_id: str = Field(..., description="User who performed the action")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    changes: Optional[Dict[str, Any]] = Field(
        None, description="Fields that were changed"
    )
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)

    model_config = ConfigDict()


class FundHierarchy(BaseModel):
    """Represents hierarchical relationships between funds (fund families, sub-funds)."""

    parent_fund_id: Optional[str] = Field(None, description="Parent fund ID")
    sub_funds: List[FundReference] = Field(default_factory=list)
    fund_family: Optional[str] = Field(None, description="Fund family name")
    fund_family_id: Optional[str] = Field(None, description="Fund family identifier")


class InvestmentStrategy(BaseModel):
    """Investment strategy details that can be shared across funds."""

    strategy_id: str = Field(..., description="Strategy identifier")
    name: str = Field(..., description="Strategy name")
    description: str = Field(..., description="Strategy description")
    risk_profile: RiskLevel = Field(..., description="Risk level")
    target_allocations: Dict[str, Decimal] = Field(
        default_factory=dict, description="Target asset allocations"
    )
    benchmark: Optional[str] = Field(None, description="Benchmark index")

    model_config = ConfigDict()


class FundManager(BaseModel):
    """Fund manager details with enhanced information."""

    manager_id: str = Field(..., description="Manager identifier")
    name: str = Field(..., description="Manager name")
    title: str = Field(..., description="Manager title")
    experience_years: Optional[int] = Field(None, description="Years of experience")
    education: Optional[str] = Field(None, description="Educational background")
    certifications: List[str] = Field(default_factory=list)
    investment_philosophy: Optional[str] = Field(
        None, description="Investment philosophy"
    )
    managed_funds: List[FundReference] = Field(default_factory=list)


class ReportSchedule(BaseModel):
    """Represents scheduled report generation."""

    schedule_id: str = Field(..., description="Schedule identifier")
    report_template: ReportReference = Field(..., description="Report template to use")
    user_id: str = Field(..., description="User who scheduled the report")
    cron_expression: str = Field(..., description="Cron expression for scheduling")
    is_active: bool = Field(default=True)
    last_run: Optional[datetime] = Field(None)
    next_run: Optional[datetime] = Field(None)
    recipients: List[str] = Field(default_factory=list, description="Email recipients")

    model_config = ConfigDict()


class UserFundAccess(BaseModel):
    """Represents a user's access permissions to specific funds."""

    user_id: str = Field(..., description="User identifier")
    fund_id: str = Field(..., description="Fund identifier")
    access_level: Literal["view", "edit", "admin"] = Field(
        ..., description="Access level"
    )
    granted_by: str = Field(..., description="User ID who granted access")
    granted_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    expires_at: Optional[datetime] = Field(None, description="Access expiration")
    restrictions: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Specific restrictions on access"
    )

    model_config = ConfigDict()


class FundBenchmark(BaseModel):
    """Benchmark comparison data for funds."""

    benchmark_id: str = Field(..., description="Benchmark identifier")
    name: str = Field(..., description="Benchmark name")
    symbol: str = Field(..., description="Benchmark symbol")
    description: Optional[str] = Field(None, description="Benchmark description")
    performance_data: Dict[str, Decimal] = Field(
        default_factory=dict, description="Historical performance data"
    )

    model_config = ConfigDict()


class FundComparison(BaseModel):
    """Represents a comparison between multiple funds."""

    comparison_id: str = Field(..., description="Comparison identifier")
    name: str = Field(..., description="Comparison name")
    funds: List[FundReference] = Field(..., description="Funds being compared")
    comparison_criteria: List[str] = Field(
        default_factory=list, description="Criteria for comparison"
    )
    created_by: str = Field(..., description="User who created the comparison")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    is_public: bool = Field(default=False, description="Is comparison publicly visible")

    model_config = ConfigDict()


class Notification(BaseModel):
    """Notification system for users."""

    notification_id: str = Field(..., description="Notification identifier")
    user_id: str = Field(..., description="Recipient user ID")
    title: str = Field(..., description="Notification title")
    message: str = Field(..., description="Notification message")
    notification_type: Literal[
        "fund_update", "report_ready", "system_alert", "performance_alert"
    ] = Field(..., description="Type of notification")
    entity_id: Optional[str] = Field(None, description="Related entity ID")
    entity_type: Optional[str] = Field(None, description="Related entity type")
    is_read: bool = Field(default=False)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    read_at: Optional[datetime] = Field(None)

    model_config = ConfigDict()


class FundDataSource(BaseModel):
    """Data source information for fund data."""

    source_id: str = Field(..., description="Data source identifier")
    name: str = Field(..., description="Data source name")
    provider: str = Field(..., description="Data provider")
    last_updated: datetime = Field(..., description="Last data update")
    reliability_score: Optional[Decimal] = Field(
        None, ge=0, le=100, description="Reliability score (0-100)"
    )
    fields_provided: List[str] = Field(
        default_factory=list, description="List of fields provided by this source"
    )

    model_config = ConfigDict()


# Complex relationship models
class EnhancedFundModel(BaseEntity):
    """Enhanced fund model with relationships."""

    fund_id: str = Field(..., description="Unique fund identifier")
    name: str = Field(..., description="Fund name")
    fund_type: FundType = Field(..., description="Fund type")
    status: FundStatus = Field(default=FundStatus.ACTIVE)

    # Relationships
    manager: Optional[FundManager] = Field(None, description="Fund manager")
    strategy: Optional[InvestmentStrategy] = Field(
        None, description="Investment strategy"
    )
    hierarchy: Optional[FundHierarchy] = Field(None, description="Fund hierarchy")
    benchmarks: List[FundBenchmark] = Field(default_factory=list)
    data_sources: List[FundDataSource] = Field(default_factory=list)

    # Enhanced fields
    nav: Optional[Decimal] = Field(None, gt=0, description="Net Asset Value")
    currency: Currency = Field(default=Currency.USD)
    total_assets: Optional[Decimal] = Field(None, ge=0)

    # Validation
    @model_validator(mode="after")
    def validate_hierarchy(self):
        """Validate fund hierarchy doesn't create circular references."""
        if self.hierarchy and self.hierarchy.parent_fund_id and self.fund_id:
            if self.hierarchy.parent_fund_id == self.fund_id:
                raise ValueError("Fund cannot be its own parent")
        return self


class EnhancedUserModel(BaseEntity):
    """Enhanced user model with relationships."""

    user_id: str = Field(..., description="Unique user identifier")
    email: str = Field(..., description="User email")
    first_name: str = Field(..., description="First name")
    last_name: str = Field(..., description="Last name")
    role: UserRole = Field(..., description="User role")
    status: UserStatus = Field(default=UserStatus.ACTIVE)

    # Relationships
    manager: Optional[UserReference] = Field(None, description="User's manager")
    direct_reports: List[UserReference] = Field(default_factory=list)
    portfolios: List[UserPortfolio] = Field(default_factory=list)
    fund_subscriptions: List[FundSubscription] = Field(default_factory=list)
    fund_access: List[UserFundAccess] = Field(default_factory=list)

    # Enhanced fields
    department: Optional[Department] = Field(None)
    job_title: Optional[str] = Field(None)

    # Validation
    @model_validator(mode="after")
    def validate_manager_not_self(self):
        """Validate user is not their own manager."""
        if self.manager and self.user_id and self.manager.user_id == self.user_id:
            raise ValueError("User cannot be their own manager")
        return self


class EnhancedReportModel(BaseEntity):
    """Enhanced report model with relationships."""

    report_id: str = Field(..., description="Unique report identifier")
    name: str = Field(..., description="Report name")
    report_type: ReportType = Field(..., description="Report type")
    status: ReportStatus = Field(default=ReportStatus.PENDING)
    format: ReportFormat = Field(..., description="Report format")

    # Relationships
    created_by: UserReference = Field(..., description="User who created the report")
    funds: List[FundReference] = Field(
        default_factory=list, description="Funds included in report"
    )
    schedule: Optional[ReportSchedule] = Field(
        None, description="Report schedule if scheduled"
    )

    # Enhanced fields
    file_path: Optional[str] = Field(None, description="Generated file path")
    parameters: Dict[str, Any] = Field(default_factory=dict)
    completed_at: Optional[datetime] = Field(None)

    model_config = ConfigDict()


# Utility functions for relationships
class RelationshipHelpers:
    """Helper functions for managing model relationships."""

    @staticmethod
    def create_fund_reference(fund: "Fund") -> FundReference:
        """Create a fund reference from a full fund model."""
        return FundReference(
            fund_id=fund.fund_id,
            name=fund.name,
            fund_type=fund.fund_type,
            nav=fund.nav,
            currency=fund.currency,
        )

    @staticmethod
    def create_user_reference(user: "User") -> UserReference:
        """Create a user reference from a full user model."""
        return UserReference(
            user_id=user.user_id,
            full_name=user.full_name or f"{user.first_name} {user.last_name}",
            email=user.email,
            role=user.role,
            department=user.department,
        )

    @staticmethod
    def create_report_reference(report: "Report") -> ReportReference:
        """Create a report reference from a full report model."""
        return ReportReference(
            report_id=report.report_id,
            name=report.name,
            report_type=report.report_type,
            status=report.status,
            created_at=report.created_at,
        )
