"""
Report data model for DynamoDB operations.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from enum import Enum

from pydantic import BaseModel, field_serializer, Field, field_validator, ConfigDict
from aws_lambda_powertools.utilities.parser import BaseModel as PowertoolsBaseModel

# Import relationship models
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .relationships import UserReference, FundReference, ReportSchedule


class ReportType(str, Enum):
    """Report type enumeration."""

    FUND_PERFORMANCE = "fund_performance"
    PORTFOLIO_SUMMARY = "portfolio_summary"
    RISK_ANALYSIS = "risk_analysis"
    COMPLIANCE_REPORT = "compliance_report"
    HOLDINGS_REPORT = "holdings_report"
    CUSTOM_REPORT = "custom_report"


class ReportStatus(str, Enum):
    """Report status enumeration."""

    PENDING = "pending"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"
    SCHEDULED = "scheduled"


class ReportFormat(str, Enum):
    """Report format enumeration."""

    PDF = "pdf"
    EXCEL = "excel"
    CSV = "csv"
    JSON = "json"


class Report(PowertoolsBaseModel):
    """
    Report data model for DynamoDB.

    DynamoDB Table: fundflow-{env}-reports
    Primary Key: report_id (HASH)
    Global Secondary Indexes:
    - user_reports_index: user_id (HASH), created_at (RANGE)
    - report_type_index: report_type (HASH), created_at (RANGE)
    """

    # Primary Key
    report_id: str = Field(..., description="Unique report identifier")

    # Basic Information
    name: str = Field(..., min_length=1, max_length=200, description="Report name")
    description: Optional[str] = Field(
        None, max_length=500, description="Report description"
    )
    report_type: ReportType = Field(..., description="Type of report")

    # Ownership
    user_id: str = Field(..., description="User who created the report")

    # Status and Processing
    status: ReportStatus = Field(
        default=ReportStatus.PENDING, description="Report status"
    )
    format: ReportFormat = Field(..., description="Report output format")

    # File Information
    file_path: Optional[str] = Field(
        None, description="S3 path to generated report file"
    )

    # Timestamps
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Creation timestamp",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Last update timestamp",
    )
    completed_at: Optional[datetime] = Field(
        None, description="Generation completion time"
    )

    # Configuration
    parameters: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Report parameters"
    )

    # Relationship fields (optional, loaded separately for performance)
    # Temporarily commented out for testing
    # created_by_details: Optional["UserReference"] = Field(
    #     None, description="Detailed information about the user who created the report"
    # )
    # related_funds: Optional[List["FundReference"]] = Field(
    #     default_factory=list, description="Funds included in this report"
    # )
    # schedule_details: Optional["ReportSchedule"] = Field(
    #     None, description="Schedule information if this is a scheduled report"
    # )

    @field_validator("updated_at", mode="before")
    def set_updated_at(cls, v):
        return datetime.now(timezone.utc)

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("created_at", "updated_at", "completed_at", when_used="json")
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        """Serialize datetime fields to ISO format for JSON serialization."""
        return value.isoformat() if value is not None else None


class ReportCreate(BaseModel):
    """Report creation model."""

    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=500)
    report_type: ReportType
    format: ReportFormat
    parameters: Optional[Dict[str, Any]] = None


class ReportUpdate(BaseModel):
    """Report update model."""

    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=500)
    status: Optional[ReportStatus] = None
    file_path: Optional[str] = None
    completed_at: Optional[datetime] = None
    parameters: Optional[Dict[str, Any]] = None
