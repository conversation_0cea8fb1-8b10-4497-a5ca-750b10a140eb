"""
Request models for API operations.
These models define the structure and validation for incoming API requests.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from decimal import Decimal

from pydantic import (
    BaseModel,
    field_serializer,
    ConfigDict,
    Field,
    field_validator,
    model_validator,
    EmailStr,
)
from .fund import (
    FundType,
    FundStatus,
    RiskLevel,
    Currency,
    PerformanceMetrics,
    Holdings,
)
from .user import UserRole, UserStatus, Department, PermissionSet, UserPreferences
from .report import ReportType, ReportStatus, ReportFormat


# ============================================================================
# Fund Request Models
# ============================================================================


class FundCreateRequest(BaseModel):
    """Request model for creating a new fund."""

    fund_id: str = Field(
        ..., min_length=3, max_length=25, description="Unique fund identifier"
    )
    name: str = Field(..., min_length=1, max_length=200, description="Fund name")
    fund_type: FundType = Field(..., description="Type of fund")
    status: FundStatus = Field(default=FundStatus.ACTIVE, description="Fund status")

    # Financial Details
    nav: Optional[Decimal] = Field(None, gt=0, description="Net Asset Value")
    currency: Currency = Field(default=Currency.USD, description="Base currency")
    inception_date: Optional[datetime] = Field(None, description="Fund inception date")
    total_assets: Optional[Decimal] = Field(
        None, ge=0, description="Total assets under management"
    )

    # Risk and Performance
    risk_level: Optional[RiskLevel] = Field(None, description="Risk level")
    performance_metrics: Optional[PerformanceMetrics] = Field(
        None, description="Performance metrics"
    )
    holdings: Optional[Holdings] = Field(None, description="Fund holdings information")

    # Management Information
    fund_manager: Optional[str] = Field(
        None, max_length=100, description="Fund manager name"
    )
    management_company: Optional[str] = Field(
        None, max_length=100, description="Management company"
    )
    expense_ratio: Optional[Decimal] = Field(
        None, ge=0, le=10, description="Annual expense ratio percentage"
    )
    minimum_investment: Optional[Decimal] = Field(
        None, ge=0, description="Minimum investment amount"
    )

    # Classification
    isin: Optional[str] = Field(
        None, pattern=r"^[A-Z]{2}[A-Z0-9]{9}[0-9]$", description="ISIN code"
    )
    cusip: Optional[str] = Field(None, description="CUSIP identifier")
    bloomberg_ticker: Optional[str] = Field(None, description="Bloomberg ticker symbol")
    symbol: Optional[str] = Field(None, max_length=20, description="Fund symbol/ticker")
    
    # Fund categorization
    category: Optional[str] = Field(None, max_length=100, description="Fund category")
    sub_category: Optional[str] = Field(None, max_length=100, description="Fund sub-category")
    
    # Additional financial data
    previous_nav: Optional[Decimal] = Field(None, gt=0, description="Previous Net Asset Value")
    aum: Optional[Decimal] = Field(None, ge=0, description="Assets under management")
    rating: Optional[Decimal] = Field(None, ge=0, le=5, description="Fund rating (0-5)")
    
    # Extended management information
    fund_manager_photo: Optional[str] = Field(None, description="Fund manager photo URL")
    fund_manager_introduction: Optional[str] = Field(
        None, max_length=1000, description="Fund manager introduction/biography"
    )

    # Metadata
    description: Optional[str] = Field(
        None, max_length=1000, description="Fund description"
    )
    investment_objective: Optional[str] = Field(
        None, max_length=500, description="Investment objective"
    )
    benchmark: Optional[str] = Field(
        None, max_length=100, description="Benchmark index"
    )
    tags: Optional[List[str]] = Field(
        default_factory=list, description="Fund tags for categorization"
    )
    custom_fields: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Custom fields"
    )

    @field_validator("inception_date")
    def validate_inception_date(cls, v):
        """Validate inception date is not in the future."""
        if v:
            # Convert both to naive datetime for comparison
            now_naive = datetime.now(timezone.utc).replace(tzinfo=None)
            # If v is timezone-aware, convert to naive; otherwise use as-is
            v_naive = v.replace(tzinfo=None) if v.tzinfo is not None else v
            if v_naive > now_naive:
                raise ValueError("Inception date cannot be in the future")
        return v

    model_config = ConfigDict()


class FundUpdateRequest(BaseModel):
    """Request model for updating an existing fund."""

    name: Optional[str] = Field(
        None, min_length=1, max_length=200, description="Fund name"
    )
    fund_type: Optional[FundType] = Field(None, description="Type of fund")
    status: Optional[FundStatus] = Field(None, description="Fund status")

    # Financial Details
    nav: Optional[Decimal] = Field(None, gt=0, description="Net Asset Value")
    currency: Optional[Currency] = Field(None, description="Base currency")
    inception_date: Optional[datetime] = Field(None, description="Fund inception date")
    total_assets: Optional[Decimal] = Field(
        None, ge=0, description="Total assets under management"
    )

    # Risk and Performance
    risk_level: Optional[RiskLevel] = Field(None, description="Risk level")
    performance_metrics: Optional[PerformanceMetrics] = Field(
        None, description="Performance metrics"
    )
    holdings: Optional[Holdings] = Field(None, description="Fund holdings information")

    # Management Information
    fund_manager: Optional[str] = Field(
        None, max_length=100, description="Fund manager name"
    )
    management_company: Optional[str] = Field(
        None, max_length=100, description="Management company"
    )
    expense_ratio: Optional[Decimal] = Field(
        None, ge=0, le=10, description="Annual expense ratio percentage"
    )
    minimum_investment: Optional[Decimal] = Field(
        None, ge=0, description="Minimum investment amount"
    )

    # Classification
    isin: Optional[str] = Field(
        None, pattern=r"^[A-Z]{2}[A-Z0-9]{9}[0-9]$", description="ISIN code"
    )
    cusip: Optional[str] = Field(None, description="CUSIP identifier")
    bloomberg_ticker: Optional[str] = Field(None, description="Bloomberg ticker symbol")
    symbol: Optional[str] = Field(None, max_length=20, description="Fund symbol/ticker")
    
    # Fund categorization
    category: Optional[str] = Field(None, max_length=100, description="Fund category")
    sub_category: Optional[str] = Field(None, max_length=100, description="Fund sub-category")
    
    # Additional financial data
    previous_nav: Optional[Decimal] = Field(None, gt=0, description="Previous Net Asset Value")
    aum: Optional[Decimal] = Field(None, ge=0, description="Assets under management")
    rating: Optional[Decimal] = Field(None, ge=0, le=5, description="Fund rating (0-5)")
    
    # Extended management information
    fund_manager_photo: Optional[str] = Field(None, description="Fund manager photo URL")
    fund_manager_introduction: Optional[str] = Field(
        None, max_length=1000, description="Fund manager introduction/biography"
    )

    # Metadata
    description: Optional[str] = Field(
        None, max_length=1000, description="Fund description"
    )
    investment_objective: Optional[str] = Field(
        None, max_length=500, description="Investment objective"
    )
    benchmark: Optional[str] = Field(
        None, max_length=100, description="Benchmark index"
    )
    tags: Optional[List[str]] = Field(None, description="Fund tags for categorization")
    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Custom fields")

    @field_validator("inception_date")
    def validate_inception_date(cls, v):
        """Validate inception date is not in the future."""
        if v:
            # Convert both to naive datetime for comparison
            now_naive = datetime.now(timezone.utc).replace(tzinfo=None)
            # If v is timezone-aware, convert to naive; otherwise use as-is
            v_naive = v.replace(tzinfo=None) if v.tzinfo is not None else v
            if v_naive > now_naive:
                raise ValueError("Inception date cannot be in the future")
        return v

    model_config = ConfigDict()


class FundQueryRequest(BaseModel):
    """Request model for querying funds with filters and pagination."""

    # Filters
    fund_type: Optional[FundType] = Field(None, description="Filter by fund type")
    status: Optional[FundStatus] = Field(None, description="Filter by fund status")
    risk_level: Optional[RiskLevel] = Field(None, description="Filter by risk level")
    currency: Optional[Currency] = Field(None, description="Filter by currency")
    fund_manager: Optional[str] = Field(None, description="Filter by fund manager")
    management_company: Optional[str] = Field(
        None, description="Filter by management company"
    )
    tags: Optional[List[str]] = Field(None, description="Filter by tags (any match)")

    # Range filters
    nav_min: Optional[Decimal] = Field(None, ge=0, description="Minimum NAV")
    nav_max: Optional[Decimal] = Field(None, ge=0, description="Maximum NAV")
    total_assets_min: Optional[Decimal] = Field(
        None, ge=0, description="Minimum total assets"
    )
    total_assets_max: Optional[Decimal] = Field(
        None, ge=0, description="Maximum total assets"
    )
    expense_ratio_min: Optional[Decimal] = Field(
        None, ge=0, description="Minimum expense ratio"
    )
    expense_ratio_max: Optional[Decimal] = Field(
        None, ge=0, le=10, description="Maximum expense ratio"
    )
    minimum_investment_min: Optional[Decimal] = Field(
        None, ge=0, description="Minimum investment threshold"
    )
    minimum_investment_max: Optional[Decimal] = Field(
        None, ge=0, description="Maximum investment threshold"
    )

    # Date filters
    inception_date_from: Optional[datetime] = Field(
        None, description="Inception date from"
    )
    inception_date_to: Optional[datetime] = Field(None, description="Inception date to")
    created_at_from: Optional[datetime] = Field(None, description="Created date from")
    created_at_to: Optional[datetime] = Field(None, description="Created date to")

    # Search
    search: Optional[str] = Field(
        None, max_length=200, description="Search in name, description, or fund_id"
    )

    # Pagination
    page: int = Field(default=1, ge=1, description="Page number")
    page_size: int = Field(default=50, ge=1, le=100, description="Items per page")

    # Sorting
    sort_by: Optional[str] = Field(default="created_at", description="Field to sort by")
    sort_order: Optional[str] = Field(
        default="desc", pattern="^(asc|desc)$", description="Sort order"
    )

    @model_validator(mode="after")
    def validate_nav_range(self):
        """Validate NAV range."""
        if self.nav_max is not None and self.nav_min is not None:
            if self.nav_max < self.nav_min:
                raise ValueError("nav_max must be greater than or equal to nav_min")
        return self

    @model_validator(mode="after")
    def validate_inception_date_range(self):
        """Validate inception date range."""
        if self.inception_date_to is not None and self.inception_date_from is not None:
            if self.inception_date_to < self.inception_date_from:
                raise ValueError(
                    "inception_date_to must be greater than or equal to inception_date_from"
                )
        return self

    model_config = ConfigDict()


# ============================================================================
# User Request Models
# ============================================================================


class UserCreateRequest(BaseModel):
    """Request model for creating a new user."""

    user_id: str = Field(
        ..., min_length=3, max_length=50, description="Unique user identifier"
    )
    email: EmailStr = Field(..., description="User email address")

    # Personal Information
    first_name: str = Field(..., min_length=1, max_length=50, description="First name")
    last_name: str = Field(..., min_length=1, max_length=50, description="Last name")
    phone_number: Optional[str] = Field(None, description="Phone number")

    # Professional Information
    role: UserRole = Field(..., description="User role")
    department: Optional[Department] = Field(None, description="Department")
    job_title: Optional[str] = Field(None, max_length=100, description="Job title")
    employee_id: Optional[str] = Field(None, max_length=20, description="Employee ID")
    manager_id: Optional[str] = Field(None, description="Manager's user ID")

    # Account Settings
    status: UserStatus = Field(default=UserStatus.ACTIVE, description="User status")
    is_mfa_enabled: bool = Field(
        default=False, description="Multi-factor authentication status"
    )

    # Permissions and Access
    permissions: Optional[PermissionSet] = Field(None, description="User permissions")
    access_groups: Optional[List[str]] = Field(
        default_factory=list, description="Access group memberships"
    )

    # User Preferences
    preferences: Optional[UserPreferences] = Field(None, description="User preferences")

    # Additional metadata
    notes: Optional[str] = Field(
        None, max_length=500, description="Admin notes about the user"
    )
    tags: Optional[List[str]] = Field(
        default_factory=list, description="User tags for categorization"
    )
    custom_fields: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Custom fields"
    )

    @model_validator(mode="after")
    def validate_manager_hierarchy(self):
        """Validate manager ID and prevent self-reference."""
        if (
            self.manager_id is not None
            and self.user_id
            and self.manager_id == self.user_id
        ):
            raise ValueError("User cannot be their own manager")
        return self

    model_config = ConfigDict()


class UserUpdateRequest(BaseModel):
    """Request model for updating an existing user."""

    # Personal Information
    first_name: Optional[str] = Field(
        None, min_length=1, max_length=50, description="First name"
    )
    last_name: Optional[str] = Field(
        None, min_length=1, max_length=50, description="Last name"
    )
    phone_number: Optional[str] = Field(None, description="Phone number")

    # Professional Information
    role: Optional[UserRole] = Field(None, description="User role")
    department: Optional[Department] = Field(None, description="Department")
    job_title: Optional[str] = Field(None, max_length=100, description="Job title")
    employee_id: Optional[str] = Field(None, max_length=20, description="Employee ID")
    manager_id: Optional[str] = Field(None, description="Manager's user ID")

    # Account Settings
    status: Optional[UserStatus] = Field(None, description="User status")
    is_verified: Optional[bool] = Field(None, description="Email verification status")
    is_mfa_enabled: Optional[bool] = Field(
        None, description="Multi-factor authentication status"
    )

    # Permissions and Access
    permissions: Optional[PermissionSet] = Field(None, description="User permissions")
    access_groups: Optional[List[str]] = Field(
        None, description="Access group memberships"
    )

    # User Preferences
    preferences: Optional[UserPreferences] = Field(None, description="User preferences")

    # Additional metadata
    notes: Optional[str] = Field(
        None, max_length=500, description="Admin notes about the user"
    )
    tags: Optional[List[str]] = Field(None, description="User tags for categorization")
    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Custom fields")

    model_config = ConfigDict()


class UserQueryRequest(BaseModel):
    """Request model for querying users with filters and pagination."""

    # Filters
    role: Optional[UserRole] = Field(None, description="Filter by user role")
    status: Optional[UserStatus] = Field(None, description="Filter by user status")
    department: Optional[Department] = Field(None, description="Filter by department")
    manager_id: Optional[str] = Field(None, description="Filter by manager")
    is_verified: Optional[bool] = Field(
        None, description="Filter by verification status"
    )
    is_mfa_enabled: Optional[bool] = Field(None, description="Filter by MFA status")
    access_groups: Optional[List[str]] = Field(
        None, description="Filter by access groups (any match)"
    )
    tags: Optional[List[str]] = Field(None, description="Filter by tags (any match)")

    # Date filters
    created_at_from: Optional[datetime] = Field(None, description="Created date from")
    created_at_to: Optional[datetime] = Field(None, description="Created date to")
    last_login_from: Optional[datetime] = Field(None, description="Last login from")
    last_login_to: Optional[datetime] = Field(None, description="Last login to")

    # Search
    search: Optional[str] = Field(
        None, max_length=200, description="Search in name, email, or user_id"
    )

    # Pagination
    page: int = Field(default=1, ge=1, description="Page number")
    page_size: int = Field(default=50, ge=1, le=100, description="Items per page")

    # Sorting
    sort_by: Optional[str] = Field(default="created_at", description="Field to sort by")
    sort_order: Optional[str] = Field(
        default="desc", pattern="^(asc|desc)$", description="Sort order"
    )

    model_config = ConfigDict()


class UserPasswordChangeRequest(BaseModel):
    """Request model for changing user password."""

    current_password: str = Field(..., min_length=8, description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., min_length=8, description="Confirm new password")

    @model_validator(mode="after")
    def passwords_match(self):
        """Validate that passwords match."""
        if self.new_password and self.confirm_password != self.new_password:
            raise ValueError("Passwords do not match")
        return self


class UserPasswordResetRequest(BaseModel):
    """Request model for password reset."""

    email: EmailStr = Field(..., description="User email address")


class UserLoginRequest(BaseModel):
    """Request model for user login."""

    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=8, description="User password")

    model_config = ConfigDict()


class UserRegisterRequest(BaseModel):
    """Request model for user registration."""

    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=8, description="User password")
    confirm_password: str = Field(..., min_length=8, description="Confirm password")
    first_name: str = Field(..., min_length=1, max_length=50, description="First name")
    last_name: str = Field(..., min_length=1, max_length=50, description="Last name")
    phone_number: Optional[str] = Field(None, description="Phone number")

    @model_validator(mode="after")
    def passwords_match(self):
        """Validate that passwords match."""
        if self.password and self.confirm_password != self.password:
            raise ValueError("Passwords do not match")
        return self

    model_config = ConfigDict()


class TokenRefreshRequest(BaseModel):
    """Request model for refreshing access token."""

    refresh_token: str = Field(..., description="Valid refresh token")

    model_config = ConfigDict()


class ConfirmRegistrationRequest(BaseModel):
    """Request model for confirming user registration."""

    email: EmailStr = Field(..., description="User email address")
    confirmation_code: str = Field(
        ..., min_length=6, max_length=6, description="6-digit confirmation code"
    )

    model_config = ConfigDict()


# ============================================================================
# Report Request Models
# ============================================================================


class ReportCreateRequest(BaseModel):
    """Request model for creating a new report."""

    report_id: str = Field(
        ..., min_length=3, max_length=50, description="Unique report identifier"
    )
    name: str = Field(..., min_length=1, max_length=200, description="Report name")
    description: Optional[str] = Field(
        None, max_length=500, description="Report description"
    )
    report_type: ReportType = Field(..., description="Type of report")
    format: ReportFormat = Field(..., description="Report output format")

    # Configuration
    parameters: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Report parameters"
    )

    # Scheduling (optional)
    schedule_enabled: bool = Field(
        default=False, description="Enable scheduled generation"
    )
    schedule_cron: Optional[str] = Field(
        None, description="Cron expression for scheduling"
    )

    model_config = ConfigDict()


class ReportUpdateRequest(BaseModel):
    """Request model for updating an existing report."""

    name: Optional[str] = Field(
        None, min_length=1, max_length=200, description="Report name"
    )
    description: Optional[str] = Field(
        None, max_length=500, description="Report description"
    )
    status: Optional[ReportStatus] = Field(None, description="Report status")
    file_path: Optional[str] = Field(
        None, description="S3 path to generated report file"
    )
    completed_at: Optional[datetime] = Field(
        None, description="Generation completion time"
    )
    parameters: Optional[Dict[str, Any]] = Field(None, description="Report parameters")

    model_config = ConfigDict()


class ReportQueryRequest(BaseModel):
    """Request model for querying reports with filters and pagination."""

    # Filters
    report_type: Optional[ReportType] = Field(None, description="Filter by report type")
    status: Optional[ReportStatus] = Field(None, description="Filter by report status")
    format: Optional[ReportFormat] = Field(None, description="Filter by report format")
    user_id: Optional[str] = Field(None, description="Filter by creator")

    # Date filters
    created_at_from: Optional[datetime] = Field(None, description="Created date from")
    created_at_to: Optional[datetime] = Field(None, description="Created date to")
    completed_at_from: Optional[datetime] = Field(
        None, description="Completed date from"
    )
    completed_at_to: Optional[datetime] = Field(None, description="Completed date to")

    # Search
    search: Optional[str] = Field(
        None, max_length=200, description="Search in name, description, or report_id"
    )

    # Pagination
    page: int = Field(default=1, ge=1, description="Page number")
    page_size: int = Field(default=50, ge=1, le=100, description="Items per page")

    # Sorting
    sort_by: Optional[str] = Field(default="created_at", description="Field to sort by")
    sort_order: Optional[str] = Field(
        default="desc", pattern="^(asc|desc)$", description="Sort order"
    )

    model_config = ConfigDict()


class ReportGenerateRequest(BaseModel):
    """Request model for generating a report."""

    report_id: str = Field(..., description="Report ID to generate")
    parameters: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Generation parameters"
    )
    force_regenerate: bool = Field(
        default=False, description="Force regeneration even if already completed"
    )

    model_config = ConfigDict()


# ============================================================================
# Common Request Models
# ============================================================================


class BulkOperationRequest(BaseModel):
    """Request model for bulk operations."""

    operation: str = Field(..., description="Operation type (create, update, delete)")
    items: List[Dict[str, Any]] = Field(
        ..., min_length=1, max_length=100, description="Items to process"
    )
    options: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Operation options"
    )

    @field_validator("operation")
    def validate_operation(cls, v):
        """Validate operation type."""
        allowed_operations = ["create", "update", "delete", "archive"]
        if v not in allowed_operations:
            raise ValueError(
                f"Operation must be one of: {', '.join(allowed_operations)}"
            )
        return v


class PaginationRequest(BaseModel):
    """Base request model for pagination."""

    page: int = Field(default=1, ge=1, description="Page number")
    page_size: int = Field(default=50, ge=1, le=100, description="Items per page")
    sort_by: Optional[str] = Field(default="created_at", description="Field to sort by")
    sort_order: Optional[str] = Field(
        default="desc", pattern="^(asc|desc)$", description="Sort order"
    )


class SearchRequest(BaseModel):
    """Base request model for search operations."""

    query: str = Field(..., min_length=1, max_length=200, description="Search query")
    filters: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Additional filters"
    )
    page: int = Field(default=1, ge=1, description="Page number")
    page_size: int = Field(default=50, ge=1, le=100, description="Items per page")

    model_config = ConfigDict()


# ============================================================================
# Additional Specialized Request Models
# ============================================================================


class FundComparisonRequest(BaseModel):
    """Request model for comparing multiple funds."""

    fund_ids: List[str] = Field(
        ..., min_length=2, max_length=10, description="Fund IDs to compare"
    )
    metrics: Optional[List[str]] = Field(
        None, description="Specific metrics to compare (nav, performance, risk)"
    )
    date_range: Optional[Dict[str, datetime]] = Field(
        None, description="Date range for comparison"
    )

    model_config = ConfigDict()


class FundPerformanceRequest(BaseModel):
    """Request model for fund performance analysis."""

    fund_id: str = Field(..., description="Fund ID for performance analysis")
    start_date: Optional[datetime] = Field(None, description="Analysis start date")
    end_date: Optional[datetime] = Field(None, description="Analysis end date")
    benchmark: Optional[str] = Field(None, description="Benchmark for comparison")
    metrics: Optional[List[str]] = Field(
        None, description="Specific performance metrics to calculate"
    )

    @model_validator(mode="after")
    def validate_date_range(self):
        """Validate end date is after start date."""
        if self.end_date and self.start_date and self.end_date <= self.start_date:
            raise ValueError("End date must be after start date")
        return self

    model_config = ConfigDict()


class UserBulkUpdateRequest(BaseModel):
    """Request model for bulk user operations."""

    user_ids: List[str] = Field(
        ..., min_length=1, max_length=50, description="User IDs to update"
    )
    updates: Dict[str, Any] = Field(..., description="Fields to update for all users")
    confirmation_required: bool = Field(
        default=True, description="Whether to require confirmation"
    )

    @field_validator("user_ids")
    def validate_user_ids(cls, v):
        """Validate all user IDs are unique."""
        if len(v) != len(set(v)):
            raise ValueError("User IDs must be unique")
        return v


class ReportScheduleRequest(BaseModel):
    """Request model for scheduling report generation."""

    report_id: str = Field(..., description="Report ID to schedule")
    schedule_type: str = Field(
        ..., description="Schedule type (daily, weekly, monthly)"
    )
    schedule_time: Optional[str] = Field(
        None,
        pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$",
        description="Time in HH:MM format",
    )
    recipients: List[str] = Field(
        ..., min_length=1, description="Email addresses to send report to"
    )
    enabled: bool = Field(default=True, description="Whether schedule is active")

    @field_validator("schedule_type")
    def validate_schedule_type(cls, v):
        """Validate schedule type."""
        allowed_types = ["daily", "weekly", "monthly", "quarterly"]
        if v not in allowed_types:
            raise ValueError(f"Schedule type must be one of {allowed_types}")
        return v

    @field_validator("recipients")
    def validate_recipients(cls, v):
        """Validate email addresses."""
        import re

        email_pattern = r"^[^\s@]+@[^\s@]+\.[^\s@]+$"
        for email in v:
            if not re.match(email_pattern, email):
                raise ValueError(f"Invalid email address: {email}")
        return v


class BatchOperationRequest(BaseModel):
    """Request model for batch operations with rollback support."""

    operations: List[Dict[str, Any]] = Field(
        ..., min_length=1, max_length=100, description="List of operations to execute"
    )
    rollback_on_error: bool = Field(
        default=True, description="Whether to rollback all operations if any fail"
    )
    dry_run: bool = Field(
        default=False, description="Whether to simulate operations without executing"
    )

    @field_validator("operations")
    def validate_operations(cls, v):
        """Validate operation structure."""
        required_fields = ["operation_type", "resource_type", "data"]
        for i, op in enumerate(v):
            for field in required_fields:
                if field not in op:
                    raise ValueError(f"Operation {i} missing required field: {field}")
        return v
