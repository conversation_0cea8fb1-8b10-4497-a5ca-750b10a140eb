"""
Response models for API operations.
These models define the structure for API responses and control data exposure.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List, Generic, TypeVar
from decimal import Decimal

from pydantic import BaseModel, field_serializer, ConfigDict, Field
from .fund import (
    Fund,
    FundType,
    FundStatus,
    RiskLevel,
    Currency,
    PerformanceMetrics,
    Holdings,
)
from .user import (
    User,
    UserRole,
    UserStatus,
    Department,
    PermissionSet,
    UserPreferences,
    SessionInfo,
)
from .report import Report, ReportType, ReportStatus, ReportFormat

T = TypeVar("T")


# ============================================================================
# Generic Response Models
# ============================================================================


class BaseResponse(BaseModel):
    """Base response model with common fields."""

    success: bool = Field(default=True, description="Operation success status")
    message: Optional[str] = Field(None, description="Response message")
    timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Response timestamp",
    )

    model_config = ConfigDict()


class ErrorResponse(BaseResponse):
    """Error response model."""

    success: bool = Field(default=False, description="Operation success status")
    error_code: Optional[str] = Field(None, description="Error code")
    error_details: Optional[Dict[str, Any]] = Field(
        None, description="Detailed error information"
    )
    validation_errors: Optional[List[Dict[str, str]]] = Field(
        None, description="Validation error details"
    )


class PaginatedResponse(BaseResponse, Generic[T]):
    """Generic paginated response model."""

    data: List[T] = Field(default_factory=list, description="Response data items")
    pagination: Dict[str, Any] = Field(..., description="Pagination metadata")

    @classmethod
    def create(
        cls, items: List[T], page: int, page_size: int, total_count: int, **kwargs
    ):
        """Create a paginated response."""
        total_pages = (total_count + page_size - 1) // page_size

        pagination = {
            "page": page,
            "page_size": page_size,
            "total_count": total_count,
            "total_pages": total_pages,
            "has_next": page < total_pages,
            "has_previous": page > 1,
            "next_page": page + 1 if page < total_pages else None,
            "previous_page": page - 1 if page > 1 else None,
        }

        return cls(data=items, pagination=pagination, **kwargs)


class SingleResponse(BaseResponse, Generic[T]):
    """Generic single item response model."""

    data: T = Field(..., description="Response data")


class ListResponse(BaseResponse, Generic[T]):
    """Generic list response model."""

    data: List[T] = Field(default_factory=list, description="Response data items")
    count: int = Field(default=0, description="Number of items returned")


class BulkOperationResponse(BaseResponse):
    """Response model for bulk operations."""

    total_processed: int = Field(..., description="Total items processed")
    successful: int = Field(..., description="Successfully processed items")
    failed: int = Field(..., description="Failed items")
    errors: List[Dict[str, Any]] = Field(
        default_factory=list, description="Error details for failed items"
    )


# ============================================================================
# Fund Response Models
# ============================================================================


class FundSummaryResponse(BaseModel):
    """Summary response model for Fund (excludes sensitive data)."""

    fund_id: str = Field(..., description="Unique fund identifier")
    name: str = Field(..., description="Fund name")
    fund_type: FundType = Field(..., description="Type of fund")
    status: FundStatus = Field(..., description="Fund status")

    # Financial Details (public)
    nav: Optional[Decimal] = Field(None, description="Net Asset Value")
    currency: Currency = Field(..., description="Base currency")
    inception_date: Optional[datetime] = Field(None, description="Fund inception date")
    total_assets: Optional[Decimal] = Field(
        None, description="Total assets under management"
    )

    # Risk and Performance (public)
    risk_level: Optional[RiskLevel] = Field(None, description="Risk level")

    # Management Information (public)
    fund_manager: Optional[str] = Field(None, description="Fund manager name")
    management_company: Optional[str] = Field(None, description="Management company")
    expense_ratio: Optional[Decimal] = Field(
        None, description="Annual expense ratio percentage"
    )
    minimum_investment: Optional[Decimal] = Field(
        None, description="Minimum investment amount"
    )

    # Classification (public)
    isin: Optional[str] = Field(None, description="ISIN code")
    bloomberg_ticker: Optional[str] = Field(None, description="Bloomberg ticker symbol")

    # Metadata (public)
    description: Optional[str] = Field(None, description="Fund description")
    investment_objective: Optional[str] = Field(
        None, description="Investment objective"
    )
    benchmark: Optional[str] = Field(None, description="Benchmark index")
    tags: List[str] = Field(default_factory=list, description="Fund tags")

    # Timestamps
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict()


class FundDetailResponse(FundSummaryResponse):
    """Detailed response model for Fund (includes performance and holdings)."""

    # Additional details for authenticated users
    performance_metrics: Optional[PerformanceMetrics] = Field(
        None, description="Performance metrics"
    )
    holdings: Optional[Holdings] = Field(None, description="Fund holdings information")
    cusip: Optional[str] = Field(None, description="CUSIP identifier")
    custom_fields: Dict[str, Any] = Field(
        default_factory=dict, description="Custom fields"
    )


class FundCreateResponse(SingleResponse[FundDetailResponse]):
    """Response model for fund creation."""

    pass


class FundUpdateResponse(SingleResponse[FundDetailResponse]):
    """Response model for fund update."""

    pass


class FundListResponse(PaginatedResponse[FundSummaryResponse]):
    """Response model for fund listing."""

    pass


class FundSearchResponse(BaseResponse):
    """Response model for fund search."""

    results: List[FundSummaryResponse] = Field(
        default_factory=list, description="Search results"
    )
    total_count: int = Field(default=0, description="Total matching results")
    search_query: str = Field(..., description="Original search query")
    filters_applied: Dict[str, Any] = Field(
        default_factory=dict, description="Applied filters"
    )


# ============================================================================
# User Response Models
# ============================================================================


class UserSummaryResponse(BaseModel):
    """Summary response model for User (excludes sensitive data)."""

    user_id: str = Field(..., description="Unique user identifier")
    email: str = Field(..., description="User email address")

    # Personal Information (public)
    first_name: str = Field(..., description="First name")
    last_name: str = Field(..., description="Last name")
    full_name: Optional[str] = Field(None, description="Full name")

    # Professional Information (public)
    role: UserRole = Field(..., description="User role")
    department: Optional[Department] = Field(None, description="Department")
    job_title: Optional[str] = Field(None, description="Job title")

    # Account Status (public)
    status: UserStatus = Field(..., description="User status")
    is_verified: bool = Field(..., description="Email verification status")

    # Timestamps
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict()


class UserDetailResponse(UserSummaryResponse):
    """Detailed response model for User (includes additional fields for authorized access)."""

    # Additional professional information
    employee_id: Optional[str] = Field(None, description="Employee ID")
    manager_id: Optional[str] = Field(None, description="Manager's user ID")

    # Account Settings
    is_mfa_enabled: bool = Field(..., description="Multi-factor authentication status")

    # Access Information
    access_groups: List[str] = Field(
        default_factory=list, description="Access group memberships"
    )
    tags: List[str] = Field(default_factory=list, description="User tags")

    # Timestamps
    last_password_change: Optional[datetime] = Field(
        None, description="Last password change timestamp"
    )
    email_verified_at: Optional[datetime] = Field(
        None, description="Email verification timestamp"
    )


class UserProfileResponse(UserDetailResponse):
    """Profile response model for User (includes personal preferences and permissions)."""

    # Personal Information
    phone_number: Optional[str] = Field(None, description="Phone number")

    # Permissions and Preferences (for own profile or admin access)
    permissions: PermissionSet = Field(..., description="User permissions")
    preferences: UserPreferences = Field(..., description="User preferences")

    # Session Information (limited)
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    login_count: int = Field(default=0, description="Total login count")

    # Additional metadata
    notes: Optional[str] = Field(None, description="Admin notes")
    custom_fields: Dict[str, Any] = Field(
        default_factory=dict, description="Custom fields"
    )


class UserCreateResponse(SingleResponse[UserDetailResponse]):
    """Response model for user creation."""

    pass


class UserUpdateResponse(SingleResponse[UserDetailResponse]):
    """Response model for user update."""

    pass


class UserListResponse(PaginatedResponse[UserSummaryResponse]):
    """Response model for user listing."""

    pass


class UserSearchResponse(BaseResponse):
    """Response model for user search."""

    results: List[UserSummaryResponse] = Field(
        default_factory=list, description="Search results"
    )
    total_count: int = Field(default=0, description="Total matching results")
    search_query: str = Field(..., description="Original search query")
    filters_applied: Dict[str, Any] = Field(
        default_factory=dict, description="Applied filters"
    )


class UserPasswordChangeResponse(BaseResponse):
    """Response model for password change."""

    password_changed_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Password change timestamp",
    )

    model_config = ConfigDict()


class UserLoginResponse(BaseResponse):
    """Response model for user login."""

    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="Bearer", description="Token type")
    expires_in: int = Field(..., description="Access token expiration time in seconds")
    user_info: UserSummaryResponse = Field(..., description="User information")

    model_config = ConfigDict()


class UserRegisterResponse(BaseResponse):
    """Response model for user registration."""

    user_id: str = Field(..., description="New user identifier")
    email: str = Field(..., description="User email address")
    verification_required: bool = Field(
        default=True, description="Whether email verification is required"
    )
    message: str = Field(
        default="Registration successful. Please check your email for verification instructions."
    )

    model_config = ConfigDict()


class TokenRefreshResponse(BaseResponse):
    """Response model for token refresh."""

    access_token: str = Field(..., description="New JWT access token")
    token_type: str = Field(default="Bearer", description="Token type")
    expires_in: int = Field(..., description="Access token expiration time in seconds")

    model_config = ConfigDict()


class ConfirmRegistrationResponse(BaseResponse):
    """Response model for registration confirmation."""

    email: str = Field(..., description="Confirmed email address")
    account_verified: bool = Field(
        default=True, description="Account verification status"
    )
    message: str = Field(default="Account successfully verified. You can now log in.")

    model_config = ConfigDict()


# ============================================================================
# Report Response Models
# ============================================================================


class ReportSummaryResponse(BaseModel):
    """Summary response model for Report."""

    report_id: str = Field(..., description="Unique report identifier")
    name: str = Field(..., description="Report name")
    description: Optional[str] = Field(None, description="Report description")
    report_type: ReportType = Field(..., description="Type of report")

    # Ownership
    user_id: str = Field(..., description="User who created the report")

    # Status and Processing
    status: ReportStatus = Field(..., description="Report status")
    format: ReportFormat = Field(..., description="Report output format")

    # Timestamps
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    completed_at: Optional[datetime] = Field(
        None, description="Generation completion time"
    )

    model_config = ConfigDict()


class ReportDetailResponse(ReportSummaryResponse):
    """Detailed response model for Report."""

    # File Information
    file_path: Optional[str] = Field(
        None, description="S3 path to generated report file"
    )
    file_size: Optional[int] = Field(None, description="File size in bytes")
    download_url: Optional[str] = Field(None, description="Temporary download URL")

    # Configuration
    parameters: Dict[str, Any] = Field(
        default_factory=dict, description="Report parameters"
    )

    # Processing Information
    generation_time_seconds: Optional[float] = Field(
        None, description="Time taken to generate report"
    )
    error_message: Optional[str] = Field(
        None, description="Error message if generation failed"
    )


class ReportCreateResponse(SingleResponse[ReportDetailResponse]):
    """Response model for report creation."""

    pass


class ReportUpdateResponse(SingleResponse[ReportDetailResponse]):
    """Response model for report update."""

    pass


class ReportListResponse(PaginatedResponse[ReportSummaryResponse]):
    """Response model for report listing."""

    pass


class ReportGenerateResponse(BaseResponse):
    """Response model for report generation."""

    report_id: str = Field(..., description="Report ID")
    status: ReportStatus = Field(..., description="Generation status")
    estimated_completion_time: Optional[datetime] = Field(
        None, description="Estimated completion time"
    )
    job_id: Optional[str] = Field(None, description="Background job ID for tracking")


class ReportDownloadResponse(BaseResponse):
    """Response model for report download."""

    download_url: str = Field(..., description="Temporary download URL")
    expires_at: datetime = Field(..., description="URL expiration time")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    content_type: str = Field(..., description="File content type")


# ============================================================================
# Analytics and Statistics Response Models
# ============================================================================


class FundStatisticsResponse(BaseResponse):
    """Response model for fund statistics."""

    total_funds: int = Field(..., description="Total number of funds")
    active_funds: int = Field(..., description="Number of active funds")
    total_assets: Decimal = Field(..., description="Total assets under management")
    average_expense_ratio: Optional[Decimal] = Field(
        None, description="Average expense ratio"
    )
    fund_types_distribution: Dict[str, int] = Field(
        default_factory=dict, description="Distribution by fund type"
    )
    risk_levels_distribution: Dict[str, int] = Field(
        default_factory=dict, description="Distribution by risk level"
    )

    model_config = ConfigDict()


class UserStatisticsResponse(BaseResponse):
    """Response model for user statistics."""

    total_users: int = Field(..., description="Total number of users")
    active_users: int = Field(..., description="Number of active users")
    verified_users: int = Field(..., description="Number of verified users")
    mfa_enabled_users: int = Field(..., description="Number of users with MFA enabled")
    roles_distribution: Dict[str, int] = Field(
        default_factory=dict, description="Distribution by role"
    )
    departments_distribution: Dict[str, int] = Field(
        default_factory=dict, description="Distribution by department"
    )


class ReportStatisticsResponse(BaseResponse):
    """Response model for report statistics."""

    total_reports: int = Field(..., description="Total number of reports")
    completed_reports: int = Field(..., description="Number of completed reports")
    pending_reports: int = Field(..., description="Number of pending reports")
    failed_reports: int = Field(..., description="Number of failed reports")
    report_types_distribution: Dict[str, int] = Field(
        default_factory=dict, description="Distribution by report type"
    )
    formats_distribution: Dict[str, int] = Field(
        default_factory=dict, description="Distribution by format"
    )


# ============================================================================
# Health and System Response Models
# ============================================================================


class HealthCheckResponse(BaseResponse):
    """Response model for health check."""

    status: str = Field(..., description="Overall system status")
    version: str = Field(..., description="Application version")
    uptime_seconds: float = Field(..., description="System uptime in seconds")
    checks: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict, description="Individual health checks"
    )


class SystemInfoResponse(BaseResponse):
    """Response model for system information."""

    application: str = Field(..., description="Application name")
    version: str = Field(..., description="Application version")
    environment: str = Field(..., description="Environment name")
    build_time: datetime = Field(..., description="Build timestamp")
    features: List[str] = Field(default_factory=list, description="Enabled features")

    model_config = ConfigDict()


# ============================================================================
# Additional Specialized Response Models
# ============================================================================


class FundComparisonResponse(BaseResponse):
    """Response model for fund comparison."""

    funds: List[FundSummaryResponse] = Field(..., description="Funds being compared")
    comparison_metrics: Dict[str, Dict[str, Any]] = Field(
        ..., description="Comparison metrics by fund ID"
    )
    analysis_date: datetime = Field(..., description="Analysis timestamp")
    relative_performance: Optional[Dict[str, Any]] = Field(
        None, description="Relative performance analysis"
    )

    model_config = ConfigDict()


class FundPerformanceResponse(BaseResponse):
    """Response model for fund performance analysis."""

    fund_id: str = Field(..., description="Fund ID")
    fund_name: str = Field(..., description="Fund name")
    analysis_period: Dict[str, datetime] = Field(
        ..., description="Analysis period (start/end dates)"
    )
    performance_metrics: Dict[str, Any] = Field(
        ..., description="Calculated performance metrics"
    )
    benchmark_comparison: Optional[Dict[str, Any]] = Field(
        None, description="Benchmark comparison if provided"
    )
    risk_analysis: Optional[Dict[str, Any]] = Field(
        None, description="Risk metrics and analysis"
    )

    model_config = ConfigDict()


class UserBulkUpdateResponse(BulkOperationResponse):
    """Response model for bulk user operations."""

    updated_users: List[str] = Field(
        default_factory=list, description="IDs of successfully updated users"
    )
    validation_errors: Dict[str, List[str]] = Field(
        default_factory=dict, description="Validation errors by user ID"
    )


class ReportScheduleResponse(SingleResponse[Dict[str, Any]]):
    """Response model for report scheduling."""

    schedule_id: str = Field(..., description="Unique schedule identifier")
    next_run_time: Optional[datetime] = Field(
        None, description="Next scheduled execution time"
    )
    status: str = Field(..., description="Schedule status")

    model_config = ConfigDict()


class BatchOperationResponse(BaseResponse):
    """Response model for batch operations."""

    batch_id: str = Field(..., description="Unique batch operation identifier")
    total_operations: int = Field(..., description="Total operations requested")
    completed_operations: int = Field(
        ..., description="Successfully completed operations"
    )
    failed_operations: int = Field(..., description="Failed operations")
    operation_results: List[Dict[str, Any]] = Field(
        default_factory=list, description="Detailed results for each operation"
    )
    rollback_performed: bool = Field(
        default=False, description="Whether rollback was performed"
    )
    execution_time_seconds: Optional[float] = Field(
        None, description="Total execution time"
    )


class ValidationErrorDetailResponse(BaseResponse):
    """Enhanced validation error response with field-level details."""

    success: bool = Field(default=False, description="Operation success status")
    validation_errors: Dict[str, List[str]] = Field(
        ..., description="Validation errors grouped by field"
    )
    error_count: int = Field(..., description="Total number of validation errors")
    suggested_fixes: Optional[List[str]] = Field(
        None, description="Suggested fixes for common validation errors"
    )


class MetricsResponse(BaseResponse):
    """Response model for system metrics and analytics."""

    metrics: Dict[str, Any] = Field(..., description="System metrics")
    period: Dict[str, datetime] = Field(..., description="Metrics period")
    aggregation_level: str = Field(..., description="Metrics aggregation level")

    model_config = ConfigDict()
