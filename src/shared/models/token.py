"""
Token management models for JWT blacklist and token operations.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any
from pydantic import BaseModel, field_serializer, Field, ConfigDict

from shared.models.powertools_base import PowertoolsBaseModel


class TokenBlacklist(PowertoolsBaseModel):
    """
    Token blacklist model for DynamoDB.

    DynamoDB Table: fundflow-{env}-token-blacklist
    Primary Key: token_jti (HASH)
    TTL: expires_at
    """

    # Primary Key - JWT ID (jti claim)
    token_jti: str = Field(..., description="JWT ID from token claims")

    # Token info
    token_hash: str = Field(
        ..., description="SHA256 hash of the token for verification"
    )
    user_id: str = Field(..., description="User ID who owns this token")
    token_type: str = Field(
        default="access", description="Type of token (access/refresh)"
    )

    # Expiration
    expires_at: datetime = Field(..., description="When the token expires (TTL)")
    blacklisted_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When the token was blacklisted",
    )

    # Metadata
    reason: Optional[str] = Field(None, description="Reason for blacklisting")
    user_agent: Optional[str] = Field(None, description="User agent when blacklisted")
    ip_address: Optional[str] = Field(None, description="IP address when blacklisted")

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("expires_at", "blacklisted_at", when_used="json")
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        """Serialize datetime fields to ISO format for JSON serialization."""
        return value.isoformat() if value is not None else None


class TokenBlacklistCreate(BaseModel):
    """Model for creating token blacklist entries."""

    token_jti: str = Field(..., description="JWT ID from token claims")
    token_hash: str = Field(..., description="SHA256 hash of the token")
    user_id: str = Field(..., description="User ID who owns this token")
    token_type: str = Field(default="access", description="Type of token")
    expires_at: datetime = Field(..., description="When the token expires")
    reason: Optional[str] = Field(None, description="Reason for blacklisting")
    user_agent: Optional[str] = Field(None, description="User agent")
    ip_address: Optional[str] = Field(None, description="IP address")

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("expires_at", when_used="json")
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        """Serialize datetime fields to ISO format for JSON serialization."""
        return value.isoformat() if value is not None else None


class TokenValidationResult(BaseModel):
    """Result of token validation including blacklist check."""

    is_valid: bool = Field(..., description="Whether token is valid")
    is_blacklisted: bool = Field(
        default=False, description="Whether token is blacklisted"
    )
    error_message: Optional[str] = Field(None, description="Error message if invalid")
    user_info: Optional[Dict[str, Any]] = Field(
        None, description="User information if valid"
    )
    token_claims: Optional[Dict[str, Any]] = Field(
        None, description="Token claims if valid"
    )

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer("*", when_used="json")
    def serialize_datetime(self, value: Any) -> Any:
        """Serialize datetime objects to ISO format."""
        if isinstance(value, datetime):
            return value.isoformat()
        return value


# DynamoDB specific utilities
class TokenBlacklistDynamoDBItem:
    """Utility class for DynamoDB item transformations."""

    @staticmethod
    def to_dynamodb_item(token_blacklist: TokenBlacklist) -> Dict[str, Any]:
        """Convert TokenBlacklist model to DynamoDB item format."""
        item = token_blacklist.dict()

        # Convert datetime objects to ISO strings
        for field in ["expires_at", "blacklisted_at"]:
            if item.get(field):
                item[field] = (
                    item[field].isoformat()
                    if isinstance(item[field], datetime)
                    else item[field]
                )

        return item

    @staticmethod
    def from_dynamodb_item(item: Dict[str, Any]) -> TokenBlacklist:
        """Convert DynamoDB item to TokenBlacklist model."""
        # Convert string dates back to datetime objects
        for field in ["expires_at", "blacklisted_at"]:
            if item.get(field):
                if isinstance(item[field], str):
                    item[field] = datetime.fromisoformat(item[field])

        return TokenBlacklist(**item)
