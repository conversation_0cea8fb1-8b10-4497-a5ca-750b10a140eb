"""
User data model for DynamoDB operations.
Defines the structure and validation for User entities.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum

from pydantic import (
    BaseModel,
    Field,
    EmailStr,
    field_validator,
    field_serializer,
    model_validator,
    ConfigDict,
)
from aws_lambda_powertools.utilities.parser import BaseModel as PowertoolsBaseModel

# Import relationship models
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .relationships import (
        UserReference,
        UserPortfolio,
        FundSubscription,
        UserFundAccess,
    )


class UserRole(str, Enum):
    """User role enumeration."""

    ADMIN = "admin"
    FUND_MANAGER = "fund_manager"
    ANALYST = "analyst"
    VIEWER = "viewer"
    COMPLIANCE_OFFICER = "compliance_officer"
    PORTFOLIO_MANAGER = "portfolio_manager"


class UserStatus(str, Enum):
    """User status enumeration."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING_VERIFICATION = "pending_verification"
    LOCKED = "locked"


class Department(str, Enum):
    """Department enumeration."""

    INVESTMENT_MANAGEMENT = "investment_management"
    RESEARCH = "research"
    COMPLIANCE = "compliance"
    OPERATIONS = "operations"
    RISK_MANAGEMENT = "risk_management"
    SALES = "sales"
    CLIENT_SERVICES = "client_services"
    IT = "it"
    FINANCE = "finance"
    ADMINISTRATION = "administration"


class UserPreferences(BaseModel):
    """User preferences nested model."""

    theme: Optional[str] = Field(default="light", description="UI theme preference")
    language: Optional[str] = Field(default="en", description="Language preference")
    timezone: Optional[str] = Field(default="UTC", description="Timezone preference")
    date_format: Optional[str] = Field(
        default="YYYY-MM-DD", description="Date format preference"
    )
    currency_display: Optional[str] = Field(
        default="USD", description="Default currency for display"
    )
    dashboard_layout: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Dashboard layout configuration"
    )
    notification_settings: Optional[Dict[str, bool]] = Field(
        default_factory=dict, description="Notification preferences"
    )

    @field_validator("notification_settings", mode="before")
    def set_default_notifications(cls, v):
        """Set default notification settings."""
        if v is None:
            return {
                "email_alerts": True,
                "fund_updates": True,
                "system_notifications": True,
                "performance_reports": False,
                "maintenance_alerts": True,
            }
        return v


class PermissionSet(BaseModel):
    """Permission set nested model."""

    can_view_funds: bool = Field(default=True, description="Can view fund information")
    can_edit_funds: bool = Field(default=False, description="Can edit fund information")
    can_create_funds: bool = Field(default=False, description="Can create new funds")
    can_delete_funds: bool = Field(default=False, description="Can delete funds")
    can_view_reports: bool = Field(default=True, description="Can view reports")
    can_generate_reports: bool = Field(
        default=False, description="Can generate reports"
    )
    can_manage_users: bool = Field(default=False, description="Can manage other users")
    can_view_audit_logs: bool = Field(default=False, description="Can view audit logs")
    can_export_data: bool = Field(default=False, description="Can export data")
    can_import_data: bool = Field(default=False, description="Can import data")
    can_access_admin_panel: bool = Field(
        default=False, description="Can access admin panel"
    )
    custom_permissions: Optional[Dict[str, bool]] = Field(
        default_factory=dict, description="Custom permissions"
    )


class SessionInfo(BaseModel):
    """Session information nested model."""

    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    last_activity: Optional[datetime] = Field(
        None, description="Last activity timestamp"
    )
    login_count: int = Field(default=0, description="Total login count")
    failed_login_attempts: int = Field(default=0, description="Failed login attempts")
    ip_address: Optional[str] = Field(None, description="Last known IP address")
    user_agent: Optional[str] = Field(None, description="Last known user agent")
    session_duration: Optional[int] = Field(
        None, description="Session duration in minutes"
    )


class User(PowertoolsBaseModel):
    """
    User data model for DynamoDB.

    DynamoDB Table: fundflow-{env}-users
    Primary Key: user_id (HASH)
    Global Secondary Indexes:
    - email_index: email (HASH)
    - role_index: role (HASH), created_at (RANGE)
    """

    # Primary Key
    user_id: str = Field(..., description="Unique user identifier")

    # Authentication Information
    email: EmailStr = Field(..., description="User email address")
    cognito_sub: Optional[str] = Field(None, description="AWS Cognito subject")

    # Personal Information
    first_name: str = Field(..., min_length=1, max_length=50)
    last_name: str = Field(..., min_length=1, max_length=50)
    full_name: Optional[str] = Field(None, description="Full name (computed)")
    phone_number: Optional[str] = Field(None, description="Phone number")

    # Professional Information
    role: UserRole = Field(..., description="User role")
    department: Optional[Department] = Field(None, description="Department")
    job_title: Optional[str] = Field(None, max_length=100, description="Job title")
    employee_id: Optional[str] = Field(None, max_length=20, description="Employee ID")
    manager_id: Optional[str] = Field(None, description="Manager's user ID")

    # Account Status
    status: UserStatus = Field(default=UserStatus.ACTIVE)
    is_verified: bool = Field(default=False, description="Email verification status")
    is_mfa_enabled: bool = Field(
        default=False, description="Multi-factor authentication status"
    )

    # Permissions and Access
    permissions: PermissionSet = Field(
        default_factory=PermissionSet, description="User permissions"
    )
    access_groups: Optional[List[str]] = Field(
        default_factory=list, description="Access group memberships"
    )

    # User Preferences
    preferences: UserPreferences = Field(
        default_factory=UserPreferences, description="User preferences"
    )

    # Session and Activity
    session_info: Optional[SessionInfo] = Field(
        default_factory=SessionInfo, description="Session information"
    )

    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_password_change: Optional[datetime] = Field(
        None, description="Last password change timestamp"
    )
    email_verified_at: Optional[datetime] = Field(
        None, description="Email verification timestamp"
    )

    # Additional metadata
    notes: Optional[str] = Field(
        None, max_length=500, description="Admin notes about the user"
    )
    tags: Optional[List[str]] = Field(
        default_factory=list, description="User tags for categorization"
    )
    custom_fields: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Custom fields for extensibility"
    )

    # Relationship fields (optional, loaded separately for performance)
    manager_details: Optional["UserReference"] = Field(
        None, description="Detailed manager information"
    )
    direct_reports: Optional[List["UserReference"]] = Field(
        default_factory=list, description="Users reporting to this user"
    )
    portfolios: Optional[List["UserPortfolio"]] = Field(
        default_factory=list, description="User's fund portfolios"
    )
    fund_subscriptions: Optional[List["FundSubscription"]] = Field(
        default_factory=list, description="Fund update subscriptions"
    )
    fund_access_permissions: Optional[List["UserFundAccess"]] = Field(
        default_factory=list, description="Specific fund access permissions"
    )

    @model_validator(mode="after")
    def compute_full_name(self):
        """Compute full name from first and last name."""
        if self.first_name and self.last_name:
            self.full_name = f"{self.first_name} {self.last_name}"
        return self

    @field_validator("updated_at", mode="before")
    def set_updated_at(cls, v):
        """Always update the updated_at timestamp."""
        return datetime.now(timezone.utc)

    @field_validator("email")
    def email_must_be_lowercase(cls, v):
        """Ensure email is lowercase."""
        return v.lower() if v else v

    @model_validator(mode="after")
    def set_role_permissions(self):
        """Set default permissions based on role."""
        if not self.permissions or (
            hasattr(self.permissions, "__dict__")
            and not any(vars(self.permissions).values())
        ):
            if self.role == UserRole.ADMIN:
                self.permissions = PermissionSet(
                    can_view_funds=True,
                    can_edit_funds=True,
                    can_create_funds=True,
                    can_delete_funds=True,
                    can_view_reports=True,
                    can_generate_reports=True,
                    can_manage_users=True,
                    can_view_audit_logs=True,
                    can_export_data=True,
                    can_import_data=True,
                    can_access_admin_panel=True,
                )
            elif self.role == UserRole.FUND_MANAGER:
                self.permissions = PermissionSet(
                    can_view_funds=True,
                    can_edit_funds=True,
                    can_create_funds=True,
                    can_view_reports=True,
                    can_generate_reports=True,
                    can_export_data=True,
                )
            elif self.role == UserRole.ANALYST:
                self.permissions = PermissionSet(
                    can_view_funds=True,
                    can_view_reports=True,
                    can_generate_reports=True,
                    can_export_data=True,
                )
            elif self.role == UserRole.COMPLIANCE_OFFICER:
                self.permissions = PermissionSet(
                    can_view_funds=True,
                    can_view_reports=True,
                    can_generate_reports=True,
                    can_view_audit_logs=True,
                    can_export_data=True,
                )
            else:  # VIEWER
                self.permissions = PermissionSet(
                    can_view_funds=True, can_view_reports=True
                )

        if not self.permissions:
            self.permissions = PermissionSet()
        return self

    @field_validator("user_id")
    def validate_user_id_format(cls, v):
        """Validate user ID follows expected format."""
        if not v:
            raise ValueError("User ID is required")
        if len(v) < 3:
            raise ValueError("User ID must be at least 3 characters")
        if len(v) > 50:
            raise ValueError("User ID must not exceed 50 characters")
        # Allow alphanumeric, hyphens, and underscores
        import re

        if not re.match(r"^[a-zA-Z0-9_-]+$", v):
            raise ValueError(
                "User ID can only contain letters, numbers, hyphens, and underscores"
            )
        return v

    @field_validator("phone_number")
    def validate_phone_number(cls, v):
        """Validate phone number format if provided."""
        if v is not None:
            import re

            # Remove all non-digit characters for validation
            digits_only = re.sub(r"\D", "", v)
            if len(digits_only) < 10 or len(digits_only) > 15:
                raise ValueError("Phone number must contain 10-15 digits")
            # Allow common phone number formats
            if not re.match(r"^[\+]?[\d\s\-\(\)\.]+$", v):
                raise ValueError("Phone number contains invalid characters")
        return v

    @field_validator("employee_id")
    def validate_employee_id(cls, v):
        """Validate employee ID format if provided."""
        if v is not None:
            if len(v) > 20:
                raise ValueError("Employee ID must not exceed 20 characters")
            import re

            if not re.match(r"^[a-zA-Z0-9_-]+$", v):
                raise ValueError(
                    "Employee ID can only contain letters, numbers, hyphens, and underscores"
                )
        return v

    @model_validator(mode="after")
    def validate_manager_hierarchy(self):
        """Validate manager ID and prevent self-reference."""
        if self.manager_id is not None:
            # Prevent self-reference
            if self.user_id and self.manager_id == self.user_id:
                raise ValueError("User cannot be their own manager")

            # Validate manager ID format (same as user_id)
            if len(self.manager_id) < 3 or len(self.manager_id) > 50:
                raise ValueError("Manager ID must be between 3 and 50 characters")

            import re

            if not re.match(r"^[a-zA-Z0-9_-]+$", self.manager_id):
                raise ValueError(
                    "Manager ID can only contain letters, numbers, hyphens, and underscores"
                )
        return self

    @model_validator(mode="after")
    def validate_status_transitions(self):
        """Validate user status and related field consistency."""
        # If user is suspended/locked, they shouldn't be verified for new activities
        if self.status in [UserStatus.SUSPENDED, UserStatus.LOCKED]:
            if self.is_verified is True:
                # This is more of a warning - suspended users can still be verified
                pass

        # Pending verification users should not be fully verified
        if self.status == UserStatus.PENDING_VERIFICATION:
            if self.is_verified is True:
                raise ValueError(
                    "Users with pending verification status cannot be marked as verified"
                )

        return self

    @model_validator(mode="after")
    def validate_role_permissions_consistency(self):
        """Validate role and permissions consistency."""
        if self.role == UserRole.ADMIN:
            # Admin users should have admin panel access
            pass  # Permissions are set automatically in the permissions validator
        elif self.role == UserRole.VIEWER:
            # Viewers should have minimal permissions
            pass  # Handled in permissions validator

        return self

    @model_validator(mode="after")
    def validate_permission_consistency(self):
        """Validate permission set consistency and business rules."""
        if self.permissions is not None:
            # Users who can delete should also be able to edit
            if (
                self.permissions.can_delete_funds
                and not self.permissions.can_edit_funds
            ):
                raise ValueError(
                    "Users who can delete funds must also be able to edit them"
                )

            # Users who can edit should also be able to view
            if self.permissions.can_edit_funds and not self.permissions.can_view_funds:
                raise ValueError(
                    "Users who can edit funds must also be able to view them"
                )

            # Users who can create should also be able to view
            if (
                self.permissions.can_create_funds
                and not self.permissions.can_view_funds
            ):
                raise ValueError(
                    "Users who can create funds must also be able to view them"
                )

            # Admin panel access should come with user management capabilities
            if (
                self.permissions.can_access_admin_panel
                and not self.permissions.can_manage_users
            ):
                # This is a soft requirement - admins might have panel access for other reasons
                pass

            # Data import should come with export capabilities
            if (
                self.permissions.can_import_data
                and not self.permissions.can_export_data
            ):
                raise ValueError(
                    "Users who can import data should also be able to export data"
                )

            # Report generation should come with report viewing
            if (
                self.permissions.can_generate_reports
                and not self.permissions.can_view_reports
            ):
                raise ValueError(
                    "Users who can generate reports must also be able to view them"
                )

        return self

    @field_validator("access_groups")
    def validate_access_groups(cls, v):
        """Validate access groups format and constraints."""
        if v is not None:
            if len(v) > 20:  # Reasonable limit
                raise ValueError("User cannot belong to more than 20 access groups")

            for group in v:
                if not isinstance(group, str):
                    raise ValueError("Access group names must be strings")
                if len(group) > 50:
                    raise ValueError("Access group name cannot exceed 50 characters")
                import re

                if not re.match(r"^[a-zA-Z0-9_\-\s]+$", group):
                    raise ValueError("Access group name contains invalid characters")

        return v

    @field_validator("preferences")
    def validate_user_preferences(cls, v):
        """Validate user preferences consistency."""
        if v is not None:
            # Validate timezone
            if v.timezone:
                # Basic timezone validation - could be enhanced with pytz
                if len(v.timezone) > 50:
                    raise ValueError("Timezone string too long")

            # Validate language code
            if v.language:
                import re

                if not re.match(r"^[a-z]{2}(-[A-Z]{2})?$", v.language):
                    raise ValueError("Language must be in format 'en' or 'en-US'")

            # Validate currency display
            if v.currency_display:
                if len(v.currency_display) != 3:
                    raise ValueError("Currency display must be a 3-character code")
                if not v.currency_display.isupper():
                    raise ValueError("Currency code must be uppercase")

        return v

    @field_validator("session_info")
    def validate_session_info(cls, v):
        """Validate session information consistency."""
        if v is not None:
            # Validate failed login attempts
            if v.failed_login_attempts is not None:
                if v.failed_login_attempts < 0:
                    raise ValueError("Failed login attempts cannot be negative")
                if v.failed_login_attempts > 100:  # Reasonable upper bound
                    raise ValueError("Failed login attempts seems unreasonably high")

            # Validate login count
            if v.login_count is not None:
                if v.login_count < 0:
                    raise ValueError("Login count cannot be negative")

            # Validate session duration
            if v.session_duration is not None:
                if v.session_duration < 0:
                    raise ValueError("Session duration cannot be negative")
                if v.session_duration > 86400:  # 24 hours in minutes
                    raise ValueError("Session duration cannot exceed 24 hours")

            # Validate IP address format
            if v.ip_address:
                import re

                # Basic IPv4/IPv6 validation
                ipv4_pattern = r"^(\d{1,3}\.){3}\d{1,3}$"
                ipv6_pattern = r"^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$"
                if not (
                    re.match(ipv4_pattern, v.ip_address)
                    or re.match(ipv6_pattern, v.ip_address)
                ):
                    # Allow simplified validation for now
                    if len(v.ip_address) > 45:  # Max IPv6 length
                        raise ValueError("IP address format appears invalid")

        return v

    @field_validator("last_password_change")
    def validate_password_change_date(cls, v):
        """Validate password change date is not in the future."""
        if v and v > datetime.now(timezone.utc):
            raise ValueError("Password change date cannot be in the future")
        return v

    @model_validator(mode="after")
    def validate_email_verification_date(self):
        """Validate email verification date consistency."""
        if self.email_verified_at is not None:
            if self.email_verified_at > datetime.now(timezone.utc):
                raise ValueError("Email verification date cannot be in the future")

            # If verification date is set, is_verified should be True
            if not self.is_verified:
                raise ValueError("Email verification date set but is_verified is False")

        return self

    @field_validator("tags")
    def validate_tags(cls, v):
        """Validate user tags format and constraints."""
        if v is not None:
            if len(v) > 20:  # Reasonable limit
                raise ValueError("User cannot have more than 20 tags")

            for tag in v:
                if not isinstance(tag, str):
                    raise ValueError("Tags must be strings")
                if len(tag) > 30:
                    raise ValueError("Tag cannot exceed 30 characters")
                import re

                if not re.match(r"^[a-zA-Z0-9_\-\s]+$", tag):
                    raise ValueError("Tag contains invalid characters")

        return v

    model_config = ConfigDict(
        use_enum_values=True,
        validate_assignment=True,
    )

    @field_serializer(
        "created_at",
        "updated_at",
        "last_password_change",
        "email_verified_at",
        when_used="json",
    )
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        """Serialize datetime fields to ISO format for JSON serialization."""
        return value.isoformat() if value is not None else None


class UserCreate(BaseModel):
    """User creation model (excludes auto-generated fields)."""

    email: EmailStr
    first_name: str = Field(..., min_length=1, max_length=50)
    last_name: str = Field(..., min_length=1, max_length=50)
    phone_number: Optional[str] = None
    role: UserRole
    department: Optional[Department] = None
    job_title: Optional[str] = None
    employee_id: Optional[str] = None
    manager_id: Optional[str] = None
    access_groups: Optional[List[str]] = None
    notes: Optional[str] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None


class UserUpdate(BaseModel):
    """User update model (all fields optional)."""

    first_name: Optional[str] = Field(None, min_length=1, max_length=50)
    last_name: Optional[str] = Field(None, min_length=1, max_length=50)
    phone_number: Optional[str] = None
    role: Optional[UserRole] = None
    department: Optional[Department] = None
    job_title: Optional[str] = Field(None, max_length=100)
    employee_id: Optional[str] = Field(None, max_length=20)
    manager_id: Optional[str] = None
    status: Optional[UserStatus] = None
    is_verified: Optional[bool] = None
    is_mfa_enabled: Optional[bool] = None
    permissions: Optional[PermissionSet] = None
    access_groups: Optional[List[str]] = None
    preferences: Optional[UserPreferences] = None
    notes: Optional[str] = Field(None, max_length=500)
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None


class UserResponse(User):
    """User response model for API responses (excludes sensitive fields)."""

    # Note: In Pydantic V2, field exclusion should be handled at the serialization level
    # or by overriding specific fields. The 'fields' config has been removed.
    pass


class UserLogin(BaseModel):
    """User login model."""

    email: EmailStr
    password: str = Field(..., min_length=8)
    remember_me: bool = Field(default=False)


class UserPasswordReset(BaseModel):
    """User password reset model."""

    email: EmailStr


class UserPasswordChange(BaseModel):
    """User password change model."""

    current_password: str = Field(..., min_length=8)
    new_password: str = Field(..., min_length=8)
    confirm_password: str = Field(..., min_length=8)

    @model_validator(mode="after")
    def passwords_match(self):
        if self.new_password and self.confirm_password != self.new_password:
            raise ValueError("Passwords do not match")
        return self


# DynamoDB specific utilities
class UserDynamoDBItem:
    """Utility class for DynamoDB item transformations."""

    @staticmethod
    def to_dynamodb_item(user: User) -> Dict[str, Any]:
        """Convert User model to DynamoDB item format."""
        item = user.dict()

        # Convert datetime objects to ISO strings
        datetime_fields = [
            "created_at",
            "updated_at",
            "last_password_change",
            "email_verified_at",
        ]
        for field in datetime_fields:
            if item.get(field):
                item[field] = (
                    item[field].isoformat()
                    if isinstance(item[field], datetime)
                    else item[field]
                )

        # Handle nested session_info datetime fields
        if item.get("session_info"):
            session_datetime_fields = ["last_login", "last_activity"]
            for field in session_datetime_fields:
                if item["session_info"].get(field):
                    session_field = item["session_info"][field]
                    item["session_info"][field] = (
                        session_field.isoformat()
                        if isinstance(session_field, datetime)
                        else session_field
                    )

        return item

    @staticmethod
    def from_dynamodb_item(item: Dict[str, Any]) -> User:
        """Convert DynamoDB item to User model."""
        # Convert string dates back to datetime objects
        datetime_fields = [
            "created_at",
            "updated_at",
            "last_password_change",
            "email_verified_at",
        ]
        for field in datetime_fields:
            if item.get(field) and isinstance(item[field], str):
                item[field] = datetime.fromisoformat(item[field])

        # Handle nested session_info datetime fields
        if item.get("session_info"):
            session_datetime_fields = ["last_login", "last_activity"]
            for field in session_datetime_fields:
                if item["session_info"].get(field) and isinstance(
                    item["session_info"][field], str
                ):
                    item["session_info"][field] = datetime.fromisoformat(
                        item["session_info"][field]
                    )

        return User(**item)
