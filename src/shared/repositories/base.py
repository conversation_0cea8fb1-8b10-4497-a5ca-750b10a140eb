"""
Base repository class for DynamoDB operations.
"""

import boto3
from typing import Dict, List, Optional, Any, Type, TypeVar
from abc import ABC, abstractmethod
from botocore.exceptions import ClientError
from aws_lambda_powertools import Logger

logger = Logger()

T = TypeVar("T")


class DynamoDBRepository(ABC):
    """Base repository class for DynamoDB operations."""

    def __init__(self, table_name: str, region: str = "us-east-1"):
        """Initialize the repository with table name and region."""
        self.table_name = table_name
        self.region = region
        self.dynamodb = boto3.client("dynamodb", region_name=region)
        self.resource = boto3.resource("dynamodb", region_name=region)
        self.table = self.resource.Table(table_name)

    @abstractmethod
    def _get_primary_key(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Get the primary key for an item."""
        pass

    @abstractmethod
    def _serialize_item(self, item: Any) -> Dict[str, Any]:
        """Serialize a model object to DynamoDB format."""
        pass

    @abstractmethod
    def _deserialize_item(self, item: Dict[str, Any]) -> Any:
        """Deserialize a DynamoDB item to model object."""
        pass

    def create(self, item: Any) -> Any:
        """Create a new item in DynamoDB."""
        try:
            serialized_item = self._serialize_item(item)

            # Use condition to prevent overwriting existing items
            self.table.put_item(
                Item=serialized_item,
                ConditionExpression="attribute_not_exists(#pk)",
                ExpressionAttributeNames={
                    "#pk": list(self._get_primary_key(serialized_item).keys())[0]
                },
            )

            logger.info(
                f"Created item in {self.table_name}",
                extra={"item_key": self._get_primary_key(serialized_item)},
            )
            return item

        except ClientError as e:
            if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
                logger.warning(
                    f"Item already exists in {self.table_name}",
                    extra={"item_key": self._get_primary_key(serialized_item)},
                )
                raise ValueError("Item already exists")
            logger.error(
                f"Error creating item in {self.table_name}", extra={"error": str(e)}
            )
            raise

    def get_by_id(self, primary_key: Dict[str, Any]) -> Optional[Any]:
        """Get an item by its primary key."""
        try:
            response = self.table.get_item(Key=primary_key)

            if "Item" in response:
                logger.info(
                    f"Retrieved item from {self.table_name}",
                    extra={"item_key": primary_key},
                )
                return self._deserialize_item(response["Item"])

            logger.info(
                f"Item not found in {self.table_name}", extra={"item_key": primary_key}
            )
            return None

        except ClientError as e:
            logger.error(
                f"Error retrieving item from {self.table_name}",
                extra={"error": str(e), "item_key": primary_key},
            )
            raise

    def update(self, item: Any) -> Any:
        """Update an existing item."""
        try:
            serialized_item = self._serialize_item(item)
            primary_key = self._get_primary_key(serialized_item)

            # Build update expression dynamically
            update_expression_parts = []
            expression_attribute_names = {}
            expression_attribute_values = {}

            for key, value in serialized_item.items():
                if key not in primary_key:
                    attr_name = f"#{key}"
                    attr_value = f":{key}"
                    update_expression_parts.append(f"{attr_name} = {attr_value}")
                    expression_attribute_names[attr_name] = key
                    expression_attribute_values[attr_value] = value

            if not update_expression_parts:
                logger.warning(f"No attributes to update for item in {self.table_name}")
                return item

            update_expression = "SET " + ", ".join(update_expression_parts)

            self.table.update_item(
                Key=primary_key,
                UpdateExpression=update_expression,
                ExpressionAttributeNames={
                    **expression_attribute_names,
                    "#pk": list(primary_key.keys())[0],
                },
                ExpressionAttributeValues=expression_attribute_values,
                ConditionExpression="attribute_exists(#pk)",
            )

            logger.info(
                f"Updated item in {self.table_name}", extra={"item_key": primary_key}
            )
            return item

        except ClientError as e:
            if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
                logger.warning(
                    f"Item not found for update in {self.table_name}",
                    extra={"item_key": primary_key},
                )
                raise ValueError("Item not found")
            logger.error(
                f"Error updating item in {self.table_name}", extra={"error": str(e)}
            )
            raise

    def delete(self, primary_key: Dict[str, Any]) -> bool:
        """Delete an item by its primary key."""
        try:
            self.table.delete_item(
                Key=primary_key,
                ConditionExpression="attribute_exists(#pk)",
                ExpressionAttributeNames={"#pk": list(primary_key.keys())[0]},
            )

            logger.info(
                f"Deleted item from {self.table_name}", extra={"item_key": primary_key}
            )
            return True

        except ClientError as e:
            if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
                logger.warning(
                    f"Item not found for deletion in {self.table_name}",
                    extra={"item_key": primary_key},
                )
                return False
            logger.error(
                f"Error deleting item from {self.table_name}", extra={"error": str(e)}
            )
            raise

    def list_all(
        self, limit: Optional[int] = None, last_evaluated_key: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """List all items with optional pagination."""
        try:
            scan_kwargs = {}
            if limit:
                scan_kwargs["Limit"] = limit
            if last_evaluated_key:
                scan_kwargs["ExclusiveStartKey"] = last_evaluated_key

            response = self.table.scan(**scan_kwargs)

            items = [self._deserialize_item(item) for item in response.get("Items", [])]

            result = {
                "items": items,
                "count": len(items),
                "scanned_count": response.get("ScannedCount", 0),
            }

            if "LastEvaluatedKey" in response:
                result["last_evaluated_key"] = response["LastEvaluatedKey"]

            logger.info(
                f"Listed items from {self.table_name}", extra={"count": len(items)}
            )
            return result

        except ClientError as e:
            logger.error(
                f"Error listing items from {self.table_name}", extra={"error": str(e)}
            )
            raise

    def query_index(
        self,
        index_name: str,
        key_condition: str,
        expression_attribute_names: Dict[str, str],
        expression_attribute_values: Dict[str, Any],
        filter_expression: Optional[str] = None,
        limit: Optional[int] = None,
        last_evaluated_key: Optional[Dict] = None,
        scan_index_forward: bool = True,
    ) -> Dict[str, Any]:
        """Query a Global Secondary Index."""
        try:
            query_kwargs = {
                "IndexName": index_name,
                "KeyConditionExpression": key_condition,
                "ExpressionAttributeNames": expression_attribute_names,
                "ExpressionAttributeValues": expression_attribute_values,
                "ScanIndexForward": scan_index_forward,
            }

            if filter_expression:
                query_kwargs["FilterExpression"] = filter_expression
            if limit:
                query_kwargs["Limit"] = limit
            if last_evaluated_key:
                query_kwargs["ExclusiveStartKey"] = last_evaluated_key

            response = self.table.query(**query_kwargs)

            items = [self._deserialize_item(item) for item in response.get("Items", [])]

            result = {
                "items": items,
                "count": len(items),
                "scanned_count": response.get("ScannedCount", 0),
            }

            if "LastEvaluatedKey" in response:
                result["last_evaluated_key"] = response["LastEvaluatedKey"]

            logger.info(
                f"Queried index {index_name} on {self.table_name}",
                extra={"count": len(items)},
            )
            return result

        except ClientError as e:
            logger.error(
                f"Error querying index {index_name} on {self.table_name}",
                extra={"error": str(e)},
            )
            raise

    def batch_get_items(self, keys: List[Dict[str, Any]]) -> List[Any]:
        """Get multiple items by their keys."""
        if not keys:
            return []

        try:
            # DynamoDB batch_get_item can handle max 100 items
            batch_size = 100
            all_items = []

            for i in range(0, len(keys), batch_size):
                batch_keys = keys[i : i + batch_size]

                response = self.dynamodb.batch_get_item(
                    RequestItems={
                        self.table_name: {
                            "Keys": [
                                {
                                    k: {"S": v} if isinstance(v, str) else {"N": str(v)}
                                    for k, v in key.items()
                                }
                                for key in batch_keys
                            ]
                        }
                    }
                )

                if self.table_name in response.get("Responses", {}):
                    items = response["Responses"][self.table_name]
                    # Convert DynamoDB format back to Python dict
                    python_items = []
                    for item in items:
                        python_item = {}
                        for k, v in item.items():
                            if "S" in v:
                                python_item[k] = v["S"]
                            elif "N" in v:
                                python_item[k] = v["N"]
                            # Add more type conversions as needed
                        python_items.append(self._deserialize_item(python_item))

                    all_items.extend(python_items)

            logger.info(
                f"Batch retrieved {len(all_items)} items from {self.table_name}"
            )
            return all_items

        except ClientError as e:
            logger.error(
                f"Error batch retrieving items from {self.table_name}",
                extra={"error": str(e)},
            )
            raise
