"""
Repository for fund snapshot operations.
Handles CRUD operations for monthly fund data snapshots.
"""

import os
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone
from decimal import Decimal

import boto3
from boto3.dynamodb.conditions import Key, Attr
from aws_lambda_powertools import Logger
from botocore.exceptions import ClientError

from ..shared.models import (
    FundSnapshot,
    FundSnapshotCreate,
    FundSnapshotUpdate,
    FundSnapshotDynamoDBItem,
)
from ..shared.exceptions import (
    NotFoundException,
    ConflictException,
    ValidationException,
    RepositoryException,
)

logger = Logger()


class FundSnapshotRepository:
    """Repository for fund snapshot operations."""
    
    def __init__(self, table_name: Optional[str] = None):
        """
        Initialize the repository.
        
        Args:
            table_name: Optional DynamoDB table name override
        """
        self.dynamodb = boto3.resource("dynamodb")
        self.table_name = table_name or os.environ.get("FUND_SNAPSHOT_TABLE")
        if not self.table_name:
            raise ValueError("FUND_SNAPSHOT_TABLE environment variable not set")
        self.table = self.dynamodb.Table(self.table_name)
        logger.info(f"Initialized FundSnapshotRepository with table: {self.table_name}")
    
    def create_or_update_snapshot(
        self,
        fund_id: str,
        snapshot_month: str,
        snapshot_data: FundSnapshotCreate,
        created_by: str
    ) -> FundSnapshot:
        """
        Create or update a fund snapshot for a specific month.
        
        Args:
            fund_id: Fund identifier
            snapshot_month: Month in YYYY-MM format
            snapshot_data: Snapshot data to create/update
            created_by: User creating/updating the snapshot
            
        Returns:
            Created or updated fund snapshot
        """
        try:
            # Check if snapshot already exists
            existing_snapshot = self.get_snapshot(fund_id, snapshot_month)
            
            if existing_snapshot:
                # Update existing snapshot
                return self._update_snapshot(
                    fund_id, snapshot_month, snapshot_data, created_by
                )
            else:
                # Create new snapshot
                return self._create_snapshot(
                    fund_id, snapshot_month, snapshot_data, created_by
                )
                
        except Exception as e:
            logger.error(f"Failed to create/update snapshot: {str(e)}")
            raise RepositoryException(f"Failed to create/update snapshot: {str(e)}")
    
    def _create_snapshot(
        self,
        fund_id: str,
        snapshot_month: str,
        snapshot_data: FundSnapshotCreate,
        created_by: str
    ) -> FundSnapshot:
        """Create a new fund snapshot."""
        try:
            # Create FundSnapshot instance
            snapshot = FundSnapshot(
                fund_id=fund_id,
                snapshot_month=snapshot_month,
                created_by=created_by,
                **snapshot_data.model_dump(exclude_unset=True)
            )
            
            # Convert to DynamoDB item
            item = FundSnapshotDynamoDBItem.to_dynamodb_item(snapshot)
            
            # Put item in DynamoDB
            self.table.put_item(Item=item)
            
            logger.info(f"Created snapshot for fund {fund_id}, month {snapshot_month}")
            return snapshot
            
        except ClientError as e:
            logger.error(f"DynamoDB error creating snapshot: {str(e)}")
            raise RepositoryException(f"Failed to create snapshot: {str(e)}")
    
    def _update_snapshot(
        self,
        fund_id: str,
        snapshot_month: str,
        snapshot_data: FundSnapshotUpdate,
        created_by: str
    ) -> FundSnapshot:
        """Update an existing fund snapshot."""
        try:
            # Get existing snapshot
            existing = self.get_snapshot(fund_id, snapshot_month)
            if not existing:
                raise NotFoundException(
                    f"Snapshot not found for fund {fund_id}, month {snapshot_month}"
                )
            
            # Update fields
            update_data = snapshot_data.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(existing, key, value)
            
            # Update metadata
            existing.updated_at = datetime.now(timezone.utc)
            existing.created_by = created_by
            
            # Convert to DynamoDB item
            item = FundSnapshotDynamoDBItem.to_dynamodb_item(existing)
            
            # Update item in DynamoDB
            self.table.put_item(Item=item)
            
            logger.info(f"Updated snapshot for fund {fund_id}, month {snapshot_month}")
            return existing
            
        except ClientError as e:
            logger.error(f"DynamoDB error updating snapshot: {str(e)}")
            raise RepositoryException(f"Failed to update snapshot: {str(e)}")
    
    def get_snapshot(
        self, fund_id: str, snapshot_month: str
    ) -> Optional[FundSnapshot]:
        """
        Get a specific fund snapshot.
        
        Args:
            fund_id: Fund identifier
            snapshot_month: Month in YYYY-MM format
            
        Returns:
            Fund snapshot if found, None otherwise
        """
        try:
            response = self.table.get_item(
                Key={
                    "fund_id": fund_id,
                    "snapshot_month": snapshot_month
                }
            )
            
            if "Item" not in response:
                return None
            
            return FundSnapshotDynamoDBItem.from_dynamodb_item(response["Item"])
            
        except ClientError as e:
            logger.error(f"DynamoDB error getting snapshot: {str(e)}")
            raise RepositoryException(f"Failed to get snapshot: {str(e)}")
    
    def get_latest_snapshot(self, fund_id: str) -> Optional[FundSnapshot]:
        """
        Get the most recent snapshot for a fund.
        
        Args:
            fund_id: Fund identifier
            
        Returns:
            Most recent fund snapshot if found, None otherwise
        """
        try:
            # Query snapshots for the fund, sorted by month in descending order
            response = self.table.query(
                KeyConditionExpression=Key("fund_id").eq(fund_id),
                ScanIndexForward=False,  # Sort in descending order
                Limit=1
            )
            
            items = response.get("Items", [])
            if not items:
                return None
            
            return FundSnapshotDynamoDBItem.from_dynamodb_item(items[0])
            
        except ClientError as e:
            logger.error(f"DynamoDB error getting latest snapshot: {str(e)}")
            raise RepositoryException(f"Failed to get latest snapshot: {str(e)}")
    
    def list_snapshots(
        self,
        fund_id: str,
        start_month: Optional[str] = None,
        end_month: Optional[str] = None,
        limit: int = 100
    ) -> List[FundSnapshot]:
        """
        List all snapshots for a fund, optionally filtered by date range.
        
        Args:
            fund_id: Fund identifier
            start_month: Optional start month (inclusive) in YYYY-MM format
            end_month: Optional end month (inclusive) in YYYY-MM format
            limit: Maximum number of results
            
        Returns:
            List of fund snapshots
        """
        try:
            # Build key condition
            key_condition = Key("fund_id").eq(fund_id)
            
            if start_month and end_month:
                key_condition = key_condition & Key("snapshot_month").between(
                    start_month, end_month
                )
            elif start_month:
                key_condition = key_condition & Key("snapshot_month").gte(start_month)
            elif end_month:
                key_condition = key_condition & Key("snapshot_month").lte(end_month)
            
            # Query snapshots
            response = self.table.query(
                KeyConditionExpression=key_condition,
                ScanIndexForward=False,  # Sort by month descending
                Limit=limit
            )
            
            items = response.get("Items", [])
            return [
                FundSnapshotDynamoDBItem.from_dynamodb_item(item)
                for item in items
            ]
            
        except ClientError as e:
            logger.error(f"DynamoDB error listing snapshots: {str(e)}")
            raise RepositoryException(f"Failed to list snapshots: {str(e)}")
    
    def delete_snapshot(
        self, fund_id: str, snapshot_month: str
    ) -> bool:
        """
        Delete a fund snapshot.
        
        Args:
            fund_id: Fund identifier
            snapshot_month: Month in YYYY-MM format
            
        Returns:
            True if deleted, False if not found
        """
        try:
            # Check if snapshot exists
            existing = self.get_snapshot(fund_id, snapshot_month)
            if not existing:
                return False
            
            # Delete item
            self.table.delete_item(
                Key={
                    "fund_id": fund_id,
                    "snapshot_month": snapshot_month
                }
            )
            
            logger.info(f"Deleted snapshot for fund {fund_id}, month {snapshot_month}")
            return True
            
        except ClientError as e:
            logger.error(f"DynamoDB error deleting snapshot: {str(e)}")
            raise RepositoryException(f"Failed to delete snapshot: {str(e)}")
    
    def get_snapshots_by_created_date(
        self,
        fund_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[FundSnapshot]:
        """
        Get snapshots for a fund filtered by creation date.
        Uses the local secondary index on created_at.
        
        Args:
            fund_id: Fund identifier
            start_date: Optional start date (inclusive)
            end_date: Optional end date (inclusive)
            limit: Maximum number of results
            
        Returns:
            List of fund snapshots
        """
        try:
            # Build key condition
            key_condition = Key("fund_id").eq(fund_id)
            
            if start_date and end_date:
                key_condition = key_condition & Key("created_at").between(
                    start_date.isoformat(), end_date.isoformat()
                )
            elif start_date:
                key_condition = key_condition & Key("created_at").gte(
                    start_date.isoformat()
                )
            elif end_date:
                key_condition = key_condition & Key("created_at").lte(
                    end_date.isoformat()
                )
            
            # Query using LSI
            response = self.table.query(
                IndexName="fund_created_at_index",
                KeyConditionExpression=key_condition,
                ScanIndexForward=False,  # Sort by created_at descending
                Limit=limit
            )
            
            items = response.get("Items", [])
            return [
                FundSnapshotDynamoDBItem.from_dynamodb_item(item)
                for item in items
            ]
            
        except ClientError as e:
            logger.error(f"DynamoDB error querying by created date: {str(e)}")
            raise RepositoryException(f"Failed to query snapshots by date: {str(e)}")
    
    def batch_get_latest_snapshots(
        self, fund_ids: List[str]
    ) -> Dict[str, Optional[FundSnapshot]]:
        """
        Get the latest snapshots for multiple funds.
        
        Args:
            fund_ids: List of fund identifiers
            
        Returns:
            Dictionary mapping fund_id to latest snapshot (or None)
        """
        result = {}
        
        # Get latest snapshot for each fund
        # Note: This could be optimized with parallel queries
        for fund_id in fund_ids:
            try:
                latest = self.get_latest_snapshot(fund_id)
                result[fund_id] = latest
            except Exception as e:
                logger.warning(f"Failed to get latest snapshot for fund {fund_id}: {str(e)}")
                result[fund_id] = None
        
        return result