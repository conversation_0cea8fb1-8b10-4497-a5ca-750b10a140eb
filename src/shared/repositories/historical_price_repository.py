"""
Historical price data repository for DynamoDB operations.
Handles CRUD operations for historical price data.
"""

import os
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Dict, Any
from decimal import Decimal

import boto3
from boto3.dynamodb.conditions import Key, Attr
from botocore.exceptions import ClientError

try:
    from aws_lambda_powertools import Logger, Tracer, Metrics
    from aws_lambda_powertools.metrics import MetricUnit

    logger = Logger()
    tracer = Tracer()
    metrics = Metrics()

except ImportError:
    # Fallback for local development
    import logging

    logger = logging.getLogger(__name__)

    class MockTracer:
        def capture_method(self, func):
            return func

    class MockMetrics:
        def add_metric(self, name, unit, value):
            pass

    class MockMetricUnit:
        Count = "Count"

    tracer = MockTracer()
    metrics = MockMetrics()
    MetricUnit = MockMetricUnit()

from ..models.historical_price import (
    HistoricalPrice,
    HistoricalPriceCreate,
    HistoricalPriceUpdate,
    HistoricalPriceDynamoDBItem,
)


class HistoricalPriceRepository:
    """Repository for historical price data operations."""

    def __init__(self, table_name: Optional[str] = None):
        """Initialize repository with DynamoDB table."""
        self.table_name = table_name or os.environ.get("HISTORICAL_PRICES_TABLE")
        if not self.table_name:
            raise ValueError("HISTORICAL_PRICES_TABLE environment variable is required")

        self.dynamodb = boto3.resource("dynamodb")
        self.table = self.dynamodb.Table(self.table_name)  # type: ignore
        logger.info(
            f"Initialized HistoricalPriceRepository with table: {self.table_name}"
        )

    @tracer.capture_method
    def create(self, price_data: HistoricalPriceCreate) -> HistoricalPrice:
        """Create a new historical price record."""
        try:
            # Convert to HistoricalPrice model
            price = HistoricalPrice(**price_data.model_dump())

            # Convert to DynamoDB item
            item = HistoricalPriceDynamoDBItem.to_dynamodb_item(price)

            # Put item to DynamoDB
            self.table.put_item(
                Item=item,
                ConditionExpression="attribute_not_exists(fund_id) AND attribute_not_exists(#date_attr)",
                ExpressionAttributeNames={"#date_attr": "date"},
            )

            metrics.add_metric(
                name="HistoricalPriceCreated", unit=MetricUnit.Count, value=1
            )
            logger.info(f"Created historical price: {price.fund_id} - {price.date}")

            return price

        except ClientError as e:
            if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
                logger.warning(
                    f"Historical price already exists: {price_data.fund_id} - {price_data.date}"
                )
                raise ValueError(
                    f"Historical price already exists for {price_data.fund_id} on {price_data.date}"
                )
            else:
                logger.exception("Failed to create historical price")
                metrics.add_metric(
                    name="HistoricalPriceCreateErrors", unit=MetricUnit.Count, value=1
                )
                raise Exception("Failed to create historical price")

    @tracer.capture_method
    def get_by_fund_and_date(
        self, fund_id: str, date: str
    ) -> Optional[HistoricalPrice]:
        """Get historical price by fund ID and date."""
        try:
            response = self.table.get_item(Key={"fund_id": fund_id, "date": date})

            if "Item" not in response:
                return None

            price = HistoricalPriceDynamoDBItem.from_dynamodb_item(response["Item"])
            metrics.add_metric(
                name="HistoricalPriceRetrieved", unit=MetricUnit.Count, value=1
            )

            return price

        except Exception as e:
            logger.exception(f"Failed to get historical price: {fund_id} - {date}")
            metrics.add_metric(
                name="HistoricalPriceGetErrors", unit=MetricUnit.Count, value=1
            )
            raise Exception(f"Failed to retrieve historical price")

    @tracer.capture_method
    def get_fund_price_history(
        self,
        fund_id: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        limit: Optional[int] = None,
    ) -> List[HistoricalPrice]:
        """Get historical prices for a fund within a date range."""
        try:
            # Build query parameters with conditional KeyConditionExpression
            if start_date and end_date:
                key_condition = Key("fund_id").eq(fund_id) & Key("date").between(
                    start_date, end_date
                )
            elif start_date:
                key_condition = Key("fund_id").eq(fund_id) & Key("date").gte(start_date)
            elif end_date:
                key_condition = Key("fund_id").eq(fund_id) & Key("date").lte(end_date)
            else:
                key_condition = Key("fund_id").eq(fund_id)

            query_kwargs: Dict[str, Any] = {"KeyConditionExpression": key_condition}

            # Add limit if specified
            if limit:
                query_kwargs["Limit"] = limit

            # Sort by date descending (most recent first)
            query_kwargs["ScanIndexForward"] = False

            response = self.table.query(**query_kwargs)

            prices = []
            for item in response["Items"]:
                price = HistoricalPriceDynamoDBItem.from_dynamodb_item(item)
                prices.append(price)

            metrics.add_metric(
                name="HistoricalPriceHistoryRetrieved",
                unit=MetricUnit.Count,
                value=len(prices),
            )
            logger.info(
                f"Retrieved {len(prices)} historical prices for fund: {fund_id}"
            )

            return prices

        except Exception as e:
            logger.exception(f"Failed to get price history for fund: {fund_id}")
            metrics.add_metric(
                name="HistoricalPriceHistoryErrors", unit=MetricUnit.Count, value=1
            )
            raise Exception(f"Failed to retrieve price history")

    @tracer.capture_method
    def get_latest_price(self, fund_id: str) -> Optional[HistoricalPrice]:
        """Get the most recent price for a fund."""
        prices = self.get_fund_price_history(fund_id, limit=1)
        return prices[0] if prices else None

    @tracer.capture_method
    def get_market_prices_by_date(
        self, date: str, fund_ids: Optional[List[str]] = None
    ) -> List[HistoricalPrice]:
        """Get all fund prices for a specific date."""
        try:
            query_kwargs = {
                "IndexName": "date_index",
                "KeyConditionExpression": Key("date").eq(date),
            }

            response = self.table.query(**query_kwargs)

            prices = []
            for item in response["Items"]:
                price = HistoricalPriceDynamoDBItem.from_dynamodb_item(item)

                # Filter by fund_ids if provided
                if fund_ids is None or price.fund_id in fund_ids:
                    prices.append(price)

            metrics.add_metric(
                name="MarketPricesRetrieved", unit=MetricUnit.Count, value=len(prices)
            )
            logger.info(f"Retrieved {len(prices)} market prices for date: {date}")

            return prices

        except Exception as e:
            logger.exception(f"Failed to get market prices for date: {date}")
            metrics.add_metric(
                name="MarketPricesErrors", unit=MetricUnit.Count, value=1
            )
            raise Exception(f"Failed to retrieve market prices")

    @tracer.capture_method
    def update(
        self, fund_id: str, date: str, update_data: HistoricalPriceUpdate
    ) -> Optional[HistoricalPrice]:
        """Update an existing historical price record."""
        try:
            # Get existing record
            existing_price = self.get_by_fund_and_date(fund_id, date)
            if not existing_price:
                logger.warning(
                    f"Historical price not found for update: {fund_id} - {date}"
                )
                return None

            # Update fields
            update_dict = update_data.model_dump(exclude_unset=True)
            if not update_dict:
                logger.warning("No update data provided")
                return existing_price

            # Build update expression
            update_expression_parts = []
            expression_attribute_values = {}
            expression_attribute_names = {}

            for field, value in update_dict.items():
                attr_name = f"#{field}"
                attr_value = f":{field}"

                update_expression_parts.append(f"{attr_name} = {attr_value}")
                expression_attribute_names[attr_name] = field
                expression_attribute_values[attr_value] = (
                    str(value) if isinstance(value, Decimal) else value
                )

            # Always update the updated_at timestamp
            update_expression_parts.append("#updated_at = :updated_at")
            expression_attribute_names["#updated_at"] = "updated_at"
            expression_attribute_values[":updated_at"] = datetime.now(
                timezone.utc
            ).isoformat()

            update_expression = "SET " + ", ".join(update_expression_parts)

            # Update item in DynamoDB
            response = self.table.update_item(
                Key={"fund_id": fund_id, "date": date},
                UpdateExpression=update_expression,
                ExpressionAttributeNames=expression_attribute_names,
                ExpressionAttributeValues=expression_attribute_values,
                ReturnValues="ALL_NEW",
            )

            updated_price = HistoricalPriceDynamoDBItem.from_dynamodb_item(
                response["Attributes"]
            )

            metrics.add_metric(
                name="HistoricalPriceUpdated", unit=MetricUnit.Count, value=1
            )
            logger.info(f"Updated historical price: {fund_id} - {date}")

            return updated_price

        except Exception as e:
            logger.exception(f"Failed to update historical price: {fund_id} - {date}")
            metrics.add_metric(
                name="HistoricalPriceUpdateErrors", unit=MetricUnit.Count, value=1
            )
            raise Exception("Failed to update historical price")

    @tracer.capture_method
    def delete(self, fund_id: str, date: str) -> bool:
        """Delete a historical price record."""
        try:
            self.table.delete_item(
                Key={"fund_id": fund_id, "date": date},
                ConditionExpression="attribute_exists(fund_id) AND attribute_exists(#date_attr)",
                ExpressionAttributeNames={"#date_attr": "date"},
            )

            metrics.add_metric(
                name="HistoricalPriceDeleted", unit=MetricUnit.Count, value=1
            )
            logger.info(f"Deleted historical price: {fund_id} - {date}")

            return True

        except ClientError as e:
            if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
                logger.warning(
                    f"Historical price not found for deletion: {fund_id} - {date}"
                )
                return False
            else:
                logger.exception(
                    f"Failed to delete historical price: {fund_id} - {date}"
                )
                metrics.add_metric(
                    name="HistoricalPriceDeleteErrors", unit=MetricUnit.Count, value=1
                )
                raise Exception("Failed to delete historical price")

    @tracer.capture_method
    def batch_create(
        self, price_data_list: List[HistoricalPriceCreate]
    ) -> Dict[str, Any]:
        """Batch create multiple historical price records."""
        try:
            success_count = 0
            error_count = 0
            errors = []

            # Process in batches of 25 (DynamoDB limit)
            batch_size = 25
            for i in range(0, len(price_data_list), batch_size):
                batch = price_data_list[i : i + batch_size]

                with self.table.batch_writer() as batch_writer:
                    for price_data in batch:
                        try:
                            price = HistoricalPrice(**price_data.model_dump())
                            item = HistoricalPriceDynamoDBItem.to_dynamodb_item(price)
                            batch_writer.put_item(Item=item)
                            success_count += 1
                        except Exception as e:
                            error_count += 1
                            errors.append(
                                {
                                    "fund_id": price_data.fund_id,
                                    "date": price_data.date,
                                    "error": str(e),
                                }
                            )

            metrics.add_metric(
                name="HistoricalPriceBatchCreated",
                unit=MetricUnit.Count,
                value=success_count,
            )
            metrics.add_metric(
                name="HistoricalPriceBatchErrors",
                unit=MetricUnit.Count,
                value=error_count,
            )

            logger.info(
                f"Batch created {success_count} historical prices with {error_count} errors"
            )

            return {
                "success_count": success_count,
                "error_count": error_count,
                "errors": errors,
            }

        except Exception as e:
            logger.exception("Failed to batch create historical prices")
            metrics.add_metric(
                name="HistoricalPriceBatchCreateErrors", unit=MetricUnit.Count, value=1
            )
            raise Exception("Failed to batch create historical prices")

    @tracer.capture_method
    def get_price_range_summary(
        self, fund_id: str, start_date: str, end_date: str
    ) -> Dict[str, Any]:
        """Get price range summary statistics for a fund."""
        try:
            prices = self.get_fund_price_history(fund_id, start_date, end_date)

            if not prices:
                return {
                    "fund_id": fund_id,
                    "start_date": start_date,
                    "end_date": end_date,
                    "count": 0,
                    "min_nav": None,
                    "max_nav": None,
                    "avg_nav": None,
                    "latest_nav": None,
                    "total_return": None,
                }

            navs = [price.nav for price in prices]
            min_nav = min(navs)
            max_nav = max(navs)
            avg_nav = sum(navs) / len(navs)

            # Sort by date to get chronological order
            sorted_prices = sorted(prices, key=lambda p: p.date)
            latest_nav = sorted_prices[-1].nav
            earliest_nav = sorted_prices[0].nav
            total_return = (
                ((latest_nav - earliest_nav) / earliest_nav) * 100
                if earliest_nav > 0
                else None
            )

            summary = {
                "fund_id": fund_id,
                "start_date": start_date,
                "end_date": end_date,
                "count": len(prices),
                "min_nav": float(min_nav),
                "max_nav": float(max_nav),
                "avg_nav": float(avg_nav),
                "latest_nav": float(latest_nav),
                "total_return": float(total_return) if total_return else None,
            }

            metrics.add_metric(
                name="PriceRangeSummaryGenerated", unit=MetricUnit.Count, value=1
            )
            logger.info(f"Generated price range summary for fund: {fund_id}")

            return summary

        except Exception as e:
            logger.exception(f"Failed to generate price range summary: {fund_id}")
            metrics.add_metric(
                name="PriceRangeSummaryErrors", unit=MetricUnit.Count, value=1
            )
            raise Exception("Failed to generate price range summary")


def get_historical_price_repository() -> HistoricalPriceRepository:
    """Factory function to get historical price repository instance."""
    return HistoricalPriceRepository()
