"""
Market Data Repository for enhanced DynamoDB schema.
Handles storage and retrieval of comprehensive market data.
"""

import os
import time
from datetime import datetime, timezone, date
from typing import Dict, Any, Optional, List
from decimal import Decimal

import boto3
from botocore.exceptions import ClientError
from aws_lambda_powertools import Logger

from shared.models.market_data import (
    MarketDataInput,
    PriceData,
    ValuationMetrics,
    TechnicalIndicators,
    RiskAnalytics,
    MarketDataSummary,
    BenchmarkData,
    MarketDataSource,
    DataQuality,
)

logger = Logger()


class MarketDataRepository:
    """Repository for market data using enhanced DynamoDB schema."""

    def __init__(self, region: str = "ap-northeast-1"):
        """Initialize the Market Data repository."""
        # Get table name from environment variable or construct it
        environment = os.getenv("ENVIRONMENT", "dev")
        self.table_name = f"fundflow-{environment}-funds-enhanced"
        
        # Initialize DynamoDB resources
        self.dynamodb = boto3.resource('dynamodb', region_name=region)
        self.table = self.dynamodb.Table(self.table_name)
        self.logger = logger

    def store_market_data_input(self, market_data_input: MarketDataInput) -> Dict[str, Any]:
        """
        Store market data input record.
        
        Args:
            market_data_input: Market data input to store
            
        Returns:
            Dictionary with input_id and storage confirmation
        """
        try:
            # Generate unique input ID
            input_id = f"input_{int(time.time())}_{market_data_input.input_by}"
            
            # Create DynamoDB item
            item = {
                'PK': f'FUND#{market_data_input.fund_id}',
                'SK': f'INPUT#{market_data_input.input_timestamp.isoformat()}#{market_data_input.input_by}',
                'data_type': 'INPUT',
                'fund_id': market_data_input.fund_id,
                'input_id': input_id,
                'input_timestamp': market_data_input.input_timestamp.isoformat(),
                'data_timestamp': market_data_input.data_timestamp.isoformat(),
                'input_by': market_data_input.input_by,
                'validated': market_data_input.validated,
                'ttl': int(time.time()) + (365 * 24 * 60 * 60)  # 1 year TTL
            }
            
            # Add optional fields
            if market_data_input.nav:
                item['nav'] = str(market_data_input.nav)
            if market_data_input.market_price:
                item['market_price'] = str(market_data_input.market_price)
            if market_data_input.volume:
                item['volume'] = market_data_input.volume
            if market_data_input.price_to_book:
                item['price_to_book'] = str(market_data_input.price_to_book)
            if market_data_input.price_to_earnings:
                item['price_to_earnings'] = str(market_data_input.price_to_earnings)
            if market_data_input.dividend_yield:
                item['dividend_yield'] = str(market_data_input.dividend_yield)
            if market_data_input.volatility:
                item['volatility'] = str(market_data_input.volatility)
            if market_data_input.beta:
                item['beta'] = str(market_data_input.beta)
            if market_data_input.notes:
                item['notes'] = market_data_input.notes
            if market_data_input.validation_notes:
                item['validation_notes'] = market_data_input.validation_notes
            
            # Store in DynamoDB
            self.table.put_item(Item=item)
            
            self.logger.info(
                f"Stored market data input for fund {market_data_input.fund_id}",
                extra={"input_id": input_id, "fund_id": market_data_input.fund_id}
            )
            
            return {"input_id": input_id, "stored": True}
            
        except ClientError as e:
            self.logger.error(f"Error storing market data input: {e}")
            raise

    def get_market_data_summary(self, fund_id: str) -> Dict[str, Any]:
        """
        Get comprehensive market data summary for a fund.
        
        Args:
            fund_id: Fund identifier
            
        Returns:
            Dictionary with comprehensive market data
        """
        try:
            # Query all market data for the fund
            response = self.table.query(
                KeyConditionExpression='PK = :pk',
                ExpressionAttributeValues={
                    ':pk': f'FUND#{fund_id}'
                }
            )
            
            # Process items by data type
            price_data = None
            valuation_metrics = None
            technical_indicators = None
            risk_analytics = None
            market_data_inputs = []
            
            for item in response.get('Items', []):
                data_type = item.get('data_type')
                
                if data_type == 'PRICE':
                    price_data = self._parse_price_data(item)
                elif data_type == 'VALUATION':
                    valuation_metrics = self._parse_valuation_metrics(item)
                elif data_type == 'TECHNICAL':
                    technical_indicators = self._parse_technical_indicators(item)
                elif data_type == 'RISK':
                    risk_analytics = self._parse_risk_analytics(item)
                elif data_type == 'INPUT':
                    market_data_inputs.append(self._parse_market_data_input(item))
            
            # Build comprehensive summary
            summary = {
                "fund_id": fund_id,
                "last_updated": datetime.now(timezone.utc).isoformat(),
                "price_data": price_data,
                "valuation_metrics": valuation_metrics,
                "technical_indicators": technical_indicators,
                "risk_analytics": risk_analytics,
                "market_data_inputs": market_data_inputs,
                "data_sources": {
                    "price": "fund_company",
                    "valuation": "bloomberg",
                    "technical": "bloomberg",
                    "risk": "internal"
                },
                "overall_quality": "good"
            }
            
            self.logger.info(f"Retrieved market data summary for fund {fund_id}")
            return summary
            
        except ClientError as e:
            self.logger.error(f"Error retrieving market data summary: {e}")
            raise

    def _parse_price_data(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Parse price data from DynamoDB item."""
        return {
            "fund_id": item.get("fund_id"),
            "as_of": item.get("timestamp"),
            "nav": item.get("nav"),
            "market_price": item.get("market_price"),
            "bid": item.get("bid"),
            "ask": item.get("ask"),
            "volume": item.get("volume"),
            "bid_ask_spread": item.get("bid_ask_spread"),
            "market_cap": item.get("market_cap"),
            "price_change_1d": item.get("price_change_1d"),
            "price_change_1d_pct": item.get("price_change_1d_pct")
        }

    def _parse_valuation_metrics(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Parse valuation metrics from DynamoDB item."""
        return {
            "fund_id": item.get("fund_id"),
            "as_of": item.get("as_of_date"),
            "price_to_book": item.get("price_to_book"),
            "price_to_earnings": item.get("price_to_earnings"),
            "price_to_sales": item.get("price_to_sales"),
            "enterprise_value": item.get("enterprise_value"),
            "return_on_equity": item.get("return_on_equity"),
            "return_on_assets": item.get("return_on_assets"),
            "dividend_yield": item.get("dividend_yield"),
            "debt_to_equity": item.get("debt_to_equity")
        }

    def _parse_technical_indicators(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Parse technical indicators from DynamoDB item."""
        return {
            "fund_id": item.get("fund_id"),
            "as_of": item.get("timestamp"),
            "sma_20": item.get("sma_20"),
            "sma_50": item.get("sma_50"),
            "sma_200": item.get("sma_200"),
            "rsi_14": item.get("rsi_14"),
            "macd_line": item.get("macd_line"),
            "macd_signal": item.get("macd_signal"),
            "support_level": item.get("support_level"),
            "resistance_level": item.get("resistance_level"),
            "vwap": item.get("vwap")
        }

    def _parse_risk_analytics(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Parse risk analytics from DynamoDB item."""
        return {
            "fund_id": item.get("fund_id"),
            "as_of": item.get("as_of_date"),
            "var_1d_95": item.get("var_1d_95"),
            "var_1d_99": item.get("var_1d_99"),
            "sharpe_ratio": item.get("sharpe_ratio"),
            "sortino_ratio": item.get("sortino_ratio"),
            "max_drawdown": item.get("max_drawdown"),
            "volatility": item.get("volatility_1y"),
            "beta": item.get("beta_vs_benchmark"),
            "correlation": item.get("correlation_vs_benchmark")
        }

    def _parse_market_data_input(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Parse market data input from DynamoDB item."""
        return {
            "input_id": item.get("input_id"),
            "fund_id": item.get("fund_id"),
            "input_timestamp": item.get("input_timestamp"),
            "data_timestamp": item.get("data_timestamp"),
            "input_by": item.get("input_by"),
            "nav": item.get("nav"),
            "market_price": item.get("market_price"),
            "validated": item.get("validated", False),
            "notes": item.get("notes")
        }

    def get_latest_price_data(self, fund_id: str) -> Optional[Dict[str, Any]]:
        """Get the latest price data for a fund."""
        try:
            response = self.table.query(
                KeyConditionExpression='PK = :pk AND begins_with(SK, :sk_prefix)',
                ExpressionAttributeValues={
                    ':pk': f'FUND#{fund_id}',
                    ':sk_prefix': 'PRICE#'
                },
                ScanIndexForward=False,  # Get latest first
                Limit=1
            )
            
            items = response.get('Items', [])
            if items:
                return self._parse_price_data(items[0])
            return None
            
        except ClientError as e:
            self.logger.error(f"Error retrieving latest price data: {e}")
            raise

    def get_market_data_inputs_by_fund(self, fund_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent market data inputs for a fund."""
        try:
            response = self.table.query(
                KeyConditionExpression='PK = :pk AND begins_with(SK, :sk_prefix)',
                ExpressionAttributeValues={
                    ':pk': f'FUND#{fund_id}',
                    ':sk_prefix': 'INPUT#'
                },
                ScanIndexForward=False,  # Get latest first
                Limit=limit
            )
            
            return [self._parse_market_data_input(item) for item in response.get('Items', [])]
            
        except ClientError as e:
            self.logger.error(f"Error retrieving market data inputs: {e}")
            raise
