"""
Portfolio repository for DynamoDB operations.
"""

import os
import uuid
from typing import Dict, List, Optional, Any
from decimal import Decimal
from datetime import datetime, timezone
from aws_lambda_powertools import Logger

from .base import DynamoDBRepository
from ..models.portfolio import (
    Portfolio,
    PortfolioDynamoDBItem,
    PortfolioType,
    PortfolioStatus,
    PortfolioCreateRequest,
    PortfolioUpdateRequest,
    AddHoldingRequest,
    AddTransactionRequest,
    PortfolioHolding,
    PortfolioTransaction,
    TransactionType,
)

# Initialize logger at module level
logger = Logger()


class PortfolioRepository(DynamoDBRepository):
    """Repository for Portfolio entities in DynamoDB."""

    def __init__(self, region: str = "us-east-1"):
        """Initialize the Portfolio repository."""
        # Get table name from environment variable or construct it
        environment = os.getenv("ENVIRONMENT", "dev")
        table_name = f"fundflow-{environment}-portfolios"
        super().__init__(table_name, region)
        # Set instance logger to use the module logger
        self.logger = logger

    def _get_primary_key(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Get the primary key for a portfolio item."""
        return {"portfolio_id": item["portfolio_id"]}

    def _serialize_item(self, portfolio: Portfolio) -> Dict[str, Any]:
        """Serialize Portfolio object to DynamoDB format."""
        return PortfolioDynamoDBItem.to_dynamodb_item(portfolio)

    def _deserialize_item(self, item: Dict[str, Any]) -> Portfolio:
        """Deserialize DynamoDB item to Portfolio object."""
        return PortfolioDynamoDBItem.from_dynamodb_item(item)

    def get_portfolio(self, portfolio_id: str) -> Optional[Portfolio]:
        """Get a portfolio by its ID."""
        return self.get_by_id({"portfolio_id": portfolio_id})

    def list_portfolios(
        self,
        user_id: str,
        limit: Optional[int] = None,
        last_evaluated_key: Optional[Dict] = None,
        status: Optional[str] = None,
        portfolio_type: Optional[str] = None,
        search: Optional[str] = None,
    ) -> Dict[str, Any]:
        """List portfolios for a user with optional filtering."""
        try:
            # Start with user_id filter
            filter_conditions = ["user_id = :user_id"]
            expression_attribute_values = {":user_id": user_id}
            expression_attribute_names = {}

            # Add additional filters
            if status:
                filter_conditions.append("#status = :status")
                expression_attribute_names["#status"] = "status"
                expression_attribute_values[":status"] = status

            if portfolio_type:
                filter_conditions.append("portfolio_type = :portfolio_type")
                expression_attribute_values[":portfolio_type"] = portfolio_type

            if search:
                filter_conditions.append("contains(#name, :search_term)")
                expression_attribute_names["#name"] = "name"
                expression_attribute_values[":search_term"] = search.lower()

            scan_kwargs = {
                "FilterExpression": " AND ".join(filter_conditions),
                "ExpressionAttributeValues": expression_attribute_values,
            }

            if expression_attribute_names:
                scan_kwargs["ExpressionAttributeNames"] = expression_attribute_names

            if limit:
                scan_kwargs["Limit"] = limit
            if last_evaluated_key:
                scan_kwargs["ExclusiveStartKey"] = last_evaluated_key

            response = self.table.scan(**scan_kwargs)

            portfolios = [self._deserialize_item(item) for item in response.get("Items", [])]

            result = {
                "portfolios": portfolios,
                "count": len(portfolios),
                "scanned_count": response.get("ScannedCount", 0),
            }

            if "LastEvaluatedKey" in response:
                result["last_evaluated_key"] = response["LastEvaluatedKey"]
                result["has_more"] = True
            else:
                result["has_more"] = False

            return result

        except Exception as e:
            self.logger.error(
                f"Error listing portfolios",
                extra={"error": str(e), "user_id": user_id},
            )
            raise

    def create_portfolio(self, portfolio_data: PortfolioCreateRequest, user_id: str) -> Portfolio:
        """Create a new portfolio."""
        try:
            portfolio = Portfolio(
                portfolio_id=self._generate_id(),
                name=portfolio_data.name,
                description=portfolio_data.description,
                portfolio_type=portfolio_data.portfolio_type,
                user_id=user_id,
                base_currency=portfolio_data.base_currency or "USD",
                inception_date=portfolio_data.inception_date or datetime.now(timezone.utc),
                cash_balance=portfolio_data.cash_balance or Decimal("0"),
                risk_level=portfolio_data.risk_level,
                benchmark=portfolio_data.benchmark,
                tags=portfolio_data.tags or [],
                custom_fields=portfolio_data.custom_fields or {},
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
            )

            self.logger.info(f"Creating portfolio: {portfolio.portfolio_id}")

            # Create the portfolio in DynamoDB
            created_portfolio = self.create(portfolio)

            self.logger.info(f"Portfolio created successfully: {portfolio.portfolio_id}")
            return created_portfolio

        except Exception as e:
            self.logger.error(
                f"Error creating portfolio",
                extra={"error": str(e), "user_id": user_id},
            )
            raise

    def update_portfolio(
        self, portfolio_id: str, portfolio_data: PortfolioUpdateRequest, user_id: str
    ) -> Portfolio:
        """Update an existing portfolio."""
        try:
            # Get existing portfolio
            existing_portfolio = self.get_portfolio(portfolio_id)
            if not existing_portfolio:
                raise ValueError(f"Portfolio {portfolio_id} not found")

            # Check ownership
            if existing_portfolio.user_id != user_id:
                raise ValueError(f"Access denied to portfolio {portfolio_id}")

            # Update fields
            update_data = portfolio_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(existing_portfolio, field):
                    setattr(existing_portfolio, field, value)

            # Update timestamp
            existing_portfolio.updated_at = datetime.now(timezone.utc)

            # Save to DynamoDB
            updated_portfolio = self.update(existing_portfolio)

            self.logger.info(f"Portfolio updated successfully: {portfolio_id}")
            return updated_portfolio

        except Exception as e:
            self.logger.error(
                f"Error updating portfolio",
                extra={"error": str(e), "portfolio_id": portfolio_id, "user_id": user_id},
            )
            raise

    def delete_portfolio(self, portfolio_id: str) -> bool:
        """Delete a portfolio."""
        try:
            self.delete_by_id({"portfolio_id": portfolio_id})
            self.logger.info(f"Portfolio deleted successfully: {portfolio_id}")
            return True

        except Exception as e:
            self.logger.error(
                f"Error deleting portfolio",
                extra={"error": str(e), "portfolio_id": portfolio_id},
            )
            raise

    def _generate_id(self) -> str:
        """Generate a unique portfolio ID."""
        return f"portfolio-{str(uuid.uuid4())[:8]}"

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        return datetime.now(timezone.utc).isoformat()

    def add_holding(self, portfolio_id: str, holding_data: AddHoldingRequest) -> Portfolio:
        """Add a holding to a portfolio."""
        try:
            # Get existing portfolio
            portfolio = self.get_portfolio(portfolio_id)
            if not portfolio:
                raise ValueError(f"Portfolio {portfolio_id} not found")

            # Calculate holding values
            market_value = holding_data.shares * holding_data.purchase_price
            cost_basis = market_value + (holding_data.fees or Decimal("0"))

            # Create new holding
            new_holding = PortfolioHolding(
                fund_id=holding_data.fund_id,
                fund_name=f"Fund {holding_data.fund_id}",  # This should be fetched from fund service
                shares=holding_data.shares,
                average_cost=holding_data.purchase_price,
                current_price=holding_data.purchase_price,  # Initially same as purchase price
                market_value=market_value,
                cost_basis=cost_basis,
                unrealized_gain_loss=Decimal("0"),  # Initially zero
                unrealized_gain_loss_pct=Decimal("0"),
                weight=Decimal("0"),  # Will be recalculated
                first_purchase_date=holding_data.purchase_date,
                last_updated=datetime.now(timezone.utc),
            )

            # Add to portfolio holdings
            portfolio.holdings.append(new_holding)

            # Update portfolio totals
            portfolio.total_value += market_value
            portfolio.total_cost_basis += cost_basis
            portfolio.updated_at = datetime.now(timezone.utc)

            # Recalculate weights
            self._recalculate_weights(portfolio)

            # Save to DynamoDB
            updated_portfolio = self.update(portfolio)

            self.logger.info(f"Holding added to portfolio: {portfolio_id}")
            return updated_portfolio

        except Exception as e:
            self.logger.error(
                f"Error adding holding to portfolio",
                extra={"error": str(e), "portfolio_id": portfolio_id},
            )
            raise

    def add_transaction(self, portfolio_id: str, transaction_data: AddTransactionRequest) -> Portfolio:
        """Add a transaction to a portfolio."""
        try:
            # Get existing portfolio
            portfolio = self.get_portfolio(portfolio_id)
            if not portfolio:
                raise ValueError(f"Portfolio {portfolio_id} not found")

            # Create new transaction
            new_transaction = PortfolioTransaction(
                transaction_id=f"tx-{str(uuid.uuid4())[:8]}",
                fund_id=transaction_data.fund_id,
                fund_name=f"Fund {transaction_data.fund_id}",  # This should be fetched from fund service
                transaction_type=transaction_data.transaction_type,
                transaction_date=transaction_data.transaction_date,
                shares=transaction_data.shares,
                price=transaction_data.price,
                amount=transaction_data.amount,
                fees=transaction_data.fees or Decimal("0"),
                net_amount=transaction_data.amount - (transaction_data.fees or Decimal("0")),
                description=transaction_data.description,
                reference_number=transaction_data.reference_number,
                created_at=datetime.now(timezone.utc),
            )

            # Add to portfolio transactions (keep only recent 10)
            portfolio.recent_transactions.append(new_transaction)
            if len(portfolio.recent_transactions) > 10:
                portfolio.recent_transactions = portfolio.recent_transactions[-10:]

            # Update portfolio based on transaction type
            if transaction_data.transaction_type in [TransactionType.BUY, TransactionType.SELL]:
                self._update_portfolio_for_trade(portfolio, new_transaction)
            elif transaction_data.transaction_type in [TransactionType.DIVIDEND, TransactionType.INTEREST]:
                portfolio.cash_balance += new_transaction.net_amount

            portfolio.updated_at = datetime.now(timezone.utc)

            # Save to DynamoDB
            updated_portfolio = self.update(portfolio)

            self.logger.info(f"Transaction added to portfolio: {portfolio_id}")
            return updated_portfolio

        except Exception as e:
            self.logger.error(
                f"Error adding transaction to portfolio",
                extra={"error": str(e), "portfolio_id": portfolio_id},
            )
            raise

    def _recalculate_weights(self, portfolio: Portfolio) -> None:
        """Recalculate holding weights in the portfolio."""
        if not portfolio.holdings or portfolio.total_value <= 0:
            return

        for holding in portfolio.holdings:
            holding.weight = (holding.market_value / portfolio.total_value) * 100

    def _update_portfolio_for_trade(self, portfolio: Portfolio, transaction: PortfolioTransaction) -> None:
        """Update portfolio holdings based on buy/sell transaction."""
        # Find existing holding for this fund
        existing_holding = None
        for holding in portfolio.holdings:
            if holding.fund_id == transaction.fund_id:
                existing_holding = holding
                break

        if transaction.transaction_type == TransactionType.BUY:
            if existing_holding:
                # Update existing holding
                total_shares = existing_holding.shares + transaction.shares
                total_cost = (existing_holding.shares * existing_holding.average_cost) + transaction.net_amount
                existing_holding.average_cost = total_cost / total_shares
                existing_holding.shares = total_shares
                existing_holding.cost_basis = total_cost
                existing_holding.market_value = total_shares * existing_holding.current_price
                existing_holding.last_updated = datetime.now(timezone.utc)
            else:
                # Create new holding
                new_holding = PortfolioHolding(
                    fund_id=transaction.fund_id,
                    fund_name=transaction.fund_name,
                    shares=transaction.shares,
                    average_cost=transaction.price,
                    current_price=transaction.price,
                    market_value=transaction.shares * transaction.price,
                    cost_basis=transaction.net_amount,
                    unrealized_gain_loss=Decimal("0"),
                    unrealized_gain_loss_pct=Decimal("0"),
                    weight=Decimal("0"),
                    first_purchase_date=transaction.transaction_date,
                    last_updated=datetime.now(timezone.utc),
                )
                portfolio.holdings.append(new_holding)

            # Reduce cash balance
            portfolio.cash_balance -= transaction.net_amount

        elif transaction.transaction_type == TransactionType.SELL:
            if existing_holding and existing_holding.shares >= transaction.shares:
                # Update existing holding
                existing_holding.shares -= transaction.shares
                existing_holding.cost_basis = existing_holding.shares * existing_holding.average_cost
                existing_holding.market_value = existing_holding.shares * existing_holding.current_price
                existing_holding.last_updated = datetime.now(timezone.utc)

                # Remove holding if shares become zero
                if existing_holding.shares == 0:
                    portfolio.holdings.remove(existing_holding)

                # Increase cash balance
                portfolio.cash_balance += transaction.net_amount
