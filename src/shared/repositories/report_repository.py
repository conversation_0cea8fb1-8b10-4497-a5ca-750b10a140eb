"""
Report repository for DynamoDB operations.
Handles CRUD operations and complex queries for report data.
"""

import os
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
from boto3.dynamodb.conditions import Key, Attr
from botocore.exceptions import ClientError
from aws_lambda_powertools import Logger

from shared.repositories.base import DynamoDBRepository
from shared.models.report import Report, ReportCreate, ReportUpdate

logger = Logger()


class ReportRepository(DynamoDBRepository):
    """Repository for Report DynamoDB operations."""

    def __init__(self):
        """Initialize the report repository."""
        table_name = os.getenv("REPORT_TABLE", "fundflow-dev-reports")
        super().__init__(table_name)

    def _get_primary_key(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Get the primary key for a report item."""
        return {"report_id": item["report_id"]}

    def _serialize_item(self, report: Report) -> Dict[str, Any]:
        """Serialize a Report object to DynamoDB format."""
        # Convert Report object to dictionary
        item = report.dict()

        # Convert datetime objects to ISO strings for DynamoDB
        for field in ["created_at", "updated_at", "completed_at"]:
            if item.get(field):
                if isinstance(item[field], datetime):
                    item[field] = item[field].isoformat()

        return item

    def _deserialize_item(self, item: Dict[str, Any]) -> Report:
        """Deserialize a DynamoDB item to Report object."""
        # Convert datetime strings back to datetime objects
        for field in ["created_at", "updated_at", "completed_at"]:
            if item.get(field) and isinstance(item[field], str):
                try:
                    item[field] = datetime.fromisoformat(item[field])
                except ValueError:
                    # Handle legacy formats or keep as string
                    pass

        return Report(**item)

    def create_report(self, user_id: str, report_data: ReportCreate) -> Report:
        """Create a new report."""
        try:
            # Generate report ID
            report_id = str(uuid.uuid4())

            # Create Report object
            report = Report(
                report_id=report_id,
                user_id=user_id,
                name=report_data.name,
                description=report_data.description,
                report_type=report_data.report_type,
                format=report_data.format,
                parameters=report_data.parameters or {},
            )

            return self.create(report)

        except Exception as e:
            logger.error(f"Error creating report: {str(e)}")
            raise

    def get_report(self, report_id: str) -> Optional[Report]:
        """Get a report by ID."""
        return self.get_by_id({"report_id": report_id})

    def update_report(
        self, report_id: str, report_data: ReportUpdate
    ) -> Optional[Report]:
        """Update an existing report."""
        try:
            # Get existing report
            existing_report = self.get_report(report_id)
            if not existing_report:
                return None

            # Update fields
            update_data = report_data.dict(exclude_unset=True)

            for field, value in update_data.items():
                if hasattr(existing_report, field):
                    setattr(existing_report, field, value)

            # Update timestamp
            existing_report.updated_at = datetime.now(timezone.utc)

            return self.update(existing_report)

        except Exception as e:
            logger.error(f"Error updating report: {str(e)}")
            raise

    def delete_report(self, report_id: str) -> bool:
        """Delete a report by ID."""
        return self.delete({"report_id": report_id})

    def list_user_reports(
        self, user_id: str, limit: int = 50, last_key: Optional[str] = None
    ) -> Dict[str, Any]:
        """List reports for a specific user using GSI."""
        try:
            query_kwargs = {
                "IndexName": "user_reports_index",
                "KeyConditionExpression": Key("user_id").eq(user_id),
                "Limit": limit,
                "ScanIndexForward": False,  # Sort by created_at descending
            }

            if last_key:
                # For GSI queries, we need both user_id and created_at for the key
                query_kwargs["ExclusiveStartKey"] = {
                    "user_id": user_id,
                    "created_at": last_key,
                    "report_id": last_key,  # Add report_id as it's the table's primary key
                }

            response = self.table.query(**query_kwargs)

            reports = [
                self._deserialize_item(item) for item in response.get("Items", [])
            ]

            result = {
                "reports": reports,
                "count": len(reports),
            }

            if "LastEvaluatedKey" in response:
                result["last_key"] = response["LastEvaluatedKey"]["created_at"]
                result["has_more"] = True
            else:
                result["has_more"] = False

            logger.info(f"Listed {len(reports)} reports for user: {user_id}")
            return result

        except ClientError as e:
            logger.error(f"Error listing user reports: {str(e)}")
            raise

    def list_reports_by_type(
        self, report_type: str, limit: int = 50, last_key: Optional[str] = None
    ) -> Dict[str, Any]:
        """List reports by type using GSI."""
        try:
            query_kwargs = {
                "IndexName": "report_type_index",
                "KeyConditionExpression": Key("report_type").eq(report_type),
                "Limit": limit,
                "ScanIndexForward": False,  # Sort by created_at descending
            }

            if last_key:
                # For GSI queries, we need both report_type and created_at for the key
                query_kwargs["ExclusiveStartKey"] = {
                    "report_type": report_type,
                    "created_at": last_key,
                    "report_id": last_key,  # Add report_id as it's the table's primary key
                }

            response = self.table.query(**query_kwargs)

            reports = [
                self._deserialize_item(item) for item in response.get("Items", [])
            ]

            result = {
                "reports": reports,
                "count": len(reports),
            }

            if "LastEvaluatedKey" in response:
                result["last_key"] = response["LastEvaluatedKey"]["created_at"]
                result["has_more"] = True
            else:
                result["has_more"] = False

            logger.info(f"Listed {len(reports)} reports for type: {report_type}")
            return result

        except ClientError as e:
            logger.error(f"Error listing reports by type: {str(e)}")
            raise

    def list_reports(
        self,
        status: Optional[str] = None,
        report_type: Optional[str] = None,
        user_id: Optional[str] = None,
        limit: int = 50,
        last_key: Optional[str] = None,
    ) -> Dict[str, Any]:
        """List reports with optional filtering."""
        try:
            # If user_id is specified, use the GSI for better performance
            if user_id:
                return self.list_user_reports(user_id, limit, last_key)

            # If report_type is specified, use the GSI for better performance
            if report_type:
                return self.list_reports_by_type(report_type, limit, last_key)

            # Otherwise, use scan with filters
            scan_kwargs = {"Limit": limit}

            if last_key:
                scan_kwargs["ExclusiveStartKey"] = {"report_id": last_key}

            # Build filter expression
            filter_conditions = []
            expression_values = {}

            if status:
                filter_conditions.append("#status = :status")
                expression_values[":status"] = status
                scan_kwargs["ExpressionAttributeNames"] = {"#status": "status"}

            if filter_conditions:
                scan_kwargs["FilterExpression"] = " AND ".join(filter_conditions)
                scan_kwargs["ExpressionAttributeValues"] = expression_values

            # Execute scan
            response = self.table.scan(**scan_kwargs)

            reports = [
                self._deserialize_item(item) for item in response.get("Items", [])
            ]

            result = {
                "reports": reports,
                "count": len(reports),
                "scanned_count": response.get("ScannedCount", 0),
            }

            if "LastEvaluatedKey" in response:
                result["last_key"] = response["LastEvaluatedKey"]["report_id"]
                result["has_more"] = True
            else:
                result["has_more"] = False

            logger.info(f"Listed {len(reports)} reports")
            return result

        except ClientError as e:
            logger.error(f"Error listing reports: {str(e)}")
            raise

    def update_report_status(
        self, report_id: str, status: str, file_path: Optional[str] = None
    ) -> bool:
        """Update report status and optionally file path."""
        try:
            update_expression = "SET #status = :status, updated_at = :updated_at"
            expression_values = {
                ":status": status,
                ":updated_at": datetime.now(timezone.utc).isoformat(),
            }
            expression_attribute_names = {"#status": "status"}

            if status == "completed":
                update_expression += ", completed_at = :completed_at"
                expression_values[":completed_at"] = datetime.now(timezone.utc).isoformat()

            if file_path:
                update_expression += ", file_path = :file_path"
                expression_values[":file_path"] = file_path

            self.table.update_item(
                Key={"report_id": report_id},
                UpdateExpression=update_expression,
                ExpressionAttributeNames=expression_attribute_names,
                ExpressionAttributeValues=expression_values,
                ConditionExpression="attribute_exists(report_id)",
            )

            logger.info(f"Updated report status: {report_id} -> {status}")
            return True

        except ClientError as e:
            if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
                logger.warning(f"Report not found for status update: {report_id}")
                return False
            logger.error(f"Error updating report status: {str(e)}")
            raise

    def get_pending_reports(self, limit: int = 50) -> List[Report]:
        """Get reports with pending status."""
        try:
            response = self.table.scan(
                FilterExpression=Attr("status").eq("pending"), Limit=limit
            )

            reports = [
                self._deserialize_item(item) for item in response.get("Items", [])
            ]

            logger.info(f"Found {len(reports)} pending reports")
            return reports

        except ClientError as e:
            logger.error(f"Error getting pending reports: {str(e)}")
            raise
