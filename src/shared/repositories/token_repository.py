"""
Token blacklist repository for DynamoDB operations.
"""

import os
import hashlib
from typing import Dict, Any, Optional
from datetime import datetime, timezone, timedelta

from aws_lambda_powertools import Logger

from .base import DynamoDBRepository
from ..models.token import (
    TokenBlacklist,
    TokenBlacklistDynamoDBItem,
    TokenBlacklistCreate,
)

logger = Logger(service="token_repository")


class TokenRepository(DynamoDBRepository):
    """Repository for Token Blacklist entities in DynamoDB."""

    def __init__(self, region: str = "us-east-1"):
        """Initialize the Token repository."""
        # Get table name from environment variable or construct it
        environment = os.getenv("ENVIRONMENT", "dev")
        table_name = f"fundflow-{environment}-token-blacklist"
        super().__init__(table_name, region)

    def _get_primary_key(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Get the primary key for a token blacklist item."""
        return {"token_jti": item["token_jti"]}

    def _serialize_item(self, token_blacklist: TokenBlacklist) -> Dict[str, Any]:
        """Serialize TokenBlacklist object to DynamoDB format."""
        return TokenBlacklistDynamoDBItem.to_dynamodb_item(token_blacklist)

    def _deserialize_item(self, item: Dict[str, Any]) -> TokenBlacklist:
        """Deserialize DynamoDB item to TokenBlacklist object."""
        return TokenBlacklistDynamoDBItem.from_dynamodb_item(item)

    def blacklist_token(
        self,
        token_jti: str,
        token_hash: str,
        user_id: str,
        expires_at: datetime,
        token_type: str = "access",
        reason: Optional[str] = None,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
    ) -> TokenBlacklist:
        """
        Add a token to the blacklist.

        Args:
            token_jti: JWT ID from token claims
            token_hash: SHA256 hash of the token
            user_id: User ID who owns the token
            expires_at: When the token expires
            token_type: Type of token (access/refresh)
            reason: Reason for blacklisting
            user_agent: User agent when blacklisted
            ip_address: IP address when blacklisted

        Returns:
            Created TokenBlacklist object
        """
        try:
            token_blacklist = TokenBlacklist(
                token_jti=token_jti,
                token_hash=token_hash,
                user_id=user_id,
                token_type=token_type,
                expires_at=expires_at,
                blacklisted_at=datetime.now(timezone.utc),
                reason=reason,
                user_agent=user_agent,
                ip_address=ip_address,
            )

            created_entry = self.create(token_blacklist)

            logger.info(
                f"Token blacklisted successfully",
                extra={
                    "token_jti": token_jti,
                    "user_id": user_id,
                    "token_type": token_type,
                    "reason": reason,
                },
            )

            return created_entry

        except Exception as e:
            logger.error(
                f"Error blacklisting token",
                extra={
                    "error": str(e),
                    "token_jti": token_jti,
                    "user_id": user_id,
                },
            )
            raise

    def is_token_blacklisted(self, token_jti: str) -> bool:
        """
        Check if a token is blacklisted.

        Args:
            token_jti: JWT ID to check

        Returns:
            True if token is blacklisted, False otherwise
        """
        try:
            blacklist_entry = self.get_by_id({"token_jti": token_jti})
            is_blacklisted = blacklist_entry is not None

            if is_blacklisted:
                logger.info(
                    f"Token found in blacklist",
                    extra={
                        "token_jti": token_jti,
                        "blacklisted_at": blacklist_entry.blacklisted_at.isoformat(),
                        "reason": blacklist_entry.reason,
                    },
                )

            return is_blacklisted

        except Exception as e:
            logger.error(
                f"Error checking token blacklist",
                extra={"error": str(e), "token_jti": token_jti},
            )
            # In case of error, assume token is not blacklisted to avoid false positives
            return False

    def get_blacklisted_token(self, token_jti: str) -> Optional[TokenBlacklist]:
        """
        Get blacklisted token entry by JWT ID.

        Args:
            token_jti: JWT ID to retrieve

        Returns:
            TokenBlacklist object if found, None otherwise
        """
        return self.get_by_id({"token_jti": token_jti})

    def blacklist_user_tokens(
        self,
        user_id: str,
        token_type: Optional[str] = None,
        reason: str = "User logout",
    ) -> int:
        """
        Blacklist all tokens for a specific user.

        Args:
            user_id: User ID whose tokens should be blacklisted
            token_type: Specific token type to blacklist (optional)
            reason: Reason for blacklisting

        Returns:
            Number of tokens blacklisted
        """
        try:
            # Note: This would require a GSI on user_id for efficient querying
            # For now, we'll implement a scan operation
            scan_kwargs = {
                "FilterExpression": "user_id = :user_id",
                "ExpressionAttributeValues": {":user_id": user_id},
            }

            if token_type:
                scan_kwargs["FilterExpression"] += " AND token_type = :token_type"
                scan_kwargs["ExpressionAttributeValues"][":token_type"] = token_type

            response = self.table.scan(**scan_kwargs)
            tokens_found = response.get("Items", [])

            # Update reason for existing blacklisted tokens
            blacklisted_count = 0
            for token_item in tokens_found:
                try:
                    token_blacklist = self._deserialize_item(token_item)
                    token_blacklist.reason = reason
                    token_blacklist.blacklisted_at = datetime.now(timezone.utc)
                    self.update(token_blacklist)
                    blacklisted_count += 1
                except Exception as e:
                    logger.warning(
                        f"Error updating blacklisted token",
                        extra={
                            "error": str(e),
                            "token_jti": token_item.get("token_jti"),
                        },
                    )

            logger.info(
                f"Blacklisted {blacklisted_count} tokens for user",
                extra={"user_id": user_id, "token_type": token_type, "reason": reason},
            )

            return blacklisted_count

        except Exception as e:
            logger.error(
                f"Error blacklisting user tokens",
                extra={"error": str(e), "user_id": user_id},
            )
            raise

    def cleanup_expired_tokens(self) -> int:
        """
        Clean up expired tokens from the blacklist.
        Note: This is optional since DynamoDB TTL should handle this automatically.

        Returns:
            Number of tokens cleaned up
        """
        try:
            current_time = datetime.now(timezone.utc)

            # Scan for expired tokens
            response = self.table.scan(
                FilterExpression="expires_at < :current_time",
                ExpressionAttributeValues={":current_time": current_time.isoformat()},
            )

            expired_tokens = response.get("Items", [])
            cleaned_count = 0

            for token_item in expired_tokens:
                try:
                    token_jti = token_item.get("token_jti")
                    if token_jti:
                        self.delete({"token_jti": token_jti})
                        cleaned_count += 1
                except Exception as e:
                    logger.warning(
                        f"Error deleting expired token",
                        extra={
                            "error": str(e),
                            "token_jti": token_item.get("token_jti"),
                        },
                    )

            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} expired tokens")

            return cleaned_count

        except Exception as e:
            logger.error(f"Error cleaning up expired tokens", extra={"error": str(e)})
            return 0

    @staticmethod
    def hash_token(token: str) -> str:
        """
        Create SHA256 hash of a token for storage.

        Args:
            token: JWT token string

        Returns:
            SHA256 hash of the token
        """
        return hashlib.sha256(token.encode()).hexdigest()

    def get_user_blacklisted_tokens(
        self, user_id: str, limit: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get all blacklisted tokens for a user.

        Args:
            user_id: User ID to query
            limit: Maximum number of tokens to return

        Returns:
            Dict containing tokens and metadata
        """
        try:
            scan_kwargs = {
                "FilterExpression": "user_id = :user_id",
                "ExpressionAttributeValues": {":user_id": user_id},
            }

            if limit:
                scan_kwargs["Limit"] = limit

            response = self.table.scan(**scan_kwargs)

            tokens = [
                self._deserialize_item(item) for item in response.get("Items", [])
            ]

            return {
                "tokens": tokens,
                "count": len(tokens),
                "scanned_count": response.get("ScannedCount", 0),
            }

        except Exception as e:
            logger.error(
                f"Error retrieving user blacklisted tokens",
                extra={"error": str(e), "user_id": user_id},
            )
            raise
