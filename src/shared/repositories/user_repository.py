"""
User repository for DynamoDB operations.
Handles CRUD operations and complex queries for user data.
"""

import os
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
from boto3.dynamodb.conditions import Key, Attr
from botocore.exceptions import ClientError
from aws_lambda_powertools import Logger

from shared.repositories.base import DynamoDBRepository
from shared.models.user import User, UserCreate, UserUpdate, UserDynamoDBItem

logger = Logger()


class UserRepository(DynamoDBRepository):
    """Repository for User DynamoDB operations."""

    def __init__(self):
        """Initialize the user repository."""
        table_name = os.getenv("USER_TABLE", "fundflow-dev-users")
        super().__init__(table_name)

    def _get_primary_key(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Get the primary key for a user item."""
        return {"user_id": item["user_id"]}

    def _serialize_item(self, user: User) -> Dict[str, Any]:
        """Serialize a User object to DynamoDB format."""
        return UserDynamoDBItem.to_dynamodb_item(user)

    def _deserialize_item(self, item: Dict[str, Any]) -> User:
        """Deserialize a DynamoDB item to User object."""
        return UserDynamoDBItem.from_dynamodb_item(item)

    def create_user(self, user_data: UserCreate) -> User:
        """Create a new user."""
        try:
            # Generate user ID (in production, consider using UUID)
            import uuid

            user_id = str(uuid.uuid4())

            # Create User object
            user = User(
                user_id=user_id,
                email=user_data.email,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
                phone_number=user_data.phone_number,
                role=user_data.role,
                department=user_data.department,
                job_title=user_data.job_title,
                employee_id=user_data.employee_id,
                manager_id=user_data.manager_id,
                access_groups=user_data.access_groups or [],
                notes=user_data.notes,
                tags=user_data.tags or [],
                custom_fields=user_data.custom_fields or {},
            )

            return self.create(user)

        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            raise

    def get_user(self, user_id: str) -> Optional[User]:
        """Get a user by ID."""
        return self.get_by_id({"user_id": user_id})

    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get a user by email using GSI."""
        try:
            response = self.table.query(
                IndexName="email_index",
                KeyConditionExpression=Key("email").eq(email),
                Limit=1,
            )

            items = response.get("Items", [])
            if items:
                logger.info(f"Retrieved user by email: {email}")
                return self._deserialize_item(items[0])

            logger.info(f"User not found by email: {email}")
            return None

        except ClientError as e:
            logger.error(f"Error retrieving user by email: {str(e)}")
            raise

    def update_user(self, user_id: str, user_data: UserUpdate) -> Optional[User]:
        """Update an existing user."""
        try:
            # Get existing user
            existing_user = self.get_user(user_id)
            if not existing_user:
                return None

            # Update fields
            update_data = user_data.dict(exclude_unset=True)

            for field, value in update_data.items():
                if hasattr(existing_user, field):
                    setattr(existing_user, field, value)

            # Update timestamp
            existing_user.updated_at = datetime.now(timezone.utc)

            return self.update(existing_user)

        except Exception as e:
            logger.error(f"Error updating user: {str(e)}")
            raise

    def delete_user(self, user_id: str) -> bool:
        """Delete a user by ID."""
        return self.delete({"user_id": user_id})

    def list_users(
        self,
        role: Optional[str] = None,
        status: Optional[str] = None,
        department: Optional[str] = None,
        limit: int = 50,
        last_key: Optional[str] = None,
    ) -> Dict[str, Any]:
        """List users with optional filtering."""
        try:
            # Build query parameters
            scan_kwargs = {"Limit": limit}

            if last_key:
                scan_kwargs["ExclusiveStartKey"] = {"user_id": last_key}

            # Build filter expression
            filter_conditions = []
            expression_values = {}

            if role:
                filter_conditions.append("role = :role")
                expression_values[":role"] = role

            if status:
                filter_conditions.append("#status = :status")
                expression_values[":status"] = status
                scan_kwargs["ExpressionAttributeNames"] = {"#status": "status"}

            if department:
                filter_conditions.append("department = :department")
                expression_values[":department"] = department

            if filter_conditions:
                scan_kwargs["FilterExpression"] = " AND ".join(filter_conditions)
                scan_kwargs["ExpressionAttributeValues"] = expression_values

            # Execute scan
            response = self.table.scan(**scan_kwargs)

            users = [self._deserialize_item(item) for item in response.get("Items", [])]

            result = {
                "users": users,
                "count": len(users),
                "scanned_count": response.get("ScannedCount", 0),
            }

            if "LastEvaluatedKey" in response:
                result["last_key"] = response["LastEvaluatedKey"]["user_id"]
                result["has_more"] = True
            else:
                result["has_more"] = False

            logger.info(f"Listed {len(users)} users")
            return result

        except ClientError as e:
            logger.error(f"Error listing users: {str(e)}")
            raise

    def list_users_by_role(
        self, role: str, limit: int = 50, last_key: Optional[str] = None
    ) -> Dict[str, Any]:
        """List users by role using GSI."""
        try:
            query_kwargs = {
                "IndexName": "role_index",
                "KeyConditionExpression": Key("role").eq(role),
                "Limit": limit,
                "ScanIndexForward": False,  # Sort by created_at descending
            }

            if last_key:
                # For GSI queries, we need both role and created_at for the key
                query_kwargs["ExclusiveStartKey"] = {
                    "role": role,
                    "created_at": last_key,
                    "user_id": last_key,  # Add user_id as it's the table's primary key
                }

            response = self.table.query(**query_kwargs)

            users = [self._deserialize_item(item) for item in response.get("Items", [])]

            result = {
                "users": users,
                "count": len(users),
            }

            if "LastEvaluatedKey" in response:
                result["last_key"] = response["LastEvaluatedKey"]["created_at"]
                result["has_more"] = True
            else:
                result["has_more"] = False

            logger.info(f"Listed {len(users)} users for role: {role}")
            return result

        except ClientError as e:
            logger.error(f"Error listing users by role: {str(e)}")
            raise

    def search_users(self, search_term: str, limit: int = 50) -> List[User]:
        """Search users by name or email."""
        try:
            # Note: This is a basic implementation using scan with filter
            # For production, consider using Amazon OpenSearch for better search capabilities

            filter_expression = (
                "contains(#first_name, :search) OR "
                "contains(#last_name, :search) OR "
                "contains(#email, :search) OR "
                "contains(#full_name, :search)"
            )

            response = self.table.scan(
                FilterExpression=filter_expression,
                ExpressionAttributeNames={
                    "#first_name": "first_name",
                    "#last_name": "last_name",
                    "#email": "email",
                    "#full_name": "full_name",
                },
                ExpressionAttributeValues={":search": search_term.lower()},
                Limit=limit,
            )

            users = [self._deserialize_item(item) for item in response.get("Items", [])]

            logger.info(f"Search returned {len(users)} users for term: {search_term}")
            return users

        except ClientError as e:
            logger.error(f"Error searching users: {str(e)}")
            raise
