"""
JWT utilities for FundFlow authentication.
Handles JWT token generation, validation, and management with AWS Cognito.
"""

import os
import time
import uuid
import hashlib
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, Union

import boto3
import jwt
from aws_lambda_powertools import Logger
from botocore.exceptions import ClientError
from jwt.exceptions import ExpiredSignatureError, InvalidTokenError

from ..database import get_token_repository
from ..models.token import TokenValidationResult

logger = Logger()


class JWTManager:
    """Manages JWT token generation and validation for FundFlow authentication."""

    def __init__(self):
        self.cognito_client = boto3.client("cognito-idp")
        self.user_pool_id = os.environ.get("USER_POOL_ID")
        self.user_pool_client_id = os.environ.get("USER_POOL_CLIENT_ID")
        self.region = os.environ.get("AWS_REGION", "us-east-1")

        # Token configuration
        self.access_token_expire_minutes = int(
            os.environ.get("ACCESS_TOKEN_EXPIRE_MINUTES", "60")  # 1 hour
        )
        self.refresh_token_expire_days = int(
            os.environ.get("REFRESH_TOKEN_EXPIRE_DAYS", "30")  # 30 days
        )

        # JWT secret for local token generation (for non-Cognito tokens if needed)
        self.jwt_secret = os.environ.get(
            "JWT_SECRET_KEY", f"fundflow-{self.user_pool_id}"
        )
        self.jwt_algorithm = "HS256"

    def authenticate_user(self, email: str, password: str) -> Dict[str, Any]:
        """
        Authenticate user with Cognito and return tokens.

        Args:
            email: User email address
            password: User password

        Returns:
            Dict containing access_token, refresh_token, and user info

        Raises:
            ValueError: If authentication fails
            ClientError: If Cognito operation fails
        """
        try:
            # Initiate authentication with Cognito
            response = self.cognito_client.initiate_auth(
                ClientId=self.user_pool_client_id,
                AuthFlow="USER_PASSWORD_AUTH",
                AuthParameters={
                    "USERNAME": email,
                    "PASSWORD": password,
                },
            )

            # Check if challenge is required (MFA, new password, etc.)
            if "ChallengeName" in response:
                logger.warning(
                    f"Authentication challenge required: {response['ChallengeName']}"
                )
                raise ValueError(
                    f"Authentication challenge required: {response['ChallengeName']}"
                )

            # Extract tokens from response
            auth_result = response["AuthenticationResult"]
            access_token = auth_result["AccessToken"]
            refresh_token = auth_result.get("RefreshToken")
            id_token = auth_result.get("IdToken")

            # Get user information using the access token
            user_info = self.get_user_info_from_token(access_token)

            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "id_token": id_token,
                "token_type": "Bearer",
                "expires_in": auth_result.get("ExpiresIn", 3600),
                "user_info": user_info,
            }

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            error_message = e.response["Error"]["Message"]

            logger.warning(
                f"Cognito authentication error: {error_code} - {error_message}"
            )

            if error_code == "NotAuthorizedException":
                raise ValueError("Invalid email or password")
            elif error_code == "UserNotConfirmedException":
                raise ValueError(
                    "Account not verified. Please check your email for verification instructions."
                )
            elif error_code == "UserNotFoundException":
                raise ValueError(
                    "Invalid email or password"
                )  # Don't reveal user existence
            elif error_code == "TooManyRequestsException":
                raise ValueError("Too many login attempts. Please try again later.")
            else:
                logger.error(f"Unexpected Cognito error: {error_code}")
                raise ValueError("Authentication failed due to server error")

    def register_user(
        self,
        email: str,
        password: str,
        first_name: str,
        last_name: str,
        phone_number: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Register a new user with Cognito.

        Args:
            email: User email address
            password: User password
            first_name: User's first name
            last_name: User's last name
            phone_number: Optional phone number

        Returns:
            Dict containing registration result

        Raises:
            ValueError: If registration fails
            ClientError: If Cognito operation fails
        """
        try:
            # Prepare user attributes
            user_attributes = [
                {"Name": "email", "Value": email},
                {"Name": "given_name", "Value": first_name},
                {"Name": "family_name", "Value": last_name},
            ]

            if phone_number:
                user_attributes.append({"Name": "phone_number", "Value": phone_number})

            # Register user with Cognito
            response = self.cognito_client.sign_up(
                ClientId=self.user_pool_client_id,
                Username=email,
                Password=password,
                UserAttributes=user_attributes,
            )

            user_sub = response["UserSub"]
            confirmation_required = not response.get("UserConfirmed", False)

            logger.info(f"User registered successfully: {email} (ID: {user_sub})")

            return {
                "user_sub": user_sub,
                "email": email,
                "confirmation_required": confirmation_required,
                "message": (
                    "Registration successful. Please check your email for verification instructions."
                    if confirmation_required
                    else "Registration successful."
                ),
            }

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            error_message = e.response["Error"]["Message"]

            logger.warning(
                f"Cognito registration error: {error_code} - {error_message}"
            )

            if error_code == "UsernameExistsException":
                raise ValueError("An account with this email address already exists")
            elif error_code == "InvalidPasswordException":
                raise ValueError("Password does not meet security requirements")
            elif error_code == "InvalidParameterException":
                raise ValueError("Invalid registration parameters")
            elif error_code == "TooManyRequestsException":
                raise ValueError(
                    "Too many registration attempts. Please try again later."
                )
            else:
                logger.error(f"Unexpected Cognito error: {error_code}")
                raise ValueError("Registration failed due to server error")

    def confirm_registration(
        self, email: str, confirmation_code: str
    ) -> Dict[str, Any]:
        """
        Confirm user registration with verification code.

        Args:
            email: User email address
            confirmation_code: Verification code from email

        Returns:
            Dict containing confirmation result

        Raises:
            ValueError: If confirmation fails
            ClientError: If Cognito operation fails
        """
        try:
            # Confirm sign up with Cognito
            response = self.cognito_client.confirm_sign_up(
                ClientId=self.user_pool_client_id,
                Username=email,
                ConfirmationCode=confirmation_code,
            )

            logger.info(f"User confirmed successfully: {email}")

            return {
                "email": email,
                "confirmed": True,
                "message": "Account successfully verified. You can now log in.",
            }

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            error_message = e.response["Error"]["Message"]

            logger.warning(
                f"Cognito confirmation error: {error_code} - {error_message}"
            )

            if error_code == "CodeMismatchException":
                raise ValueError("Invalid verification code")
            elif error_code == "ExpiredCodeException":
                raise ValueError(
                    "Verification code has expired. Please request a new one."
                )
            elif error_code == "UserNotFoundException":
                raise ValueError("User not found")
            elif error_code == "NotAuthorizedException":
                raise ValueError("User is already confirmed")
            else:
                logger.error(f"Unexpected Cognito error: {error_code}")
                raise ValueError("Confirmation failed due to server error")

    def refresh_access_token(self, refresh_token: str) -> Dict[str, Any]:
        """
        Refresh access token using refresh token.

        Args:
            refresh_token: Valid refresh token

        Returns:
            Dict containing new access token

        Raises:
            ValueError: If refresh fails
            ClientError: If Cognito operation fails
        """
        try:
            # Refresh tokens with Cognito
            response = self.cognito_client.initiate_auth(
                ClientId=self.user_pool_client_id,
                AuthFlow="REFRESH_TOKEN_AUTH",
                AuthParameters={
                    "REFRESH_TOKEN": refresh_token,
                },
            )

            # Extract new tokens
            auth_result = response["AuthenticationResult"]
            new_access_token = auth_result["AccessToken"]
            new_id_token = auth_result.get("IdToken")

            logger.info("Access token refreshed successfully")

            return {
                "access_token": new_access_token,
                "id_token": new_id_token,
                "token_type": "Bearer",
                "expires_in": auth_result.get("ExpiresIn", 3600),
            }

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            error_message = e.response["Error"]["Message"]

            logger.warning(
                f"Cognito token refresh error: {error_code} - {error_message}"
            )

            if error_code == "NotAuthorizedException":
                raise ValueError("Invalid or expired refresh token")
            else:
                logger.error(f"Unexpected Cognito error: {error_code}")
                raise ValueError("Token refresh failed due to server error")

    def get_user_info_from_token(self, access_token: str) -> Dict[str, Any]:
        """
        Get user information from access token.

        Args:
            access_token: Valid Cognito access token

        Returns:
            Dict containing user information

        Raises:
            ValueError: If token is invalid
            ClientError: If Cognito operation fails
        """
        try:
            # Get user info from Cognito
            response = self.cognito_client.get_user(AccessToken=access_token)

            # Extract user attributes
            user_attributes = {}
            for attr in response.get("UserAttributes", []):
                user_attributes[attr["Name"]] = attr["Value"]

            return {
                "user_id": response.get("Username"),
                "user_sub": user_attributes.get("sub"),
                "email": user_attributes.get("email"),
                "email_verified": user_attributes.get("email_verified") == "true",
                "first_name": user_attributes.get("given_name"),
                "last_name": user_attributes.get("family_name"),
                "phone_number": user_attributes.get("phone_number"),
                "user_status": response.get("UserStatus"),
                "mfa_enabled": len(response.get("UserMFASettingList", [])) > 0,
                "attributes": user_attributes,
            }

        except ClientError as e:
            error_code = e.response["Error"]["Code"]

            if error_code == "NotAuthorizedException":
                raise ValueError("Invalid or expired access token")
            elif error_code == "UserNotFoundException":
                raise ValueError("User not found")
            else:
                logger.error(f"Cognito error during user info retrieval: {error_code}")
                raise ValueError(f"Failed to retrieve user info: {error_code}")

    def validate_access_token(self, access_token: str) -> bool:
        """
        Validate access token with Cognito.

        Args:
            access_token: Access token to validate

        Returns:
            bool: True if token is valid

        Raises:
            ValueError: If token is invalid
        """
        try:
            # Validate token by attempting to get user info
            self.get_user_info_from_token(access_token)
            return True

        except ValueError:
            return False

    def validate_token_with_blacklist(self, access_token: str) -> TokenValidationResult:
        """
        Validate access token with Cognito and check against blacklist.

        Args:
            access_token: Access token to validate

        Returns:
            TokenValidationResult: Validation result with blacklist check

        """
        result = TokenValidationResult(is_valid=False)

        try:
            # First decode the token locally to get the jti (JWT ID)
            unverified_payload = jwt.decode(
                access_token, options={"verify_signature": False}
            )
            token_jti = unverified_payload.get("jti")

            if not token_jti:
                result.error_message = "Token missing JWT ID (jti)"
                return result

            # Check if token is blacklisted
            token_repo = get_token_repository()
            if token_repo.is_token_blacklisted(token_jti):
                result.is_blacklisted = True
                result.error_message = "Token has been revoked"
                logger.warning(
                    f"Blacklisted token access attempt", extra={"jti": token_jti}
                )
                return result

            # Validate token with Cognito
            user_info = self.get_user_info_from_token(access_token)

            result.is_valid = True
            result.user_info = user_info
            result.token_claims = unverified_payload

            return result

        except ValueError as e:
            result.error_message = str(e)
            return result
        except Exception as e:
            logger.error(
                f"Unexpected error during token validation", extra={"error": str(e)}
            )
            result.error_message = "Token validation failed"
            return result

    def blacklist_token(
        self,
        access_token: str,
        reason: Optional[str] = None,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
    ) -> bool:
        """
        Add a token to the blacklist.

        Args:
            access_token: Token to blacklist
            reason: Reason for blacklisting
            user_agent: User agent when blacklisted
            ip_address: IP address when blacklisted

        Returns:
            bool: True if successfully blacklisted
        """
        try:
            # Decode token to get claims
            unverified_payload = jwt.decode(
                access_token, options={"verify_signature": False}
            )

            token_jti = unverified_payload.get("jti")
            if not token_jti:
                logger.error("Cannot blacklist token without JWT ID (jti)")
                return False

            # Get user ID and expiration
            user_id = unverified_payload.get("sub")
            exp_timestamp = unverified_payload.get("exp")

            if not user_id or not exp_timestamp:
                logger.error("Token missing required claims (sub or exp)")
                return False

            # Convert expiration timestamp to datetime
            expires_at = datetime.fromtimestamp(exp_timestamp)

            # Hash the token for storage
            token_hash = hashlib.sha256(access_token.encode()).hexdigest()

            # Add to blacklist
            token_repo = get_token_repository()
            token_repo.blacklist_token(
                token_jti=token_jti,
                token_hash=token_hash,
                user_id=user_id,
                expires_at=expires_at,
                token_type="access",
                reason=reason or "User logout",
                user_agent=user_agent,
                ip_address=ip_address,
            )

            logger.info(
                f"Token blacklisted successfully",
                extra={"jti": token_jti, "user_id": user_id, "reason": reason},
            )

            return True

        except Exception as e:
            logger.error(f"Failed to blacklist token", extra={"error": str(e)})
            return False

    def blacklist_user_tokens(
        self,
        user_id: str,
        reason: str = "User logout all devices",
    ) -> int:
        """
        Blacklist all tokens for a specific user.

        Args:
            user_id: User ID whose tokens should be blacklisted
            reason: Reason for blacklisting

        Returns:
            int: Number of tokens blacklisted
        """
        try:
            token_repo = get_token_repository()
            blacklisted_count = token_repo.blacklist_user_tokens(
                user_id=user_id,
                token_type="access",
                reason=reason,
            )

            logger.info(
                f"Blacklisted {blacklisted_count} tokens for user",
                extra={"user_id": user_id, "reason": reason},
            )

            return blacklisted_count

        except Exception as e:
            logger.error(
                f"Failed to blacklist user tokens",
                extra={"user_id": user_id, "error": str(e)},
            )
            return 0

    def revoke_token(self, access_token: str) -> bool:
        """
        Revoke access token (logout).

        Args:
            access_token: Access token to revoke

        Returns:
            bool: True if successfully revoked

        Raises:
            ValueError: If revocation fails
        """
        try:
            # Sign out user globally (revokes all tokens)
            self.cognito_client.global_sign_out(AccessToken=access_token)
            logger.info("User signed out successfully")
            return True

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            logger.error(f"Failed to sign out user: {error_code}")
            return False

    def generate_local_jwt(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate local JWT token (for testing or custom scenarios).

        Args:
            user_data: User data to include in token

        Returns:
            Dict containing JWT token and metadata
        """
        now = datetime.now(timezone.utc)
        payload = {
            "sub": user_data.get("user_id"),
            "email": user_data.get("email"),
            "iat": int(now.timestamp()),
            "exp": int(
                (now + timedelta(minutes=self.access_token_expire_minutes)).timestamp()
            ),
            "jti": str(uuid.uuid4()),
            "token_type": "access",
        }

        token = jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)

        return {
            "access_token": token,
            "token_type": "Bearer",
            "expires_in": self.access_token_expire_minutes * 60,
        }

    def validate_local_jwt(self, token: str) -> Dict[str, Any]:
        """
        Validate local JWT token.

        Args:
            token: JWT token to validate

        Returns:
            Dict containing decoded token payload

        Raises:
            ValueError: If token is invalid or expired
        """
        try:
            payload = jwt.decode(
                token, self.jwt_secret, algorithms=[self.jwt_algorithm]
            )
            return payload

        except ExpiredSignatureError:
            raise ValueError("Token has expired")
        except InvalidTokenError as e:
            raise ValueError(f"Invalid token: {str(e)}")


# Global JWT manager instance
jwt_manager = JWTManager()
