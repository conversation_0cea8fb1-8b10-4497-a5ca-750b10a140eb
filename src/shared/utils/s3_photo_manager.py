"""
S3 Photo Manager for fund manager photos.
Handles uploading, validating, and managing photos in S3.
"""

import os
import uuid
import mimetypes
import time
from typing import Optional, Dict, Any, Tu<PERSON>
from io import BytesIO
import base64
import boto3
from botocore.exceptions import ClientError
from PIL import Image
import logging

logger = logging.getLogger(__name__)

class S3PhotoManager:
    """Manager for handling photo uploads to S3."""
    
    def __init__(self):
        self.s3_client = boto3.client('s3')
        self.bucket_name = os.environ.get('STATIC_ASSETS_BUCKET')
        self.photo_prefix = "fund-manager-photos"
        self.max_file_size = 5 * 1024 * 1024  # 5MB
        self.allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}
        self.max_dimensions = (800, 800)  # Max width/height for optimization
        
        if not self.bucket_name:
            raise ValueError("STATIC_ASSETS_BUCKET environment variable is required")
    
    def validate_image(self, image_data: bytes, filename: str) -> Tuple[bool, str]:
        """
        Validate image file.
        
        Args:
            image_data: Raw image data
            filename: Original filename
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Check file size
            if len(image_data) > self.max_file_size:
                return False, f"File size {len(image_data)} exceeds maximum {self.max_file_size} bytes"
            
            # Check file extension
            _, ext = os.path.splitext(filename.lower())
            if ext not in self.allowed_extensions:
                return False, f"File extension {ext} not allowed. Allowed: {', '.join(self.allowed_extensions)}"
            
            # Validate image format using PIL
            try:
                with Image.open(BytesIO(image_data)) as img:
                    img.verify()  # Verify it's a valid image
            except Exception as e:
                return False, f"Invalid image format: {str(e)}"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"Error validating image: {str(e)}")
            return False, f"Validation error: {str(e)}"
    
    def optimize_image(self, image_data: bytes) -> bytes:
        """
        Optimize image for web display.
        
        Args:
            image_data: Raw image data
            
        Returns:
            Optimized image data
        """
        try:
            with Image.open(BytesIO(image_data)) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    # Create white background for transparent images
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                
                # Resize if too large
                if img.width > self.max_dimensions[0] or img.height > self.max_dimensions[1]:
                    img.thumbnail(self.max_dimensions, Image.Resampling.LANCZOS)
                
                # Save as optimized JPEG
                output = BytesIO()
                img.save(output, format='JPEG', quality=85, optimize=True)
                return output.getvalue()
                
        except Exception as e:
            logger.error(f"Error optimizing image: {str(e)}")
            # Return original data if optimization fails
            return image_data
    
    def upload_photo(self, image_data: bytes, filename: str, fund_id: str) -> Dict[str, Any]:
        """
        Upload photo to S3.
        
        Args:
            image_data: Raw image data
            filename: Original filename
            fund_id: Fund ID for organizing photos
            
        Returns:
            Dict with upload result and metadata
        """
        try:
            # Validate image
            is_valid, error_message = self.validate_image(image_data, filename)
            if not is_valid:
                return {
                    'success': False,
                    'error': error_message,
                    'url': None
                }
            
            # Optimize image
            optimized_data = self.optimize_image(image_data)
            
            # Generate unique filename
            file_ext = os.path.splitext(filename.lower())[1]
            unique_filename = f"{uuid.uuid4()}{file_ext}"
            s3_key = f"{self.photo_prefix}/{fund_id}/{unique_filename}"
            
            # Upload to S3
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=optimized_data,
                ContentType=mimetypes.guess_type(filename)[0] or 'image/jpeg',
                CacheControl='max-age=31536000',  # 1 year cache
                Metadata={
                    'fund_id': fund_id,
                    'original_filename': filename,
                    'upload_timestamp': str(int(time.time()))
                }
            )
            
            # Generate URL with region
            region = os.environ.get('AWS_DEFAULT_REGION', 'us-east-1')
            if region == 'us-east-1':
                photo_url = f"https://{self.bucket_name}.s3.amazonaws.com/{s3_key}"
            else:
                photo_url = f"https://{self.bucket_name}.s3.{region}.amazonaws.com/{s3_key}"
            
            return {
                'success': True,
                'url': photo_url,
                's3_key': s3_key,
                'original_size': len(image_data),
                'optimized_size': len(optimized_data),
                'error': None
            }
            
        except ClientError as e:
            logger.error(f"AWS S3 error uploading photo: {str(e)}")
            return {
                'success': False,
                'error': f"S3 upload failed: {str(e)}",
                'url': None
            }
        except Exception as e:
            logger.error(f"Unexpected error uploading photo: {str(e)}")
            return {
                'success': False,
                'error': f"Upload failed: {str(e)}",
                'url': None
            }
    
    def upload_base64_photo(self, base64_data: str, filename: str, fund_id: str) -> Dict[str, Any]:
        """
        Upload base64 encoded photo to S3.
        
        Args:
            base64_data: Base64 encoded image data (with or without data URI prefix)
            filename: Original filename
            fund_id: Fund ID for organizing photos
            
        Returns:
            Dict with upload result and metadata
        """
        try:
            # Remove data URI prefix if present
            if base64_data.startswith('data:'):
                base64_data = base64_data.split(',')[1]
            
            # Decode base64
            image_data = base64.b64decode(base64_data)
            
            return self.upload_photo(image_data, filename, fund_id)
            
        except Exception as e:
            logger.error(f"Error processing base64 photo: {str(e)}")
            return {
                'success': False,
                'error': f"Base64 processing failed: {str(e)}",
                'url': None
            }
    
    def delete_photo(self, s3_key: str) -> bool:
        """
        Delete photo from S3.
        
        Args:
            s3_key: S3 key of the photo to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=s3_key
            )
            return True
            
        except ClientError as e:
            logger.error(f"Error deleting photo from S3: {str(e)}")
            return False
    
    def delete_photo_by_url(self, photo_url: str) -> bool:
        """
        Delete photo by URL.
        
        Args:
            photo_url: Full S3 URL of the photo
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Extract S3 key from URL (handle both regional and us-east-1 formats)
            if f"https://{self.bucket_name}.s3.amazonaws.com/" in photo_url:
                s3_key = photo_url.replace(f"https://{self.bucket_name}.s3.amazonaws.com/", "")
                return self.delete_photo(s3_key)
            elif f"https://{self.bucket_name}.s3." in photo_url and ".amazonaws.com/" in photo_url:
                # Handle regional URLs like https://bucket.s3.ap-northeast-1.amazonaws.com/key
                parts = photo_url.split(".amazonaws.com/")
                if len(parts) == 2:
                    s3_key = parts[1]
                    return self.delete_photo(s3_key)
                else:
                    logger.warning(f"Invalid S3 URL format: {photo_url}")
                    return False
            else:
                logger.warning(f"Invalid S3 URL format: {photo_url}")
                return False
                
        except Exception as e:
            logger.error(f"Error extracting S3 key from URL: {str(e)}")
            return False
    
    def generate_presigned_url(self, s3_key: str, expiration: int = 3600) -> Optional[str]:
        """
        Generate presigned URL for photo access.
        
        Args:
            s3_key: S3 key of the photo
            expiration: URL expiration time in seconds
            
        Returns:
            Presigned URL or None if error
        """
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': s3_key},
                ExpiresIn=expiration
            )
            return url
            
        except ClientError as e:
            logger.error(f"Error generating presigned URL: {str(e)}")
            return None


# Singleton instance
photo_manager = S3PhotoManager()