"""
Validation Response Utilities.
Converts validation results to standardized API responses.
"""

from typing import Dict, Any, List
from ..validation import ValidationResult
from ..api.responses import APIResponse


class ValidationResponseHandler:
    """Handles conversion of validation results to API responses."""

    @staticmethod
    def create_validation_error_response(
        validation_result: ValidationResult,
    ) -> Dict[str, Any]:
        """
        Create a standardized validation error response.

        Args:
            validation_result: ValidationResult object with validation issues

        Returns:
            API Gateway response dictionary
        """
        if validation_result.is_valid:
            return APIResponse.success(
                data={
                    "validation_summary": {
                        "is_valid": True,
                        "warnings": len(validation_result.warnings),
                        "info": len(validation_result.info),
                    },
                    "warnings": validation_result.warnings,
                    "info": validation_result.info,
                },
                message="Validation passed",
            )

        # Group errors by field for better UX
        field_errors = {}
        for error in validation_result.errors:
            field = error.get("field", "general")
            if field not in field_errors:
                field_errors[field] = []
            field_errors[field].append(
                {
                    "message": error.get("message"),
                    "code": error.get("code"),
                    "severity": error.get("severity"),
                }
            )

        # Create validation error response - convert field_errors dict to list format
        validation_errors_list = []

        # Add field-specific errors
        for field, errors in field_errors.items():
            for error in errors:
                validation_errors_list.append({
                    "field": field,
                    "message": error.get("message"),
                    "code": error.get("code"),
                    "severity": error.get("severity"),
                })

        # Add validation summary as a special entry
        validation_errors_list.append({
            "field": "_summary",
            "message": f"Validation failed with {len(validation_result.errors)} errors, {len(validation_result.warnings)} warnings",
            "code": "VALIDATION_SUMMARY",
            "severity": "error",
            "details": {
                "is_valid": False,
                "errors": len(validation_result.errors),
                "warnings": len(validation_result.warnings),
                "info": len(validation_result.info),
                "all_issues": validation_result.get_all_issues(),
            }
        })

        return APIResponse.validation_error(
            message="Validation failed",
            validation_errors=validation_errors_list,
        )

    @staticmethod
    def create_bulk_validation_response(
        results: Dict[str, ValidationResult],
    ) -> Dict[str, Any]:
        """
        Create a response for bulk validation operations.

        Args:
            results: Dictionary mapping fund_id to ValidationResult

        Returns:
            API Gateway response dictionary
        """
        summary = {
            "total_items": len(results),
            "valid_items": 0,
            "invalid_items": 0,
            "items_with_warnings": 0,
        }

        detailed_results = {}
        overall_valid = True

        for fund_id, result in results.items():
            if result.is_valid:
                summary["valid_items"] += 1
                if result.warnings:
                    summary["items_with_warnings"] += 1
            else:
                summary["invalid_items"] += 1
                overall_valid = False

            detailed_results[fund_id] = {
                "is_valid": result.is_valid,
                "errors": result.errors,
                "warnings": result.warnings,
                "info": result.info,
            }

        if overall_valid:
            return APIResponse.success(
                message="Bulk validation completed",
                data={"summary": summary, "results": detailed_results},
            )
        else:
            # Convert detailed_results dict to list format for validation_errors
            validation_errors_list = []

            for fund_id, result in detailed_results.items():
                if not result["is_valid"]:
                    for error in result["errors"]:
                        validation_errors_list.append({
                            "field": f"{fund_id}.{error.get('field', 'general')}",
                            "message": error.get("message"),
                            "code": error.get("code"),
                            "severity": "error",
                        })

            # Add summary information
            validation_errors_list.append({
                "field": "_bulk_summary",
                "message": f"Bulk validation failed for {summary['invalid_items']} out of {summary['total_items']} items",
                "code": "BULK_VALIDATION_SUMMARY",
                "severity": "error",
                "details": summary
            })

            return APIResponse.validation_error(
                message="Bulk validation failed for some items",
                validation_errors=validation_errors_list,
            )

    @staticmethod
    def extract_field_errors(
        validation_result: ValidationResult,
    ) -> List[Dict[str, Any]]:
        """
        Extract field-level errors for form validation feedback.

        Args:
            validation_result: ValidationResult object

        Returns:
            List of field error objects
        """
        field_errors = []

        for error in validation_result.errors:
            field_errors.append(
                {
                    "field": error.get("field"),
                    "message": error.get("message"),
                    "code": error.get("code"),
                    "type": "error",
                }
            )

        for warning in validation_result.warnings:
            field_errors.append(
                {
                    "field": warning.get("field"),
                    "message": warning.get("message"),
                    "code": warning.get("code"),
                    "type": "warning",
                }
            )

        return field_errors
