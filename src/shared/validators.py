"""
Reusable validation functions for Pydantic models.
Common validation patterns that can be shared across Fund, User, and Report models.
"""

import re
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional, Union


class ValidationError(Exception):
    """Custom validation error for business logic validation."""

    pass


class CommonValidators:
    """Common validation functions for Pydantic models."""

    @staticmethod
    def validate_id_format(
        value: str, field_name: str = "ID", min_length: int = 3, max_length: int = 50
    ) -> str:
        """
        Validate ID format (alphanumeric, hyphens, underscores).

        Args:
            value: The ID value to validate
            field_name: Name of the field for error messages
            min_length: Minimum length requirement
            max_length: Maximum length requirement

        Returns:
            The validated ID value

        Raises:
            ValueError: If validation fails
        """
        if not value:
            raise ValueError(f"{field_name} is required")

        if len(value) < min_length:
            raise ValueError(f"{field_name} must be at least {min_length} characters")

        if len(value) > max_length:
            raise ValueError(f"{field_name} must not exceed {max_length} characters")

        if not re.match(r"^[a-zA-Z0-9_-]+$", value):
            raise ValueError(
                f"{field_name} can only contain letters, numbers, hyphens, and underscores"
            )

        return value

    @staticmethod
    def validate_phone_number(value: Optional[str]) -> Optional[str]:
        """
        Validate phone number format.

        Args:
            value: Phone number to validate

        Returns:
            The validated phone number

        Raises:
            ValueError: If phone number format is invalid
        """
        if value is None:
            return value

        # Remove all non-digit characters for validation
        digits_only = re.sub(r"\D", "", value)
        if len(digits_only) < 10 or len(digits_only) > 15:
            raise ValueError("Phone number must contain 10-15 digits")

        # Allow common phone number formats
        if not re.match(r"^[\+]?[\d\s\-\(\)\.]+$", value):
            raise ValueError("Phone number contains invalid characters")

        return value

    @staticmethod
    def validate_email_format(email: str) -> str:
        """
        Validate and normalize email format.

        Args:
            email: Email address to validate

        Returns:
            Normalized (lowercase) email

        Raises:
            ValueError: If email format is invalid
        """
        if not email:
            raise ValueError("Email is required")

        # Basic email format validation (Pydantic EmailStr handles this better)
        if not re.match(r"^[^\s@]+@[^\s@]+\.[^\s@]+$", email):
            raise ValueError("Invalid email format")

        return email.lower()

    @staticmethod
    def validate_currency_code(currency: str) -> str:
        """
        Validate currency code format.

        Args:
            currency: 3-letter currency code

        Returns:
            Uppercase currency code

        Raises:
            ValueError: If currency format is invalid
        """
        if len(currency) != 3:
            raise ValueError("Currency code must be exactly 3 characters")

        if not currency.isalpha():
            raise ValueError("Currency code must contain only letters")

        return currency.upper()

    @staticmethod
    def validate_percentage(
        value: Optional[Decimal],
        field_name: str = "Percentage",
        min_val: Decimal = Decimal("-100"),
        max_val: Decimal = Decimal("1000"),
    ) -> Optional[Decimal]:
        """
        Validate percentage values.

        Args:
            value: Percentage value to validate
            field_name: Name of the field for error messages
            min_val: Minimum allowed percentage
            max_val: Maximum allowed percentage

        Returns:
            The validated percentage value

        Raises:
            ValueError: If percentage is out of range
        """
        if value is None:
            return value

        if value < min_val:
            raise ValueError(f"{field_name} cannot be less than {min_val}%")

        if value > max_val:
            raise ValueError(f"{field_name} seems unreasonably high (> {max_val}%)")

        return value

    @staticmethod
    def validate_positive_decimal(
        value: Optional[Decimal],
        field_name: str = "Value",
        max_val: Optional[Decimal] = None,
    ) -> Optional[Decimal]:
        """
        Validate positive decimal values.

        Args:
            value: Decimal value to validate
            field_name: Name of the field for error messages
            max_val: Optional maximum value

        Returns:
            The validated decimal value

        Raises:
            ValueError: If value is negative or exceeds maximum
        """
        if value is None:
            return value

        if value < 0:
            raise ValueError(f"{field_name} cannot be negative")

        if max_val is not None and value > max_val:
            raise ValueError(f"{field_name} seems unreasonably high (> {max_val})")

        return value

    @staticmethod
    def validate_date_not_future(
        date_value: Optional[datetime], field_name: str = "Date"
    ) -> Optional[datetime]:
        """
        Validate that a date is not in the future.

        Args:
            date_value: Date to validate
            field_name: Name of the field for error messages

        Returns:
            The validated date

        Raises:
            ValueError: If date is in the future
        """
        if date_value is None:
            return date_value

        if date_value > datetime.now(timezone.utc):
            raise ValueError(f"{field_name} cannot be in the future")

        return date_value

    @staticmethod
    def validate_string_list(
        values: Optional[List[str]],
        field_name: str = "List",
        max_items: int = 50,
        max_item_length: int = 50,
    ) -> Optional[List[str]]:
        """
        Validate a list of strings.

        Args:
            values: List of strings to validate
            field_name: Name of the field for error messages
            max_items: Maximum number of items allowed
            max_item_length: Maximum length per item

        Returns:
            The validated list

        Raises:
            ValueError: If validation fails
        """
        if values is None:
            return values

        if len(values) > max_items:
            raise ValueError(f"{field_name} cannot have more than {max_items} items")

        for item in values:
            if not isinstance(item, str):
                raise ValueError(f"{field_name} items must be strings")

            if len(item) > max_item_length:
                raise ValueError(
                    f"{field_name} item cannot exceed {max_item_length} characters"
                )

        return values

    @staticmethod
    def validate_alphanumeric_with_spaces(value: str, field_name: str = "Value") -> str:
        """
        Validate alphanumeric string with spaces, hyphens, and underscores.

        Args:
            value: String to validate
            field_name: Name of the field for error messages

        Returns:
            The validated string

        Raises:
            ValueError: If string contains invalid characters
        """
        if not re.match(r"^[a-zA-Z0-9_\-\s]+$", value):
            raise ValueError(f"{field_name} contains invalid characters")

        return value

    @staticmethod
    def validate_isin_code(isin: Optional[str]) -> Optional[str]:
        """
        Validate ISIN code format.

        Args:
            isin: ISIN code to validate

        Returns:
            The validated ISIN code

        Raises:
            ValueError: If ISIN format is invalid
        """
        if isin is None:
            return isin

        if not re.match(r"^[A-Z]{2}[A-Z0-9]{9}[0-9]$", isin):
            raise ValueError(
                "ISIN must be 12 characters: 2 letters, 9 alphanumeric, 1 digit"
            )

        return isin

    @staticmethod
    def validate_cusip_code(cusip: Optional[str]) -> Optional[str]:
        """
        Validate CUSIP code format.

        Args:
            cusip: CUSIP code to validate

        Returns:
            The validated CUSIP code

        Raises:
            ValueError: If CUSIP format is invalid
        """
        if cusip is None:
            return cusip

        if not re.match(r"^[A-Z0-9]{9}$", cusip):
            raise ValueError("CUSIP must be exactly 9 alphanumeric characters")

        return cusip

    @staticmethod
    def validate_ip_address(ip: Optional[str]) -> Optional[str]:
        """
        Validate IP address format (basic validation).

        Args:
            ip: IP address to validate

        Returns:
            The validated IP address

        Raises:
            ValueError: If IP format appears invalid
        """
        if ip is None:
            return ip

        # Basic IPv4/IPv6 validation
        ipv4_pattern = r"^(\d{1,3}\.){3}\d{1,3}$"
        ipv6_pattern = r"^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$"

        if not (re.match(ipv4_pattern, ip) or re.match(ipv6_pattern, ip)):
            # Allow simplified validation for now
            if len(ip) > 45:  # Max IPv6 length
                raise ValueError("IP address format appears invalid")

        return ip

    @staticmethod
    def validate_percentage_allocation(
        allocations: Dict[str, Decimal],
        field_name: str = "Allocation",
        tolerance: Decimal = Decimal("1"),
    ) -> Dict[str, Decimal]:
        """
        Validate that percentage allocations sum to approximately 100%.

        Args:
            allocations: Dictionary of allocation percentages
            field_name: Name of the field for error messages
            tolerance: Tolerance for deviation from 100%

        Returns:
            The validated allocations

        Raises:
            ValueError: If allocations don't sum to ~100%
        """
        if not allocations:
            return allocations

        total = sum(allocations.values())
        if total > (Decimal("100") + tolerance) or total < (Decimal("100") - tolerance):
            raise ValueError(
                f"{field_name} percentages should sum to approximately 100%"
            )

        return allocations


class BusinessLogicValidators:
    """Business logic validation functions."""

    @staticmethod
    def validate_fund_nav_consistency(
        nav: Optional[Decimal], fund_type: str, status: str
    ) -> Optional[Decimal]:
        """
        Validate NAV consistency with fund type and status.

        Args:
            nav: Net Asset Value
            fund_type: Type of fund
            status: Fund status

        Returns:
            The validated NAV

        Raises:
            ValueError: If NAV is inconsistent with fund characteristics
        """
        if nav is None:
            return nav

        # Closed or liquidating funds might have zero NAV
        if status in ["closed", "liquidating"] and nav == 0:
            return nav

        # Active funds should have positive NAV
        if status == "active" and nav <= 0:
            raise ValueError("Active funds must have positive NAV")

        # Money market funds typically have NAV around 1.00
        if fund_type == "money_market" and nav > Decimal("2.0"):
            # This is more of a warning - some money market funds can have higher NAV
            pass

        return nav

    @staticmethod
    def validate_user_role_permissions(
        role: str, permissions: Dict[str, bool]
    ) -> Dict[str, bool]:
        """
        Validate that user permissions are consistent with their role.

        Args:
            role: User role
            permissions: Permission dictionary

        Returns:
            The validated permissions

        Raises:
            ValueError: If permissions are inconsistent with role
        """
        # Viewers should not have edit permissions
        if role == "viewer":
            edit_permissions = [
                "can_edit_funds",
                "can_create_funds",
                "can_delete_funds",
                "can_manage_users",
            ]
            for perm in edit_permissions:
                if permissions.get(perm, False):
                    raise ValueError(f"Viewers cannot have {perm} permission")

        # Admins should have comprehensive permissions
        if role == "admin":
            required_permissions = [
                "can_view_funds",
                "can_manage_users",
                "can_access_admin_panel",
            ]
            for perm in required_permissions:
                if not permissions.get(perm, False):
                    # This is more of a warning - allow flexibility
                    pass

        return permissions

    @staticmethod
    def validate_manager_hierarchy(
        user_id: str, manager_id: Optional[str]
    ) -> Optional[str]:
        """
        Validate manager hierarchy constraints.

        Args:
            user_id: User's ID
            manager_id: Manager's ID

        Returns:
            The validated manager ID

        Raises:
            ValueError: If hierarchy constraints are violated
        """
        if manager_id is None:
            return manager_id

        # Prevent self-reference
        if manager_id == user_id:
            raise ValueError("User cannot be their own manager")

        # Additional hierarchy validation could be added here
        # (e.g., check for circular references in the database)

        return manager_id
