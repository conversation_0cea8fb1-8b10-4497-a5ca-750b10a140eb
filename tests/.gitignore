# Test environment files with sensitive data
.env_test
.env_test.*
integration/.env_test
integration/.env_test.*

# Test configuration files that may contain secrets
**/test_config.json
**/test_secrets.json
**/test_credentials.json

# Test data files that may contain sensitive information
**/test_data_sensitive.json
**/mock_credentials.json

# Python test cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# Test coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Test logs
test_*.log
*.test.log
