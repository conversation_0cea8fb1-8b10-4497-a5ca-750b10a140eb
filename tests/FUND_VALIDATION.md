# Fund Validation Testing Guide

## Subtask 9.3: Server-Side Validation Implementation

This document provides comprehensive testing instructions for the fund validation functionality implemented in subtask 9.3 of the FundFlow project.

## 🎯 What Was Implemented

### Core Components

1. **FundValidationService** (`src/shared/validators/fund_validation.py`)

   - Comprehensive business logic validation
   - ISIN/CUSIP code validation with check digit algorithms
   - Cross-field consistency checks
   - Permission-based validation
   - Status transition validation

2. **ValidationResponseHandler** (`src/shared/utils/validation_response.py`)

   - Standardized API response formatting
   - Validation error and warning handling
   - Bulk operation response formatting

3. **Enhanced API Handlers** (`src/functions/api/funds.py`)

   - Integrated validation into create/update operations
   - Added bulk update endpoint with validation
   - Enhanced error responses with detailed validation feedback

4. **Comprehensive Test Suite** (`src/functions/api/test_fund_validation.py`)
   - 15+ test scenarios covering all validation rules
   - Integration tests for API handlers
   - Mock dependencies for isolated testing

## 🚀 Deployment Instructions

### Option 1: Automated Deployment (Recommended)

```bash
# Fix dependency conflicts first
cd /path/to/FundFlow

# Update src/requirements.txt with compatible versions
echo "boto3>=1.33.0
aws-lambda-powertools>=2.23.0
pydantic>=2.0.0,<3.0.0
python-json-logger>=2.0.0" > src/requirements.txt

# Deploy using SAM
./deploy.sh
```

### Option 2: Manual SAM Deployment

```bash
# Build the application
sam build --parallel

# Deploy to dev environment
sam deploy --config-env dev

# Check deployment status
aws cloudformation describe-stacks --stack-name fundflow-dev --profile fundflow-dev
```

### Option 3: Local Testing Setup

```bash
# Start SAM local API
sam local start-api --port 3000

# Test endpoints locally
curl -X POST http://localhost:3000/funds -H "Content-Type: application/json" -d @test_data.json
```

## 🧪 Testing the Validation Implementation

### 1. Python Test Script

Use the provided `test_fund_validation_demo.py` script:

```bash
# Install dependencies
pip install requests

# Run the test script
python test_fund_validation_demo.py

# Enter your API Gateway URL when prompted
# Example: https://abc123.execute-api.us-east-1.amazonaws.com/dev
```

### 2. Postman Collection

Import `postman_fund_validation_tests.json` into Postman:

1. Open Postman
2. Click "Import" → "Choose Files"
3. Select `postman_fund_validation_tests.json`
4. Update the `{{baseUrl}}` variable with your API Gateway URL
5. Run the collection to test all scenarios

### 3. Manual cURL Testing

#### Test 1: Valid Fund Creation

```bash
curl -X POST https://your-api-gateway.amazonaws.com/dev/funds \
  -H "Content-Type: application/json" \
  -d '{
    "fund_id": "VALID-FUND-001",
    "name": "Test Growth Fund",
    "fund_type": "equity",
    "status": "active",
    "nav": 10.50,
    "currency": "USD",
    "risk_level": "moderate",
    "expense_ratio": 0.75,
    "minimum_investment": 1000.00,
    "description": "A test fund for growth investments",
    "fund_manager": "John Smith",
    "management_company": "Test Asset Management"
  }'
```

**Expected Response:** `201 Created` with fund data

#### Test 2: Invalid Fund ID Format

```bash
curl -X POST https://your-api-gateway.amazonaws.com/dev/funds \
  -H "Content-Type: application/json" \
  -d '{
    "fund_id": "invalid fund id with spaces!",
    "name": "Test Fund",
    "fund_type": "equity"
  }'
```

**Expected Response:** `400 Bad Request` with validation errors:

```json
{
  "error": "Validation failed",
  "details": {
    "errors": [
      {
        "field": "fund_id",
        "message": "Fund ID contains invalid characters",
        "severity": "error"
      }
    ]
  }
}
```

#### Test 3: Invalid Financial Data

```bash
curl -X POST https://your-api-gateway.amazonaws.com/dev/funds \
  -H "Content-Type: application/json" \
  -d '{
    "fund_id": "INVALID-FINANCIAL-001",
    "name": "Test Fund",
    "fund_type": "equity",
    "nav": -5.0,
    "expense_ratio": -1.0,
    "total_assets": -1000000
  }'
```

**Expected Response:** `400 Bad Request` with multiple validation errors

#### Test 4: ISIN Validation

```bash
curl -X POST https://your-api-gateway.amazonaws.com/dev/funds \
  -H "Content-Type: application/json" \
  -d '{
    "fund_id": "ISIN-TEST-001",
    "name": "ISIN Test Fund",
    "fund_type": "equity",
    "isin": "INVALID-ISIN-FORMAT"
  }'
```

**Expected Response:** `400 Bad Request` with ISIN validation error

#### Test 5: Valid ISIN Test

```bash
curl -X POST https://your-api-gateway.amazonaws.com/dev/funds \
  -H "Content-Type: application/json" \
  -d '{
    "fund_id": "VALID-ISIN-001",
    "name": "Valid ISIN Fund",
    "fund_type": "equity",
    "isin": "US0378331005"
  }'
```

**Expected Response:** `201 Created` (Apple Inc. ISIN)

#### Test 6: Fund Type vs Risk Level Consistency

```bash
curl -X POST https://your-api-gateway.amazonaws.com/dev/funds \
  -H "Content-Type: application/json" \
  -d '{
    "fund_id": "CONSISTENCY-TEST-001",
    "name": "Money Market High Risk Fund",
    "fund_type": "money_market",
    "risk_level": "very_high"
  }'
```

**Expected Response:** `201 Created` with warnings about inconsistency

#### Test 7: Performance Metrics Validation

```bash
curl -X POST https://your-api-gateway.amazonaws.com/dev/funds \
  -H "Content-Type: application/json" \
  -d '{
    "fund_id": "PERFORMANCE-TEST-001",
    "name": "Performance Test Fund",
    "fund_type": "equity",
    "performance_metrics": {
      "ytd_return": -150.0,
      "volatility": -5.0,
      "sharpe_ratio": 15.0
    }
  }'
```

**Expected Response:** `400 Bad Request` with performance validation errors

#### Test 8: Holdings Allocation Validation

```bash
curl -X POST https://your-api-gateway.amazonaws.com/dev/funds \
  -H "Content-Type: application/json" \
  -d '{
    "fund_id": "HOLDINGS-TEST-001",
    "name": "Holdings Test Fund",
    "fund_type": "equity",
    "holdings": {
      "sector_allocation": {
        "Technology": 60.0,
        "Healthcare": 30.0,
        "Finance": 20.0
      }
    }
  }'
```

**Expected Response:** `400 Bad Request` (allocations total 110% instead of 100%)

#### Test 9: Fund Update with Large NAV Change

```bash
# First create a fund, then update it
curl -X PUT https://your-api-gateway.amazonaws.com/dev/funds/VALID-FUND-001 \
  -H "Content-Type: application/json" \
  -d '{
    "nav": 20.0
  }'
```

**Expected Response:** `200 OK` with warnings about large NAV change

#### Test 10: Status Transition Validation

```bash
# Create a closed fund first
curl -X POST https://your-api-gateway.amazonaws.com/dev/funds \
  -H "Content-Type: application/json" \
  -d '{
    "fund_id": "STATUS-TEST-001",
    "name": "Status Test Fund",
    "fund_type": "equity",
    "status": "closed"
  }'

# Try to transition from closed to active (invalid)
curl -X PUT https://your-api-gateway.amazonaws.com/dev/funds/STATUS-TEST-001 \
  -H "Content-Type: application/json" \
  -d '{
    "status": "active"
  }'
```

**Expected Response:** `400 Bad Request` with status transition error

#### Test 11: Bulk Update with Mixed Validation

```bash
curl -X POST https://your-api-gateway.amazonaws.com/dev/funds/bulk-update \
  -H "Content-Type: application/json" \
  -d '{
    "updates": [
      {
        "fund_id": "BULK-TEST-001",
        "data": {
          "name": "Updated Fund 1",
          "nav": 15.0
        }
      },
      {
        "fund_id": "BULK-TEST-002",
        "data": {
          "name": "Updated Fund 2",
          "nav": -5.0
        }
      }
    ]
  }'
```

**Expected Response:** Mixed results with some successful and some failed validations

## 📋 Validation Rules Implemented

### 1. Fund ID Validation

- **Format:** Alphanumeric characters, hyphens, and underscores only
- **Length:** 3-50 characters
- **Pattern:** Must not start or end with hyphen/underscore

### 2. Financial Data Validation

- **NAV:** Must be positive
- **Expense Ratio:** 0-10% range
- **Total Assets:** Must be positive if provided
- **Minimum Investment:** Must be positive if provided

### 3. ISIN/CUSIP Validation

- **ISIN:** 12-character format with valid check digit (Luhn algorithm)
- **CUSIP:** 9-character format with valid check digit
- **Format:** Proper country code and issuer validation

### 4. Cross-Field Consistency

- **Fund Type vs Risk Level:** Money market funds should be low risk
- **Currency Consistency:** Related fields should use same currency
- **Performance Metrics:** Values should be reasonable for fund type

### 5. Business Rules

- **Status Transitions:** Certain transitions are not allowed (e.g., closed → active)
- **Immutable Fields:** Fund ID and inception date cannot be changed
- **Permission Validation:** Role-based access control for operations

### 6. Performance Metrics

- **Returns:** YTD return between -100% and +1000%
- **Volatility:** Non-negative values, reasonable upper bounds
- **Ratios:** Sharpe ratio and other metrics within expected ranges

### 7. Holdings Validation

- **Allocation Percentages:** Must sum to 100% (±0.01% tolerance)
- **Sector Limits:** Individual sectors should not exceed reasonable limits
- **Geographic Distribution:** Regional allocations should be valid

## 🔍 Expected Validation Response Format

### Success Response

```json
{
  "success": true,
  "data": {
    "fund": {
      /* fund data */
    },
    "validation_warnings": [
      {
        "field": "risk_level",
        "message": "Risk level seems high for money market fund",
        "severity": "warning"
      }
    ]
  }
}
```

### Error Response

```json
{
  "error": "Validation failed",
  "details": {
    "errors": [
      {
        "field": "nav",
        "message": "NAV must be positive",
        "severity": "error"
      },
      {
        "field": "isin",
        "message": "Invalid ISIN format",
        "severity": "error"
      }
    ],
    "warnings": [
      {
        "field": "expense_ratio",
        "message": "Expense ratio is unusually high",
        "severity": "warning"
      }
    ]
  }
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Dependency Conflicts During Deployment**

   ```bash
   # Fix by updating requirements.txt with compatible versions
   # Remove version conflicts between aws-lambda-powertools and pydantic
   ```

2. **Authentication Errors**

   ```bash
   # Add Authorization header if required
   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" ...
   ```

3. **CORS Issues**

   ```bash
   # Add CORS headers if testing from browser
   -H "Origin: http://localhost:3000"
   ```

4. **Timeout Issues**
   ```bash
   # Increase Lambda timeout in template.yaml if needed
   Timeout: 30
   ```

## 📊 Performance Considerations

- **Validation Time:** Each validation should complete within 100ms
- **Memory Usage:** Validation service uses minimal memory footprint
- **Scalability:** Supports concurrent validation requests
- **Error Handling:** Graceful degradation when external services are unavailable

## 🔄 Integration with Frontend

The validation responses are designed to integrate seamlessly with the frontend form validation:

```javascript
// Example frontend integration
const response = await fetch("/api/funds", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify(fundData),
});

if (!response.ok) {
  const error = await response.json();
  // Display validation errors to user
  error.details.errors.forEach((err) => {
    showFieldError(err.field, err.message);
  });
}
```

## ✅ Success Metrics

A successful implementation should demonstrate:

1. **Comprehensive Validation:** All business rules enforced
2. **User-Friendly Errors:** Clear, actionable error messages
3. **Performance:** Sub-second response times
4. **Reliability:** Consistent validation across all endpoints
5. **Security:** Permission-based validation working correctly

## 🎉 Conclusion

The fund validation implementation provides robust, comprehensive validation for all fund operations. The testing approaches outlined above will verify that all validation rules are working correctly and providing appropriate feedback to users.

For any issues or questions about the validation implementation, refer to the code documentation in the respective files or contact the development team.
