# AWS API Gateway Funds API Testing

This directory contains scripts to test the AWS API Gateway for the FundFlow application.

## API Overview

- **Base URL**: `https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev`
- **Authentication**: AWS Cognito JWT tokens
- **Test User**: `<EMAIL>` / `TestPassword123!`

## Available Endpoints

| Method | Endpoint             | Description                                    |
| ------ | -------------------- | ---------------------------------------------- |
| GET    | `/funds`             | List all funds (supports pagination & filters) |
| GET    | `/funds/{fund_id}`   | Get specific fund details                      |
| POST   | `/funds`             | Create new fund                                |
| PUT    | `/funds/{fund_id}`   | Update existing fund                           |
| DELETE | `/funds/{fund_id}`   | Delete fund (soft delete)                      |
| POST   | `/funds/bulk-update` | Bulk update multiple funds                     |

## Testing Options

### Option 1: Python Script (Recommended)

A comprehensive Python-based client that tests all endpoints with proper authentication.

#### Prerequisites

```bash
# Install Python dependencies
pip install -r requirements.txt

# Configure AWS credentials (for Cognito authentication)
aws configure
```

#### Usage

```bash
python test_aws_api_client.py
```

#### Features

- ✅ Automatic AWS Cognito authentication
- ✅ Tests all CRUD operations
- ✅ Comprehensive error handling
- ✅ Detailed request/response logging
- ✅ Test result summary
- ✅ Validation testing (invalid data, duplicates, etc.)
- ✅ Bulk operations testing
- ✅ Security testing (unauthorized access)

### Option 2: Bash Script with curl

A lightweight bash script using curl commands for quick testing.

#### Prerequisites

```bash
# Install required tools (if not already available)
# - curl
# - jq (for JSON parsing)
# - aws CLI (for authentication)

# Configure AWS credentials
aws configure
```

#### Usage

```bash
# Make the script executable
chmod +x test_api_curl.sh

# Run the tests
./test_api_curl.sh
```

#### Features

- ✅ Quick setup and execution
- ✅ No Python dependencies
- ✅ Color-coded output
- ✅ Step-by-step test execution
- ✅ Test results summary

## Test Coverage

Both scripts test the following scenarios:

### Authentication & Security

- ✅ AWS Cognito authentication flow
- ✅ Unauthorized access attempts
- ✅ JWT token validation

### Fund Listing

- ✅ List all funds
- ✅ Pagination (`page`, `page_size`)
- ✅ Filtering (`status`, `fund_type`, `search`)

### Fund Retrieval

- ✅ Get specific fund by ID
- ✅ Handle non-existent fund IDs

### Fund Creation

- ✅ Create new fund with valid data
- ✅ Prevent duplicate fund creation
- ✅ Validate required fields
- ✅ Handle invalid data

### Fund Updates

- ✅ Update existing fund
- ✅ Partial updates
- ✅ Handle non-existent fund updates
- ✅ Validate update data

### Fund Deletion

- ✅ Soft delete funds
- ✅ Handle non-existent fund deletion

### Bulk Operations

- ✅ Bulk update multiple funds
- ✅ Handle mixed success/failure scenarios
- ✅ Validation for bulk operations

## Sample Fund Data Structure

```json
{
  "fund_id": "TEST-FUND-123456789",
  "fund_name": "Test Equity Fund",
  "fund_type": "EQUITY",
  "status": "ACTIVE",
  "nav": 125.75,
  "expense_ratio": 1.25,
  "min_investment": 1000,
  "fund_manager": "Test Fund Manager",
  "inception_date": "2024-01-01",
  "description": "Test fund for API validation",
  "benchmark": "NIFTY 50",
  "risk_level": "MODERATE",
  "holdings": [
    {
      "security_name": "Test Security",
      "percentage": 15.5,
      "sector": "Technology"
    }
  ],
  "performance": {
    "1_year": 12.5,
    "3_year": 15.2,
    "5_year": 18.7,
    "inception": 20.1
  }
}
```

## Expected Test Results

When running the tests successfully, you should see:

```
🎉 All tests passed! AWS API Gateway is working correctly.

✅ List Funds: PASS
✅ List Funds (Pagination): PASS
✅ List Funds (Filters): PASS
✅ Get Fund: PASS
✅ Get Non-existent Fund: PASS
✅ Create Fund: PASS
✅ Duplicate Fund Creation: PASS
✅ Update Fund: PASS
✅ Update Non-existent Fund: PASS
✅ Bulk Update: PASS
✅ Delete Fund: PASS
✅ Delete Non-existent Fund: PASS

Overall: 12/12 tests passed
```

## Troubleshooting

### Authentication Issues

If authentication fails:

1. **Check AWS CLI configuration**:

   ```bash
   aws configure list
   aws sts get-caller-identity
   ```

2. **Verify region**:

   ```bash
   aws configure set region ap-northeast-1
   ```

3. **Test Cognito access**:
   ```bash
   aws cognito-idp list-user-pools --max-results 10 --region ap-northeast-1
   ```

### API Connection Issues

1. **Check network connectivity**:

   ```bash
   curl -I https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev
   ```

2. **Verify SSL certificates**:
   ```bash
   curl -v https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev
   ```

### Common Error Responses

| Status Code | Meaning               | Common Causes                            |
| ----------- | --------------------- | ---------------------------------------- |
| 401         | Unauthorized          | Missing/invalid JWT token                |
| 403         | Forbidden             | Valid token but insufficient permissions |
| 404         | Not Found             | Invalid endpoint or fund ID              |
| 422         | Validation Error      | Invalid request data                     |
| 500         | Internal Server Error | Backend service issues                   |

## Integration with Frontend

The API Gateway is already integrated with the Next.js frontend:

- **Frontend URL**: http://localhost:3000
- **Environment**: `NEXT_PUBLIC_USE_AWS_API=true`
- **Authentication**: NextAuth.js with AWS Cognito

To test the full flow:

1. Start the frontend: `npm run dev`
2. Navigate to http://localhost:3000
3. Sign in with: `<EMAIL>` / `TestPassword123!`
4. Use the funds management interface

## Notes

- All API endpoints require authentication except health checks
- Fund deletion is a soft delete (status changed to 'CLOSED')
- The API includes comprehensive validation and error handling
- Sample data is pre-populated in DynamoDB for testing
- Rate limiting and throttling are configured on the API Gateway
