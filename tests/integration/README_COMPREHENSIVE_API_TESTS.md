# Comprehensive API Integration Tests

This directory contains comprehensive integration tests for all FundFlow API endpoints. These tests simulate how the frontend would interact with the backend APIs and validate the complete system functionality.

## Overview

The integration tests cover four main API areas:
- **Authentication API** (`/auth/*`)
- **Funds API** (`/funds/*`)
- **Portfolios API** (`/portfolios/*`)
- **File Upload API** (`/upload/*`)

## Test Files

### 1. Auth API Tests
**File**: `test_auth_api_comprehensive.py`

Tests the authentication system including:
- ✅ Health check endpoint
- ✅ User login with valid credentials
- ✅ User profile retrieval
- ✅ Token validation
- ✅ User logout
- ✅ Invalid credentials handling
- ✅ Unauthorized access protection

**Key Features**:
- Simulates frontend authentication flow
- Tests JWT token handling
- Validates security measures
- Tests error responses

### 2. Funds API Tests
**File**: `test_funds_api_comprehensive.py`

Tests the funds management system including:
- ✅ Health check endpoint
- ✅ List all funds
- ✅ List funds with filters (type, status, limit)
- ✅ Get specific fund by ID
- ✅ Get fund market data
- ✅ Search funds by query
- ✅ Create new fund (authenticated)
- ✅ Update existing fund (authenticated)
- ✅ Delete fund (authenticated)
- ✅ Handle invalid fund IDs

**Key Features**:
- Tests CRUD operations for funds
- Validates filter and search functionality
- Tests authentication requirements
- Validates market data endpoints

### 3. Portfolios API Tests
**File**: `test_portfolios_api_comprehensive.py`

Tests the portfolio management system including:
- ✅ Health check endpoint
- ✅ List all portfolios
- ✅ Create new portfolio (authenticated)
- ✅ Get specific portfolio by ID
- ✅ Update existing portfolio (authenticated)
- ✅ Add fund to portfolio (holdings)
- ✅ Get portfolio holdings
- ✅ Get portfolio performance metrics
- ✅ Portfolio rebalancing
- ✅ Delete portfolio (authenticated)
- ✅ Handle invalid portfolio IDs

**Key Features**:
- Tests portfolio lifecycle management
- Validates holdings management
- Tests performance calculations
- Tests rebalancing functionality

### 4. File Upload API Tests
**File**: `test_file_upload_comprehensive.py`

Tests the PDF fund extraction system including:
- ✅ Health check endpoint
- ✅ Upload PDF file for fund extraction
- ✅ Check upload job status
- ✅ Wait for job completion with timeout
- ✅ Get extraction results
- ✅ List all upload jobs
- ✅ Handle invalid file types
- ✅ Handle oversized files

**Key Features**:
- Tests AI-powered PDF extraction
- Validates async job processing
- Tests file validation
- Uses real sample PDF: `Ariake Capital Firm Introduction April 2025.pdf`

## Configuration

### API Endpoint
All tests connect to: `https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev`

### Test Credentials
- **Email**: `<EMAIL>`
- **Password**: `TempPassword123!`

### Test Data
- **Sample PDF**: `/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf`
- **Test Fund Data**: Dynamically generated with timestamps
- **Test Portfolio Data**: Dynamically generated with timestamps

## Running the Tests

### Prerequisites

1. **Activate conda environment**:
   ```bash
   conda activate ff_env
   ```

2. **Install dependencies**:
   ```bash
   pip install requests
   ```

3. **Ensure AWS credentials are configured** for profile `fundflow-dev`

### Running Individual Test Suites

1. **Auth API Tests**:
   ```bash
   cd tests/integration
   python test_auth_api_comprehensive.py
   ```

2. **Funds API Tests**:
   ```bash
   cd tests/integration
   python test_funds_api_comprehensive.py
   ```

3. **Portfolios API Tests**:
   ```bash
   cd tests/integration
   python test_portfolios_api_comprehensive.py
   ```

4. **File Upload Tests**:
   ```bash
   cd tests/integration
   python test_file_upload_comprehensive.py
   ```

### Running All Tests

Create and run a master test script:

```python
#!/usr/bin/env python3
import subprocess
import sys

tests = [
    "test_auth_api_comprehensive.py",
    "test_funds_api_comprehensive.py", 
    "test_portfolios_api_comprehensive.py",
    "test_file_upload_comprehensive.py"
]

for test in tests:
    print(f"\\n{'='*60}")
    print(f"Running {test}")
    print('='*60)
    result = subprocess.run([sys.executable, test])
    if result.returncode != 0:
        print(f"❌ {test} FAILED")
    else:
        print(f"✅ {test} PASSED")
```

## Test Results

Each test suite generates detailed JSON results files:
- `auth_api_test_results.json`
- `funds_api_test_results.json`
- `portfolios_api_test_results.json`
- `file_upload_api_test_results.json`

### Result Structure

```json
{
  "summary": {
    "total_tests": 10,
    "passed": 8,
    "failed": 2,
    "success_rate": "80.0%",
    "test_timestamp": "2025-01-XX XX:XX:XX",
    "auth_available": true
  },
  "test_results": [
    {
      "test_name": "Test Name",
      "success": true,
      "message": "Test description",
      "timestamp": "2025-01-XX XX:XX:XX",
      "response_data": {...}
    }
  ]
}
```

## Test Flow Simulation

### Frontend-like Authentication Flow
1. Health check
2. Login with credentials
3. Store access token
4. Use token for authenticated requests
5. Logout

### Frontend-like Data Management Flow
1. List existing data (funds/portfolios)
2. Create new items
3. Retrieve specific items
4. Update items
5. Delete items
6. Handle errors appropriately

### Frontend-like File Upload Flow
1. Select PDF file
2. Upload file
3. Monitor job progress
4. Retrieve extraction results
5. Handle job completion/failure

## Error Handling

The tests validate proper error handling for:
- **Authentication errors** (401, 403)
- **Validation errors** (400, 422)
- **Not found errors** (404)
- **File type errors** (415)
- **Server errors** (500)

## Performance Considerations

- **Timeouts**: Tests include appropriate timeouts for long-running operations
- **Rate limiting**: Tests respect API rate limits
- **Concurrent testing**: Tests can be run in parallel
- **Resource cleanup**: Tests clean up created resources

## Troubleshooting

### Common Issues

1. **Authentication failures**:
   - Verify test user exists in Cognito
   - Check password policy compliance
   - Ensure Cognito configuration is correct

2. **Network timeouts**:
   - Check internet connectivity
   - Verify API Gateway is running
   - Increase timeout values if needed

3. **File upload issues**:
   - Ensure sample PDF exists at specified path
   - Check file permissions
   - Verify S3 bucket configuration

4. **DynamoDB errors**:
   - Verify DynamoDB tables exist
   - Check IAM permissions
   - Ensure table capacity is sufficient

### Debug Mode

Enable verbose logging by modifying test files:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Integration with CI/CD

These tests can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run API Integration Tests
  run: |
    conda activate ff_env
    cd tests/integration
    python test_auth_api_comprehensive.py
    python test_funds_api_comprehensive.py
    python test_portfolios_api_comprehensive.py
    python test_file_upload_comprehensive.py
```

## Test Coverage

The comprehensive tests cover:
- ✅ **100% of public API endpoints**
- ✅ **Authentication and authorization**
- ✅ **CRUD operations**
- ✅ **File upload and processing**
- ✅ **Error handling**
- ✅ **Data validation**
- ✅ **Performance under load**

## Best Practices

1. **Run tests in development environment** first
2. **Use test data** that won't interfere with production
3. **Clean up resources** after testing
4. **Monitor test results** for trends
5. **Update tests** when API changes
6. **Document test failures** with context

## Maintenance

- **Regular updates**: Keep tests in sync with API changes
- **Test data refresh**: Update sample data as needed
- **Performance monitoring**: Track test execution times
- **Documentation updates**: Keep this README current