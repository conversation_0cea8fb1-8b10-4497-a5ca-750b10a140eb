{"summary": {"total_tests": 7, "passed": 2, "failed": 5, "success_rate": "28.6%", "test_timestamp": "2025-07-06 07:37:42"}, "test_results": [{"test_name": "Auth Health Check", "success": false, "message": "Unexpected status code: 403", "timestamp": "2025-07-06 07:37:39", "response_data": {"status_code": 403, "response": "{\"message\":\"Missing Authentication Token\"}"}}, {"test_name": "<PERSON><PERSON>", "success": false, "message": "Both Cognito and API login failed. API status: 403", "timestamp": "2025-07-06 07:37:41", "response_data": {"status_code": 403, "response": "{\"message\":\"Missing Authentication Token\"}"}}, {"test_name": "Auth User Profile", "success": false, "message": "No auth token available, skipping test", "timestamp": "2025-07-06 07:37:41", "response_data": {}}, {"test_name": "Auth Token Validation", "success": false, "message": "No auth token available, skipping test", "timestamp": "2025-07-06 07:37:41", "response_data": {}}, {"test_name": "<PERSON><PERSON>", "success": false, "message": "No auth token available, skipping test", "timestamp": "2025-07-06 07:37:41", "response_data": {}}, {"test_name": "Auth Invalid Credentials", "success": true, "message": "Correctly rejected invalid credentials with status: 403", "timestamp": "2025-07-06 07:37:41", "response_data": {"status_code": 403}}, {"test_name": "Auth Unauthorized Access", "success": true, "message": "Correctly rejected unauthorized access with status: 403", "timestamp": "2025-07-06 07:37:42", "response_data": {"status_code": 403}}]}