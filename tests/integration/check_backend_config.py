#!/usr/bin/env python3
"""
Backend Configuration Checker for AWS API Gateway Lambda
"""

import json
import requests
import boto3
from typing import Dict, Any, Optional


def check_lambda_environment():
    """Check Lambda function environment variables"""

    print("🔍 Checking Lambda Function Configuration...")
    print("=" * 50)

    lambda_client = boto3.client("lambda", region_name="ap-northeast-1")

    # Try to find the function (you may need to adjust the function name)
    function_names = [
        "fundflow-dev-funds-api",
        "funds-api",
        "FundFlowFundsAPI",
        "fundflow-funds",
    ]

    for function_name in function_names:
        try:
            print(f"   Checking function: {function_name}")
            response = lambda_client.get_function(FunctionName=function_name)

            env_vars = (
                response["Configuration"].get("Environment", {}).get("Variables", {})
            )

            print(f"   ✅ Found function: {function_name}")
            print(f"   Runtime: {response['Configuration']['Runtime']}")
            print(f"   Timeout: {response['Configuration']['Timeout']}s")

            # Check important environment variables
            required_vars = ["USER_POOL_ID", "USER_POOL_CLIENT_ID", "AWS_REGION"]

            print("   Environment Variables:")
            for var in required_vars:
                value = env_vars.get(var, "NOT SET")
                if var == "USER_POOL_CLIENT_ID":
                    expected = "2jh76f894g6lv9vrus4qbb9hu7"
                    status = "✅" if value == expected else "❌"
                    print(f"     {var}: {value} {status}")
                elif var == "USER_POOL_ID":
                    expected = "ap-northeast-1_H2kKHGUAT"
                    status = "✅" if value == expected else "❌"
                    print(f"     {var}: {value} {status}")
                else:
                    status = "✅" if value != "NOT SET" else "❌"
                    print(f"     {var}: {value} {status}")

            return function_name

        except Exception as e:
            print(f"   ❌ Function {function_name} not found: {str(e)}")
            continue

    print("   ❌ No Lambda functions found with expected names")
    return None


def check_api_gateway():
    """Check API Gateway configuration"""

    print("\n🔍 Checking API Gateway Configuration...")
    print("=" * 50)

    apigateway_client = boto3.client("apigateway", region_name="ap-northeast-1")

    try:
        # List REST APIs
        apis = apigateway_client.get_rest_apis()

        for api in apis["items"]:
            if "fundflow" in api["name"].lower() or "funds" in api["name"].lower():
                print(f"   ✅ Found API: {api['name']}")
                print(f"   API ID: {api['id']}")
                print(f"   Description: {api.get('description', 'N/A')}")

                # Check if this matches our URL
                expected_id = "b5uqilw5yk"
                if api["id"] == expected_id:
                    print(f"   ✅ Matches expected API ID: {expected_id}")
                else:
                    print(f"   ⚠️  API ID doesn't match expected: {expected_id}")

                return api["id"]

    except Exception as e:
        print(f"   ❌ Failed to check API Gateway: {str(e)}")

    return None


def check_cognito_configuration():
    """Check Cognito User Pool configuration"""

    print("\n🔍 Checking Cognito Configuration...")
    print("=" * 50)

    cognito_client = boto3.client("cognito-idp", region_name="ap-northeast-1")

    user_pool_id = "ap-northeast-1_H2kKHGUAT"
    client_id = "2jh76f894g6lv9vrus4qbb9hu7"

    try:
        # Check User Pool
        pool_info = cognito_client.describe_user_pool(UserPoolId=user_pool_id)

        print(f"   ✅ User Pool found: {user_pool_id}")
        print(f"   Pool name: {pool_info['UserPool']['Name']}")
        print(f"   Status: {pool_info['UserPool']['Status']}")

        # Check User Pool Client
        client_info = cognito_client.describe_user_pool_client(
            UserPoolId=user_pool_id, ClientId=client_id
        )

        client_details = client_info["UserPoolClient"]
        print(f"   ✅ Client found: {client_id}")
        print(f"   Client name: {client_details['ClientName']}")
        print(f"   Auth flows: {client_details.get('ExplicitAuthFlows', [])}")

        # Check if required auth flows are enabled
        required_flows = ["USER_PASSWORD_AUTH"]
        enabled_flows = client_details.get("ExplicitAuthFlows", [])

        for flow in required_flows:
            if flow in enabled_flows:
                print(f"     ✅ {flow} enabled")
            else:
                print(f"     ❌ {flow} not enabled")

        return True

    except Exception as e:
        print(f"   ❌ Failed to check Cognito: {str(e)}")
        return False


def test_token_validation():
    """Test token validation with both access and ID tokens"""

    print("\n🔍 Testing Token Validation...")
    print("=" * 50)

    cognito_client = boto3.client("cognito-idp", region_name="ap-northeast-1")

    client_id = "2jh76f894g6lv9vrus4qbb9hu7"
    test_email = "<EMAIL>"
    test_password = "TestPassword123!"

    try:
        # Get tokens
        response = cognito_client.initiate_auth(
            ClientId=client_id,
            AuthFlow="USER_PASSWORD_AUTH",
            AuthParameters={"USERNAME": test_email, "PASSWORD": test_password},
        )

        auth_result = response["AuthenticationResult"]
        access_token = auth_result["AccessToken"]
        id_token = auth_result.get("IdToken")

        print("   ✅ Authentication successful")

        # Test access token with get_user
        try:
            user_info = cognito_client.get_user(AccessToken=access_token)
            print("   ✅ Access token valid for get_user")
            print(f"   Username: {user_info['Username']}")
        except Exception as e:
            print(f"   ❌ Access token invalid: {str(e)}")

        # Decode tokens without verification to check contents
        import jwt

        print("\n   Token Analysis:")
        try:
            access_payload = jwt.decode(
                access_token, options={"verify_signature": False}
            )
            print(f"   Access token scope: {access_payload.get('scope')}")
            print(f"   Access token client_id: {access_payload.get('client_id')}")
            print(f"   Access token token_use: {access_payload.get('token_use')}")
        except Exception as e:
            print(f"   ❌ Failed to decode access token: {str(e)}")

        if id_token:
            try:
                id_payload = jwt.decode(id_token, options={"verify_signature": False})
                print(f"   ID token aud: {id_payload.get('aud')}")
                print(f"   ID token token_use: {id_payload.get('token_use')}")
                print(f"   ID token email: {id_payload.get('email')}")
            except Exception as e:
                print(f"   ❌ Failed to decode ID token: {str(e)}")

        return True

    except Exception as e:
        print(f"   ❌ Token validation test failed: {str(e)}")
        return False


def generate_fix_commands():
    """Generate commands to fix common issues"""

    print("\n🔧 Potential Fix Commands:")
    print("=" * 50)

    print("1. Update Lambda Environment Variables:")
    print("   aws lambda update-function-configuration \\")
    print("     --region ap-northeast-1 \\")
    print("     --function-name <FUNCTION_NAME> \\")
    print("     --environment Variables='{")
    print('       "USER_POOL_ID": "ap-northeast-1_H2kKHGUAT",')
    print('       "USER_POOL_CLIENT_ID": "2jh76f894g6lv9vrus4qbb9hu7",')
    print('       "AWS_REGION": "ap-northeast-1"')
    print("     }'")

    print("\n2. Ensure Cognito Auth Flows:")
    print("   aws cognito-idp update-user-pool-client \\")
    print("     --region ap-northeast-1 \\")
    print("     --user-pool-id ap-northeast-1_H2kKHGUAT \\")
    print("     --client-id 2jh76f894g6lv9vrus4qbb9hu7 \\")
    print("     --explicit-auth-flows USER_PASSWORD_AUTH ADMIN_USER_PASSWORD_AUTH")

    print("\n3. Check CloudWatch Logs:")
    print("   aws logs describe-log-groups \\")
    print("     --region ap-northeast-1 \\")
    print("     --log-group-name-prefix '/aws/lambda/fundflow'")


def main():
    """Main diagnostics function"""

    print("🚀 AWS API Gateway Backend Configuration Checker")
    print("=" * 60)

    try:
        lambda_function = check_lambda_environment()
        api_id = check_api_gateway()
        cognito_ok = check_cognito_configuration()
        token_ok = test_token_validation()

        print("\n📊 Summary:")
        print("=" * 20)
        print(f"Lambda Function: {'✅' if lambda_function else '❌'}")
        print(f"API Gateway: {'✅' if api_id else '❌'}")
        print(f"Cognito Config: {'✅' if cognito_ok else '❌'}")
        print(f"Token Validation: {'✅' if token_ok else '❌'}")

        if not all([lambda_function, api_id, cognito_ok, token_ok]):
            generate_fix_commands()
        else:
            print(
                "\n✅ All checks passed! The issue might be in the Lambda code itself."
            )

    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")


if __name__ == "__main__":
    main()
