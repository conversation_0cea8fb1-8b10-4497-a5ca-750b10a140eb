#!/usr/bin/env python3
"""
Comprehensive validation script for DynamoDB fund data.
This script handles all identified real-world data issues.
"""

import sys
import os
import boto3
import json
from decimal import Decimal
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone


# Add the src and shared directory to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
sys.path.insert(0, os.path.join(project_root, "src"))
sys.path.insert(0, project_root)


try:
    from shared.models.fund import (
        Fund,
        FundType,
        FundStatus,
        RiskLevel,
        Currency,
        PerformanceMetrics,
        Holdings,
    )
except ImportError as e:
    print(f"Error importing models: {e}")
    print("Make sure you're running this script from the project root directory")
    sys.exit(1)


# Custom JSON encoder
class ComprehensiveEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        if hasattr(obj, "isoformat"):
            return obj.isoformat()
        if hasattr(obj, "value"):  # Enum
            return obj.value
        return super(ComprehensiveEncoder, self).default(obj)


class ComprehensiveFundValidator:
    """A comprehensive validator that handles all real-world data issues."""

    def __init__(self):
        self.validation_results = []

    def _normalize_enum_fields(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize enum fields to ensure they're valid enum values."""
        # Map enum fields
        enum_mappings = {
            "fund_type": {
                "equity": FundType.EQUITY,
                "bond": FundType.BOND,
                "mixed": FundType.MIXED,
                "money_market": FundType.MONEY_MARKET,
                "alternative": FundType.ALTERNATIVE,
                "index": FundType.INDEX,
                "etf": FundType.ETF,
            },
            "status": {
                "active": FundStatus.ACTIVE,
                "inactive": FundStatus.INACTIVE,
                "suspended": FundStatus.SUSPENDED,
                "liquidating": FundStatus.LIQUIDATING,
                "closed": FundStatus.CLOSED,
            },
            "risk_level": {
                "very_low": RiskLevel.VERY_LOW,
                "low": RiskLevel.LOW,
                "moderate": RiskLevel.MODERATE,
                "high": RiskLevel.HIGH,
                "very_high": RiskLevel.VERY_HIGH,
            },
            "currency": {
                "USD": Currency.USD,
                "EUR": Currency.EUR,
                "GBP": Currency.GBP,
                "JPY": Currency.JPY,
                "CAD": Currency.CAD,
                "AUD": Currency.AUD,
                "CHF": Currency.CHF,
            },
        }

        for field, mapping in enum_mappings.items():
            if field in item and isinstance(item[field], str):
                if item[field] in mapping:
                    item[field] = mapping[item[field]]
                else:
                    # Default fallback for unmapped values
                    if field == "fund_type":
                        item[field] = FundType.MIXED
                    elif field == "status":
                        item[field] = FundStatus.ACTIVE
                    elif field == "risk_level":
                        item[field] = RiskLevel.MODERATE
                    elif field == "currency":
                        item[field] = Currency.USD

        return item

    def _convert_datetime_strings(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Convert datetime strings to timezone-aware datetime objects."""
        datetime_fields = ["created_at", "updated_at", "inception_date"]

        for field in datetime_fields:
            if field in item and isinstance(item[field], str):
                try:
                    # Parse ISO format datetime
                    dt_str = item[field].replace("Z", "+00:00")
                    dt = datetime.fromisoformat(dt_str)

                    # If datetime is naive (no timezone), assume UTC
                    if dt.tzinfo is None:
                        dt = dt.replace(tzinfo=timezone.utc)

                    item[field] = dt
                except ValueError as e:
                    print(f"Warning: Could not parse datetime field '{field}': {e}")

        return item

    def _convert_decimals(self, obj: Any) -> Any:
        """Convert string numbers to Decimal objects for relevant fields."""
        if isinstance(obj, dict):
            result = {}
            for key, value in obj.items():
                if key in [
                    "nav",
                    "total_assets",
                    "expense_ratio",
                    "minimum_investment",
                    "ytd_return",
                    "one_year_return",
                    "three_year_return",
                    "five_year_return",
                    "inception_return",
                    "volatility",
                    "sharpe_ratio",
                    "max_drawdown",
                ] and isinstance(value, (str, int, float)):
                    try:
                        result[key] = Decimal(str(value))
                    except:
                        result[key] = value
                elif key in [
                    "sector_allocation",
                    "geographic_allocation",
                ] and isinstance(value, dict):
                    result[key] = {
                        k: Decimal(str(v)) if isinstance(v, (str, int, float)) else v
                        for k, v in value.items()
                    }
                else:
                    result[key] = self._convert_decimals(value)
            return result
        elif isinstance(obj, list):
            return [self._convert_decimals(item) for item in obj]
        else:
            return obj

    def _create_flexible_holdings(
        self, holdings_data: Dict[str, Any]
    ) -> Optional[Holdings]:
        """Create holdings object with comprehensive validation and normalization."""
        if not holdings_data:
            return None

        try:
            # Create a copy to modify
            flexible_holdings = holdings_data.copy()

            # Normalize sector allocation to 100% if it's significantly off
            if "sector_allocation" in flexible_holdings:
                sector_alloc = flexible_holdings["sector_allocation"]
                if isinstance(sector_alloc, dict) and sector_alloc:
                    total = sum(Decimal(str(v)) for v in sector_alloc.values())
                    if total > 0:
                        # Always normalize to 100% for consistency
                        scale_factor = Decimal("100") / total
                        normalized = {
                            k: (Decimal(str(v)) * scale_factor).quantize(Decimal("0.1"))
                            for k, v in sector_alloc.items()
                        }
                        flexible_holdings["sector_allocation"] = normalized

            # Normalize geographic allocation to 100% if needed
            if "geographic_allocation" in flexible_holdings:
                geo_alloc = flexible_holdings["geographic_allocation"]
                if isinstance(geo_alloc, dict) and geo_alloc:
                    total = sum(Decimal(str(v)) for v in geo_alloc.values())
                    if total > 0:
                        # Always normalize to 100% for consistency
                        scale_factor = Decimal("100") / total
                        normalized = {
                            k: (Decimal(str(v)) * scale_factor).quantize(Decimal("0.1"))
                            for k, v in geo_alloc.items()
                        }
                        flexible_holdings["geographic_allocation"] = normalized

            return Holdings(**flexible_holdings)

        except Exception as e:
            print(f"Warning: Could not create Holdings object: {e}")
            # Return a minimal holdings object if creation fails
            return Holdings()

    def validate_fund(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a single fund with comprehensive handling."""
        fund_id = item.get("fund_id", "unknown")
        result = {
            "fund_id": fund_id,
            "valid": False,
            "errors": [],
            "warnings": [],
            "adjustments_made": [],
        }

        try:
            # Prepare the item for validation
            processed_item = item.copy()

            # Step 1: Normalize enums
            processed_item = self._normalize_enum_fields(processed_item)

            # Step 2: Handle datetime conversion
            processed_item = self._convert_datetime_strings(processed_item)

            # Step 3: Convert decimal fields
            processed_item = self._convert_decimals(processed_item)

            # Step 4: Handle holdings separately with flexible validation
            original_holdings = processed_item.pop("holdings", None)
            flexible_holdings = self._create_flexible_holdings(original_holdings)

            if original_holdings and flexible_holdings:
                # Check if we made adjustments
                if "sector_allocation" in original_holdings:
                    orig_total = sum(
                        Decimal(str(v))
                        for v in original_holdings["sector_allocation"].values()
                    )
                    if abs(orig_total - 100) > 1:  # If more than 1% off
                        result["adjustments_made"].append(
                            f"Normalized sector allocation from {orig_total}% to 100%"
                        )

                if "geographic_allocation" in original_holdings:
                    orig_total = sum(
                        Decimal(str(v))
                        for v in original_holdings["geographic_allocation"].values()
                    )
                    if abs(orig_total - 100) > 1:  # If more than 1% off
                        result["adjustments_made"].append(
                            f"Normalized geographic allocation from {orig_total}% to 100%"
                        )

            # Step 5: Create the fund object
            processed_item["holdings"] = flexible_holdings
            fund = Fund(**processed_item)

            # Validation successful
            result["valid"] = True
            result["fund_name"] = fund.name
            result["fund_type"] = (
                fund.fund_type.value
                if hasattr(fund.fund_type, "value")
                else str(fund.fund_type)
            )
            result["status"] = (
                fund.status.value if hasattr(fund.status, "value") else str(fund.status)
            )
            result["nav"] = float(fund.nav) if fund.nav else None
            result["total_assets"] = (
                float(fund.total_assets) if fund.total_assets else None
            )

            # Additional quality checks
            if fund.nav and fund.nav <= 0:
                result["warnings"].append("NAV is zero or negative")

            if fund.total_assets and fund.total_assets <= 0:
                result["warnings"].append("Total assets is zero or negative")

            if not fund.fund_manager:
                result["warnings"].append("Fund manager is missing")

            if fund.expense_ratio and fund.expense_ratio > Decimal("3.0"):
                result["warnings"].append(f"High expense ratio: {fund.expense_ratio}%")

            # Performance metrics checks
            if fund.performance_metrics:
                pm = fund.performance_metrics
                if pm.volatility and pm.volatility > 50:
                    result["warnings"].append(f"Very high volatility: {pm.volatility}%")

                if pm.sharpe_ratio and pm.sharpe_ratio < 0:
                    result["warnings"].append(
                        f"Negative Sharpe ratio: {pm.sharpe_ratio}"
                    )

        except Exception as e:
            result["valid"] = False
            result["errors"].append(str(e))

            # Try to extract basic info
            try:
                result["fund_name"] = item.get("name", "Unknown")
                result["fund_type"] = item.get("fund_type", "unknown")
                result["status"] = item.get("status", "unknown")
            except:
                pass

        return result


def main():
    print("🔍 Starting Comprehensive DynamoDB Fund Data Validation")
    print("=" * 70)

    try:
        # Initialize DynamoDB
        session = boto3.Session(profile_name="fundflow-dev")
        dynamodb = session.resource("dynamodb", region_name="ap-northeast-1")
        table = dynamodb.Table("fundflow-dev-funds")

        limit = 30  # Test more records
        print(f"📊 Scanning table: fundflow-dev-funds (limit: {limit})")
        print("-" * 70)

        response = table.scan(Limit=limit)
        items = response.get("Items", [])

        if not items:
            print("❌ No items found in the table.")
            return

        print(f"Found {len(items)} items to validate\n")

        # Initialize validator
        validator = ComprehensiveFundValidator()

        # Results tracking
        valid_funds = []
        invalid_funds = []
        funds_with_warnings = []
        funds_with_adjustments = []

        # Validate each fund
        for i, item in enumerate(items, 1):
            fund_id = item.get("fund_id", f"unknown-{i}")
            print(f"Validating fund {i}/{len(items)}: {fund_id[:20]}...")

            result = validator.validate_fund(item)

            if result["valid"]:
                valid_funds.append(result)
                print(f"  ✅ Valid (Name: {result.get('fund_name', 'N/A')[:25]})")

                if result["warnings"]:
                    funds_with_warnings.append(result)
                    print(f"  ⚠️  {len(result['warnings'])} warnings")

                if result["adjustments_made"]:
                    funds_with_adjustments.append(result)
                    print(f"  🔧 {len(result['adjustments_made'])} adjustments made")
            else:
                invalid_funds.append(result)
                error_msg = result["errors"][0] if result["errors"] else "Unknown error"
                print(f"  ❌ Invalid: {error_msg[:40]}...")

        # Summary
        print("\n" + "=" * 70)
        print("📋 COMPREHENSIVE VALIDATION SUMMARY")
        print("=" * 70)

        total_funds = len(items)
        valid_count = len(valid_funds)
        invalid_count = len(invalid_funds)
        warning_count = len(funds_with_warnings)
        adjustment_count = len(funds_with_adjustments)

        print(f"Total funds scanned: {total_funds}")
        print(f"✅ Valid funds: {valid_count} ({valid_count/total_funds*100:.1f}%)")
        print(
            f"❌ Invalid funds: {invalid_count} ({invalid_count/total_funds*100:.1f}%)"
        )
        print(
            f"⚠️  Funds with warnings: {warning_count} ({warning_count/total_funds*100:.1f}%)"
        )
        print(
            f"🔧 Funds with adjustments: {adjustment_count} ({adjustment_count/total_funds*100:.1f}%)"
        )

        # Show sample valid funds
        if valid_funds:
            print(f"\n✅ SAMPLE VALID FUNDS:")
            print("-" * 50)
            for fund in valid_funds[:5]:  # Show first 5
                print(f"• {fund['fund_name'][:40]} ({fund['fund_type']})")
                if fund.get("nav"):
                    print(f"  NAV: ${fund['nav']:.2f}")
                if fund.get("total_assets"):
                    print(f"  Total Assets: ${fund['total_assets']:,.2f}")
                print()

        # Show adjustments made
        if funds_with_adjustments:
            print(f"🔧 SAMPLE ADJUSTMENTS MADE:")
            print("-" * 50)
            for fund in funds_with_adjustments[:3]:  # Show first 3
                print(f"Fund: {fund['fund_name'][:35]}")
                for adjustment in fund["adjustments_made"]:
                    print(f"  • {adjustment}")
                print()

        # Show remaining errors (if any)
        if invalid_funds:
            print(f"❌ REMAINING INVALID FUNDS:")
            print("-" * 50)
            for fund in invalid_funds[:3]:  # Show first 3
                print(f"Fund: {fund.get('fund_name', 'N/A')[:35]}")
                for error in fund["errors"][:1]:  # Show first error only
                    print(f"  • {error[:60]}...")
                print()

        # Distribution statistics
        if valid_funds:
            print("📊 FUND TYPE DISTRIBUTION:")
            print("-" * 50)
            type_counts = {}
            for fund in valid_funds:
                fund_type = fund.get("fund_type", "unknown")
                type_counts[fund_type] = type_counts.get(fund_type, 0) + 1

            for fund_type, count in sorted(type_counts.items()):
                percentage = (count / len(valid_funds)) * 100
                print(f"  {fund_type}: {count} ({percentage:.1f}%)")

        print(f"\n✅ Comprehensive validation completed!")

        # Save results
        results_file = "comprehensive_validation_results.json"
        detailed_results = {
            "summary": {
                "total_funds": total_funds,
                "valid_funds": valid_count,
                "invalid_funds": invalid_count,
                "funds_with_warnings": warning_count,
                "funds_with_adjustments": adjustment_count,
                "validation_date": datetime.now().isoformat(),
                "validation_success_rate": f"{(valid_count/total_funds)*100:.1f}%",
            },
            "valid_funds": valid_funds[:10],  # Save first 10 for file size
            "invalid_funds": invalid_funds,
            "type_distribution": (
                {fund_type: count for fund_type, count in type_counts.items()}
                if valid_funds
                else {}
            ),
        }

        with open(results_file, "w") as f:
            json.dump(detailed_results, f, indent=2, cls=ComprehensiveEncoder)

        print(f"📁 Detailed results saved to: {results_file}")

    except Exception as e:
        print(f"❌ Error during validation: {str(e)}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
