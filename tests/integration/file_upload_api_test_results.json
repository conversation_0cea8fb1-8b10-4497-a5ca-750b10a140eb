{"summary": {"total_tests": 8, "passed": 0, "failed": 8, "success_rate": "0.0%", "test_timestamp": "2025-07-06 07:38:49", "auth_available": false, "test_pdf_available": true, "test_pdf_path": "/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"}, "test_results": [{"test_name": "Upload Health Check", "success": false, "message": "Unexpected status code: 403", "timestamp": "2025-07-06 07:38:49", "response_data": {"status_code": 403, "response": "{\"message\":\"Missing Authentication Token\"}"}}, {"test_name": "Upload PDF File", "success": false, "message": "No authentication token available", "timestamp": "2025-07-06 07:38:49", "response_data": {}}, {"test_name": "Check Upload Job Status", "success": false, "message": "No upload job ID available", "timestamp": "2025-07-06 07:38:49", "response_data": {}}, {"test_name": "Wait for Job Completion", "success": false, "message": "No upload job ID available", "timestamp": "2025-07-06 07:38:49", "response_data": {}}, {"test_name": "Get Extraction Result", "success": false, "message": "No upload job ID available", "timestamp": "2025-07-06 07:38:49", "response_data": {}}, {"test_name": "List Upload Jobs", "success": false, "message": "Failed to list upload jobs. Status: 403", "timestamp": "2025-07-06 07:38:49", "response_data": {"status_code": 403, "response": "{\"message\":\"Missing Authentication Token\"}"}}, {"test_name": "Upload Invalid File", "success": false, "message": "No authentication token available", "timestamp": "2025-07-06 07:38:49", "response_data": {}}, {"test_name": "Upload Oversized File", "success": false, "message": "No authentication token available", "timestamp": "2025-07-06 07:38:49", "response_data": {}}]}