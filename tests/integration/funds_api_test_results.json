{"summary": {"total_tests": 10, "passed": 0, "failed": 10, "success_rate": "0.0%", "test_timestamp": "2025-07-06 07:38:01", "auth_available": false}, "test_results": [{"test_name": "Funds Health Check", "success": false, "message": "Unexpected status code: 401", "timestamp": "2025-07-06 07:38:00", "response_data": {"status_code": 401, "response": "{\"message\":\"Unauthorized\"}"}}, {"test_name": "List Funds", "success": false, "message": "Failed to list funds. Status: 401", "timestamp": "2025-07-06 07:38:00", "response_data": {"status_code": 401, "response": "{\"message\":\"Unauthorized\"}"}}, {"test_name": "List Funds with Filter: {'fund_type': 'equity'}", "success": false, "message": "Failed to list funds with filter. Status: 401", "timestamp": "2025-07-06 07:38:00", "response_data": {"filter": {"fund_type": "equity"}, "status_code": 401}}, {"test_name": "List Funds with Filter: {'status': 'active'}", "success": false, "message": "Failed to list funds with filter. Status: 401", "timestamp": "2025-07-06 07:38:00", "response_data": {"filter": {"status": "active"}, "status_code": 401}}, {"test_name": "List Funds with Filter: {'limit': '5'}", "success": false, "message": "Failed to list funds with filter. Status: 401", "timestamp": "2025-07-06 07:38:00", "response_data": {"filter": {"limit": "5"}, "status_code": 401}}, {"test_name": "List Funds with Filter: {'fund_type': 'bond', 'status': 'active'}", "success": false, "message": "Failed to list funds with filter. Status: 401", "timestamp": "2025-07-06 07:38:00", "response_data": {"filter": {"fund_type": "bond", "status": "active"}, "status_code": 401}}, {"test_name": "Get Fund by ID", "success": false, "message": "Could not get fund list to test individual fund retrieval", "timestamp": "2025-07-06 07:38:01", "response_data": {}}, {"test_name": "Get Fund Market Data", "success": false, "message": "Could not get fund list for market data test", "timestamp": "2025-07-06 07:38:01", "response_data": {}}, {"test_name": "Search Funds: 'equity'", "success": false, "message": "Search failed. Status: 401", "timestamp": "2025-07-06 07:38:01", "response_data": {"query": "equity", "status_code": 401}}, {"test_name": "Search Funds: 'bond'", "success": false, "message": "Search failed. Status: 401", "timestamp": "2025-07-06 07:38:01", "response_data": {"query": "bond", "status_code": 401}}, {"test_name": "Search Funds: 'growth'", "success": false, "message": "Search failed. Status: 401", "timestamp": "2025-07-06 07:38:01", "response_data": {"query": "growth", "status_code": 401}}, {"test_name": "Search Funds: 'income'", "success": false, "message": "Search failed. Status: 401", "timestamp": "2025-07-06 07:38:01", "response_data": {"query": "income", "status_code": 401}}, {"test_name": "Create Fund", "success": false, "message": "No authentication token available", "timestamp": "2025-07-06 07:38:01", "response_data": {}}, {"test_name": "Update Fund", "success": false, "message": "No authentication token available", "timestamp": "2025-07-06 07:38:01", "response_data": {}}, {"test_name": "Delete Fund", "success": false, "message": "No authentication token available", "timestamp": "2025-07-06 07:38:01", "response_data": {}}, {"test_name": "Invalid Fund ID", "success": false, "message": "Unexpected response for invalid fund ID. Status: 401", "timestamp": "2025-07-06 07:38:01", "response_data": {"fund_id": "invalid-fund-id-12345", "status_code": 401}}]}