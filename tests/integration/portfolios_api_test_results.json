{"summary": {"total_tests": 11, "passed": 0, "failed": 11, "success_rate": "0.0%", "test_timestamp": "2025-07-06 07:38:34", "auth_available": false, "sample_fund_available": false}, "test_results": [{"test_name": "Portfolios Health Check", "success": false, "message": "Unexpected status code: 401", "timestamp": "2025-07-06 07:38:34", "response_data": {"status_code": 401, "response": "{\"message\":\"Unauthorized\"}"}}, {"test_name": "List Portfolios", "success": false, "message": "Failed to list portfolios. Status: 401", "timestamp": "2025-07-06 07:38:34", "response_data": {"status_code": 401, "response": "{\"message\":\"Unauthorized\"}"}}, {"test_name": "Create Portfolio", "success": false, "message": "No authentication token available", "timestamp": "2025-07-06 07:38:34", "response_data": {}}, {"test_name": "Get Portfolio by ID", "success": false, "message": "No portfolio ID available for testing", "timestamp": "2025-07-06 07:38:34", "response_data": {}}, {"test_name": "Update Portfolio", "success": false, "message": "No authentication token available", "timestamp": "2025-07-06 07:38:34", "response_data": {}}, {"test_name": "Add Fund to Portfolio", "success": false, "message": "No authentication token available", "timestamp": "2025-07-06 07:38:34", "response_data": {}}, {"test_name": "Get Portfolio Holdings", "success": false, "message": "No portfolio ID available", "timestamp": "2025-07-06 07:38:34", "response_data": {}}, {"test_name": "Get Portfolio Performance", "success": false, "message": "No portfolio ID available", "timestamp": "2025-07-06 07:38:34", "response_data": {}}, {"test_name": "Rebalance Portfolio", "success": false, "message": "No authentication token available", "timestamp": "2025-07-06 07:38:34", "response_data": {}}, {"test_name": "Delete Portfolio", "success": false, "message": "No authentication token available", "timestamp": "2025-07-06 07:38:34", "response_data": {}}, {"test_name": "Invalid Portfolio ID", "success": false, "message": "Unexpected response for invalid portfolio ID. Status: 401", "timestamp": "2025-07-06 07:38:34", "response_data": {"portfolio_id": "invalid-portfolio-id-12345", "status_code": 401}}]}