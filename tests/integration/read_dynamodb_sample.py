#!/usr/bin/env python3
"""
Simple script to read the first 10 rows from fundflow-dev-funds DynamoDB table
"""

import boto3
import json
from decimal import Decimal


# Custom JSON encoder to handle Decimal types from DynamoDB
class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super(DecimalEncoder, self).default(obj)


def read_dynamodb_sample():
    try:
        # Initialize DynamoDB client using the fundflow-dev profile
        # AWS credentials will be loaded from ~/.aws/credentials
        session = boto3.Session(profile_name="fundflow-dev")
        dynamodb = session.resource("dynamodb", region_name="ap-northeast-1")

        # Get the table
        table = dynamodb.Table("fundflow-dev-funds")

        print("Connecting to DynamoDB table: fundflow-dev-funds")
        print("=" * 50)

        # Scan the table to get first 10 items
        response = table.scan(Limit=10)

        items = response.get("Items", [])

        if not items:
            print("No items found in the table.")
            return

        print(f"Found {len(items)} items:")
        print("=" * 50)

        # Print each item
        for i, item in enumerate(items, 1):
            print(f"\nItem {i}:")
            print("-" * 30)
            # Convert to JSON string for pretty printing
            print(json.dumps(item, indent=2, cls=DecimalEncoder))

        print(f"\n" + "=" * 50)
        print(f"Successfully retrieved {len(items)} items from the table.")

    except Exception as e:
        print(f"Error reading from DynamoDB: {str(e)}")
        print("Please ensure:")
        print("1. AWS credentials are properly configured in ~/.aws/credentials")
        print("2. You have the necessary permissions to access the table")
        print("3. The table 'fundflow-dev-funds' exists in the ap-northeast-1 region")


if __name__ == "__main__":
    read_dynamodb_sample()
