#!/usr/bin/env python3
"""
FundFlow API Testing Script
Tests deployed API endpoints to verify functionality.
"""

import json
import requests
import sys
from typing import Dict, Any


# API Configuration
API_BASE_URL = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"

# Test data
TEST_USER = {
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "given_name": "Test",
    "family_name": "User",
}


def make_request(
    method: str,
    endpoint: str,
    data: Dict[Any, Any] = None,
    headers: Dict[str, str] = None,
) -> Dict[str, Any]:
    """Make HTTP request to API endpoint."""
    url = f"{API_BASE_URL}{endpoint}"

    default_headers = {"Content-Type": "application/json"}

    if headers:
        default_headers.update(headers)

    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=default_headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=default_headers)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=default_headers)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=default_headers)
        else:
            return {"error": f"Unsupported method: {method}"}

        result = {
            "status_code": response.status_code,
            "url": url,
            "method": method.upper(),
        }

        try:
            result["response"] = response.json()
        except:
            result["response"] = response.text

        return result

    except requests.exceptions.RequestException as e:
        return {"error": str(e), "url": url, "method": method.upper()}


def test_health_endpoint():
    """Test health endpoint."""
    print("🔍 Testing Health Endpoint...")
    result = make_request("GET", "/health")

    if result.get("status_code") == 200:
        print("✅ Health endpoint working")
        return True
    else:
        print(f"❌ Health endpoint failed: {result}")
        return False


def test_unauthenticated_endpoints():
    """Test endpoints that don't require authentication."""
    print("\n🔍 Testing Unauthenticated Endpoints...")

    # Test forgot password endpoint (should be available without auth)
    print("Testing forgot password endpoint...")
    forgot_data = {"email": "<EMAIL>"}
    result = make_request("POST", "/auth/forgot-password", forgot_data)

    if result.get("status_code") in [
        200,
        400,
        404,
    ]:  # Any response except 401/403 means endpoint is accessible
        print(
            f"✅ Forgot password endpoint accessible (status: {result.get('status_code')})"
        )
        if result.get("status_code") == 400:
            print(f"   Response: {result.get('response')}")
    else:
        print(f"❌ Forgot password endpoint not accessible: {result}")


def test_authenticated_endpoints():
    """Test endpoints that require authentication."""
    print("\n🔍 Testing Authenticated Endpoints (should return 401)...")

    endpoints_to_test = [
        ("GET", "/funds"),
        ("GET", "/users"),
        ("GET", "/reports"),
    ]

    for method, endpoint in endpoints_to_test:
        print(f"Testing {method} {endpoint}...")
        result = make_request(method, endpoint)

        if result.get("status_code") == 401:
            print(f"✅ {endpoint} properly protected (401 Unauthorized)")
        else:
            print(f"❌ {endpoint} unexpected response: {result}")


def test_cognito_integration():
    """Test Cognito User Pool configuration."""
    print("\n🔍 Testing Cognito Integration...")

    # Test user registration (should work but user might already exist)
    print("Testing user registration...")
    register_data = {
        "email": TEST_USER["email"],
        "password": TEST_USER["password"],
        "given_name": TEST_USER["given_name"],
        "family_name": TEST_USER["family_name"],
    }

    result = make_request("POST", "/auth/register", register_data)

    if result.get("status_code") in [200, 201, 400]:  # Success or user already exists
        print(
            f"✅ Registration endpoint accessible (status: {result.get('status_code')})"
        )
        if result.get("response"):
            print(f"   Response: {json.dumps(result.get('response'), indent=2)}")
    else:
        print(f"❌ Registration endpoint error: {result}")


def test_database_connectivity():
    """Test if Lambda functions can connect to DynamoDB."""
    print("\n🔍 Testing Database Connectivity...")

    # Check if we have any test endpoints that might show DB status
    result = make_request("GET", "/health")

    if result.get("status_code") == 200 and result.get("response"):
        response = result.get("response")
        if isinstance(response, dict):
            checks = response.get("checks", {})
            dynamodb_check = checks.get("dynamodb", {})

            if dynamodb_check.get("status") == "healthy":
                print("✅ DynamoDB connectivity working")
            else:
                print(f"⚠️  DynamoDB status: {dynamodb_check}")
        else:
            print("ℹ️  Health endpoint response format not as expected")


def main():
    """Run all tests."""
    print("🚀 FundFlow API Test Suite")
    print("=" * 50)

    tests = [
        test_health_endpoint,
        test_unauthenticated_endpoints,
        test_authenticated_endpoints,
        test_cognito_integration,
        test_database_connectivity,
    ]

    for test in tests:
        try:
            test()
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with error: {e}")

    print("\n" + "=" * 50)
    print("🏁 Test suite completed")

    print(f"\n📋 Summary:")
    print(f"   API Base URL: {API_BASE_URL}")
    print(f"   Frontend URL: http://localhost:3000")
    print(f"   \n🔗 You can now:")
    print(f"   1. Open http://localhost:3000 in your browser")
    print(f"   2. Test the authentication UI")
    print(f"   3. Register a new user through the frontend")


if __name__ == "__main__":
    main()
