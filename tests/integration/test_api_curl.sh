#!/bin/bash

# AWS API Gateway Funds API Test Script (curl-based)
# This script tests the AWS API Gateway endpoints using curl commands

set -e  # Exit on any error

# Configuration
BASE_URL="https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
REGION="ap-northeast-1"
USER_POOL_ID="ap-northeast-1_H2kKHGUAT"
CLIENT_ID="2jh76f894g6lv9vrus4qbb9hu7"  # CloudFormation client ID
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="TestPassword123!"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting AWS API Gateway Funds API Test Suite${NC}"
echo "============================================================"

# Function to try different authentication flows
authenticate_cognito() {
    echo -e "\n${YELLOW}🔐 Step 1: Authenticating with AWS Cognito...${NC}"
    
    # Array of auth flows to try
    AUTH_FLOWS=(
        "ADMIN_USER_PASSWORD_AUTH"
        "USER_PASSWORD_AUTH"
        "ALLOW_ADMIN_USER_PASSWORD_AUTH"
        "ALLOW_USER_PASSWORD_AUTH"
    )
    
    for auth_flow in "${AUTH_FLOWS[@]}"; do
        echo -e "   Trying $auth_flow flow..."
        
        if [[ $auth_flow == ADMIN_* ]]; then
            # Use admin authentication
            TOKEN_RESPONSE=$(aws cognito-idp admin-initiate-auth \
                --region "$REGION" \
                --user-pool-id "$USER_POOL_ID" \
                --client-id "$CLIENT_ID" \
                --auth-flow "$auth_flow" \
                --auth-parameters "USERNAME=$TEST_EMAIL,PASSWORD=$TEST_PASSWORD" \
                --output json 2>/dev/null || echo '{"error": "authentication_failed"}')
        else
            # Use regular authentication
            TOKEN_RESPONSE=$(aws cognito-idp initiate-auth \
                --region "$REGION" \
                --client-id "$CLIENT_ID" \
                --auth-flow "$auth_flow" \
                --auth-parameters "USERNAME=$TEST_EMAIL,PASSWORD=$TEST_PASSWORD" \
                --output json 2>/dev/null || echo '{"error": "authentication_failed"}')
        fi
        
        if ! echo "$TOKEN_RESPONSE" | grep -q "error"; then
            ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.AuthenticationResult.AccessToken' 2>/dev/null)
            
            if [ "$ACCESS_TOKEN" != "null" ] && [ -n "$ACCESS_TOKEN" ]; then
                echo -e "   ${GREEN}✅ Authentication successful using $auth_flow!${NC}"
                return 0
            fi
        fi
        
        echo -e "   ${RED}❌ $auth_flow failed${NC}"
    done
    
    echo -e "${RED}❌ All authentication flows failed${NC}"
    return 1
}

# Function to get manual token input
get_manual_token() {
    echo -e "\n${YELLOW}🔑 Manual token input option:${NC}"
    echo "You can get a token by:"
    echo "1. Logging into the frontend (http://localhost:3000)"
    echo "2. Opening browser DevTools > Application > Local Storage"
    echo "3. Finding the JWT token"
    echo ""
    
    read -p "Enter JWT token (or press Enter to skip): " manual_token
    
    if [ -n "$manual_token" ]; then
        ACCESS_TOKEN="$manual_token"
        echo -e "${GREEN}✅ Manual token set! Testing authentication...${NC}"
        
        # Test the token with a simple API call
        test_response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
            -H "Authorization: Bearer $ACCESS_TOKEN" \
            -H "Content-Type: application/json" \
            "$BASE_URL/funds")
        
        test_code=$(echo "$test_response" | tail -n1 | sed 's/.*HTTP_STATUS://')
        
        if [ "$test_code" = "401" ]; then
            echo -e "${RED}❌ Token appears to be invalid or expired${NC}"
            return 1
        else
            echo -e "${GREEN}✅ Token appears to be working!${NC}"
            return 0
        fi
    else
        echo -e "${YELLOW}⏭️  Skipping manual token input${NC}"
        return 1
    fi
}

# Try authentication methods
ACCESS_TOKEN=""

if authenticate_cognito; then
    echo -e "${GREEN}✅ Using Cognito authentication${NC}"
elif get_manual_token; then
    echo -e "${GREEN}✅ Using manual token${NC}"
else
    echo -e "${RED}❌ No authentication available. Running limited tests...${NC}"
fi

# Function to make authenticated API requests
make_request() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local description="$4"
    
    echo -e "\n${BLUE}📡 $description${NC}"
    echo "   $method $BASE_URL$endpoint"
    
    if [ -n "$ACCESS_TOKEN" ]; then
        auth_header="Authorization: Bearer $ACCESS_TOKEN"
    else
        auth_header=""
    fi
    
    if [ -n "$data" ]; then
        echo "   Request body: $data"
        if [ -n "$auth_header" ]; then
            response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
                -X "$method" \
                -H "$auth_header" \
                -H "Content-Type: application/json" \
                -d "$data" \
                "$BASE_URL$endpoint")
        else
            response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
                -X "$method" \
                -H "Content-Type: application/json" \
                -d "$data" \
                "$BASE_URL$endpoint")
        fi
    else
        if [ -n "$auth_header" ]; then
            response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
                -X "$method" \
                -H "$auth_header" \
                -H "Content-Type: application/json" \
                "$BASE_URL$endpoint")
        else
            response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
                -X "$method" \
                -H "Content-Type: application/json" \
                "$BASE_URL$endpoint")
        fi
    fi
    
    http_code=$(echo "$response" | tail -n1 | sed 's/.*HTTP_STATUS://')
    body=$(echo "$response" | head -n -1)
    
    echo "   Status: $http_code"
    echo "   Response: $body" | jq '.' 2>/dev/null || echo "   Response: $body"
    
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        echo -e "   ${GREEN}✅ Success${NC}"
        return 0
    else
        echo -e "   ${RED}❌ Failed${NC}"
        return 1
    fi
}

# Test Results Tracking
test_results=()

# Step 2: Test Unauthorized Access
echo -e "\n${YELLOW}🧪 Step 2: Testing Unauthorized Access${NC}"
echo "=================================================="

echo -e "\n${BLUE}📡 Testing unauthorized access (should fail)${NC}"
unauth_response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
    -X GET \
    -H "Content-Type: application/json" \
    "$BASE_URL/funds")

unauth_code=$(echo "$unauth_response" | tail -n1 | sed 's/.*HTTP_STATUS://')
unauth_body=$(echo "$unauth_response" | head -n -1)

echo "   GET $BASE_URL/funds (no auth)"
echo "   Status: $unauth_code"
echo "   Response: $unauth_body" | jq '.' 2>/dev/null || echo "   Response: $unauth_body"

if [ "$unauth_code" = "401" ]; then
    echo -e "   ${GREEN}✅ Correctly rejected unauthorized request${NC}"
else
    echo -e "   ${RED}❌ Should have rejected unauthorized request${NC}"
fi

# Only run authenticated tests if we have a token
if [ -n "$ACCESS_TOKEN" ]; then
    echo -e "\n${GREEN}🔓 Running authenticated tests...${NC}"
    
    # Step 3: Test List Funds
    echo -e "\n${YELLOW}🧪 Step 3: Testing List Funds${NC}"
    echo "================================="
    
    if make_request "GET" "/funds" "" "List all funds"; then
        test_results+=("List Funds: PASS")
    else
        test_results+=("List Funds: FAIL")
    fi
    
    # Test with pagination
    if make_request "GET" "/funds?page=1&page_size=5" "" "List funds with pagination"; then
        test_results+=("List Funds (Pagination): PASS")
    else
        test_results+=("List Funds (Pagination): FAIL")
    fi
    
    # Test with filters
    if make_request "GET" "/funds?status=ACTIVE&fund_type=EQUITY&search=HDFC" "" "List funds with filters"; then
        test_results+=("List Funds (Filters): PASS")
    else
        test_results+=("List Funds (Filters): FAIL")
    fi
    
    # Step 4: Get a specific fund (get fund ID from list first)
    echo -e "\n${YELLOW}🧪 Step 4: Testing Get Specific Fund${NC}"
    echo "====================================="
    
    # Get first fund ID from the list
    fund_list_response=$(curl -s \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -H "Content-Type: application/json" \
        "$BASE_URL/funds?page_size=1")
    
    fund_id=$(echo "$fund_list_response" | jq -r '.data.funds[0].fund_id' 2>/dev/null || echo "null")
    
    if [ "$fund_id" != "null" ] && [ -n "$fund_id" ]; then
        echo "   Using fund_id: $fund_id"
        if make_request "GET" "/funds/$fund_id" "" "Get specific fund"; then
            test_results+=("Get Fund: PASS")
        else
            test_results+=("Get Fund: FAIL")
        fi
    else
        echo -e "${RED}❌ No funds available to test get operation${NC}"
        test_results+=("Get Fund: SKIP")
    fi
    
    # Test non-existent fund
    if make_request "GET" "/funds/NON-EXISTENT-FUND" "" "Get non-existent fund (should fail)"; then
        test_results+=("Get Non-existent Fund: UNEXPECTED_PASS")
    else
        test_results+=("Get Non-existent Fund: PASS")
    fi
    
    # Ask user if they want to run write tests
    echo -e "\n${YELLOW}🤔 Do you want to run write tests (create, update, delete)?${NC}"
    echo "This will create test data in your system."
    read -p "Run write tests? (y/N): " run_writes
    
    if [[ $run_writes =~ ^[Yy]$ ]]; then
        # Step 5: Test Create Fund
        echo -e "\n${YELLOW}🧪 Step 5: Testing Create Fund${NC}"
        echo "================================"
        
        timestamp=$(date +%s)
        create_fund_data='{
            "fund_id": "TEST-FUND-'$timestamp'",
            "fund_name": "Test Fund '$timestamp'",
            "fund_type": "EQUITY",
            "status": "ACTIVE",
            "nav": 125.75,
            "expense_ratio": 1.25,
            "min_investment": 1000,
            "fund_manager": "Test Fund Manager",
            "inception_date": "2024-01-01",
            "description": "Test fund created by curl script",
            "benchmark": "NIFTY 50",
            "risk_level": "MODERATE",
            "holdings": [
                {
                    "security_name": "Test Security 1",
                    "percentage": 15.5,
                    "sector": "Technology"
                }
            ],
            "performance": {
                "1_year": 12.5,
                "3_year": 15.2,
                "5_year": 18.7,
                "inception": 20.1
            }
        }'
        
        if make_request "POST" "/funds" "$create_fund_data" "Create new fund"; then
            test_results+=("Create Fund: PASS")
            created_fund_id="TEST-FUND-$timestamp"
        else
            test_results+=("Create Fund: FAIL")
            created_fund_id=""
        fi
    else
        echo -e "${YELLOW}⏭️  Skipping write tests${NC}"
    fi
    
else
    echo -e "\n${RED}⚠️  No authentication token available. Skipping authenticated tests.${NC}"
    echo "To run full tests:"
    echo "1. Fix Cognito client authentication flows, OR"
    echo "2. Get a token manually from the frontend and re-run"
fi

# Step 9: Test Summary
echo -e "\n${YELLOW}📊 TEST SUMMARY${NC}"
echo "============================================================"

if [ ${#test_results[@]} -eq 0 ]; then
    echo -e "${YELLOW}⚠️  Only unauthorized access test was run${NC}"
    echo "Authentication is required for full API testing"
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Check AWS Cognito User Pool Client settings"
    echo "2. Enable ADMIN_USER_PASSWORD_AUTH or USER_PASSWORD_AUTH flow"
    echo "3. Or use manual token input method"
else
    pass_count=0
    total_count=${#test_results[@]}
    
    for result in "${test_results[@]}"; do
        if echo "$result" | grep -q "PASS"; then
            echo -e "${GREEN}✅ $result${NC}"
            ((pass_count++))
        elif echo "$result" | grep -q "SKIP"; then
            echo -e "${YELLOW}⏭️  $result${NC}"
        else
            echo -e "${RED}❌ $result${NC}"
        fi
    done
    
    echo ""
    echo "Overall: $pass_count/$total_count tests passed"
    
    if [ "$pass_count" -eq "$total_count" ]; then
        echo -e "${GREEN}🎉 All available tests passed! AWS API Gateway is working correctly.${NC}"
        exit 0
    else
        echo -e "${YELLOW}⚠️  Some tests failed or were skipped. Check the logs above for details.${NC}"
        exit 1
    fi
fi 