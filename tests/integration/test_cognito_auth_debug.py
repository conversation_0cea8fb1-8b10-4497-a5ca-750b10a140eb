#!/usr/bin/env python3
"""
Debug Cognito Authentication
Test to understand why authentication is failing
"""

import boto3
import json
from botocore.exceptions import ClientError, NoCredentialsError

def test_cognito_auth():
    """Test Cognito authentication with detailed error reporting"""
    print("🔍 Debugging Cognito Authentication")
    print("=" * 60)
    
    # Configuration
    region = "ap-northeast-1"
    user_pool_id = "ap-northeast-1_H2kKHGUAT"
    client_id = "2jh76f894g6lv9vrus4qbb9hu7"
    test_email = "<EMAIL>"
    test_password = "TestPassword123!"
    
    print(f"Region: {region}")
    print(f"User Pool ID: {user_pool_id}")
    print(f"Client ID: {client_id}")
    print(f"Test User: {test_email}")
    print(f"Password: {'*' * len(test_password)}")
    print()
    
    try:
        # Check AWS credentials
        print("🔍 Checking AWS credentials...")
        session = boto3.Session()
        credentials = session.get_credentials()
        if credentials:
            print(f"✅ AWS credentials found")
            print(f"   Access Key: {credentials.access_key[:8]}***")
            print(f"   Region: {session.region_name}")
        else:
            print("❌ No AWS credentials found")
            return
        
        # Initialize Cognito client
        print("\n🔍 Initializing Cognito client...")
        cognito_client = boto3.client("cognito-idp", region_name=region)
        print("✅ Cognito client initialized")
        
        # Check if user pool exists and is accessible
        print(f"\n🔍 Checking user pool access...")
        try:
            user_pool = cognito_client.describe_user_pool(UserPoolId=user_pool_id)
            print(f"✅ User pool accessible: {user_pool['UserPool']['Name']}")
        except ClientError as e:
            print(f"❌ Cannot access user pool: {e}")
            return
        
        # Check client configuration
        print(f"\n🔍 Checking client configuration...")
        try:
            client_details = cognito_client.describe_user_pool_client(
                UserPoolId=user_pool_id,
                ClientId=client_id
            )
            client_info = client_details["UserPoolClient"]
            print(f"✅ Client accessible: {client_info['ClientName']}")
            print(f"   Auth flows: {client_info.get('ExplicitAuthFlows', [])}")
            print(f"   Has secret: {'Yes' if client_info.get('ClientSecret') else 'No'}")
        except ClientError as e:
            print(f"❌ Cannot access client: {e}")
            return
        
        # Check if user exists
        print(f"\n🔍 Checking if user exists...")
        try:
            user_info = cognito_client.admin_get_user(
                UserPoolId=user_pool_id,
                Username=test_email
            )
            print(f"✅ User exists: {user_info['Username']}")
            print(f"   Status: {user_info['UserStatus']}")
            print(f"   Enabled: {user_info['Enabled']}")
            
            # Show user attributes
            attributes = {attr['Name']: attr['Value'] for attr in user_info['UserAttributes']}
            print(f"   Email: {attributes.get('email', 'N/A')}")
            print(f"   Email verified: {attributes.get('email_verified', 'N/A')}")
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'UserNotFoundException':
                print(f"❌ User does not exist: {test_email}")
                print("   You need to create this user in Cognito first")
                return
            else:
                print(f"❌ Error checking user: {e}")
                return
        
        # Try authentication with different flows
        auth_flows = ["ADMIN_USER_PASSWORD_AUTH", "USER_PASSWORD_AUTH"]
        
        for auth_flow in auth_flows:
            print(f"\n🔍 Trying {auth_flow}...")
            try:
                if auth_flow.startswith("ADMIN_"):
                    response = cognito_client.admin_initiate_auth(
                        UserPoolId=user_pool_id,
                        ClientId=client_id,
                        AuthFlow=auth_flow,
                        AuthParameters={
                            "USERNAME": test_email,
                            "PASSWORD": test_password,
                        },
                    )
                else:
                    response = cognito_client.initiate_auth(
                        ClientId=client_id,
                        AuthFlow=auth_flow,
                        AuthParameters={
                            "USERNAME": test_email,
                            "PASSWORD": test_password,
                        },
                    )
                
                if "AuthenticationResult" in response:
                    auth_result = response["AuthenticationResult"]
                    access_token = auth_result["AccessToken"]
                    print(f"✅ {auth_flow} successful!")
                    print(f"   Access token length: {len(access_token)}")
                    print(f"   Token type: {auth_result.get('TokenType', 'Bearer')}")
                    print(f"   Expires in: {auth_result.get('ExpiresIn', 'N/A')} seconds")
                    
                    # Test the token with API
                    print(f"\n🔍 Testing token with API...")
                    import requests
                    
                    headers = {
                        "Authorization": f"Bearer {access_token}",
                        "Content-Type": "application/json"
                    }
                    
                    api_url = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev/funds"
                    
                    try:
                        api_response = requests.get(api_url, headers=headers, timeout=10)
                        print(f"   API response status: {api_response.status_code}")
                        
                        if api_response.status_code == 200:
                            print("   ✅ Token works with API!")
                        else:
                            print(f"   ❌ Token rejected by API: {api_response.text}")
                    except Exception as api_error:
                        print(f"   ❌ API request failed: {api_error}")
                    
                    return True
                
                elif "ChallengeName" in response:
                    challenge = response["ChallengeName"]
                    print(f"⚠️  {auth_flow} requires challenge: {challenge}")
                    
                    if challenge == "NEW_PASSWORD_REQUIRED":
                        print("   User needs to set a new password")
                    elif challenge == "SMS_MFA" or challenge == "SOFTWARE_TOKEN_MFA":
                        print("   User has MFA enabled")
                    else:
                        print(f"   Unknown challenge: {challenge}")
                
                else:
                    print(f"❌ {auth_flow} failed: unexpected response")
                    print(f"   Response: {json.dumps(response, indent=2, default=str)}")
                
            except ClientError as e:
                error_code = e.response['Error']['Code']
                error_message = e.response['Error']['Message']
                print(f"❌ {auth_flow} failed: {error_code}")
                print(f"   Message: {error_message}")
                
                if error_code == "NotAuthorizedException":
                    print("   → Incorrect username or password")
                elif error_code == "UserNotConfirmedException":
                    print("   → User email not confirmed")
                elif error_code == "PasswordResetRequiredException":
                    print("   → User must reset password")
                elif error_code == "UserNotFoundException":
                    print("   → User does not exist")
                elif error_code == "TooManyRequestsException":
                    print("   → Too many attempts, try again later")
            
            except Exception as e:
                print(f"❌ {auth_flow} failed with exception: {e}")
        
        print(f"\n❌ All authentication flows failed")
        return False
        
    except NoCredentialsError:
        print("❌ AWS credentials not configured")
        print("   Please run: aws configure")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    test_cognito_auth()