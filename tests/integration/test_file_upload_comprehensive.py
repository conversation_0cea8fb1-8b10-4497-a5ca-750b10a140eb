#!/usr/bin/env python3
"""
Comprehensive File Upload API Integration Tests
Tests the PDF fund extraction and file upload endpoints.
"""

import sys
import os
import json
import requests
from typing import Dict, Any, Optional
import time

# Add the src directory to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
sys.path.insert(0, os.path.join(project_root, "src"))

class FileUploadAPITester:
    """Comprehensive tester for File Upload API endpoints"""
    
    def __init__(self):
        self.api_base_url = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
        self.test_results = []
        self.auth_token = None
        self.test_user_email = "<EMAIL>"
        self.test_user_password = "TestPassword123!"
        self.test_pdf_path = "/Users/<USER>/2025/Projects/FundFlow/sample/Ariake Capital Firm Introduction April 2025.pdf"
        self.upload_job_id = None
        
    def log_test_result(self, test_name: str, success: bool, message: str, response_data: Dict = None):
        """Log test result"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "response_data": response_data
        }
        self.test_results.append(result)
        
        status_icon = "✅" if success else "❌"
        print(f"{status_icon} {test_name}: {message}")
        if response_data and len(str(response_data)) < 500:
            print(f"   Response: {json.dumps(response_data, indent=2, default=str)}")
        elif response_data:
            print(f"   Response: {json.dumps(response_data, indent=2, default=str)[:300]}...")
        print()
    
    def authenticate(self):
        """Authenticate to get access token"""
        try:
            # Try to authenticate directly with Cognito first
            import boto3
            
            cognito_client = boto3.client("cognito-idp", region_name="ap-northeast-1")
            user_pool_id = "ap-northeast-1_H2kKHGUAT"
            client_id = "2jh76f894g6lv9vrus4qbb9hu7"
            
            # Try different auth flows
            auth_flows = ["ADMIN_USER_PASSWORD_AUTH", "USER_PASSWORD_AUTH"]
            
            for auth_flow in auth_flows:
                try:
                    if auth_flow.startswith("ADMIN_"):
                        response = cognito_client.admin_initiate_auth(
                            UserPoolId=user_pool_id,
                            ClientId=client_id,
                            AuthFlow=auth_flow,
                            AuthParameters={
                                "USERNAME": self.test_user_email,
                                "PASSWORD": self.test_user_password,
                            },
                        )
                    else:
                        response = cognito_client.initiate_auth(
                            ClientId=client_id,
                            AuthFlow=auth_flow,
                            AuthParameters={
                                "USERNAME": self.test_user_email,
                                "PASSWORD": self.test_user_password,
                            },
                        )
                    
                    if "AuthenticationResult" in response:
                        auth_result = response["AuthenticationResult"]
                        self.auth_token = auth_result["AccessToken"]
                        print(f"✅ Cognito authentication successful using {auth_flow}")
                        return True
                        
                except Exception as flow_error:
                    continue
            
            print("⚠️  Authentication failed, proceeding with tests that may require auth...")
            return False
            
        except Exception as e:
            print(f"⚠️  Authentication error: {str(e)}, proceeding with tests...")
            return False
    
    def get_auth_headers(self):
        """Get headers with authentication"""
        headers = {}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        return headers
    
    def check_test_pdf_exists(self):
        """Check if the test PDF file exists"""
        if os.path.exists(self.test_pdf_path):
            file_size = os.path.getsize(self.test_pdf_path)
            print(f"📄 Test PDF found: {self.test_pdf_path}")
            print(f"📏 File size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
            return True
        else:
            print(f"❌ Test PDF not found: {self.test_pdf_path}")
            return False
    
    def test_upload_health_check(self):
        """Test upload API health check"""
        try:
            response = requests.get(f"{self.api_base_url}/upload/health")
            
            if response.status_code == 200:
                data = response.json()
                self.log_test_result(
                    "Upload Health Check",
                    True,
                    f"Upload API is healthy. Status: {response.status_code}",
                    data
                )
                return True
            else:
                self.log_test_result(
                    "Upload Health Check",
                    False,
                    f"Unexpected status code: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Upload Health Check",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_upload_pdf_file(self):
        """Test uploading PDF file for fund extraction"""
        if not os.path.exists(self.test_pdf_path):
            self.log_test_result(
                "Upload PDF File",
                False,
                f"Test PDF file not found: {self.test_pdf_path}",
                {}
            )
            return False
        
        if not self.auth_token:
            self.log_test_result(
                "Upload PDF File",
                False,
                "No authentication token available",
                {}
            )
            return False
        
        try:
            # Prepare file for upload
            with open(self.test_pdf_path, 'rb') as pdf_file:
                files = {
                    'file': ('Ariake Capital Firm Introduction April 2025.pdf', pdf_file, 'application/pdf')
                }
                
                headers = self.get_auth_headers()
                # Don't set Content-Type for multipart/form-data - requests will set it automatically
                
                response = requests.post(
                    f"{self.api_base_url}/upload/fund-pdf",
                    files=files,
                    headers=headers,
                    timeout=60  # Increased timeout for file upload
                )
            
            if response.status_code in [200, 201, 202]:
                data = response.json()
                self.upload_job_id = data.get("job_id")
                self.log_test_result(
                    "Upload PDF File",
                    True,
                    f"Successfully uploaded PDF. Job ID: {self.upload_job_id}",
                    {"job_id": self.upload_job_id, "status": data.get("status")}
                )
                return True
            else:
                self.log_test_result(
                    "Upload PDF File",
                    False,
                    f"Failed to upload PDF. Status: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Upload PDF File",
                False,
                f"Upload request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_check_upload_job_status(self):
        """Test checking the status of upload job"""
        if not self.upload_job_id:
            self.log_test_result(
                "Check Upload Job Status",
                False,
                "No upload job ID available",
                {}
            )
            return False
        
        try:
            response = requests.get(
                f"{self.api_base_url}/upload/jobs/{self.upload_job_id}",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                job_data = response.json()
                job_status = job_data.get("status", "unknown")
                self.log_test_result(
                    "Check Upload Job Status",
                    True,
                    f"Successfully retrieved job status: {job_status}",
                    {"job_id": self.upload_job_id, "status": job_status, "progress": job_data.get("progress")}
                )
                return True
            else:
                self.log_test_result(
                    "Check Upload Job Status",
                    False,
                    f"Failed to get job status. Status: {response.status_code}",
                    {"job_id": self.upload_job_id, "status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Check Upload Job Status",
                False,
                f"Job status request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_wait_for_job_completion(self):
        """Test waiting for job completion (with timeout)"""
        if not self.upload_job_id:
            self.log_test_result(
                "Wait for Job Completion",
                False,
                "No upload job ID available",
                {}
            )
            return False
        
        max_wait_time = 300  # 5 minutes maximum wait
        check_interval = 10   # Check every 10 seconds
        start_time = time.time()
        
        try:
            while time.time() - start_time < max_wait_time:
                response = requests.get(
                    f"{self.api_base_url}/upload/jobs/{self.upload_job_id}",
                    headers=self.get_auth_headers()
                )
                
                if response.status_code == 200:
                    job_data = response.json()
                    status = job_data.get("status", "unknown")
                    
                    if status in ["completed", "success"]:
                        self.log_test_result(
                            "Wait for Job Completion",
                            True,
                            f"Job completed successfully after {time.time() - start_time:.1f} seconds",
                            {"job_id": self.upload_job_id, "final_status": status, "result": job_data.get("result")}
                        )
                        return True
                    elif status in ["failed", "error"]:
                        self.log_test_result(
                            "Wait for Job Completion",
                            False,
                            f"Job failed with status: {status}",
                            {"job_id": self.upload_job_id, "status": status, "error": job_data.get("error")}
                        )
                        return False
                    elif status in ["processing", "pending", "in_progress"]:
                        print(f"⏳ Job status: {status}, waiting {check_interval} seconds...")
                        time.sleep(check_interval)
                    else:
                        print(f"⚠️  Unknown job status: {status}, continuing to wait...")
                        time.sleep(check_interval)
                else:
                    print(f"⚠️  Error checking job status: {response.status_code}")
                    time.sleep(check_interval)
            
            # Timeout reached
            self.log_test_result(
                "Wait for Job Completion",
                False,
                f"Job did not complete within {max_wait_time} seconds timeout",
                {"job_id": self.upload_job_id, "timeout": max_wait_time}
            )
            return False
            
        except Exception as e:
            self.log_test_result(
                "Wait for Job Completion",
                False,
                f"Error while waiting for job completion: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_get_extraction_result(self):
        """Test getting the extracted fund data"""
        if not self.upload_job_id:
            self.log_test_result(
                "Get Extraction Result",
                False,
                "No upload job ID available",
                {}
            )
            return False
        
        try:
            response = requests.get(
                f"{self.api_base_url}/upload/jobs/{self.upload_job_id}/result",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                result_data = response.json()
                extracted_funds = result_data.get("extracted_funds", [])
                funds_count = len(extracted_funds)
                
                self.log_test_result(
                    "Get Extraction Result",
                    True,
                    f"Successfully retrieved extraction result with {funds_count} funds",
                    {"job_id": self.upload_job_id, "extracted_funds_count": funds_count, "sample_fund": extracted_funds[0] if funds_count > 0 else None}
                )
                return True
            else:
                self.log_test_result(
                    "Get Extraction Result",
                    False,
                    f"Failed to get extraction result. Status: {response.status_code}",
                    {"job_id": self.upload_job_id, "status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Get Extraction Result",
                False,
                f"Extraction result request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_list_upload_jobs(self):
        """Test listing all upload jobs"""
        try:
            response = requests.get(
                f"{self.api_base_url}/upload/jobs",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                jobs_data = response.json()
                jobs_count = len(jobs_data.get("jobs", []))
                self.log_test_result(
                    "List Upload Jobs",
                    True,
                    f"Successfully retrieved {jobs_count} upload jobs",
                    {"jobs_count": jobs_count, "sample_job": jobs_data.get("jobs", [{}])[0] if jobs_count > 0 else None}
                )
                return True
            else:
                self.log_test_result(
                    "List Upload Jobs",
                    False,
                    f"Failed to list upload jobs. Status: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "List Upload Jobs",
                False,
                f"List jobs request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_upload_invalid_file(self):
        """Test uploading an invalid file type"""
        if not self.auth_token:
            self.log_test_result(
                "Upload Invalid File",
                False,
                "No authentication token available",
                {}
            )
            return False
        
        try:
            # Create a temporary text file to test invalid upload
            invalid_file_content = "This is not a PDF file"
            
            files = {
                'file': ('invalid.txt', invalid_file_content.encode(), 'text/plain')
            }
            
            headers = self.get_auth_headers()
            
            response = requests.post(
                f"{self.api_base_url}/upload/fund-pdf",
                files=files,
                headers=headers
            )
            
            if response.status_code in [400, 415, 422]:  # Expected error codes for invalid file
                self.log_test_result(
                    "Upload Invalid File",
                    True,
                    f"Correctly rejected invalid file type with status: {response.status_code}",
                    {"status_code": response.status_code}
                )
                return True
            else:
                self.log_test_result(
                    "Upload Invalid File",
                    False,
                    f"Unexpected response to invalid file upload. Status: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Upload Invalid File",
                False,
                f"Invalid file upload test failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_upload_oversized_file(self):
        """Test uploading an oversized file"""
        if not self.auth_token:
            self.log_test_result(
                "Upload Oversized File",
                False,
                "No authentication token available",
                {}
            )
            return False
        
        try:
            # Create a large fake PDF content (simulating oversized file)
            # Note: This is a simplified test - in reality, we'd need a very large file
            large_content = b"Large PDF content simulation" * 100000  # ~2.8MB
            
            files = {
                'file': ('large.pdf', large_content, 'application/pdf')
            }
            
            headers = self.get_auth_headers()
            
            response = requests.post(
                f"{self.api_base_url}/upload/fund-pdf",
                files=files,
                headers=headers,
                timeout=30
            )
            
            # The response could be successful (if size limit is high) or error (if size limit is low)
            # We'll consider both as valid outcomes
            if response.status_code in [200, 201, 202, 413, 400]:
                self.log_test_result(
                    "Upload Oversized File",
                    True,
                    f"Server handled oversized file appropriately with status: {response.status_code}",
                    {"status_code": response.status_code, "file_size": len(large_content)}
                )
                return True
            else:
                self.log_test_result(
                    "Upload Oversized File",
                    False,
                    f"Unexpected response to oversized file. Status: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Upload Oversized File",
                False,
                f"Oversized file upload test failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def run_all_tests(self):
        """Run all file upload API tests"""
        print("📤 Starting Comprehensive File Upload API Tests")
        print("=" * 60)
        
        # Check if test PDF exists
        if not self.check_test_pdf_exists():
            print("❌ Test PDF file not found, some tests will be skipped")
        
        # Authenticate first
        print("🔐 Authenticating...")
        self.authenticate()
        
        # Test sequence that mimics frontend flow
        tests = [
            self.test_upload_health_check,
            self.test_upload_pdf_file,
            self.test_check_upload_job_status,
            self.test_wait_for_job_completion,
            self.test_get_extraction_result,
            self.test_list_upload_jobs,
            self.test_upload_invalid_file,
            self.test_upload_oversized_file
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        # Summary
        print("=" * 60)
        print("📊 FILE UPLOAD API TEST SUMMARY")
        print("=" * 60)
        print(f"Total tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success rate: {(passed/total)*100:.1f}%")
        
        # Save detailed results
        results_file = "file_upload_api_test_results.json"
        with open(results_file, "w") as f:
            json.dump({
                "summary": {
                    "total_tests": total,
                    "passed": passed,
                    "failed": total - passed,
                    "success_rate": f"{(passed/total)*100:.1f}%",
                    "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "auth_available": self.auth_token is not None,
                    "test_pdf_available": os.path.exists(self.test_pdf_path),
                    "test_pdf_path": self.test_pdf_path
                },
                "test_results": self.test_results
            }, f, indent=2, default=str)
        
        print(f"\n📁 Detailed results saved to: {results_file}")
        
        return passed == total


def main():
    """Main function to run file upload API tests"""
    tester = FileUploadAPITester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ All file upload API tests passed!")
        return 0
    else:
        print("\n❌ Some file upload API tests failed!")
        return 1


if __name__ == "__main__":
    exit(main())