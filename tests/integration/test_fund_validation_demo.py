#!/usr/bin/env python3
"""
Fund Validation Demo Script
Demonstrates the comprehensive server-side validation implemented in subtask 9.3

This script tests various validation scenarios:
1. Valid fund creation and updates
2. Invalid data that should trigger validation errors
3. Business rule violations
4. Permission-based validations
5. Bulk update scenarios
"""

import requests
import json
import time
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, Any


class FundValidationTester:
    """Test harness for fund validation functionality."""

    def __init__(self, api_base_url: str, auth_token: str = None):
        """
        Initialize the tester.

        Args:
            api_base_url: Base URL of the API (e.g., https://your-api-gateway-url.com/dev)
            auth_token: Optional authentication token
        """
        self.api_base_url = api_base_url.rstrip("/")
        self.auth_token = auth_token
        self.session = requests.Session()

        if auth_token:
            self.session.headers.update(
                {
                    "Authorization": f"Bearer {auth_token}",
                    "Content-Type": "application/json",
                }
            )
        else:
            self.session.headers.update({"Content-Type": "application/json"})

    def run_all_tests(self):
        """Run comprehensive validation tests."""
        print("🧪 Starting Fund Validation Demo Tests")
        print("=" * 60)

        test_methods = [
            ("Valid Fund Creation", self.test_valid_fund_creation),
            ("Invalid Fund ID Format", self.test_invalid_fund_id),
            ("Invalid Financial Data", self.test_invalid_financial_data),
            ("ISIN Validation", self.test_isin_validation),
            (
                "Fund Type vs Risk Level Consistency",
                self.test_fund_type_risk_consistency,
            ),
            ("Performance Metrics Validation", self.test_performance_metrics),
            ("Holdings Allocation Validation", self.test_holdings_allocation),
            ("Fund Update Validation", self.test_fund_update_validation),
            ("Status Transition Validation", self.test_status_transitions),
            ("Bulk Update Validation", self.test_bulk_updates),
        ]

        results = []
        for test_name, test_method in test_methods:
            print(f"\n🔬 Testing: {test_name}")
            print("-" * 40)
            try:
                result = test_method()
                results.append((test_name, "PASS" if result else "FAIL"))
                print(f"✅ {test_name}: {'PASS' if result else 'FAIL'}")
            except Exception as e:
                results.append((test_name, f"ERROR: {str(e)}"))
                print(f"❌ {test_name}: ERROR - {str(e)}")

        print("\n" + "=" * 60)
        print("📊 Test Results Summary:")
        for test_name, result in results:
            status_emoji = "✅" if result == "PASS" else "❌"
            print(f"{status_emoji} {test_name}: {result}")

    def test_valid_fund_creation(self) -> bool:
        """Test successful fund creation with valid data."""
        fund_data = {
            "fund_id": "VALID-FUND-001",
            "name": "Test Growth Fund",
            "fund_type": "equity",
            "status": "active",
            "nav": 10.50,
            "currency": "USD",
            "risk_level": "moderate",
            "expense_ratio": 0.75,
            "minimum_investment": 1000.00,
            "description": "A test fund for growth investments",
            "fund_manager": "John Smith",
            "management_company": "Test Asset Management",
        }

        response = self.create_fund(fund_data)
        print(f"Response Status: {response.status_code}")

        if response.status_code == 201:
            print("✅ Valid fund created successfully")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            return True
        else:
            print(f"❌ Expected 201, got {response.status_code}")
            print(f"Response: {response.text}")
            return False

    def test_invalid_fund_id(self) -> bool:
        """Test validation of invalid fund ID format."""
        fund_data = {
            "fund_id": "invalid fund id with spaces!",  # Invalid characters
            "name": "Test Fund",
            "fund_type": "equity",
        }

        response = self.create_fund(fund_data)
        print(f"Response Status: {response.status_code}")

        if response.status_code == 400:
            response_data = response.json()
            print("✅ Validation error returned as expected")
            print(f"Validation errors: {json.dumps(response_data, indent=2)}")
            return True
        else:
            print(f"❌ Expected 400, got {response.status_code}")
            return False

    def test_invalid_financial_data(self) -> bool:
        """Test validation of invalid financial data."""
        fund_data = {
            "fund_id": "INVALID-FINANCIAL-001",
            "name": "Test Fund",
            "fund_type": "equity",
            "nav": -5.0,  # Negative NAV
            "expense_ratio": -1.0,  # Negative expense ratio
            "total_assets": -1000000,  # Negative total assets
        }

        response = self.create_fund(fund_data)
        print(f"Response Status: {response.status_code}")

        if response.status_code == 400:
            response_data = response.json()
            print("✅ Financial validation errors returned as expected")
            print(f"Validation errors: {json.dumps(response_data, indent=2)}")
            return True
        else:
            print(f"❌ Expected 400, got {response.status_code}")
            return False

    def test_isin_validation(self) -> bool:
        """Test ISIN code validation."""
        fund_data = {
            "fund_id": "ISIN-TEST-001",
            "name": "ISIN Test Fund",
            "fund_type": "equity",
            "isin": "INVALID-ISIN-FORMAT",  # Invalid ISIN
        }

        response = self.create_fund(fund_data)
        print(f"Response Status: {response.status_code}")

        if response.status_code == 400:
            response_data = response.json()
            print("✅ ISIN validation error returned as expected")
            print(f"Validation errors: {json.dumps(response_data, indent=2)}")
            return True
        else:
            print(f"❌ Expected 400, got {response.status_code}")
            return False

    def test_fund_type_risk_consistency(self) -> bool:
        """Test consistency validation between fund type and risk level."""
        fund_data = {
            "fund_id": "CONSISTENCY-TEST-001",
            "name": "Money Market High Risk Fund",
            "fund_type": "money_market",
            "risk_level": "very_high",  # Inconsistent: money market should be low risk
        }

        response = self.create_fund(fund_data)
        print(f"Response Status: {response.status_code}")

        # This should succeed but with warnings
        if response.status_code in [200, 201]:
            response_data = response.json()
            if "validation_warnings" in response_data.get("data", {}):
                print("✅ Fund created with consistency warnings as expected")
                print(
                    f"Warnings: {json.dumps(response_data['data']['validation_warnings'], indent=2)}"
                )
                return True

        print(f"❌ Expected success with warnings, got {response.status_code}")
        return False

    def test_performance_metrics(self) -> bool:
        """Test performance metrics validation."""
        fund_data = {
            "fund_id": "PERFORMANCE-TEST-001",
            "name": "Performance Test Fund",
            "fund_type": "equity",
            "performance_metrics": {
                "ytd_return": -150.0,  # Invalid: below -100%
                "volatility": -5.0,  # Invalid: negative volatility
                "sharpe_ratio": 15.0,  # Warning: unusually high
            },
        }

        response = self.create_fund(fund_data)
        print(f"Response Status: {response.status_code}")

        if response.status_code == 400:
            response_data = response.json()
            print("✅ Performance metrics validation errors returned as expected")
            print(f"Validation errors: {json.dumps(response_data, indent=2)}")
            return True
        else:
            print(f"❌ Expected 400, got {response.status_code}")
            return False

    def test_holdings_allocation(self) -> bool:
        """Test holdings allocation validation."""
        fund_data = {
            "fund_id": "HOLDINGS-TEST-001",
            "name": "Holdings Test Fund",
            "fund_type": "equity",
            "holdings": {
                "sector_allocation": {
                    "Technology": 60.0,
                    "Healthcare": 30.0,
                    "Finance": 20.0,  # Total = 110%, should be 100%
                }
            },
        }

        response = self.create_fund(fund_data)
        print(f"Response Status: {response.status_code}")

        if response.status_code == 400:
            response_data = response.json()
            print("✅ Holdings allocation validation errors returned as expected")
            print(f"Validation errors: {json.dumps(response_data, indent=2)}")
            return True
        else:
            print(f"❌ Expected 400, got {response.status_code}")
            return False

    def test_fund_update_validation(self) -> bool:
        """Test fund update validation."""
        # First create a fund
        fund_data = {
            "fund_id": "UPDATE-TEST-001",
            "name": "Update Test Fund",
            "fund_type": "equity",
            "nav": 10.0,
        }

        create_response = self.create_fund(fund_data)
        if create_response.status_code not in [200, 201]:
            print(f"Failed to create test fund: {create_response.status_code}")
            return False

        # Now test update with large NAV change
        update_data = {"nav": 20.0}  # 100% increase - should trigger warning

        response = self.update_fund("UPDATE-TEST-001", update_data)
        print(f"Response Status: {response.status_code}")

        if response.status_code == 200:
            response_data = response.json()
            if "validation_warnings" in response_data.get("data", {}):
                print("✅ Fund updated with warnings for large NAV change")
                print(
                    f"Warnings: {json.dumps(response_data['data']['validation_warnings'], indent=2)}"
                )
                return True

        print(f"❌ Expected success with warnings, got {response.status_code}")
        return False

    def test_status_transitions(self) -> bool:
        """Test fund status transition validation."""
        # Create a closed fund
        fund_data = {
            "fund_id": "STATUS-TEST-001",
            "name": "Status Test Fund",
            "fund_type": "equity",
            "status": "closed",
        }

        create_response = self.create_fund(fund_data)
        if create_response.status_code not in [200, 201]:
            print(f"Failed to create test fund: {create_response.status_code}")
            return False

        # Try to transition from closed to active (invalid)
        update_data = {"status": "active"}

        response = self.update_fund("STATUS-TEST-001", update_data)
        print(f"Response Status: {response.status_code}")

        if response.status_code == 400:
            response_data = response.json()
            print("✅ Invalid status transition validation error returned")
            print(f"Validation errors: {json.dumps(response_data, indent=2)}")
            return True
        else:
            print(f"❌ Expected 400, got {response.status_code}")
            return False

    def test_bulk_updates(self) -> bool:
        """Test bulk update validation."""
        bulk_data = {
            "updates": [
                {
                    "fund_id": "BULK-TEST-001",
                    "data": {"name": "Updated Fund 1", "nav": 15.0},
                },
                {
                    "fund_id": "BULK-TEST-002",
                    "data": {"name": "Updated Fund 2", "nav": -5.0},  # Invalid NAV
                },
            ]
        }

        response = self.bulk_update_funds(bulk_data)
        print(f"Response Status: {response.status_code}")

        if response.status_code in [200, 400]:
            response_data = response.json()
            print("✅ Bulk update validation completed")
            print(f"Results: {json.dumps(response_data, indent=2)}")
            return True
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            return False

    def create_fund(self, fund_data: Dict[str, Any]) -> requests.Response:
        """Create a fund via API."""
        url = f"{self.api_base_url}/funds"
        return self.session.post(url, json=fund_data)

    def update_fund(
        self, fund_id: str, update_data: Dict[str, Any]
    ) -> requests.Response:
        """Update a fund via API."""
        url = f"{self.api_base_url}/funds/{fund_id}"
        return self.session.put(url, json=update_data)

    def bulk_update_funds(self, bulk_data: Dict[str, Any]) -> requests.Response:
        """Bulk update funds via API."""
        url = f"{self.api_base_url}/funds/bulk-update"
        return self.session.post(url, json=bulk_data)


def main():
    """Main function to run the validation tests."""
    print("🔧 Fund Validation Testing Setup")
    print("=" * 60)

    # Configuration
    api_base_url = input(
        "Enter API Base URL (e.g., https://your-api-gateway.amazonaws.com/dev): "
    ).strip()
    if not api_base_url:
        api_base_url = "http://localhost:3000"  # Default for local testing
        print(f"Using default URL: {api_base_url}")

    auth_token = input("Enter auth token (press Enter to skip): ").strip()
    if not auth_token:
        print(
            "⚠️  No auth token provided - some tests may fail if authentication is required"
        )

    # Initialize tester
    tester = FundValidationTester(api_base_url, auth_token)

    # Run tests
    tester.run_all_tests()

    print(f"\n🎉 Testing completed!")
    print(f"📝 API Base URL used: {api_base_url}")


if __name__ == "__main__":
    main()
