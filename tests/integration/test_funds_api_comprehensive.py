#!/usr/bin/env python3
"""
Comprehensive Funds API Integration Tests
Tests all fund endpoints as the frontend would interact with them.
"""

import sys
import os
import json
import requests
from typing import Dict, Any, Optional, List
import time
from decimal import Decimal

# Add the src directory to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
sys.path.insert(0, os.path.join(project_root, "src"))

class FundsAPITester:
    """Comprehensive tester for Funds API endpoints"""
    
    def __init__(self):
        self.api_base_url = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
        self.test_results = []
        self.auth_token = None
        self.test_user_email = "<EMAIL>"
        self.test_user_password = "TestPassword123!"
        self.created_fund_id = None
        
    def log_test_result(self, test_name: str, success: bool, message: str, response_data: Dict = None):
        """Log test result"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "response_data": response_data
        }
        self.test_results.append(result)
        
        status_icon = "✅" if success else "❌"
        print(f"{status_icon} {test_name}: {message}")
        if response_data and len(str(response_data)) < 500:
            print(f"   Response: {json.dumps(response_data, indent=2, default=str)}")
        elif response_data:
            print(f"   Response: {json.dumps(response_data, indent=2, default=str)[:300]}...")
        print()
    
    def authenticate(self):
        """Authenticate to get access token"""
        try:
            # Try to authenticate directly with Cognito first
            import boto3
            
            cognito_client = boto3.client("cognito-idp", region_name="ap-northeast-1")
            user_pool_id = "ap-northeast-1_H2kKHGUAT"
            client_id = "2jh76f894g6lv9vrus4qbb9hu7"
            
            # Try different auth flows
            auth_flows = ["ADMIN_USER_PASSWORD_AUTH", "USER_PASSWORD_AUTH"]
            
            for auth_flow in auth_flows:
                try:
                    if auth_flow.startswith("ADMIN_"):
                        response = cognito_client.admin_initiate_auth(
                            UserPoolId=user_pool_id,
                            ClientId=client_id,
                            AuthFlow=auth_flow,
                            AuthParameters={
                                "USERNAME": self.test_user_email,
                                "PASSWORD": self.test_user_password,
                            },
                        )
                    else:
                        response = cognito_client.initiate_auth(
                            ClientId=client_id,
                            AuthFlow=auth_flow,
                            AuthParameters={
                                "USERNAME": self.test_user_email,
                                "PASSWORD": self.test_user_password,
                            },
                        )
                    
                    if "AuthenticationResult" in response:
                        auth_result = response["AuthenticationResult"]
                        self.auth_token = auth_result["AccessToken"]
                        print(f"✅ Cognito authentication successful using {auth_flow}")
                        return True
                        
                except Exception as flow_error:
                    continue
            
            # If Cognito auth failed, try the API endpoint
            login_data = {
                "email": self.test_user_email,
                "password": self.test_user_password
            }
            
            response = requests.post(
                f"{self.api_base_url}/auth/login",
                json=login_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if "access_token" in data:
                    self.auth_token = data["access_token"]
                    print("✅ API authentication successful")
                    return True
            
            # If authentication fails, we'll proceed without token for testing
            print("⚠️  Authentication failed, proceeding with tests that may require auth...")
            return False
            
        except Exception as e:
            print(f"⚠️  Authentication error: {str(e)}, proceeding with tests...")
            return False
    
    def get_auth_headers(self):
        """Get headers with authentication"""
        headers = {"Content-Type": "application/json"}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        return headers
    
    def test_funds_health_check(self):
        """Test funds API health check"""
        try:
            response = requests.get(f"{self.api_base_url}/funds/health")
            
            if response.status_code == 200:
                data = response.json()
                self.log_test_result(
                    "Funds Health Check",
                    True,
                    f"Funds API is healthy. Status: {response.status_code}",
                    data
                )
                return True
            else:
                self.log_test_result(
                    "Funds Health Check",
                    False,
                    f"Unexpected status code: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Funds Health Check",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_list_funds(self):
        """Test listing all funds"""
        try:
            response = requests.get(
                f"{self.api_base_url}/funds",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                funds_count = len(data.get("funds", []))
                self.log_test_result(
                    "List Funds",
                    True,
                    f"Successfully retrieved {funds_count} funds",
                    {"funds_count": funds_count, "sample_fund": data.get("funds", [{}])[0] if funds_count > 0 else None}
                )
                return True
            else:
                self.log_test_result(
                    "List Funds",
                    False,
                    f"Failed to list funds. Status: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "List Funds",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_list_funds_with_filters(self):
        """Test listing funds with various filters"""
        filters = [
            {"fund_type": "equity"},
            {"status": "active"},
            {"limit": "5"},
            {"fund_type": "bond", "status": "active"}
        ]
        
        all_passed = True
        
        for filter_params in filters:
            try:
                response = requests.get(
                    f"{self.api_base_url}/funds",
                    params=filter_params,
                    headers=self.get_auth_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    funds_count = len(data.get("funds", []))
                    self.log_test_result(
                        f"List Funds with Filter: {filter_params}",
                        True,
                        f"Successfully retrieved {funds_count} funds with filter",
                        {"filter": filter_params, "funds_count": funds_count}
                    )
                else:
                    self.log_test_result(
                        f"List Funds with Filter: {filter_params}",
                        False,
                        f"Failed to list funds with filter. Status: {response.status_code}",
                        {"filter": filter_params, "status_code": response.status_code}
                    )
                    all_passed = False
                    
            except Exception as e:
                self.log_test_result(
                    f"List Funds with Filter: {filter_params}",
                    False,
                    f"Request failed: {str(e)}",
                    {"filter": filter_params, "error": str(e)}
                )
                all_passed = False
        
        return all_passed
    
    def test_get_fund_by_id(self):
        """Test getting a specific fund by ID"""
        # First get a fund ID from the list
        try:
            response = requests.get(
                f"{self.api_base_url}/funds",
                params={"limit": "1"},
                headers=self.get_auth_headers()
            )
            
            if response.status_code != 200:
                self.log_test_result(
                    "Get Fund by ID",
                    False,
                    "Could not get fund list to test individual fund retrieval",
                    {}
                )
                return False
            
            data = response.json()
            funds = data.get("funds", [])
            
            if not funds:
                self.log_test_result(
                    "Get Fund by ID",
                    False,
                    "No funds available to test individual fund retrieval",
                    {}
                )
                return False
            
            fund_id = funds[0].get("fund_id")
            if not fund_id:
                self.log_test_result(
                    "Get Fund by ID",
                    False,
                    "First fund has no fund_id",
                    {"fund": funds[0]}
                )
                return False
            
            # Now test getting this specific fund
            response = requests.get(
                f"{self.api_base_url}/funds/{fund_id}",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                fund_data = response.json()
                self.log_test_result(
                    "Get Fund by ID",
                    True,
                    f"Successfully retrieved fund: {fund_data.get('name', 'Unknown')}",
                    {"fund_id": fund_id, "fund_name": fund_data.get('name')}
                )
                return True
            else:
                self.log_test_result(
                    "Get Fund by ID",
                    False,
                    f"Failed to get fund by ID. Status: {response.status_code}",
                    {"fund_id": fund_id, "status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Get Fund by ID",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_get_fund_market_data(self):
        """Test getting fund market data"""
        try:
            # Get a fund ID first
            response = requests.get(
                f"{self.api_base_url}/funds",
                params={"limit": "1"},
                headers=self.get_auth_headers()
            )
            
            if response.status_code != 200:
                self.log_test_result(
                    "Get Fund Market Data",
                    False,
                    "Could not get fund list for market data test",
                    {}
                )
                return False
            
            data = response.json()
            funds = data.get("funds", [])
            
            if not funds:
                self.log_test_result(
                    "Get Fund Market Data",
                    False,
                    "No funds available for market data test",
                    {}
                )
                return False
            
            fund_id = funds[0].get("fund_id")
            
            # Test market data endpoint
            response = requests.get(
                f"{self.api_base_url}/funds/{fund_id}/market-data",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                market_data = response.json()
                self.log_test_result(
                    "Get Fund Market Data",
                    True,
                    f"Successfully retrieved market data for fund",
                    {"fund_id": fund_id, "has_price_history": "price_history" in market_data}
                )
                return True
            else:
                self.log_test_result(
                    "Get Fund Market Data",
                    False,
                    f"Failed to get market data. Status: {response.status_code}",
                    {"fund_id": fund_id, "status_code": response.status_code}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Get Fund Market Data",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_search_funds(self):
        """Test fund search functionality"""
        search_queries = ["equity", "bond", "growth", "income"]
        
        all_passed = True
        
        for query in search_queries:
            try:
                response = requests.get(
                    f"{self.api_base_url}/funds/search",
                    params={"q": query},
                    headers=self.get_auth_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    results_count = len(data.get("funds", []))
                    self.log_test_result(
                        f"Search Funds: '{query}'",
                        True,
                        f"Search returned {results_count} results",
                        {"query": query, "results_count": results_count}
                    )
                else:
                    self.log_test_result(
                        f"Search Funds: '{query}'",
                        False,
                        f"Search failed. Status: {response.status_code}",
                        {"query": query, "status_code": response.status_code}
                    )
                    all_passed = False
                    
            except Exception as e:
                self.log_test_result(
                    f"Search Funds: '{query}'",
                    False,
                    f"Search request failed: {str(e)}",
                    {"query": query, "error": str(e)}
                )
                all_passed = False
        
        return all_passed
    
    def test_create_fund(self):
        """Test creating a new fund"""
        if not self.auth_token:
            self.log_test_result(
                "Create Fund",
                False,
                "No authentication token available",
                {}
            )
            return False
        
        test_fund_data = {
            "name": f"Test Fund {int(time.time())}",
            "fund_type": "equity",
            "status": "active",
            "description": "A test fund created by integration tests",
            "fund_manager": "Test Manager",
            "inception_date": "2024-01-01T00:00:00Z",
            "currency": "USD",
            "nav": "100.00",
            "total_assets": "1000000.00",
            "expense_ratio": "0.75",
            "minimum_investment": "1000.00",
            "risk_level": "moderate"
        }
        
        try:
            response = requests.post(
                f"{self.api_base_url}/funds",
                json=test_fund_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code in [200, 201]:
                created_fund = response.json()
                self.created_fund_id = created_fund.get("fund_id")
                self.log_test_result(
                    "Create Fund",
                    True,
                    f"Successfully created fund: {created_fund.get('name')}",
                    {"fund_id": self.created_fund_id, "fund_name": created_fund.get('name')}
                )
                return True
            else:
                self.log_test_result(
                    "Create Fund",
                    False,
                    f"Failed to create fund. Status: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Create Fund",
                False,
                f"Create fund request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_update_fund(self):
        """Test updating an existing fund"""
        if not self.auth_token:
            self.log_test_result(
                "Update Fund",
                False,
                "No authentication token available",
                {}
            )
            return False
        
        if not self.created_fund_id:
            self.log_test_result(
                "Update Fund",
                False,
                "No created fund ID available for update test",
                {}
            )
            return False
        
        update_data = {
            "description": "Updated description for test fund",
            "expense_ratio": "0.80",
            "nav": "105.50"
        }
        
        try:
            response = requests.put(
                f"{self.api_base_url}/funds/{self.created_fund_id}",
                json=update_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                updated_fund = response.json()
                self.log_test_result(
                    "Update Fund",
                    True,
                    f"Successfully updated fund",
                    {"fund_id": self.created_fund_id, "updated_fields": list(update_data.keys())}
                )
                return True
            else:
                self.log_test_result(
                    "Update Fund",
                    False,
                    f"Failed to update fund. Status: {response.status_code}",
                    {"fund_id": self.created_fund_id, "status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Update Fund",
                False,
                f"Update fund request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_delete_fund(self):
        """Test deleting a fund"""
        if not self.auth_token:
            self.log_test_result(
                "Delete Fund",
                False,
                "No authentication token available",
                {}
            )
            return False
        
        if not self.created_fund_id:
            self.log_test_result(
                "Delete Fund",
                False,
                "No created fund ID available for delete test",
                {}
            )
            return False
        
        try:
            response = requests.delete(
                f"{self.api_base_url}/funds/{self.created_fund_id}",
                headers=self.get_auth_headers()
            )
            
            if response.status_code in [200, 204]:
                self.log_test_result(
                    "Delete Fund",
                    True,
                    f"Successfully deleted fund",
                    {"fund_id": self.created_fund_id}
                )
                return True
            else:
                self.log_test_result(
                    "Delete Fund",
                    False,
                    f"Failed to delete fund. Status: {response.status_code}",
                    {"fund_id": self.created_fund_id, "status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Delete Fund",
                False,
                f"Delete fund request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_invalid_fund_id(self):
        """Test accessing non-existent fund"""
        invalid_id = "invalid-fund-id-12345"
        
        try:
            response = requests.get(
                f"{self.api_base_url}/funds/{invalid_id}",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 404:
                self.log_test_result(
                    "Invalid Fund ID",
                    True,
                    f"Correctly returned 404 for invalid fund ID",
                    {"fund_id": invalid_id, "status_code": response.status_code}
                )
                return True
            else:
                self.log_test_result(
                    "Invalid Fund ID",
                    False,
                    f"Unexpected response for invalid fund ID. Status: {response.status_code}",
                    {"fund_id": invalid_id, "status_code": response.status_code}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Invalid Fund ID",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def run_all_tests(self):
        """Run all funds API tests"""
        print("💰 Starting Comprehensive Funds API Tests")
        print("=" * 60)
        
        # Authenticate first
        print("🔐 Authenticating...")
        self.authenticate()
        
        # Test sequence that mimics frontend flow
        tests = [
            self.test_funds_health_check,
            self.test_list_funds,
            self.test_list_funds_with_filters,
            self.test_get_fund_by_id,
            self.test_get_fund_market_data,
            self.test_search_funds,
            self.test_create_fund,
            self.test_update_fund,
            self.test_delete_fund,
            self.test_invalid_fund_id
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        # Summary
        print("=" * 60)
        print("📊 FUNDS API TEST SUMMARY")
        print("=" * 60)
        print(f"Total tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success rate: {(passed/total)*100:.1f}%")
        
        # Save detailed results
        results_file = "funds_api_test_results.json"
        with open(results_file, "w") as f:
            json.dump({
                "summary": {
                    "total_tests": total,
                    "passed": passed,
                    "failed": total - passed,
                    "success_rate": f"{(passed/total)*100:.1f}%",
                    "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "auth_available": self.auth_token is not None
                },
                "test_results": self.test_results
            }, f, indent=2, default=str)
        
        print(f"\n📁 Detailed results saved to: {results_file}")
        
        return passed == total


def main():
    """Main function to run funds API tests"""
    tester = FundsAPITester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ All funds API tests passed!")
        return 0
    else:
        print("\n❌ Some funds API tests failed!")
        return 1


if __name__ == "__main__":
    exit(main())