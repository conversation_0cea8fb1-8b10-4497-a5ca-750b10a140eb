#!/usr/bin/env python3
"""
Direct Function Test for Fund Management API

This test calls the fund management functions directly without going through
AWS API Gateway, but still reads from DynamoDB to test data compatibility
with models and repository classes.

Usage:
    python test_funds_direct.py

Requirements:
    - AWS credentials configured
    - DynamoDB tables available
    - Python environment with all dependencies
"""

import json
import os
import sys
import uuid
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from dataclasses import dataclass
import boto3
from dotenv import load_dotenv

# Add src to path for imports
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))
src_path = os.path.join(project_root, "src")
sys.path.insert(0, src_path)

# Import the fund functions directly
from functions.api.funds import (
    handle_list_funds,
    handle_get_fund,
    handle_get_fund_details,
    handle_get_fund_historical,
    handle_create_fund,
    handle_update_fund,
    handle_delete_fund,
    handle_bulk_update_funds,
)

# Import shared modules for direct testing
from shared.database import get_fund_repository
from shared.models.fund import Fund, FundStatus, FundType
from shared.models.requests import (
    FundCreateRequest,
    FundUpdateRequest,
    FundQueryRequest,
)


@dataclass
class TestConfig:
    """Configuration for direct function testing"""

    region: str = "ap-northeast-1"
    table_prefix: str = "FundFlow-dev"
    test_user_id: str = "test-user-123"
    test_fund_prefix: str = "DIRECT-TEST"


class MockLambdaContext:
    """Mock Lambda context for testing"""

    def __init__(self):
        self.function_name = "test-funds-function"
        self.function_version = "$LATEST"
        self.invoked_function_arn = "arn:aws:lambda:region:account:function:test"
        self.memory_limit_in_mb = 512
        self.remaining_time_in_millis = 30000
        self.log_group_name = "/aws/lambda/test"
        self.log_stream_name = "test-stream"
        self.aws_request_id = str(uuid.uuid4())

    def get_remaining_time_in_millis(self):
        return self.remaining_time_in_millis


class DirectFundsAPITester:
    """Direct function tester for Funds API"""

    def __init__(self, config: TestConfig):
        # Load environment variables
        env_file = os.path.join(script_dir, ".env_test")
        load_dotenv(dotenv_path=env_file, override=True)

        self.config = config
        self.context = MockLambdaContext()
        self.test_session_token = self._generate_test_session_token()

        # Initialize repository for direct testing
        self.fund_repo = get_fund_repository()

        print("=" * 60)
        print("🔧 DIRECT FUNCTION TEST SETUP")
        print("=" * 60)
        print(f"Region: {self.config.region}")
        print(f"Table Prefix: {self.config.table_prefix}")
        print(f"Test User: {self.config.test_user_id}")
        print(f"Session Token: {self.test_session_token[:20]}...")

    def _generate_test_session_token(self) -> str:
        """Generate a mock session token for testing"""
        # In a real scenario, this would be a valid JWT token
        # For testing, we'll create a mock token that our session manager can handle
        token_payload = {
            "sub": self.config.test_user_id,
            "email": "<EMAIL>",
            "iss": "test-issuer",
            "exp": int(time.time()) + 3600,  # 1 hour from now
            "iat": int(time.time()),
            "token_use": "access",
        }
        # In reality, this should be properly signed, but for testing we'll use a mock
        return f"mock_token_{json.dumps(token_payload)}"

    def create_mock_event(
        self,
        method: str,
        path: str,
        body: Dict[str, Any] = None,
        query_params: Dict[str, str] = None,
    ) -> Dict[str, Any]:
        """Create a mock AWS API Gateway event"""
        event = {
            "httpMethod": method,
            "path": path,
            "headers": {
                "Authorization": f"Bearer {self.test_session_token}",
                "Content-Type": "application/json",
                "User-Agent": "DirectFunctionTester/1.0",
            },
            "queryStringParameters": query_params,
            "body": json.dumps(body) if body else None,
            "requestContext": {
                "requestId": str(uuid.uuid4()),
                "httpMethod": method,
                "path": path,
                "stage": "test",
                "requestTime": datetime.now(timezone.utc).isoformat(),
                "identity": {
                    "sourceIp": "127.0.0.1",
                    "userAgent": "DirectFunctionTester/1.0",
                },
            },
            "isBase64Encoded": False,
        }
        return event

    def call_function_safely(self, func, event: Dict[str, Any]) -> Dict[str, Any]:
        """Safely call a function and handle the response"""
        try:
            print(f"\n📡 Calling {func.__name__}")
            print(f"   Method: {event['httpMethod']}")
            print(f"   Path: {event['path']}")

            if event.get("queryStringParameters"):
                print(f"   Query: {event['queryStringParameters']}")

            if event.get("body"):
                body = (
                    json.loads(event["body"])
                    if isinstance(event["body"], str)
                    else event["body"]
                )
                print(f"   Body: {json.dumps(body, indent=2)[:200]}...")

            # Call the function directly
            response = func(event, self.context)

            # Parse response
            status_code = response.get("statusCode", 0)
            body = response.get("body", "{}")

            try:
                response_data = json.loads(body) if isinstance(body, str) else body
            except json.JSONDecodeError:
                response_data = {"raw_response": body}

            print(f"   Status: {status_code}")
            print(f"   Response: {json.dumps(response_data, indent=2)[:300]}...")

            return {
                "success": 200 <= status_code < 300,
                "status_code": status_code,
                "data": response_data,
                "raw_response": response,
            }

        except Exception as e:
            print(f"   ❌ Function call error: {str(e)}")
            return {
                "success": False,
                "status_code": 500,
                "data": {"error": str(e)},
                "raw_response": None,
            }

    def test_repository_direct(self) -> Dict[str, Any]:
        """Test repository functions directly to validate DynamoDB data compatibility"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Repository Direct Access")
        print("=" * 50)

        try:
            # Test list funds from repository
            print("\n📋 Testing repository list_funds...")
            funds_result = self.fund_repo.list_funds(limit=5)
            print(
                f"   Retrieved {len(funds_result.get('items', []))} funds from repository"
            )

            # Validate fund objects
            funds = funds_result.get("items", [])
            if funds:
                first_fund = funds[0]
                print(f"   First fund type: {type(first_fund)}")
                print(f"   First fund ID: {first_fund.fund_id}")
                print(
                    f"   First fund attributes: {list(first_fund.__dict__.keys())[:10]}..."
                )

                # Test get specific fund
                print(
                    f"\n🔍 Testing repository get_by_fund_id for {first_fund.fund_id}..."
                )
                retrieved_fund = self.fund_repo.get_by_fund_id(first_fund.fund_id)
                if retrieved_fund:
                    print(
                        f"   ✅ Successfully retrieved fund: {retrieved_fund.fund_id}"
                    )
                    print(f"   Fund name: {retrieved_fund.fund_name}")
                    print(f"   Fund type: {retrieved_fund.fund_type}")
                    print(f"   Fund status: {retrieved_fund.status}")
                    return {
                        "success": True,
                        "fund_count": len(funds),
                        "sample_fund_id": first_fund.fund_id,
                        "model_validation": "passed",
                    }
                else:
                    print(f"   ❌ Could not retrieve fund by ID")
                    return {"success": False, "error": "Could not retrieve fund by ID"}
            else:
                print("   ⚠️  No funds found in repository")
                return {
                    "success": True,
                    "fund_count": 0,
                    "note": "No funds in database",
                }

        except Exception as e:
            print(f"   ❌ Repository test error: {str(e)}")
            return {"success": False, "error": str(e)}

    def test_list_funds(self) -> Dict[str, Any]:
        """Test list funds function directly"""
        print("\n" + "=" * 50)
        print("🧪 Testing: List Funds (Direct Function Call)")
        print("=" * 50)

        # Test basic listing
        event = self.create_mock_event("GET", "/funds")
        response = self.call_function_safely(handle_list_funds, event)

        # Test with pagination
        print("\n📄 Testing with pagination...")
        event_paginated = self.create_mock_event(
            "GET", "/funds", query_params={"page": "1", "page_size": "3"}
        )
        response_paginated = self.call_function_safely(
            handle_list_funds, event_paginated
        )

        # Test with filters
        print("\n🔍 Testing with filters...")
        event_filtered = self.create_mock_event(
            "GET", "/funds", query_params={"status": "active", "fund_type": "equity"}
        )
        response_filtered = self.call_function_safely(handle_list_funds, event_filtered)

        return response

    def test_get_fund(self, fund_id: str = None) -> Dict[str, Any]:
        """Test get specific fund function directly"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Get Specific Fund (Direct Function Call)")
        print("=" * 50)

        # Get a fund ID from repository if not provided
        if not fund_id:
            try:
                funds_result = self.fund_repo.list_funds(limit=1)
                funds = funds_result.get("items", [])
                if funds:
                    fund_id = funds[0].fund_id
                    print(f"📋 Using fund_id from repository: {fund_id}")
                else:
                    fund_id = "FUND-001"  # Fallback
                    print(f"📋 Using fallback fund_id: {fund_id}")
            except Exception as e:
                fund_id = "FUND-001"
                print(f"📋 Using fallback fund_id due to error: {fund_id}")

        event = self.create_mock_event("GET", f"/funds/{fund_id}")
        response = self.call_function_safely(handle_get_fund, event)

        # Test non-existent fund
        print("\n🔍 Testing non-existent fund...")
        event_not_found = self.create_mock_event("GET", "/funds/NON-EXISTENT-FUND")
        self.call_function_safely(handle_get_fund, event_not_found)

        return response

    def test_get_fund_details(self, fund_id: str = None) -> Dict[str, Any]:
        """Test get fund details function directly"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Get Fund Details (Direct Function Call)")
        print("=" * 50)

        # Get a fund ID if not provided
        if not fund_id:
            try:
                funds_result = self.fund_repo.list_funds(limit=1)
                funds = funds_result.get("items", [])
                fund_id = funds[0].fund_id if funds else "FUND-001"
                print(f"📋 Using fund_id: {fund_id}")
            except Exception:
                fund_id = "FUND-001"
                print(f"📋 Using fallback fund_id: {fund_id}")

        event = self.create_mock_event("GET", f"/funds/{fund_id}/details")
        response = self.call_function_safely(handle_get_fund_details, event)

        return response

    def test_get_fund_historical(self, fund_id: str = None) -> Dict[str, Any]:
        """Test get fund historical data function directly"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Get Fund Historical Data (Direct Function Call)")
        print("=" * 50)

        # Get a fund ID if not provided
        if not fund_id:
            try:
                funds_result = self.fund_repo.list_funds(limit=1)
                funds = funds_result.get("items", [])
                fund_id = funds[0].fund_id if funds else "FUND-001"
                print(f"📋 Using fund_id: {fund_id}")
            except Exception:
                fund_id = "FUND-001"
                print(f"📋 Using fallback fund_id: {fund_id}")

        # Test different periods
        periods = ["1D", "1W", "1M", "1Y"]
        results = []

        for period in periods:
            print(f"\n📊 Testing period: {period}")
            event = self.create_mock_event(
                "GET",
                f"/funds/{fund_id}/historical",
                query_params={"period": period, "include_benchmark": "true"},
            )
            response = self.call_function_safely(handle_get_fund_historical, event)
            results.append((period, response["success"]))

        successful_periods = sum(1 for _, success in results if success)
        return {
            "success": successful_periods > 0,
            "tested_periods": len(periods),
            "successful_periods": successful_periods,
        }

    def test_create_fund(self) -> Dict[str, Any]:
        """Test create fund function directly"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Create Fund (Direct Function Call)")
        print("=" * 50)

        # Generate unique test fund
        timestamp = int(time.time())
        test_fund = {
            "fund_id": f"{self.config.test_fund_prefix}-{timestamp}",
            "fund_name": f"Direct Test Fund {timestamp}",
            "fund_type": "EQUITY",
            "status": "ACTIVE",
            "nav": 125.75,
            "expense_ratio": 1.25,
            "min_investment": 1000,
            "fund_manager": "Direct Test Manager",
            "inception_date": "2024-01-01",
            "description": "Test fund created by direct function call",
            "benchmark": "NIFTY 50",
            "risk_level": "MODERATE",
        }

        event = self.create_mock_event("POST", "/funds", body=test_fund)
        response = self.call_function_safely(handle_create_fund, event)

        # If successful, try to retrieve the created fund to verify it's in DynamoDB
        if response["success"]:
            print(f"\n✅ Fund created successfully, verifying in DynamoDB...")
            try:
                created_fund = self.fund_repo.get_by_fund_id(test_fund["fund_id"])
                if created_fund:
                    print(f"   ✅ Fund verified in DynamoDB: {created_fund.fund_id}")
                    print(f"   Fund name: {created_fund.fund_name}")
                    print(f"   Fund type: {created_fund.fund_type}")
                else:
                    print(f"   ❌ Fund not found in DynamoDB after creation")
            except Exception as e:
                print(f"   ❌ Error verifying fund in DynamoDB: {str(e)}")

        return response

    def test_model_compatibility(self) -> Dict[str, Any]:
        """Test that DynamoDB data is compatible with our models"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Model Compatibility with DynamoDB Data")
        print("=" * 50)

        try:
            # Get funds from repository
            funds_result = self.fund_repo.list_funds(limit=5)
            funds = funds_result.get("items", [])

            if not funds:
                print("   ⚠️  No funds available for model compatibility testing")
                return {"success": True, "note": "No funds to test"}

            compatible_funds = 0
            total_funds = len(funds)
            errors = []

            for fund in funds:
                try:
                    # Test Fund model compatibility
                    print(f"\n🔍 Testing fund: {fund.fund_id}")

                    # Verify essential attributes exist
                    essential_attrs = [
                        "fund_id",
                        "fund_name",
                        "fund_type",
                        "status",
                        "nav",
                    ]
                    for attr in essential_attrs:
                        if not hasattr(fund, attr):
                            raise ValueError(f"Missing essential attribute: {attr}")
                        print(f"   ✓ Has {attr}: {getattr(fund, attr)}")

                    # Test model methods
                    fund_dict = fund.dict()
                    print(f"   ✓ fund.dict() works: {len(fund_dict)} fields")

                    # Test enum values
                    if hasattr(fund.fund_type, "value"):
                        print(f"   ✓ FundType enum: {fund.fund_type.value}")
                    if hasattr(fund.status, "value"):
                        print(f"   ✓ FundStatus enum: {fund.status.value}")

                    # Test conversion back to Fund object
                    Fund(**fund_dict)
                    print(f"   ✅ Model round-trip successful")

                    compatible_funds += 1

                except Exception as e:
                    error_msg = f"Fund {fund.fund_id}: {str(e)}"
                    errors.append(error_msg)
                    print(f"   ❌ Model compatibility error: {error_msg}")

            success_rate = (
                (compatible_funds / total_funds) * 100 if total_funds > 0 else 100
            )

            print(f"\n📊 Model Compatibility Results:")
            print(f"   Total funds tested: {total_funds}")
            print(f"   Compatible funds: {compatible_funds}")
            print(f"   Success rate: {success_rate:.1f}%")

            if errors:
                print(f"   Errors encountered:")
                for error in errors[:5]:  # Show first 5 errors
                    print(f"     • {error}")

            return {
                "success": compatible_funds > 0,
                "total_tested": total_funds,
                "compatible": compatible_funds,
                "success_rate": success_rate,
                "errors": errors[:10],  # Limit errors in response
            }

        except Exception as e:
            print(f"   ❌ Model compatibility test error: {str(e)}")
            return {"success": False, "error": str(e)}

    def run_comprehensive_direct_test(self):
        """Run all direct function tests"""
        print("🚀 Starting Comprehensive Direct Function Tests")
        print("=" * 60)

        # Test repository directly first
        repo_result = self.test_repository_direct()

        # Test model compatibility
        model_result = self.test_model_compatibility()

        # Test function handlers
        list_result = self.test_list_funds()
        get_result = self.test_get_fund()
        details_result = self.test_get_fund_details()
        historical_result = self.test_get_fund_historical()
        create_result = self.test_create_fund()

        # Summary
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE DIRECT TEST SUMMARY")
        print("=" * 60)

        tests = [
            ("Repository Direct Access", repo_result["success"]),
            ("Model Compatibility", model_result["success"]),
            ("List Funds", list_result["success"]),
            ("Get Fund", get_result["success"]),
            ("Get Fund Details", details_result["success"]),
            ("Get Historical Data", historical_result["success"]),
            ("Create Fund", create_result["success"]),
        ]

        for test_name, success in tests:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{test_name:25} {status}")

        passed = sum(1 for _, success in tests if success)
        total = len(tests)
        print(f"\nOverall: {passed}/{total} tests passed ({(passed/total*100):.1f}%)")

        # Special notes
        if "fund_count" in repo_result:
            print(f"Repository: {repo_result['fund_count']} funds available")

        if "success_rate" in model_result:
            print(
                f"Model Compatibility: {model_result['success_rate']:.1f}% success rate"
            )

        if (
            isinstance(historical_result, dict)
            and "tested_periods" in historical_result
        ):
            print(
                f"Historical Data: {historical_result['successful_periods']}/{historical_result['tested_periods']} periods successful"
            )


def main():
    """Main function to run direct function tests"""
    try:
        config = TestConfig()
        tester = DirectFundsAPITester(config)

        print("🎯 Choose test level:")
        print("1. Repository and Model Compatibility only")
        print("2. Comprehensive function tests (includes all handlers)")

        choice = input("\nEnter choice (1 or 2, default=2): ").strip()

        if choice == "1":
            print("\n🔧 Running Repository and Model Tests Only...")
            repo_result = tester.test_repository_direct()
            model_result = tester.test_model_compatibility()

            print(
                f"\nRepository Test: {'✅ PASS' if repo_result['success'] else '❌ FAIL'}"
            )
            print(f"Model Test: {'✅ PASS' if model_result['success'] else '❌ FAIL'}")
        else:
            tester.run_comprehensive_direct_test()

    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
