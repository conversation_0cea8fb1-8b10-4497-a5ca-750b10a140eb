#!/usr/bin/env python3
"""
Improved Direct Function Test for Fund Management API

This test calls the fund management functions directly without AWS API Gateway,
focusing on testing DynamoDB data compatibility with models and repository classes.
Includes better authentication handling and more comprehensive model validation.

Usage:
    python test_funds_direct_improved.py

Requirements:
    - AWS credentials configured
    - DynamoDB tables available
    - Python environment with all dependencies
"""

import json
import os
import sys
import uuid
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import boto3
from dotenv import load_dotenv

# Add src to path for imports
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))
src_path = os.path.join(project_root, "src")
sys.path.insert(0, src_path)

# Import the fund functions directly
from functions.api.funds import (
    handle_list_funds,
    handle_get_fund,
    handle_get_fund_details,
    handle_get_fund_historical,
    handle_create_fund,
    handle_update_fund,
    handle_delete_fund,
    handle_bulk_update_funds,
)

# Import shared modules for direct testing
from shared.database import get_fund_repository
from shared.models.fund import Fund, FundStatus, FundType
from shared.models.requests import (
    FundCreateRequest,
    FundUpdateRequest,
    FundQueryRequest,
)
from shared.security.session_manager import SessionManager


@dataclass
class TestConfig:
    """Configuration for direct function testing"""

    region: str = "ap-northeast-1"
    table_prefix: str = "FundFlow-dev"
    test_user_id: str = "test-user-direct-123"
    test_fund_prefix: str = "DIRECT-TEST"


class MockLambdaContext:
    """Mock Lambda context for testing"""

    def __init__(self):
        self.function_name = "test-funds-function"
        self.function_version = "$LATEST"
        self.invoked_function_arn = "arn:aws:lambda:region:account:function:test"
        self.memory_limit_in_mb = 512
        self.remaining_time_in_millis = 30000
        self.log_group_name = "/aws/lambda/test"
        self.log_stream_name = "test-stream"
        self.aws_request_id = str(uuid.uuid4())

    def get_remaining_time_in_millis(self):
        return self.remaining_time_in_millis


class ImprovedDirectFundsAPITester:
    """Improved direct function tester for Funds API with better auth handling"""

    def __init__(self, config: TestConfig):
        # Load environment variables first to get AWS credentials
        env_file = os.path.join(script_dir, ".env_test")
        load_dotenv(dotenv_path=env_file, override=True)

        # Set AWS credentials from .env_test file
        print(f"   🔑 Setting AWS credentials from {env_file}")

        # Test AWS credentials and set up DynamoDB
        try:
            import boto3

            # Try with real credentials first
            sts = boto3.client("sts")
            identity = sts.get_caller_identity()
            print(f"   ✅ AWS credentials working: {identity.get('Arn', 'Unknown')}")
            self.use_real_dynamodb = True
        except Exception as e:
            print(f"   ⚠️  AWS credentials issue: {str(e)[:100]}...")
            print("   🔄 Falling back to mock DynamoDB for testing...")
            self.use_real_dynamodb = False
            self._setup_mock_dynamodb()

        self.config = config
        self.context = MockLambdaContext()

        # Initialize repository for direct testing
        self.fund_repo = get_fund_repository()

        # Initialize session manager
        self.session_manager = SessionManager()

        print("=" * 60)
        print("🔧 IMPROVED DIRECT FUNCTION TEST SETUP")
        print("=" * 60)
        print(f"Region: {self.config.region}")
        print(f"Table Prefix: {self.config.table_prefix}")
        print(f"Test User: {self.config.test_user_id}")

    def _setup_mock_dynamodb(self):
        """Set up mock DynamoDB for testing when real credentials aren't available"""
        try:
            from moto import mock_aws
            import boto3
            from decimal import Decimal

            # Start mocking AWS services
            self.mock_aws = mock_aws()
            self.mock_aws.start()

            # Create a mock DynamoDB client
            dynamodb = boto3.resource("dynamodb", region_name=self.config.region)

            # Create the funds table
            table_name = f"{self.config.table_prefix.lower()}-funds"
            table = dynamodb.create_table(
                TableName=table_name,
                KeySchema=[
                    {"AttributeName": "pk", "KeyType": "HASH"},
                    {"AttributeName": "sk", "KeyType": "RANGE"},
                ],
                AttributeDefinitions=[
                    {"AttributeName": "pk", "AttributeType": "S"},
                    {"AttributeName": "sk", "AttributeType": "S"},
                ],
                BillingMode="PAY_PER_REQUEST",
            )

            # Add some test data
            test_funds = [
                {
                    "pk": "FUND#MOCK-001",
                    "sk": "FUND#MOCK-001",
                    "fund_id": "MOCK-001",
                    "fund_name": "Mock Test Fund 1",
                    "fund_type": "EQUITY",
                    "status": "ACTIVE",
                    "nav": Decimal("100.50"),
                    "expense_ratio": Decimal("1.25"),
                    "min_investment": Decimal("1000"),
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                },
                {
                    "pk": "FUND#MOCK-002",
                    "sk": "FUND#MOCK-002",
                    "fund_id": "MOCK-002",
                    "fund_name": "Mock Test Fund 2",
                    "fund_type": "BOND",
                    "status": "ACTIVE",
                    "nav": Decimal("50.25"),
                    "expense_ratio": Decimal("0.75"),
                    "min_investment": Decimal("500"),
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                },
            ]

            # Insert test data
            for fund_data in test_funds:
                table.put_item(Item=fund_data)

            print(f"   ✅ Mock DynamoDB set up with {len(test_funds)} test funds")

        except Exception as e:
            print(f"   ❌ Failed to set up mock DynamoDB: {str(e)}")
            self.use_real_dynamodb = False

    def create_mock_event_bypass_auth(
        self,
        method: str,
        path: str,
        body: Dict[str, Any] = None,
        query_params: Dict[str, str] = None,
    ) -> Dict[str, Any]:
        """Create a mock event that bypasses authentication for direct testing"""
        event = {
            "httpMethod": method,
            "path": path,
            "headers": {
                "Content-Type": "application/json",
                "User-Agent": "DirectFunctionTester/1.0",
            },
            "queryStringParameters": query_params,
            "body": json.dumps(body) if body else None,
            "requestContext": {
                "requestId": str(uuid.uuid4()),
                "httpMethod": method,
                "path": path,
                "stage": "test",
                "requestTime": datetime.now(timezone.utc).isoformat(),
                "identity": {
                    "sourceIp": "127.0.0.1",
                    "userAgent": "DirectFunctionTester/1.0",
                },
                # Add test user info directly to bypass auth
                "authorizer": {
                    "user_id": self.config.test_user_id,
                    "email": "<EMAIL>",
                    "validated": True,
                },
            },
            "isBase64Encoded": False,
        }
        return event

    def patch_session_validation(self):
        """Patch session manager to bypass validation for testing"""
        # Import the modules we need to patch
        from shared.security.session_manager import SessionManager
        from shared.api import auth_dependencies

        # Store original methods
        original_validate = SessionManager.validate_session
        original_session_manager = getattr(auth_dependencies, "session_manager", None)

        test_user_id = self.config.test_user_id  # Capture in closure

        def mock_validate_session(self, event):
            return {
                "valid": True,
                "user_info": {
                    "user_id": test_user_id,
                    "email": "<EMAIL>",
                    "sub": test_user_id,
                    "username": "test_user",
                },
            }

        # Patch the class method
        SessionManager.validate_session = mock_validate_session

        # Also patch the instance in auth_dependencies if it exists
        if original_session_manager:
            auth_dependencies.session_manager.validate_session = (
                lambda event: mock_validate_session(None, event)
            )

        return (original_validate, original_session_manager)

    def call_function_with_bypass(self, func, event: Dict[str, Any]) -> Dict[str, Any]:
        """Call a function with authentication bypass"""
        try:
            print(f"\n📡 Calling {func.__name__} (with auth bypass)")
            print(f"   Method: {event['httpMethod']}")
            print(f"   Path: {event['path']}")

            if event.get("queryStringParameters"):
                print(f"   Query: {event['queryStringParameters']}")

            if event.get("body"):
                body = (
                    json.loads(event["body"])
                    if isinstance(event["body"], str)
                    else event["body"]
                )
                print(f"   Body: {json.dumps(body, indent=2)[:200]}...")

            # Patch session validation temporarily
            original_methods = self.patch_session_validation()

            try:
                # Call the function directly (handlers only take event, not context)
                response = func(event)
            finally:
                # Restore original validation
                from shared.security.session_manager import SessionManager
                from shared.api import auth_dependencies

                original_validate, original_session_manager = original_methods
                SessionManager.validate_session = original_validate

                if original_session_manager:
                    auth_dependencies.session_manager = original_session_manager

            # Parse response
            status_code = response.get("statusCode", 0)
            body = response.get("body", "{}")

            try:
                response_data = json.loads(body) if isinstance(body, str) else body
            except json.JSONDecodeError:
                response_data = {"raw_response": body}

            print(f"   Status: {status_code}")
            print(f"   Response: {json.dumps(response_data, indent=2)[:300]}...")

            return {
                "success": 200 <= status_code < 300,
                "status_code": status_code,
                "data": response_data,
                "raw_response": response,
            }

        except Exception as e:
            print(f"   ❌ Function call error: {str(e)}")
            import traceback

            traceback.print_exc()
            return {
                "success": False,
                "status_code": 500,
                "data": {"error": str(e)},
                "raw_response": None,
            }

    def test_repository_comprehensive(self) -> Dict[str, Any]:
        """Comprehensive repository test to validate DynamoDB data compatibility"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Repository Comprehensive Access")
        print("=" * 50)

        results = {
            "list_funds": False,
            "get_by_id": False,
            "model_validation": False,
            "data_integrity": False,
            "fund_count": 0,
            "errors": [],
        }

        try:
            # Test 1: List funds from repository
            print("\n📋 Test 1: Repository list_funds...")
            funds_result = self.fund_repo.list_funds(limit=10)
            funds = funds_result.get("items", [])
            results["fund_count"] = len(funds)

            if funds:
                print(f"   ✅ Retrieved {len(funds)} funds from repository")
                results["list_funds"] = True

                # Test 2: Get specific fund by ID
                print(f"\n🔍 Test 2: Repository get_by_fund_id...")
                test_fund = funds[0]
                retrieved_fund = self.fund_repo.get_by_fund_id(test_fund.fund_id)

                if retrieved_fund:
                    print(
                        f"   ✅ Successfully retrieved fund: {retrieved_fund.fund_id}"
                    )
                    results["get_by_id"] = True

                    # Test 3: Model validation
                    print(f"\n🔬 Test 3: Model validation...")
                    if self._validate_fund_model(retrieved_fund):
                        results["model_validation"] = True
                        print(f"   ✅ Model validation passed")
                    else:
                        print(f"   ❌ Model validation failed")

                    # Test 4: Data integrity
                    print(f"\n🔐 Test 4: Data integrity...")
                    if self._validate_data_integrity(retrieved_fund, test_fund):
                        results["data_integrity"] = True
                        print(f"   ✅ Data integrity check passed")
                    else:
                        print(f"   ❌ Data integrity check failed")

                else:
                    results["errors"].append("Could not retrieve fund by ID")
                    print(f"   ❌ Could not retrieve fund by ID")
            else:
                print("   ⚠️  No funds found in repository")
                results["list_funds"] = True  # Not an error if no data

        except Exception as e:
            error_msg = f"Repository test error: {str(e)}"
            results["errors"].append(error_msg)
            print(f"   ❌ {error_msg}")

        # Calculate overall success
        success = all(
            [
                results["list_funds"],
                results["get_by_id"] or results["fund_count"] == 0,  # OK if no funds
                results["model_validation"] or results["fund_count"] == 0,
                results["data_integrity"] or results["fund_count"] == 0,
            ]
        )

        results["success"] = success
        return results

    def _validate_fund_model(self, fund: Fund) -> bool:
        """Validate that a fund object conforms to the model structure"""
        try:
            # Check essential attributes
            essential_attrs = ["fund_id", "fund_name", "fund_type", "status", "nav"]
            for attr in essential_attrs:
                if not hasattr(fund, attr):
                    print(f"     ❌ Missing attribute: {attr}")
                    return False
                print(f"     ✓ Has {attr}: {getattr(fund, attr)}")

            # Test model methods
            fund_dict = fund.dict()
            print(f"     ✓ fund.dict() works: {len(fund_dict)} fields")

            # Test enum values
            if hasattr(fund.fund_type, "value"):
                print(f"     ✓ FundType enum: {fund.fund_type.value}")
            if hasattr(fund.status, "value"):
                print(f"     ✓ FundStatus enum: {fund.status.value}")

            # Test model reconstruction
            reconstructed = Fund(**fund_dict)
            print(f"     ✓ Model reconstruction successful")

            return True

        except Exception as e:
            print(f"     ❌ Model validation error: {str(e)}")
            return False

    def _validate_data_integrity(self, fund1: Fund, fund2: Fund) -> bool:
        """Validate data integrity between two fund objects"""
        try:
            # Check that key fields match
            key_fields = ["fund_id", "fund_name", "fund_type", "status"]
            for field in key_fields:
                val1 = getattr(fund1, field, None)
                val2 = getattr(fund2, field, None)
                if val1 != val2:
                    print(f"     ❌ Field mismatch {field}: {val1} != {val2}")
                    return False
                print(f"     ✓ Field {field} matches: {val1}")

            return True

        except Exception as e:
            print(f"     ❌ Data integrity error: {str(e)}")
            return False

    def test_model_round_trip(self) -> Dict[str, Any]:
        """Test complete model round trip: DB -> Model -> Dict -> Model -> DB"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Model Round Trip")
        print("=" * 50)

        try:
            # Get a fund from database
            funds_result = self.fund_repo.list_funds(limit=1)
            funds = funds_result.get("items", [])

            if not funds:
                return {"success": True, "note": "No funds to test"}

            original_fund = funds[0]
            print(f"📋 Testing with fund: {original_fund.fund_id}")

            # Step 1: Model -> Dict
            print("\n1️⃣ Step 1: Model -> Dict")
            fund_dict = original_fund.dict()
            print(f"   ✅ Converted to dict: {len(fund_dict)} fields")

            # Step 2: Dict -> Model
            print("\n2️⃣ Step 2: Dict -> Model")
            reconstructed_fund = Fund(**fund_dict)
            print(f"   ✅ Reconstructed fund: {reconstructed_fund.fund_id}")

            # Step 3: Validate equality
            print("\n3️⃣ Step 3: Validate equality")
            fields_match = self._compare_fund_objects(original_fund, reconstructed_fund)

            if fields_match:
                print("   ✅ Round trip successful - all fields match")
                return {"success": True, "fields_match": True}
            else:
                print("   ❌ Round trip failed - field mismatch")
                return {"success": False, "fields_match": False}

        except Exception as e:
            print(f"   ❌ Round trip test error: {str(e)}")
            return {"success": False, "error": str(e)}

    def _compare_fund_objects(self, fund1: Fund, fund2: Fund) -> bool:
        """Compare two fund objects for equality"""
        try:
            # Get dictionaries
            dict1 = fund1.dict()
            dict2 = fund2.dict()

            # Compare each field
            mismatches = []
            for key in dict1.keys():
                if key not in dict2:
                    mismatches.append(f"Key {key} missing in second fund")
                elif dict1[key] != dict2[key]:
                    mismatches.append(f"Key {key}: {dict1[key]} != {dict2[key]}")
                else:
                    print(f"     ✓ {key} matches")

            if mismatches:
                for mismatch in mismatches:
                    print(f"     ❌ {mismatch}")
                return False

            return True

        except Exception as e:
            print(f"     ❌ Comparison error: {str(e)}")
            return False

    def test_function_handlers_comprehensive(self) -> Dict[str, Any]:
        """Test all function handlers comprehensively"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Function Handlers Comprehensive")
        print("=" * 50)

        # Get a sample fund ID for testing
        sample_fund_id = None
        try:
            funds_result = self.fund_repo.list_funds(limit=1)
            funds = funds_result.get("items", [])
            if funds:
                sample_fund_id = funds[0].fund_id
                print(f"📋 Using sample fund ID: {sample_fund_id}")
        except Exception as e:
            print(f"⚠️  Could not get sample fund ID: {str(e)}")

        # Test each handler
        results = {}

        # Test 1: List funds
        print("\n1️⃣ Testing handle_list_funds...")
        event = self.create_mock_event_bypass_auth("GET", "/funds")
        results["list_funds"] = self.call_function_with_bypass(handle_list_funds, event)

        # Test 2: Get fund (if we have a sample)
        if sample_fund_id:
            print(f"\n2️⃣ Testing handle_get_fund...")
            event = self.create_mock_event_bypass_auth(
                "GET", f"/funds/{sample_fund_id}"
            )
            results["get_fund"] = self.call_function_with_bypass(handle_get_fund, event)

            # Test 3: Get fund details
            print(f"\n3️⃣ Testing handle_get_fund_details...")
            event = self.create_mock_event_bypass_auth(
                "GET", f"/funds/{sample_fund_id}/details"
            )
            results["get_fund_details"] = self.call_function_with_bypass(
                handle_get_fund_details, event
            )

            # Test 4: Get fund historical
            print(f"\n4️⃣ Testing handle_get_fund_historical...")
            event = self.create_mock_event_bypass_auth(
                "GET",
                f"/funds/{sample_fund_id}/historical",
                query_params={"period": "1M"},
            )
            results["get_fund_historical"] = self.call_function_with_bypass(
                handle_get_fund_historical, event
            )

        # Test 5: Create fund
        print(f"\n5️⃣ Testing handle_create_fund...")
        timestamp = int(time.time())
        test_fund = {
            "fund_id": f"{self.config.test_fund_prefix}-{timestamp}",
            "fund_name": f"Direct Test Fund {timestamp}",
            "fund_type": "EQUITY",
            "status": "ACTIVE",
            "nav": 125.75,
            "expense_ratio": 1.25,
            "min_investment": 1000,
            "fund_manager": "Direct Test Manager",
            "inception_date": "2024-01-01",
            "description": "Test fund created by direct function call",
            "benchmark": "NIFTY 50",
            "risk_level": "MODERATE",
        }
        event = self.create_mock_event_bypass_auth("POST", "/funds", body=test_fund)
        results["create_fund"] = self.call_function_with_bypass(
            handle_create_fund, event
        )

        # Calculate overall success
        successful_tests = sum(
            1 for result in results.values() if result.get("success", False)
        )
        total_tests = len(results)

        return {
            "success": successful_tests > 0,
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "results": results,
        }

    def run_focused_test(self):
        """Run focused tests on model/repository compatibility"""
        print("🚀 Starting Focused Direct Function Tests")
        print("🎯 Focus: DynamoDB Data Compatibility with Models/Repository")
        print("=" * 60)

        # Test 1: Repository comprehensive
        repo_result = self.test_repository_comprehensive()

        # Test 2: Model round trip
        round_trip_result = self.test_model_round_trip()

        # Test 3: Function handlers
        handlers_result = self.test_function_handlers_comprehensive()

        # Summary
        # Cleanup mock AWS if used
        if hasattr(self, "mock_aws"):
            try:
                self.mock_aws.stop()
                print("   🧹 Mock AWS cleaned up")
            except:
                pass

        print("\n" + "=" * 60)
        print("📊 FOCUSED TEST SUMMARY")
        print("=" * 60)

        tests = [
            ("Repository Comprehensive", repo_result["success"]),
            ("Model Round Trip", round_trip_result["success"]),
            ("Function Handlers", handlers_result["success"]),
        ]

        for test_name, success in tests:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{test_name:25} {status}")

        passed = sum(1 for _, success in tests if success)
        total = len(tests)
        print(f"\nOverall: {passed}/{total} tests passed ({(passed/total*100):.1f}%)")

        # Detailed results
        print(f"\n📋 Detailed Results:")
        print(f"Repository: {repo_result['fund_count']} funds available")
        if repo_result.get("errors"):
            print(f"Repository Errors: {len(repo_result['errors'])}")
            for error in repo_result["errors"][:3]:
                print(f"  • {error}")

        if handlers_result.get("total_tests"):
            print(
                f"Function Handlers: {handlers_result['successful_tests']}/{handlers_result['total_tests']} successful"
            )


def main():
    """Main function to run improved direct function tests"""
    try:
        config = TestConfig()
        tester = ImprovedDirectFundsAPITester(config)

        print("🎯 Running Focused Direct Function Tests")
        print("Purpose: Test DynamoDB data compatibility with models/repository")
        print("Method: Direct function calls (no API Gateway)")

        tester.run_focused_test()

    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
