#!/usr/bin/env python3
"""
Comprehensive test script for monthly market data input functionality.
Tests all UI fields from the MarketDataInput.tsx page by calling API Gateway endpoints.

This script:
1. Validates market data locally using the same validation logic as the backend
2. Authenticates with Cognito using a persistent test user (creates if doesn't exist)
3. Creates a sample fund with initial market data via API Gateway (after local validation)
4. Tests monthly market data input scenarios that the UI would perform via API Gateway
5. Tests market data retrieval and validation features
6. Verifies the updates using both DynamoDB direct access and API Gateway calls
7. Tests the complete Local Validation -> UI -> API -> DynamoDB flow

Note: This tests the full stack including local validation, authentication, API Gateway, and data persistence.
The test user is persistent across runs and will not be deleted after testing.
Market data is input on a monthly basis as per the frontend design.
"""

import json
import time
import random
import boto3
import requests
import sys
import os
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, Any, Optional, List
from botocore.exceptions import ClientError

# Add the src directory to Python path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), "../../src"))

# Import validation components
try:
    from shared.validation.fund_validation import (
        FundValidationService,
        ValidationResult,
    )
    from shared.models.fund import Fund, FundCreate, FundUpdate, FundDynamoDBItem
    from shared.models.market_data import MarketDataInput, MarketDataPoint

    # Try to import request models - these might not be available
    try:
        from shared.models.requests import FundCreateRequest, MarketDataInputRequest

        VALIDATION_AVAILABLE = True
        REQUEST_MODELS_AVAILABLE = True
    except ImportError:
        print("⚠️  Request models not available - using simplified validation")
        REQUEST_MODELS_AVAILABLE = False
        VALIDATION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Validation service not available: {e}")
    VALIDATION_AVAILABLE = False
    REQUEST_MODELS_AVAILABLE = False


class MarketDataTester:
    def __init__(self):
        # API Gateway configuration
        self.api_base_url = (
            "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
        )
        self.api_headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        # DynamoDB clients for verification
        self.dynamodb = boto3.resource("dynamodb", region_name="ap-northeast-1")
        self.funds_table = self.dynamodb.Table("fundflow-dev-funds")
        self.market_data_table = self.dynamodb.Table("fundflow-dev-market-data")

        # Cognito configuration
        self.cognito_client_id = "2jh76f894g6lv9vrus4qbb9hu7"
        self.cognito_client: Any = boto3.client(
            "cognito-idp", region_name="ap-northeast-1"
        )

        self.test_fund_id = None
        self.jwt_token = None
        self.market_data_entries = []  # Track created market data entries

        # Initialize validation service if available
        if VALIDATION_AVAILABLE:
            self.validation_service = FundValidationService()

            # Create user context for validation (simulating an admin user)
            self.user_context = {
                "user_id": "test-user-market-data",
                "role": "ADMIN",
                "permissions": ["create_fund", "update_fund", "input_market_data"],
            }
        else:
            self.validation_service = None
            self.user_context = None

        print(
            "🔧 Initialized Monthly Market Data API Gateway testing with full authentication"
        )
        print(
            "📊 This tests the complete MarketDataInput UI -> API Gateway -> DynamoDB flow"
        )
        print(
            "📈 Including monthly market data input, validation, and retrieval features"
        )
        print("✅ Local validation service initialized")
        print("🔍 Market data will be validated locally before API calls")
        print("👤 Test user will be persistent across test runs")
        print("📅 Market data is input on a monthly basis as per frontend design")

    def authenticate(self) -> bool:
        """Authenticate with Cognito to get a valid JWT token"""
        print("🔐 Authenticating with AWS Cognito...")

        # For testing, we'll use a persistent test user
        test_username = "<EMAIL>"
        test_password = "TestPassword123!"
        user_pool_id = "ap-northeast-1_H2kKHGUAT"

        try:
            # First, check if the test user exists, if not create it
            try:
                print("🔍 Checking if market data test user exists...")
                self.cognito_client.admin_get_user(
                    UserPoolId=user_pool_id, Username=test_username
                )
                print("✅ Market data test user already exists, reusing existing user")

            except ClientError as get_error:
                error_code = get_error.response.get("Error", {}).get("Code", "")
                if error_code == "UserNotFoundException":
                    print(
                        "🔧 Market data test user doesn't exist, creating new persistent test user..."
                    )
                    try:
                        self.cognito_client.admin_create_user(
                            UserPoolId=user_pool_id,
                            Username=test_username,
                            TemporaryPassword=test_password,
                            MessageAction="SUPPRESS",  # Don't send welcome email
                            UserAttributes=[
                                {"Name": "email", "Value": test_username},
                                {"Name": "email_verified", "Value": "true"},
                            ],
                        )
                        print(
                            "✅ Persistent market data test user created successfully"
                        )

                        # Set permanent password
                        self.cognito_client.admin_set_user_password(
                            UserPoolId=user_pool_id,
                            Username=test_username,
                            Password=test_password,
                            Permanent=True,
                        )
                        print("✅ Market data test user password set (persistent)")

                    except ClientError as create_error:
                        create_error_code = create_error.response.get("Error", {}).get(
                            "Code", ""
                        )
                        if create_error_code == "UsernameExistsException":
                            print(
                                "ℹ️  Market data test user was created by another process, proceeding with authentication"
                            )
                        else:
                            print(
                                f"⚠️  Error creating market data test user: {create_error}"
                            )
                            print("   Proceeding with authentication attempt...")

            # Now try to authenticate with the test user
            print("🔑 Authenticating with market data test user...")
            response = self.cognito_client.admin_initiate_auth(
                UserPoolId=user_pool_id,
                ClientId=self.cognito_client_id,
                AuthFlow="ADMIN_NO_SRP_AUTH",
                AuthParameters={"USERNAME": test_username, "PASSWORD": test_password},
            )

            if "AuthenticationResult" in response:
                auth_result = response["AuthenticationResult"]
                id_token = auth_result.get("IdToken")

                if id_token:
                    self.jwt_token = id_token
                    self.api_headers["Authorization"] = f"Bearer {self.jwt_token}"
                    print(
                        "✅ Successfully authenticated with Cognito for market data testing"
                    )
                    return True
                else:
                    print("❌ No valid tokens received from Cognito")
                    return False
            else:
                print("❌ Authentication failed - no token received")
                return False

        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False

    def validate_market_data_locally(
        self, market_data: Dict[str, Any], operation: str = "create"
    ) -> bool:
        """
        Validate market data locally using the same validation logic as the backend.

        Args:
            market_data: Market data dictionary to validate
            operation: Type of operation ("create" or "update")

        Returns:
            bool: True if validation passes, False otherwise
        """
        print(
            f"🔍 Performing local validation for market data {operation} operation..."
        )

        # Check if validation service is available
        if (
            not VALIDATION_AVAILABLE
            or self.validation_service is None
            or self.user_context is None
        ):
            print("⚠️  Validation service not available - skipping local validation")
            print("   API Gateway will perform validation on the backend")
            return True

        try:
            # Create MarketDataInput object for validation
            market_data_copy = market_data.copy()

            # Convert date strings to datetime if needed
            if "dataTimestamp" in market_data_copy and isinstance(
                market_data_copy["dataTimestamp"], str
            ):
                market_data_copy["dataTimestamp"] = datetime.fromisoformat(
                    market_data_copy["dataTimestamp"].replace("Z", "+00:00")
                )
            elif "dataTimestamp" in market_data_copy and isinstance(
                market_data_copy["dataTimestamp"], datetime
            ):
                # Ensure timezone awareness
                if market_data_copy["dataTimestamp"].tzinfo is None:
                    market_data_copy["dataTimestamp"] = market_data_copy[
                        "dataTimestamp"
                    ].replace(tzinfo=timezone.utc)

            if "inputTimestamp" in market_data_copy and isinstance(
                market_data_copy["inputTimestamp"], str
            ):
                market_data_copy["inputTimestamp"] = datetime.fromisoformat(
                    market_data_copy["inputTimestamp"].replace("Z", "+00:00")
                )
            elif "inputTimestamp" not in market_data_copy:
                market_data_copy["inputTimestamp"] = datetime.now(timezone.utc)

            # Convert numeric values to Decimal if needed
            decimal_fields = [
                "nav",
                "marketPrice",
                "volume",
                "priceToBook",
                "priceToEarnings",
                "dividendYield",
                "volatility",
                "beta",
            ]
            for field in decimal_fields:
                if field in market_data_copy and market_data_copy[field] is not None:
                    market_data_copy[field] = Decimal(str(market_data_copy[field]))

            # Create MarketDataInput object
            try:
                market_data_obj = MarketDataInput(**market_data_copy)
                print(
                    f"✅ Local Pydantic validation passed for market data {operation}"
                )
                return True
            except Exception as validation_error:
                print(f"❌ Local validation failed for market data {operation}")
                print(f"   Validation error: {validation_error}")
                return False

        except Exception as e:
            print(f"❌ Local validation error: {e}")
            print(f"   This might be due to model incompatibilities or missing fields")
            print(f"   Proceeding with API call (backend validation will catch issues)")
            # Return True to allow API call to proceed - backend will validate
            return True

    def create_sample_fund_for_market_data(self) -> Optional[str]:
        """Create a sample fund for market data testing via API Gateway"""
        print("\n📝 Creating sample fund for market data testing via API Gateway...")

        # Generate unique fund ID
        fund_id = f"MD-{int(time.time())}"

        # Create fund data in API format
        fund_data = {
            "fund_id": fund_id,
            "name": "Market Data Test Fund",
            "symbol": f"MDT{random.randint(100, 999)}",
            "fund_type": "etf",
            "category": "Equity",
            "sub_category": "Large Cap",
            "description": "Sample fund created for market data input testing",
            "nav": 100.0,
            "previous_nav": 99.50,
            "minimum_investment": 1000.0,
            "expense_ratio": 0.75,
            "total_assets": 50000000.0,
            "aum": 50000000.0,
            "rating": 4.0,
            "fund_manager": "Market Data Test Manager",
            "inception_date": "2020-01-01T00:00:00Z",
            "risk_level": "moderate",
            "status": "active",
        }

        try:
            # Call API Gateway to create fund
            response = requests.post(
                f"{self.api_base_url}/funds",
                headers=self.api_headers,
                json=fund_data,
                timeout=30,
            )

            if response.status_code in [200, 201]:
                result = response.json()
                print(
                    f"✅ Sample fund created for market data testing with ID: {fund_id}"
                )
                print(
                    f"📄 Response: {result.get('message', 'Fund created successfully')}"
                )
                self.test_fund_id = fund_id
                return fund_id
            else:
                print(f"❌ Error creating fund via API Gateway: {response.status_code}")
                print(f"📄 Response: {response.text}")
                return None

        except Exception as e:
            print(f"❌ Error calling API Gateway to create fund: {e}")
            return None

    def test_monthly_market_data_input(self) -> bool:
        """Test monthly market data input via API Gateway"""
        print("\n📊 Testing monthly market data input via API Gateway...")

        # Create monthly market data for current month
        current_date = datetime.now(timezone.utc)
        month_start = current_date.replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )

        market_data = {
            "fundId": self.test_fund_id,
            "inputTimestamp": current_date.isoformat(),
            "dataTimestamp": month_start.isoformat(),
            "inputBy": "market-data-test-user",
            "nav": 105.25,
            "marketPrice": 105.50,
            "volume": 150000,
            "priceToBook": 1.85,
            "priceToEarnings": 18.5,
            "dividendYield": 2.35,
            "volatility": 12.8,
            "beta": 1.05,
            "notes": "Monthly market data input test - current month",
            "validated": True,
            "validationNotes": "Validated by automated test system",
        }

        # Perform local validation before API call
        if not self.validate_market_data_locally(market_data, "create"):
            print("❌ Local validation failed - aborting market data input")
            return False

        try:
            # Call API Gateway to input market data
            response = requests.post(
                f"{self.api_base_url}/funds/{self.test_fund_id}/market-data",
                headers=self.api_headers,
                json=market_data,
                timeout=30,
            )

            if response.status_code in [200, 201]:
                result = response.json()
                print("✅ Monthly market data input successfully via API Gateway")
                print(
                    f"📄 Response: {result.get('message', 'Market data saved successfully')}"
                )

                # Track this entry for cleanup
                self.market_data_entries.append(
                    {
                        "fundId": self.test_fund_id,
                        "dataTimestamp": month_start.isoformat(),
                    }
                )

                # Verify using both approaches
                return self.dual_verify_market_data(market_data, "monthly input")
            else:
                print(
                    f"❌ Error inputting market data via API Gateway: {response.status_code}"
                )
                print(f"📄 Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error calling API Gateway for market data input: {e}")
            return False

    def test_historical_monthly_data_input(self) -> bool:
        """Test inputting historical monthly market data via API Gateway"""
        print("\n📈 Testing historical monthly market data input via API Gateway...")

        # Create historical data for the past 3 months
        success_count = 0
        test_months = []

        for months_back in range(1, 4):  # 1, 2, 3 months ago
            historical_date = datetime.now(timezone.utc) - timedelta(
                days=30 * months_back
            )
            month_start = historical_date.replace(
                day=1, hour=0, minute=0, second=0, microsecond=0
            )

            # Generate realistic market data with some variance
            base_nav = 100.0 + (3 - months_back) * 2.5  # Trending upward
            variance = random.uniform(-2.0, 2.0)

            market_data = {
                "fundId": self.test_fund_id,
                "inputTimestamp": datetime.now(timezone.utc).isoformat(),
                "dataTimestamp": month_start.isoformat(),
                "inputBy": "market-data-test-user",
                "nav": round(base_nav + variance, 2),
                "marketPrice": round(base_nav + variance + 0.25, 2),
                "volume": random.randint(100000, 200000),
                "priceToBook": round(1.7 + random.uniform(-0.2, 0.2), 2),
                "priceToEarnings": round(17.0 + random.uniform(-2.0, 2.0), 2),
                "dividendYield": round(2.2 + random.uniform(-0.3, 0.3), 2),
                "volatility": round(13.0 + random.uniform(-1.0, 1.0), 2),
                "beta": round(1.0 + random.uniform(-0.1, 0.1), 2),
                "notes": f"Historical monthly market data - {months_back} months ago",
                "validated": True,
                "validationNotes": f"Historical data validated for month {month_start.strftime('%Y-%m')}",
            }

            test_months.append(month_start.strftime("%Y-%m"))

            # Perform local validation before API call
            if not self.validate_market_data_locally(market_data, "create"):
                print(f"❌ Local validation failed for {months_back} months ago data")
                continue

            try:
                # Call API Gateway to input historical market data
                response = requests.post(
                    f"{self.api_base_url}/funds/{self.test_fund_id}/market-data",
                    headers=self.api_headers,
                    json=market_data,
                    timeout=30,
                )

                if response.status_code in [200, 201]:
                    result = response.json()
                    print(
                        f"✅ Historical data for {month_start.strftime('%Y-%m')} input successfully"
                    )

                    # Track this entry for cleanup
                    self.market_data_entries.append(
                        {
                            "fundId": self.test_fund_id,
                            "dataTimestamp": month_start.isoformat(),
                        }
                    )

                    success_count += 1
                else:
                    print(
                        f"❌ Error inputting historical data for {month_start.strftime('%Y-%m')}: {response.status_code}"
                    )
                    print(f"📄 Response: {response.text}")

            except Exception as e:
                print(
                    f"❌ Error calling API Gateway for historical data {months_back} months ago: {e}"
                )

        if success_count == 3:
            print(f"✅ All 3 historical monthly data entries successful")
            print(f"📅 Months tested: {', '.join(test_months)}")
            return True
        else:
            print(f"⚠️  Only {success_count}/3 historical data entries successful")
            return success_count > 0

    def test_market_data_update(self) -> bool:
        """Test updating existing monthly market data via API Gateway"""
        print("\n🔄 Testing market data update via API Gateway...")

        # Update the current month's data that we created earlier
        current_date = datetime.now(timezone.utc)
        month_start = current_date.replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )

        updated_market_data = {
            "fundId": self.test_fund_id,
            "inputTimestamp": current_date.isoformat(),
            "dataTimestamp": month_start.isoformat(),
            "inputBy": "market-data-test-user-updated",
            "nav": 107.75,  # Updated value
            "marketPrice": 108.00,  # Updated value
            "volume": 175000,  # Updated value
            "priceToBook": 1.95,  # Updated value
            "priceToEarnings": 19.2,  # Updated value
            "dividendYield": 2.45,  # Updated value
            "volatility": 13.5,  # Updated value
            "beta": 1.08,  # Updated value
            "notes": "Updated monthly market data - revised figures",
            "validated": True,
            "validationNotes": "Updated and re-validated by automated test system",
        }

        # Perform local validation before API call
        if not self.validate_market_data_locally(updated_market_data, "update"):
            print("❌ Local validation failed - aborting market data update")
            return False

        try:
            # Call API Gateway to update market data
            response = requests.put(
                f"{self.api_base_url}/funds/{self.test_fund_id}/market-data",
                headers=self.api_headers,
                json=updated_market_data,
                timeout=30,
            )

            if response.status_code == 200:
                result = response.json()
                print("✅ Monthly market data updated successfully via API Gateway")
                print(
                    f"📄 Response: {result.get('message', 'Market data updated successfully')}"
                )

                # Verify using both approaches
                return self.dual_verify_market_data(
                    updated_market_data, "monthly update"
                )
            else:
                print(
                    f"❌ Error updating market data via API Gateway: {response.status_code}"
                )
                print(f"📄 Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error calling API Gateway for market data update: {e}")
            return False

    def test_market_data_retrieval(self) -> bool:
        """Test retrieving market data via API Gateway"""
        print("\n📖 Testing market data retrieval via API Gateway...")

        try:
            # Test 1: Get all market data for the fund
            response = requests.get(
                f"{self.api_base_url}/funds/{self.test_fund_id}/market-data",
                headers=self.api_headers,
                timeout=30,
            )

            if response.status_code == 200:
                result = response.json()
                data = result.get("data", [])
                print(f"✅ Retrieved {len(data)} market data entries via API Gateway")

                if len(data) >= 3:  # Should have current month + 3 historical months
                    print("✅ Expected number of market data entries found")

                    # Test 2: Get market data for specific period
                    current_date = datetime.now(timezone.utc)
                    start_date = (current_date - timedelta(days=90)).strftime(
                        "%Y-%m-%d"
                    )
                    end_date = current_date.strftime("%Y-%m-%d")

                    response = requests.get(
                        f"{self.api_base_url}/funds/{self.test_fund_id}/market-data",
                        headers=self.api_headers,
                        params={"start_date": start_date, "end_date": end_date},
                        timeout=30,
                    )

                    if response.status_code == 200:
                        filtered_result = response.json()
                        filtered_data = filtered_result.get("data", [])
                        print(
                            f"✅ Retrieved {len(filtered_data)} market data entries for period {start_date} to {end_date}"
                        )
                        return True
                    else:
                        print(
                            f"⚠️  Period-filtered retrieval failed: {response.status_code}"
                        )
                        return True  # Main retrieval worked
                else:
                    print(f"⚠️  Expected at least 3 entries, got {len(data)}")
                    return len(data) > 0
            else:
                print(
                    f"❌ Error retrieving market data via API Gateway: {response.status_code}"
                )
                print(f"📄 Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error calling API Gateway for market data retrieval: {e}")
            return False

    def test_market_data_validation_errors(self) -> bool:
        """Test market data validation error handling via API Gateway"""
        print("\n🚫 Testing market data validation error handling via API Gateway...")

        # Test with invalid data to ensure validation works
        invalid_market_data = {
            "fundId": self.test_fund_id,
            "inputTimestamp": datetime.now(timezone.utc).isoformat(),
            "dataTimestamp": datetime.now(timezone.utc).isoformat(),
            "inputBy": "",  # Invalid: empty string
            "nav": -10.0,  # Invalid: negative NAV
            "marketPrice": -5.0,  # Invalid: negative price
            "volume": -1000,  # Invalid: negative volume
            "priceToBook": -1.0,  # Invalid: negative ratio
            "priceToEarnings": -15.0,  # Invalid: negative ratio
            "dividendYield": -2.0,  # Invalid: negative yield
            "volatility": -5.0,  # Invalid: negative volatility
            "beta": None,  # Invalid: None value
        }

        try:
            # Call API Gateway with invalid data
            response = requests.post(
                f"{self.api_base_url}/funds/{self.test_fund_id}/market-data",
                headers=self.api_headers,
                json=invalid_market_data,
                timeout=30,
            )

            if response.status_code in [400, 422]:  # Expected validation error
                print("✅ Validation errors correctly handled by API Gateway")
                result = response.json()
                print(
                    f"📄 Validation response: {result.get('message', 'Validation failed')}"
                )
                return True
            elif response.status_code in [200, 201]:
                print("⚠️  Invalid data was accepted - validation might be too lenient")
                return False
            else:
                print(
                    f"❌ Unexpected response for invalid data: {response.status_code}"
                )
                print(f"📄 Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error testing validation with API Gateway: {e}")
            return False

    def dual_verify_market_data(
        self, expected_data: Dict[str, Any], test_name: str
    ) -> bool:
        """Verify market data using both DynamoDB direct access and API Gateway"""
        print(f"🔍 Dual verification for {test_name}...")

        # Give the system a moment to propagate changes
        time.sleep(2)

        # Verification 1: Direct DynamoDB access
        dynamodb_success = self.verify_market_data_in_dynamodb(expected_data, test_name)

        # Verification 2: API Gateway access
        api_success = self.verify_market_data_via_api(expected_data, test_name)

        if dynamodb_success and api_success:
            print(f"✅ Dual verification passed for {test_name}")
            return True
        else:
            print(f"❌ Dual verification failed for {test_name}")
            print(f"   DynamoDB verification: {'✅' if dynamodb_success else '❌'}")
            print(f"   API Gateway verification: {'✅' if api_success else '❌'}")
            return False

    def verify_market_data_in_dynamodb(
        self, expected_data: Dict[str, Any], test_name: str
    ) -> bool:
        """Verify the market data in DynamoDB matches expected values"""
        print(f"   🗄️  Verifying {test_name} in DynamoDB...")

        try:
            # Query market data table for this fund
            fund_id = expected_data.get("fundId", self.test_fund_id)
            data_timestamp = expected_data.get("dataTimestamp")

            response = self.market_data_table.query(
                KeyConditionExpression=boto3.dynamodb.conditions.Key("fund_id").eq(
                    fund_id
                )
            )

            if not response.get("Items"):
                print(f"   ❌ No market data found in DynamoDB for fund {fund_id}")
                return False

            # Find the specific entry by timestamp
            target_entry = None
            for item in response["Items"]:
                if item.get("data_timestamp") == data_timestamp:
                    target_entry = item
                    break

            if not target_entry:
                print(
                    f"   ❌ Market data entry not found for timestamp {data_timestamp}"
                )
                return False

            # Convert Decimal values for comparison
            def convert_decimals(obj):
                if isinstance(obj, dict):
                    return {k: convert_decimals(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_decimals(v) for v in obj]
                elif isinstance(obj, Decimal):
                    return float(obj)
                return obj

            target_entry = convert_decimals(target_entry)

            # Verify key fields
            verification_errors = []
            field_mappings = {
                "nav": "nav",
                "marketPrice": "market_price",
                "volume": "volume",
                "priceToBook": "price_to_book",
                "priceToEarnings": "price_to_earnings",
                "dividendYield": "dividend_yield",
                "volatility": "volatility",
                "beta": "beta",
                "inputBy": "input_by",
                "validated": "validated",
                "notes": "notes",
                "validationNotes": "validation_notes",
            }

            for api_field, db_field in field_mappings.items():
                if api_field in expected_data:
                    expected_value = expected_data[api_field]
                    actual_value = target_entry.get(db_field)

                    if isinstance(expected_value, (int, float)) and isinstance(
                        actual_value, (int, float)
                    ):
                        if abs(float(expected_value) - float(actual_value)) > 0.001:
                            verification_errors.append(
                                f"Value mismatch for {api_field}: expected {expected_value}, got {actual_value}"
                            )
                    elif expected_value != actual_value:
                        verification_errors.append(
                            f"Value mismatch for {api_field}: expected {expected_value}, got {actual_value}"
                        )

            if verification_errors:
                print(f"   ❌ DynamoDB verification failed for {test_name}:")
                for error in verification_errors[:3]:  # Show first 3 errors
                    print(f"      • {error}")
                return False
            else:
                print(f"   ✅ DynamoDB verification passed for {test_name}")
                return True

        except Exception as e:
            print(f"   ❌ Error verifying market data in DynamoDB: {e}")
            return False

    def verify_market_data_via_api(
        self, expected_data: Dict[str, Any], test_name: str
    ) -> bool:
        """Verify the market data via API Gateway matches expected values"""
        print(f"   🌐 Verifying {test_name} via API Gateway...")

        try:
            # Get market data via API Gateway
            response = requests.get(
                f"{self.api_base_url}/funds/{self.test_fund_id}/market-data",
                headers=self.api_headers,
                timeout=30,
            )

            if response.status_code != 200:
                print(f"   ❌ API Gateway request failed: {response.status_code}")
                return False

            api_result = response.json()
            if "data" not in api_result:
                print(f"   ❌ Invalid API response format: missing 'data' field")
                return False

            market_data_list = api_result["data"]
            data_timestamp = expected_data.get("dataTimestamp")

            # Find the specific entry by timestamp
            target_entry = None
            for entry in market_data_list:
                if entry.get("dataTimestamp") == data_timestamp:
                    target_entry = entry
                    break

            if not target_entry:
                print(
                    f"   ❌ Market data entry not found via API for timestamp {data_timestamp}"
                )
                return False

            # Verify key fields
            verification_errors = []
            fields_to_check = [
                "nav",
                "marketPrice",
                "volume",
                "priceToBook",
                "priceToEarnings",
                "dividendYield",
                "volatility",
                "beta",
                "inputBy",
                "validated",
                "notes",
            ]

            for field in fields_to_check:
                if field in expected_data:
                    expected_value = expected_data[field]
                    actual_value = target_entry.get(field)

                    if isinstance(expected_value, (int, float)) and isinstance(
                        actual_value, (int, float)
                    ):
                        if abs(float(expected_value) - float(actual_value)) > 0.001:
                            verification_errors.append(
                                f"Value mismatch for {field}: expected {expected_value}, got {actual_value}"
                            )
                    elif expected_value != actual_value:
                        verification_errors.append(
                            f"Value mismatch for {field}: expected {expected_value}, got {actual_value}"
                        )

            if verification_errors:
                print(f"   ❌ API Gateway verification failed for {test_name}:")
                for error in verification_errors[:3]:  # Show first 3 errors
                    print(f"      • {error}")
                return False
            else:
                print(f"   ✅ API Gateway verification passed for {test_name}")
                return True

        except Exception as e:
            print(f"   ❌ Error verifying market data via API Gateway: {e}")
            return False

    def cleanup_test_data(self):
        """Clean up the test fund and market data"""
        print(f"\n🧹 Cleaning up test data...")

        # Clean up market data entries
        for entry in self.market_data_entries:
            try:
                self.market_data_table.delete_item(
                    Key={
                        "fund_id": entry["fundId"],
                        "data_timestamp": entry["dataTimestamp"],
                    }
                )
                print(f"✅ Cleaned up market data entry for {entry['dataTimestamp']}")
            except Exception as e:
                print(f"⚠️  Error cleaning up market data entry: {e}")

        # Clean up test fund
        if self.test_fund_id:
            try:
                # Try to delete via API Gateway first
                response = requests.delete(
                    f"{self.api_base_url}/funds/{self.test_fund_id}",
                    headers=self.api_headers,
                    timeout=30,
                )

                if response.status_code == 200:
                    print("✅ Test fund cleaned up successfully via API Gateway")
                else:
                    print(
                        f"⚠️  API cleanup failed: {response.status_code}, trying DynamoDB direct..."
                    )
                    # Fallback to direct DynamoDB deletion
                    self.funds_table.delete_item(Key={"fund_id": self.test_fund_id})
                    print("✅ Test fund cleaned up successfully via DynamoDB")

            except Exception as e:
                print(f"⚠️ Error during fund cleanup: {e}")
                try:
                    # Fallback to direct DynamoDB deletion
                    self.funds_table.delete_item(Key={"fund_id": self.test_fund_id})
                    print("✅ Test fund cleaned up successfully via DynamoDB fallback")
                except Exception as cleanup_error:
                    print(f"❌ Fund cleanup failed completely: {cleanup_error}")

    def run_comprehensive_test(self):
        """Run all market data tests"""
        print("🚀 Starting comprehensive monthly market data test...")
        print(
            "📊 Testing monthly market data input functionality with local validation, API Gateway, and dual verification"
        )

        # Track test results
        results = {
            "authentication": False,
            "fund_creation": False,
            "monthly_data_input": False,
            "historical_data_input": False,
            "market_data_update": False,
            "market_data_retrieval": False,
            "validation_error_handling": False,
        }

        try:
            # Step 1: Authenticate
            if not self.authenticate():
                print("❌ Authentication failed - cannot proceed with tests")
                return results
            results["authentication"] = True

            # Step 2: Create sample fund
            if not self.create_sample_fund_for_market_data():
                print("❌ Fund creation failed - cannot proceed with tests")
                return results
            results["fund_creation"] = True

            # Step 3: Test monthly market data input
            results["monthly_data_input"] = self.test_monthly_market_data_input()

            # Step 4: Test historical monthly data input
            results["historical_data_input"] = self.test_historical_monthly_data_input()

            # Step 5: Test market data update
            results["market_data_update"] = self.test_market_data_update()

            # Step 6: Test market data retrieval
            results["market_data_retrieval"] = self.test_market_data_retrieval()

            # Step 7: Test validation error handling
            results["validation_error_handling"] = (
                self.test_market_data_validation_errors()
            )

        except Exception as e:
            print(f"❌ Unexpected error during testing: {e}")

        finally:
            # Cleanup
            self.cleanup_test_data()

        return results

    def print_test_summary(self, results: Dict[str, bool]):
        """Print a summary of test results"""
        print("\n" + "=" * 85)
        print(
            "📋 COMPREHENSIVE MONTHLY MARKET DATA TEST SUMMARY (Local Validation + API Gateway + Dual Verification)"
        )
        print("=" * 85)

        total_tests = len(results)
        passed_tests = sum(results.values())

        for test_name, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{test_name.replace('_', ' ').title():40} {status}")

        print("-" * 70)
        print(f"Overall Result: {passed_tests}/{total_tests} tests passed")

        if passed_tests == total_tests:
            print(
                "🎉 ALL TESTS PASSED! Monthly market data functionality is working correctly."
            )
            print(
                "✅ Full stack testing (Local Validation -> UI -> API Gateway -> DynamoDB) validated successfully"
            )
        else:
            print("⚠️  Some tests failed. Please check the errors above.")

        print("\n📊 Validation & Verification Methods Used:")
        print("   🔍 Local validation using backend validation logic")
        print("   🗄️  Direct DynamoDB access verification")
        print("   🌐 API Gateway market data endpoint verification")
        print("\n📈 Market Data Features Tested:")
        print("   📅 Monthly market data input (current month)")
        print("   📈 Historical monthly data input (past 3 months)")
        print("   🔄 Market data updates and corrections")
        print("   📖 Market data retrieval (all data and filtered by period)")
        print("   🚫 Validation error handling")
        print("   ✅ Data validation and verification workflows")
        print("=" * 70)


def main():
    """Main function to run the comprehensive market data test"""
    tester = MarketDataTester()
    results = tester.run_comprehensive_test()
    tester.print_test_summary(results)


if __name__ == "__main__":
    main()
