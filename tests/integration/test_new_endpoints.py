#!/usr/bin/env python3
"""
Test the new fund endpoints for fund details and historical data.
"""

import json
import os
import requests
from typing import Dict, Any

# Test configuration
API_BASE_URL = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
TEST_FUND_ID = "US123456789"  # Use a fund ID that exists in your test data


def get_auth_headers() -> Dict[str, str]:
    """Get authentication headers for API requests."""
    # You'll need to implement proper auth token retrieval
    # For now, return empty headers (will need to be updated based on your auth setup)
    return {"Content-Type": "application/json", "Accept": "application/json"}


def test_fund_details_endpoint():
    """Test the /funds/{id}/details endpoint."""
    print(f"\n🧪 Testing Fund Details Endpoint")
    print(f"URL: {API_BASE_URL}/funds/{TEST_FUND_ID}/details")

    try:
        headers = get_auth_headers()
        response = requests.get(
            f"{API_BASE_URL}/funds/{TEST_FUND_ID}/details", headers=headers, timeout=30
        )

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("✅ Success! Fund details retrieved")

            # Validate response structure
            required_fields = ["success", "message", "data"]

            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                print(f"❌ Missing required fields: {missing_fields}")
                return False

            # Check if analytics data is present
            fund_data = data["data"]
            if "analytics" in fund_data:
                print("✅ Analytics data found in response")
                analytics = fund_data["analytics"]

                # Check for key analytics sections
                analytics_sections = [
                    "kpis",
                    "riskMetrics",
                    "assetAllocation",
                    "topHoldings",
                ]
                for section in analytics_sections:
                    if section in analytics:
                        print(f"  ✅ {section} section present")
                    else:
                        print(f"  ❌ {section} section missing")
            else:
                print("❌ No analytics data found in response")

            print(f"Response data preview:")
            print(json.dumps(data, indent=2)[:500] + "...")
            return True

        else:
            print(f"❌ Failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False


def test_fund_historical_endpoint():
    """Test the /funds/{id}/historical endpoint."""
    print(f"\n🧪 Testing Fund Historical Data Endpoint")

    test_periods = ["1D", "1W", "1M", "1Y"]

    for period in test_periods:
        print(f"\n  Testing period: {period}")
        url = f"{API_BASE_URL}/funds/{TEST_FUND_ID}/historical?period={period}"
        print(f"  URL: {url}")

        try:
            headers = get_auth_headers()
            response = requests.get(url, headers=headers, timeout=30)

            print(f"  Status Code: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ Success! Historical data retrieved for {period}")

                # Validate response structure
                if "data" in data and "timePeriod" in data["data"]:
                    historical_data = data["data"]
                    print(f"    Time Period: {historical_data.get('timePeriod')}")

                    if "data" in historical_data and isinstance(
                        historical_data["data"], list
                    ):
                        data_points = len(historical_data["data"])
                        print(f"    Data Points: {data_points}")

                        if data_points > 0:
                            sample_point = historical_data["data"][0]
                            print(f"    Sample Data Point: {sample_point}")

                        print(f"  ✅ Valid historical data structure for {period}")
                    else:
                        print(f"  ❌ Invalid data structure for {period}")
                else:
                    print(f"  ❌ Missing expected data structure for {period}")

            else:
                print(f"  ❌ Failed with status {response.status_code} for {period}")
                print(f"  Response: {response.text}")

        except Exception as e:
            print(f"  ❌ Exception occurred for {period}: {e}")


def test_basic_fund_endpoint_still_works():
    """Test that the basic /funds/{id} endpoint still works."""
    print(f"\n🧪 Testing Basic Fund Endpoint (Regression Test)")
    print(f"URL: {API_BASE_URL}/funds/{TEST_FUND_ID}")

    try:
        headers = get_auth_headers()
        response = requests.get(
            f"{API_BASE_URL}/funds/{TEST_FUND_ID}", headers=headers, timeout=30
        )

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("✅ Success! Basic fund data retrieved")

            # Check that it's different from details endpoint (less data)
            fund_data = data.get("data", {})
            if "analytics" in fund_data:
                print("⚠️  Warning: Basic endpoint contains analytics (unexpected)")
            else:
                print("✅ Basic endpoint correctly excludes analytics data")

            return True

        else:
            print(f"❌ Failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False


def main():
    """Run all endpoint tests."""
    print("🚀 Testing New Fund Endpoints Implementation")
    print("=" * 60)

    # Test results
    results = {
        "fund_details": test_fund_details_endpoint(),
        "fund_historical": test_fund_historical_endpoint(),
        "basic_fund_regression": test_basic_fund_endpoint_still_works(),
    }

    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")

    passed = sum(1 for result in results.values() if result)
    total = len(results)

    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! New endpoints are working correctly.")
    else:
        print("🔧 Some tests failed. Please check the implementation.")

    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
