#!/usr/bin/env python3
"""
Comprehensive Portfolios API Integration Tests
Tests all portfolio endpoints as the frontend would interact with them.
"""

import sys
import os
import json
import requests
from typing import Dict, Any, Optional, List
import time
from decimal import Decimal

# Add the src directory to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
sys.path.insert(0, os.path.join(project_root, "src"))

class PortfoliosAPITester:
    """Comprehensive tester for Portfolios API endpoints"""
    
    def __init__(self):
        self.api_base_url = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
        self.test_results = []
        self.auth_token = None
        self.test_user_email = "<EMAIL>"
        self.test_user_password = "TestPassword123!"
        self.created_portfolio_id = None
        self.sample_fund_id = None
        
    def log_test_result(self, test_name: str, success: bool, message: str, response_data: Dict = None):
        """Log test result"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "response_data": response_data
        }
        self.test_results.append(result)
        
        status_icon = "✅" if success else "❌"
        print(f"{status_icon} {test_name}: {message}")
        if response_data and len(str(response_data)) < 500:
            print(f"   Response: {json.dumps(response_data, indent=2, default=str)}")
        elif response_data:
            print(f"   Response: {json.dumps(response_data, indent=2, default=str)[:300]}...")
        print()
    
    def authenticate(self):
        """Authenticate to get access token"""
        try:
            # Try to authenticate directly with Cognito first
            import boto3
            
            cognito_client = boto3.client("cognito-idp", region_name="ap-northeast-1")
            user_pool_id = "ap-northeast-1_H2kKHGUAT"
            client_id = "2jh76f894g6lv9vrus4qbb9hu7"
            
            # Try different auth flows
            auth_flows = ["ADMIN_USER_PASSWORD_AUTH", "USER_PASSWORD_AUTH"]
            
            for auth_flow in auth_flows:
                try:
                    if auth_flow.startswith("ADMIN_"):
                        response = cognito_client.admin_initiate_auth(
                            UserPoolId=user_pool_id,
                            ClientId=client_id,
                            AuthFlow=auth_flow,
                            AuthParameters={
                                "USERNAME": self.test_user_email,
                                "PASSWORD": self.test_user_password,
                            },
                        )
                    else:
                        response = cognito_client.initiate_auth(
                            ClientId=client_id,
                            AuthFlow=auth_flow,
                            AuthParameters={
                                "USERNAME": self.test_user_email,
                                "PASSWORD": self.test_user_password,
                            },
                        )
                    
                    if "AuthenticationResult" in response:
                        auth_result = response["AuthenticationResult"]
                        self.auth_token = auth_result["AccessToken"]
                        print(f"✅ Cognito authentication successful using {auth_flow}")
                        return True
                        
                except Exception as flow_error:
                    continue
            
            print("⚠️  Authentication failed, proceeding with tests that may require auth...")
            return False
            
        except Exception as e:
            print(f"⚠️  Authentication error: {str(e)}, proceeding with tests...")
            return False
    
    def get_sample_fund_id(self):
        """Get a sample fund ID for testing"""
        try:
            response = requests.get(
                f"{self.api_base_url}/funds",
                params={"limit": "1"},
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                funds = data.get("funds", [])
                if funds:
                    self.sample_fund_id = funds[0].get("fund_id")
                    print(f"📊 Using sample fund ID: {self.sample_fund_id}")
                    return True
            
            print("⚠️  Could not get sample fund ID, some tests may fail...")
            return False
            
        except Exception as e:
            print(f"⚠️  Error getting sample fund ID: {str(e)}")
            return False
    
    def get_auth_headers(self):
        """Get headers with authentication"""
        headers = {"Content-Type": "application/json"}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        return headers
    
    def test_portfolios_health_check(self):
        """Test portfolios API health check"""
        try:
            response = requests.get(f"{self.api_base_url}/portfolios/health")
            
            if response.status_code == 200:
                data = response.json()
                self.log_test_result(
                    "Portfolios Health Check",
                    True,
                    f"Portfolios API is healthy. Status: {response.status_code}",
                    data
                )
                return True
            else:
                self.log_test_result(
                    "Portfolios Health Check",
                    False,
                    f"Unexpected status code: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Portfolios Health Check",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_list_portfolios(self):
        """Test listing all portfolios"""
        try:
            response = requests.get(
                f"{self.api_base_url}/portfolios",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                portfolios_count = len(data.get("portfolios", []))
                self.log_test_result(
                    "List Portfolios",
                    True,
                    f"Successfully retrieved {portfolios_count} portfolios",
                    {"portfolios_count": portfolios_count, "sample_portfolio": data.get("portfolios", [{}])[0] if portfolios_count > 0 else None}
                )
                return True
            else:
                self.log_test_result(
                    "List Portfolios",
                    False,
                    f"Failed to list portfolios. Status: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "List Portfolios",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_create_portfolio(self):
        """Test creating a new portfolio"""
        if not self.auth_token:
            self.log_test_result(
                "Create Portfolio",
                False,
                "No authentication token available",
                {}
            )
            return False
        
        test_portfolio_data = {
            "name": f"Test Portfolio {int(time.time())}",
            "description": "A test portfolio created by integration tests",
            "currency": "USD",
            "initial_investment": "50000.00",
            "risk_tolerance": "moderate",
            "investment_objective": "growth",
            "rebalancing_frequency": "quarterly"
        }
        
        try:
            response = requests.post(
                f"{self.api_base_url}/portfolios",
                json=test_portfolio_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code in [200, 201]:
                created_portfolio = response.json()
                self.created_portfolio_id = created_portfolio.get("portfolio_id")
                self.log_test_result(
                    "Create Portfolio",
                    True,
                    f"Successfully created portfolio: {created_portfolio.get('name')}",
                    {"portfolio_id": self.created_portfolio_id, "portfolio_name": created_portfolio.get('name')}
                )
                return True
            else:
                self.log_test_result(
                    "Create Portfolio",
                    False,
                    f"Failed to create portfolio. Status: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Create Portfolio",
                False,
                f"Create portfolio request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_get_portfolio_by_id(self):
        """Test getting a specific portfolio by ID"""
        if not self.created_portfolio_id:
            # Try to get an existing portfolio
            try:
                response = requests.get(
                    f"{self.api_base_url}/portfolios",
                    params={"limit": "1"},
                    headers=self.get_auth_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    portfolios = data.get("portfolios", [])
                    if portfolios:
                        self.created_portfolio_id = portfolios[0].get("portfolio_id")
                
                if not self.created_portfolio_id:
                    self.log_test_result(
                        "Get Portfolio by ID",
                        False,
                        "No portfolio ID available for testing",
                        {}
                    )
                    return False
                    
            except Exception as e:
                self.log_test_result(
                    "Get Portfolio by ID",
                    False,
                    f"Could not get portfolio for testing: {str(e)}",
                    {"error": str(e)}
                )
                return False
        
        try:
            response = requests.get(
                f"{self.api_base_url}/portfolios/{self.created_portfolio_id}",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                portfolio_data = response.json()
                self.log_test_result(
                    "Get Portfolio by ID",
                    True,
                    f"Successfully retrieved portfolio: {portfolio_data.get('name', 'Unknown')}",
                    {"portfolio_id": self.created_portfolio_id, "portfolio_name": portfolio_data.get('name')}
                )
                return True
            else:
                self.log_test_result(
                    "Get Portfolio by ID",
                    False,
                    f"Failed to get portfolio by ID. Status: {response.status_code}",
                    {"portfolio_id": self.created_portfolio_id, "status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Get Portfolio by ID",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_update_portfolio(self):
        """Test updating an existing portfolio"""
        if not self.auth_token:
            self.log_test_result(
                "Update Portfolio",
                False,
                "No authentication token available",
                {}
            )
            return False
        
        if not self.created_portfolio_id:
            self.log_test_result(
                "Update Portfolio",
                False,
                "No portfolio ID available for update test",
                {}
            )
            return False
        
        update_data = {
            "description": "Updated description for test portfolio",
            "risk_tolerance": "aggressive",
            "investment_objective": "income"
        }
        
        try:
            response = requests.put(
                f"{self.api_base_url}/portfolios/{self.created_portfolio_id}",
                json=update_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                updated_portfolio = response.json()
                self.log_test_result(
                    "Update Portfolio",
                    True,
                    f"Successfully updated portfolio",
                    {"portfolio_id": self.created_portfolio_id, "updated_fields": list(update_data.keys())}
                )
                return True
            else:
                self.log_test_result(
                    "Update Portfolio",
                    False,
                    f"Failed to update portfolio. Status: {response.status_code}",
                    {"portfolio_id": self.created_portfolio_id, "status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Update Portfolio",
                False,
                f"Update portfolio request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_add_fund_to_portfolio(self):
        """Test adding a fund to a portfolio"""
        if not self.auth_token:
            self.log_test_result(
                "Add Fund to Portfolio",
                False,
                "No authentication token available",
                {}
            )
            return False
        
        if not self.created_portfolio_id:
            self.log_test_result(
                "Add Fund to Portfolio",
                False,
                "No portfolio ID available",
                {}
            )
            return False
        
        if not self.sample_fund_id:
            self.log_test_result(
                "Add Fund to Portfolio",
                False,
                "No fund ID available",
                {}
            )
            return False
        
        holding_data = {
            "fund_id": self.sample_fund_id,
            "shares": "100",
            "target_allocation": "25.0",
            "purchase_price": "50.00",
            "purchase_date": "2024-01-01T00:00:00Z"
        }
        
        try:
            response = requests.post(
                f"{self.api_base_url}/portfolios/{self.created_portfolio_id}/holdings",
                json=holding_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code in [200, 201]:
                holding = response.json()
                self.log_test_result(
                    "Add Fund to Portfolio",
                    True,
                    f"Successfully added fund to portfolio",
                    {"portfolio_id": self.created_portfolio_id, "fund_id": self.sample_fund_id, "shares": holding_data["shares"]}
                )
                return True
            else:
                self.log_test_result(
                    "Add Fund to Portfolio",
                    False,
                    f"Failed to add fund to portfolio. Status: {response.status_code}",
                    {"portfolio_id": self.created_portfolio_id, "status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Add Fund to Portfolio",
                False,
                f"Add fund to portfolio request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_get_portfolio_holdings(self):
        """Test getting portfolio holdings"""
        if not self.created_portfolio_id:
            self.log_test_result(
                "Get Portfolio Holdings",
                False,
                "No portfolio ID available",
                {}
            )
            return False
        
        try:
            response = requests.get(
                f"{self.api_base_url}/portfolios/{self.created_portfolio_id}/holdings",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                holdings = response.json()
                holdings_count = len(holdings.get("holdings", []))
                self.log_test_result(
                    "Get Portfolio Holdings",
                    True,
                    f"Successfully retrieved {holdings_count} holdings",
                    {"portfolio_id": self.created_portfolio_id, "holdings_count": holdings_count}
                )
                return True
            else:
                self.log_test_result(
                    "Get Portfolio Holdings",
                    False,
                    f"Failed to get portfolio holdings. Status: {response.status_code}",
                    {"portfolio_id": self.created_portfolio_id, "status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Get Portfolio Holdings",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_get_portfolio_performance(self):
        """Test getting portfolio performance metrics"""
        if not self.created_portfolio_id:
            self.log_test_result(
                "Get Portfolio Performance",
                False,
                "No portfolio ID available",
                {}
            )
            return False
        
        try:
            response = requests.get(
                f"{self.api_base_url}/portfolios/{self.created_portfolio_id}/performance",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                performance = response.json()
                self.log_test_result(
                    "Get Portfolio Performance",
                    True,
                    f"Successfully retrieved portfolio performance",
                    {"portfolio_id": self.created_portfolio_id, "has_returns": "total_return" in performance}
                )
                return True
            else:
                self.log_test_result(
                    "Get Portfolio Performance",
                    False,
                    f"Failed to get portfolio performance. Status: {response.status_code}",
                    {"portfolio_id": self.created_portfolio_id, "status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Get Portfolio Performance",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_rebalance_portfolio(self):
        """Test portfolio rebalancing"""
        if not self.auth_token:
            self.log_test_result(
                "Rebalance Portfolio",
                False,
                "No authentication token available",
                {}
            )
            return False
        
        if not self.created_portfolio_id:
            self.log_test_result(
                "Rebalance Portfolio",
                False,
                "No portfolio ID available",
                {}
            )
            return False
        
        try:
            response = requests.post(
                f"{self.api_base_url}/portfolios/{self.created_portfolio_id}/rebalance",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                rebalance_result = response.json()
                self.log_test_result(
                    "Rebalance Portfolio",
                    True,
                    f"Successfully initiated portfolio rebalancing",
                    {"portfolio_id": self.created_portfolio_id, "rebalance_status": rebalance_result.get("status")}
                )
                return True
            else:
                self.log_test_result(
                    "Rebalance Portfolio",
                    False,
                    f"Failed to rebalance portfolio. Status: {response.status_code}",
                    {"portfolio_id": self.created_portfolio_id, "status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Rebalance Portfolio",
                False,
                f"Rebalance request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_delete_portfolio(self):
        """Test deleting a portfolio"""
        if not self.auth_token:
            self.log_test_result(
                "Delete Portfolio",
                False,
                "No authentication token available",
                {}
            )
            return False
        
        if not self.created_portfolio_id:
            self.log_test_result(
                "Delete Portfolio",
                False,
                "No portfolio ID available for delete test",
                {}
            )
            return False
        
        try:
            response = requests.delete(
                f"{self.api_base_url}/portfolios/{self.created_portfolio_id}",
                headers=self.get_auth_headers()
            )
            
            if response.status_code in [200, 204]:
                self.log_test_result(
                    "Delete Portfolio",
                    True,
                    f"Successfully deleted portfolio",
                    {"portfolio_id": self.created_portfolio_id}
                )
                return True
            else:
                self.log_test_result(
                    "Delete Portfolio",
                    False,
                    f"Failed to delete portfolio. Status: {response.status_code}",
                    {"portfolio_id": self.created_portfolio_id, "status_code": response.status_code, "response": response.text}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Delete Portfolio",
                False,
                f"Delete portfolio request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def test_invalid_portfolio_id(self):
        """Test accessing non-existent portfolio"""
        invalid_id = "invalid-portfolio-id-12345"
        
        try:
            response = requests.get(
                f"{self.api_base_url}/portfolios/{invalid_id}",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 404:
                self.log_test_result(
                    "Invalid Portfolio ID",
                    True,
                    f"Correctly returned 404 for invalid portfolio ID",
                    {"portfolio_id": invalid_id, "status_code": response.status_code}
                )
                return True
            else:
                self.log_test_result(
                    "Invalid Portfolio ID",
                    False,
                    f"Unexpected response for invalid portfolio ID. Status: {response.status_code}",
                    {"portfolio_id": invalid_id, "status_code": response.status_code}
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Invalid Portfolio ID",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)}
            )
            return False
    
    def run_all_tests(self):
        """Run all portfolios API tests"""
        print("💼 Starting Comprehensive Portfolios API Tests")
        print("=" * 60)
        
        # Authenticate first
        print("🔐 Authenticating...")
        self.authenticate()
        
        # Get sample fund ID
        print("📊 Getting sample fund ID...")
        self.get_sample_fund_id()
        
        # Test sequence that mimics frontend flow
        tests = [
            self.test_portfolios_health_check,
            self.test_list_portfolios,
            self.test_create_portfolio,
            self.test_get_portfolio_by_id,
            self.test_update_portfolio,
            self.test_add_fund_to_portfolio,
            self.test_get_portfolio_holdings,
            self.test_get_portfolio_performance,
            self.test_rebalance_portfolio,
            self.test_delete_portfolio,
            self.test_invalid_portfolio_id
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        # Summary
        print("=" * 60)
        print("📊 PORTFOLIOS API TEST SUMMARY")
        print("=" * 60)
        print(f"Total tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success rate: {(passed/total)*100:.1f}%")
        
        # Save detailed results
        results_file = "portfolios_api_test_results.json"
        with open(results_file, "w") as f:
            json.dump({
                "summary": {
                    "total_tests": total,
                    "passed": passed,
                    "failed": total - passed,
                    "success_rate": f"{(passed/total)*100:.1f}%",
                    "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "auth_available": self.auth_token is not None,
                    "sample_fund_available": self.sample_fund_id is not None
                },
                "test_results": self.test_results
            }, f, indent=2, default=str)
        
        print(f"\n📁 Detailed results saved to: {results_file}")
        
        return passed == total


def main():
    """Main function to run portfolios API tests"""
    tester = PortfoliosAPITester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ All portfolios API tests passed!")
        return 0
    else:
        print("\n❌ Some portfolios API tests failed!")
        return 1


if __name__ == "__main__":
    exit(main())