#!/usr/bin/env python3
"""
Simple endpoint test for the new fund details and historical endpoints.
This script tests that the endpoints exist and return the correct response format.
"""

import requests
import json
import sys

# API Configuration
BASE_URL = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"


def test_endpoint_exists(endpoint, expected_status=401):
    """Test that an endpoint exists and returns expected status for unauthorized access"""
    try:
        url = f"{BASE_URL}{endpoint}"
        print(f"\n🧪 Testing: {endpoint}")
        print(f"   URL: {url}")

        response = requests.get(url, timeout=10)

        print(f"   Status: {response.status_code}")

        if response.status_code == expected_status:
            print(f"   ✅ Expected status {expected_status} - endpoint exists!")
            return True
        elif response.status_code == 403:
            print(f"   ✅ Status 403 - endpoint exists but requires auth token!")
            return True
        else:
            print(f"   ❌ Unexpected status {response.status_code}")
            try:
                response_data = response.json()
                print(f"   Response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"   Response: {response.text}")
            return False

    except Exception as e:
        print(f"   ❌ Error testing {endpoint}: {str(e)}")
        return False


def main():
    """Test all endpoints to verify deployment"""
    print("🚀 Simple Endpoint Verification Test")
    print("=" * 50)
    print("Testing that new endpoints are deployed and accessible...")
    print("(We expect 401/403 responses since we're not authenticated)")

    # Test endpoints
    endpoints = [
        "/funds",
        "/funds/FUND-001",
        "/funds/FUND-001/details",  # NEW
        "/funds/FUND-001/historical",  # NEW
        "/health",
    ]

    results = []

    for endpoint in endpoints:
        success = test_endpoint_exists(endpoint)
        results.append((endpoint, success))

    # Summary
    print("\n" + "=" * 50)
    print("📊 ENDPOINT VERIFICATION SUMMARY")
    print("=" * 50)

    for endpoint, success in results:
        status = "✅ EXISTS" if success else "❌ MISSING"
        print(f"{endpoint:30} {status}")

    passed = sum(1 for _, success in results if success)
    total = len(results)

    print(f"\nEndpoints verified: {passed}/{total}")

    if passed == total:
        print("\n🎉 All endpoints are deployed successfully!")
        print("The 401/403 responses are expected for unauthorized requests.")
        print("This confirms that:")
        print("  ✓ API Gateway routing is working")
        print("  ✓ Lambda functions are deployed")
        print("  ✓ New endpoints /funds/{id}/details and /funds/{id}/historical exist")
        print("  ✓ Authentication is properly configured")
    else:
        print(f"\n❌ {total - passed} endpoints are missing or not working")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
