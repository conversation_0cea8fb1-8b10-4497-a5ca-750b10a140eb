#!/usr/bin/env python3
"""
Local test script for fund validation functionality
"""

import sys
import os

sys.path.append("src")

from src.shared.validators.fund_validation import (
    FundValidationService,
    ValidationResult,
)
from src.shared.models.requests import FundCreateRequest


def test_validation():
    print("🧪 Testing Fund Validation Service")
    print("=" * 50)

    validator = FundValidationService()

    # Test 1: Valid fund data
    print("\n1. Testing valid fund data...")
    valid_data = {
        "fund_id": "VALID-FUND-001",
        "name": "Test Growth Fund",
        "fund_type": "equity",
        "status": "active",
        "nav": 10.50,
        "currency": "USD",
        "risk_level": "moderate",
    }

    try:
        fund_request = FundCreateRequest(**valid_data)
        result = validator.validate_create(fund_request)
        print(f"✅ Valid fund validation: {result.is_valid}")
        if result.errors:
            print(f"   Errors: {result.errors}")
        if result.warnings:
            print(f"   Warnings: {result.warnings}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test 2: Invalid NAV
    print("\n2. Testing invalid NAV...")
    invalid_data = valid_data.copy()
    invalid_data["nav"] = -5.0

    try:
        fund_request = FundCreateRequest(**invalid_data)
        result = validator.validate_create(fund_request)
        print(f"❌ Invalid NAV validation: {result.is_valid}")
        if result.errors:
            print(f"   Errors: {result.errors}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test 3: Invalid Fund ID
    print("\n3. Testing invalid fund ID...")
    invalid_id_data = valid_data.copy()
    invalid_id_data["fund_id"] = "invalid fund id with spaces!"

    try:
        fund_request = FundCreateRequest(**invalid_id_data)
        result = validator.validate_create(fund_request)
        print(f"❌ Invalid Fund ID validation: {result.is_valid}")
        if result.errors:
            print(f"   Errors: {result.errors}")
    except Exception as e:
        print(f"❌ Error: {e}")

    print("\n✅ Basic validation tests completed!")


if __name__ == "__main__":
    test_validation()
