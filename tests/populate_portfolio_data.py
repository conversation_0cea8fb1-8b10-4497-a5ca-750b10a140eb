#!/usr/bin/env python3
"""
Populate the fundflow-dev-portfolios DynamoDB table with sample portfolio data.
Based on the generateMockPortfolios function from frontend/src/lib/api.ts
"""

import boto3
import json
import random
import time
from datetime import datetime, timedelta
from decimal import Decimal
import uuid

# Initialize DynamoDB
dynamodb = boto3.resource("dynamodb", region_name="ap-northeast-1")
table = dynamodb.Table("fundflow-dev-portfolios")


def generate_mock_funds():
    """Generate mock fund data that portfolios will reference"""
    fund_types = ["mutual_fund", "etf", "index_fund", "bond_fund", "money_market"]
    risk_levels = ["low", "medium", "high"]
    categories = ["Equity", "Debt", "Hybrid", "International", "Sectoral", "Tax Saving"]
    fund_managers = ["Aditya Birla", "HDFC", "ICICI Prudential", "SBI", "Axis", "Kotak"]

    funds = []
    for index in range(50):
        nav = 15 + random.random() * 500
        previous_nav = nav - (random.random() - 0.5) * 10
        change = nav - previous_nav
        change_percent = (change / previous_nav) * 100 if previous_nav > 0 else 0

        fund = {
            "id": f"fund-{index + 1}",
            "name": f"{fund_managers[index % len(fund_managers)]} {categories[index % len(categories)]} Fund {index + 1}",
            "symbol": f"{categories[index % len(categories)].upper()[:3]}{str(index + 1).zfill(3)}",
            "type": fund_types[index % len(fund_types)],
            "category": categories[index % len(categories)],
            "subCategory": ["Large Cap", "Mid Cap", "Small Cap"][index % 3],
            "nav": round(nav, 2),
            "previousNav": round(previous_nav, 2),
            "change": round(change, 2),
            "changePercent": round(change_percent, 2),
            "volume": random.randint(10000, 1000000),
            "aum": random.randint(500, 10000),  # in crores
            "expenseRatio": round(0.5 + random.random() * 2, 2),
            "minimumInvestment": [500, 1000, 5000, 10000][index % 4],
            "riskLevel": risk_levels[index % len(risk_levels)],
            "rating": random.randint(1, 5),
            "inceptionDate": (
                datetime(2020, 1, 1) - timedelta(days=random.randint(0, 3650))
            ).isoformat(),
            "fundManager": fund_managers[index % len(fund_managers)],
            "description": f"A professionally managed investment fund that pools money from many investors to purchase securities. This {categories[index % len(categories)].lower()} fund focuses on long-term capital appreciation.",
            "performance": {
                "oneDay": round((random.random() - 0.5), 2),
                "oneWeek": round((random.random() * 4 - 2), 2),
                "oneMonth": round((random.random() * 8 - 4), 2),
                "threeMonths": round((random.random() * 15 - 7.5), 2),
                "sixMonths": round((random.random() * 20 - 10), 2),
                "oneYear": round((random.random() * 30 - 15), 2),
                "threeYears": round((random.random() * 40 - 10), 2),
                "fiveYears": round((random.random() * 60 - 5), 2),
            },
            "holdings": [
                {
                    "id": "1",
                    "name": "Reliance Industries",
                    "symbol": "RELIANCE",
                    "percentage": 8.5,
                },
                {
                    "id": "2",
                    "name": "HDFC Bank",
                    "symbol": "HDFCBANK",
                    "percentage": 7.2,
                },
                {"id": "3", "name": "Infosys", "symbol": "INFY", "percentage": 6.8},
            ],
            "sectors": [
                {"name": "Financial Services", "percentage": 25.4},
                {"name": "Information Technology", "percentage": 18.7},
                {"name": "Consumer Goods", "percentage": 12.3},
            ],
            "createdAt": datetime(2023, 1, 1).isoformat(),
            "updatedAt": datetime.now().isoformat(),
        }
        funds.append(fund)

    return funds


def generate_mock_portfolios():
    """Generate mock portfolio data based on the TypeScript generateMockPortfolios function"""
    portfolio_types = [
        "personal",
        "retirement",
        "taxable",
        "trust",
        "corporate",
        "education",
    ]
    portfolio_statuses = ["active", "inactive", "closed"]
    risk_levels = ["very_low", "low", "moderate", "high", "very_high"]
    portfolio_names = [
        "Growth Portfolio",
        "Conservative Portfolio",
        "Balanced Portfolio",
        "Aggressive Growth",
        "Income Portfolio",
        "Retirement Fund",
        "Emergency Fund",
        "Education Savings",
        "Tax-Advantaged Portfolio",
        "International Portfolio",
    ]

    # Generate mock funds for portfolio holdings
    mock_funds = generate_mock_funds()

    portfolios = []

    for index in range(10):
        total_value = 50000 + random.random() * 500000
        total_cost_basis = total_value * (
            0.8 + random.random() * 0.3
        )  # 80-110% of current value
        total_gain_loss = total_value - total_cost_basis
        total_gain_loss_pct = (
            (total_gain_loss / total_cost_basis) * 100 if total_cost_basis > 0 else 0
        )
        cash_balance = total_value * (0.02 + random.random() * 0.08)  # 2-10% cash

        # Generate holdings
        num_holdings = 3 + random.randint(0, 7)  # 3-10 holdings
        selected_funds = mock_funds[:num_holdings]

        holdings = []
        for fund in selected_funds:
            weight = random.random() * 30 + 5  # 5-35% weight
            market_value = (total_value - cash_balance) * (weight / 100)
            shares = market_value / fund["nav"]
            average_cost = fund["nav"] * (
                0.9 + random.random() * 0.2
            )  # ±10% from current price
            cost_basis = shares * average_cost
            unrealized_gain_loss = market_value - cost_basis
            unrealized_gain_loss_pct = (
                (unrealized_gain_loss / cost_basis) * 100 if cost_basis > 0 else 0
            )

            holding = {
                "fund_id": fund["id"],
                "fund_name": fund["name"],
                "fund_symbol": fund["symbol"],
                "shares": round(shares, 4),
                "average_cost": round(average_cost, 2),
                "current_price": fund["nav"],
                "market_value": round(market_value, 2),
                "cost_basis": round(cost_basis, 2),
                "unrealized_gain_loss": round(unrealized_gain_loss, 2),
                "unrealized_gain_loss_pct": round(unrealized_gain_loss_pct, 2),
                "weight": round(weight, 2),
                "first_purchase_date": (
                    datetime.now() - timedelta(days=random.randint(0, 730))
                ).isoformat(),
                "last_updated": datetime.now().isoformat(),
            }
            holdings.append(holding)

        # Generate recent transactions
        num_transactions = 5 + random.randint(0, 9)  # 5-15 transactions
        transaction_types = ["buy", "sell", "dividend", "interest", "fee"]

        recent_transactions = []
        for tx_index in range(min(num_transactions, 10)):
            fund = random.choice(selected_funds)
            transaction_type = random.choice(transaction_types)
            shares = (
                round(random.random() * 100 + 10, 4)
                if transaction_type in ["buy", "sell"]
                else None
            )
            price = (
                round(fund["nav"] * (0.95 + random.random() * 0.1), 2)
                if transaction_type in ["buy", "sell"]
                else None
            )
            amount = (
                shares * price
                if shares and price
                else round(random.random() * 1000 + 100, 2)
            )
            fees = (
                round(random.random() * 20 + 5, 2)
                if transaction_type in ["buy", "sell"]
                else 0
            )

            transaction = {
                "transaction_id": f"tx-{index}-{tx_index}",
                "fund_id": fund["id"],
                "fund_name": fund["name"],
                "fund_symbol": fund["symbol"],
                "transaction_type": transaction_type,
                "transaction_date": (
                    datetime.now() - timedelta(days=random.randint(0, 90))
                ).isoformat(),
                "shares": shares,
                "price": price,
                "amount": round(amount, 2),
                "fees": round(fees, 2),
                "net_amount": round(amount - fees, 2),
                "description": f'{transaction_type.capitalize()} transaction for {fund["name"]}',
                "reference_number": f"REF{int(time.time())}{tx_index}",
                "created_at": (
                    datetime.now() - timedelta(days=random.randint(0, 90))
                ).isoformat(),
            }
            recent_transactions.append(transaction)

        # Generate performance metrics
        performance = {
            "total_return": round(total_gain_loss, 2),
            "total_return_pct": round(total_gain_loss_pct, 2),
            "one_day_return": round(random.random() * 4 - 2, 2),
            "one_week_return": round(random.random() * 8 - 4, 2),
            "one_month_return": round(random.random() * 12 - 6, 2),
            "three_month_return": round(random.random() * 20 - 10, 2),
            "six_month_return": round(random.random() * 30 - 15, 2),
            "one_year_return": round(random.random() * 40 - 20, 2),
            "three_year_return": round(random.random() * 60 - 30, 2),
            "five_year_return": round(random.random() * 80 - 40, 2),
            "inception_return": round(total_gain_loss_pct, 2),
            "volatility": round(random.random() * 20 + 5, 2),
            "sharpe_ratio": round(random.random() * 2 - 0.5, 2),
            "max_drawdown": round(random.random() * -15 - 5, 2),
            "benchmark_return": round(random.random() * 30 - 15, 2),
            "alpha": round(random.random() * 10 - 5, 2),
            "beta": round(random.random() * 0.5 + 0.8, 2),
            "as_of_date": datetime.now().isoformat(),
        }

        # Create portfolio with backend snake_case field names
        portfolio = {
            "portfolio_id": f"portfolio-{index + 1}",
            "name": f"{portfolio_names[index % len(portfolio_names)]} {index + 1}",
            "description": f"A {portfolio_types[index % len(portfolio_types)]} portfolio designed for {risk_levels[index % len(risk_levels)]} risk tolerance with diversified holdings.",
            "portfolio_type": portfolio_types[index % len(portfolio_types)],
            "status": portfolio_statuses[index % len(portfolio_statuses)],
            "user_id": f"user-{index // 2 + 1}",  # Multiple portfolios per user
            "base_currency": "USD",
            "inception_date": (
                datetime.now() - timedelta(days=random.randint(0, 1095))
            ).isoformat(),
            "total_value": round(total_value, 2),
            "total_cost_basis": round(total_cost_basis, 2),
            "cash_balance": round(cash_balance, 2),
            "total_gain_loss": round(total_gain_loss, 2),
            "total_gain_loss_pct": round(total_gain_loss_pct, 2),
            "holdings": holdings,
            "recent_transactions": recent_transactions,
            "performance": performance,
            "risk_level": risk_levels[index % len(risk_levels)],
            "benchmark": ["S&P 500", "NASDAQ", "Russell 2000"][index % 3],
            "tags": [
                f"{portfolio_types[index % len(portfolio_types)]}",
                f"{risk_levels[index % len(risk_levels)]}-risk",
            ],
            "custom_fields": {
                "advisor": f"Advisor {index // 3 + 1}",
                "strategy": f"Strategy {index % 4 + 1}",
                "rebalance_frequency": (
                    "quarterly" if index % 2 == 0 else "semi-annually"
                ),
            },
            "created_at": (
                datetime.now() - timedelta(days=random.randint(0, 1095))
            ).isoformat(),
            "updated_at": datetime.now().isoformat(),
            "last_rebalanced": (
                (datetime.now() - timedelta(days=random.randint(0, 90))).isoformat()
                if random.random() > 0.3
                else None
            ),
        }

        portfolios.append(portfolio)

    return portfolios


def convert_floats_to_decimal(obj):
    """Convert float values to Decimal for DynamoDB compatibility"""
    if isinstance(obj, dict):
        return {k: convert_floats_to_decimal(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_floats_to_decimal(v) for v in obj]
    elif isinstance(obj, float):
        return Decimal(str(obj))
    else:
        return obj


def populate_portfolios():
    """Populate the DynamoDB table with sample portfolio data"""
    print("🚀 Generating mock portfolio data...")
    portfolios = generate_mock_portfolios()

    print(f"📊 Generated {len(portfolios)} portfolios")

    # Convert floats to Decimal for DynamoDB
    portfolios_decimal = [
        convert_floats_to_decimal(portfolio) for portfolio in portfolios
    ]

    print("💾 Inserting portfolios into DynamoDB...")

    # Insert portfolios into DynamoDB
    success_count = 0
    error_count = 0

    for i, portfolio in enumerate(portfolios_decimal):
        try:
            table.put_item(Item=portfolio)
            success_count += 1
            print(f"✅ Inserted portfolio {i+1}/{len(portfolios)}: {portfolio['name']}")
        except Exception as e:
            error_count += 1
            print(
                f"❌ Failed to insert portfolio {i+1}/{len(portfolios)}: {portfolio['name']} - {str(e)}"
            )

    print(f"\n🎉 Portfolio data population complete!")
    print(f"✅ Successfully inserted: {success_count} portfolios")
    if error_count > 0:
        print(f"❌ Failed to insert: {error_count} portfolios")

    # Print summary of inserted data
    print(f"\n📋 Summary:")
    print(f"Table: fundflow-dev-portfolios")
    print(f"Total portfolios: {success_count}")

    if success_count > 0:
        print(f"\n📊 Sample portfolio data:")
        sample_portfolio = portfolios[0]
        print(f"- Portfolio ID: {sample_portfolio['portfolio_id']}")
        print(f"- Name: {sample_portfolio['name']}")
        print(f"- Type: {sample_portfolio['portfolio_type']}")
        print(f"- Status: {sample_portfolio['status']}")
        print(f"- Total Value: ${sample_portfolio['total_value']:,.2f}")
        print(f"- Holdings Count: {len(sample_portfolio['holdings'])}")
        print(f"- Transactions Count: {len(sample_portfolio['recent_transactions'])}")


if __name__ == "__main__":
    try:
        populate_portfolios()
    except Exception as e:
        print(f"❌ Error during portfolio data population: {str(e)}")
        import traceback

        traceback.print_exc()
