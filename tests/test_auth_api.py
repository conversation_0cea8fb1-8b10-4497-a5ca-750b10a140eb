"""
Tests for authentication API functions.
"""

import json
import pytest
from unittest.mock import patch, MagicMock
from botocore.exceptions import ClientError

# Import the functions we want to test
from src.functions.api.auth import (
    handler,
    handle_forgot_password,
    handle_confirm_forgot_password,
    handle_change_password,
    _mask_email,
    _extract_access_token,
)


class TestAuthHandler:
    """Test the main auth handler routing."""

    def test_forgot_password_routing(self):
        """Test that forgot password requests are routed correctly."""
        event = {
            "httpMethod": "POST",
            "path": "/auth/forgot-password",
            "body": json.dumps({"email": "<EMAIL>"}),
        }

        with patch("src.functions.api.auth.handle_forgot_password") as mock_handler:
            mock_handler.return_value = {"statusCode": 200}
            result = handler(event, MagicMock())
            mock_handler.assert_called_once_with(event)

    def test_confirm_forgot_password_routing(self):
        """Test that confirm forgot password requests are routed correctly."""
        event = {
            "httpMethod": "POST",
            "path": "/auth/confirm-forgot-password",
            "body": json.dumps(
                {
                    "email": "<EMAIL>",
                    "confirmation_code": "123456",
                    "new_password": "NewPassword123!",
                }
            ),
        }

        with patch(
            "src.functions.api.auth.handle_confirm_forgot_password"
        ) as mock_handler:
            mock_handler.return_value = {"statusCode": 200}
            result = handler(event, MagicMock())
            mock_handler.assert_called_once_with(event)

    def test_change_password_routing(self):
        """Test that change password requests are routed correctly."""
        event = {
            "httpMethod": "PUT",
            "path": "/auth/change-password",
            "body": json.dumps(
                {
                    "current_password": "OldPassword123!",
                    "new_password": "NewPassword123!",
                    "confirm_password": "NewPassword123!",
                }
            ),
        }

        with patch("src.functions.api.auth.handle_change_password") as mock_handler:
            mock_handler.return_value = {"statusCode": 200}
            result = handler(event, MagicMock())
            mock_handler.assert_called_once_with(event)

    def test_unsupported_method(self):
        """Test that unsupported HTTP methods return 405."""
        event = {
            "httpMethod": "GET",
            "path": "/auth/forgot-password",
        }

        result = handler(event, MagicMock())
        assert result["statusCode"] == 405

    def test_unsupported_path(self):
        """Test that unsupported paths return 404."""
        event = {
            "httpMethod": "POST",
            "path": "/auth/unknown-endpoint",
        }

        result = handler(event, MagicMock())
        assert result["statusCode"] == 404

    def test_cors_options_request(self):
        """Test that CORS OPTIONS requests are handled."""
        event = {"httpMethod": "OPTIONS"}

        with patch(
            "src.functions.api.auth.CORSHandler.is_options_request", return_value=True
        ), patch("src.functions.api.auth.CORSHandler.handle_options") as mock_options:
            mock_options.return_value = {"statusCode": 200}
            result = handler(event, MagicMock())
            mock_options.assert_called_once()


class TestForgotPassword:
    """Test forgot password functionality."""

    @patch("src.functions.api.auth.cognito_client")
    def test_forgot_password_success(self, mock_cognito):
        """Test successful forgot password request."""
        # Mock Cognito response
        mock_cognito.forgot_password.return_value = {
            "CodeDeliveryDetails": {
                "Destination": "t***@example.com",
                "DeliveryMedium": "EMAIL",
            }
        }

        event = {"body": json.dumps({"email": "<EMAIL>"})}

        with patch("src.functions.api.auth.USER_POOL_CLIENT_ID", "test-client-id"):
            result = handle_forgot_password(event)

        assert result["statusCode"] == 200
        body = json.loads(result["body"])
        assert "message" in body["data"]
        assert "delivery_medium" in body["data"]

        mock_cognito.forgot_password.assert_called_once_with(
            ClientId="test-client-id", Username="<EMAIL>"
        )

    @patch("src.functions.api.auth.cognito_client")
    def test_forgot_password_user_not_found(self, mock_cognito):
        """Test forgot password with non-existent user."""
        # Mock Cognito error
        mock_cognito.forgot_password.side_effect = ClientError(
            error_response={
                "Error": {"Code": "UserNotFoundException", "Message": "User not found"}
            },
            operation_name="ForgotPassword",
        )

        event = {"body": json.dumps({"email": "<EMAIL>"})}

        result = handle_forgot_password(event)

        # Should still return success for security reasons
        assert result["statusCode"] == 200
        body = json.loads(result["body"])
        assert "If an account with that email exists" in body["data"]["message"]

    @patch("src.functions.api.auth.cognito_client")
    def test_forgot_password_rate_limit(self, mock_cognito):
        """Test forgot password rate limiting."""
        # Mock Cognito rate limit error
        mock_cognito.forgot_password.side_effect = ClientError(
            error_response={
                "Error": {
                    "Code": "LimitExceededException",
                    "Message": "Rate limit exceeded",
                }
            },
            operation_name="ForgotPassword",
        )

        event = {"body": json.dumps({"email": "<EMAIL>"})}

        result = handle_forgot_password(event)

        assert result["statusCode"] == 429
        body = json.loads(result["body"])
        assert body["error"] == "RATE_LIMIT_EXCEEDED"

    def test_forgot_password_invalid_body(self):
        """Test forgot password with invalid request body."""
        event = {"body": "invalid json"}

        result = handle_forgot_password(event)

        assert result["statusCode"] == 400

    def test_forgot_password_missing_email(self):
        """Test forgot password with missing email."""
        event = {"body": json.dumps({})}

        result = handle_forgot_password(event)

        assert result["statusCode"] == 422


class TestConfirmForgotPassword:
    """Test confirm forgot password functionality."""

    @patch("src.functions.api.auth.cognito_client")
    def test_confirm_forgot_password_success(self, mock_cognito):
        """Test successful password reset confirmation."""
        # Mock successful Cognito response
        mock_cognito.confirm_forgot_password.return_value = {}

        event = {
            "body": json.dumps(
                {
                    "email": "<EMAIL>",
                    "confirmation_code": "123456",
                    "new_password": "NewPassword123!",
                }
            )
        }

        with patch("src.functions.api.auth.USER_POOL_CLIENT_ID", "test-client-id"):
            result = handle_confirm_forgot_password(event)

        assert result["statusCode"] == 200
        body = json.loads(result["body"])
        assert "successfully reset" in body["data"]["message"]

        mock_cognito.confirm_forgot_password.assert_called_once_with(
            ClientId="test-client-id",
            Username="<EMAIL>",
            ConfirmationCode="123456",
            Password="NewPassword123!",
        )

    @patch("src.functions.api.auth.cognito_client")
    def test_confirm_forgot_password_invalid_code(self, mock_cognito):
        """Test password reset with invalid confirmation code."""
        # Mock Cognito error
        mock_cognito.confirm_forgot_password.side_effect = ClientError(
            error_response={
                "Error": {"Code": "CodeMismatchException", "Message": "Invalid code"}
            },
            operation_name="ConfirmForgotPassword",
        )

        event = {
            "body": json.dumps(
                {
                    "email": "<EMAIL>",
                    "confirmation_code": "wrong_code",
                    "new_password": "NewPassword123!",
                }
            )
        }

        result = handle_confirm_forgot_password(event)

        assert result["statusCode"] == 400
        body = json.loads(result["body"])
        assert "Invalid verification code" in body["message"]

    @patch("src.functions.api.auth.cognito_client")
    def test_confirm_forgot_password_expired_code(self, mock_cognito):
        """Test password reset with expired confirmation code."""
        # Mock Cognito error
        mock_cognito.confirm_forgot_password.side_effect = ClientError(
            error_response={
                "Error": {"Code": "ExpiredCodeException", "Message": "Code expired"}
            },
            operation_name="ConfirmForgotPassword",
        )

        event = {
            "body": json.dumps(
                {
                    "email": "<EMAIL>",
                    "confirmation_code": "123456",
                    "new_password": "NewPassword123!",
                }
            )
        }

        result = handle_confirm_forgot_password(event)

        assert result["statusCode"] == 400
        body = json.loads(result["body"])
        assert "expired" in body["message"]

    def test_confirm_forgot_password_short_password(self):
        """Test password reset with password too short."""
        event = {
            "body": json.dumps(
                {
                    "email": "<EMAIL>",
                    "confirmation_code": "123456",
                    "new_password": "short",
                }
            )
        }

        result = handle_confirm_forgot_password(event)

        assert result["statusCode"] == 400
        body = json.loads(result["body"])
        assert "8 characters" in body["message"]

    def test_confirm_forgot_password_missing_fields(self):
        """Test password reset with missing required fields."""
        event = {
            "body": json.dumps(
                {
                    "email": "<EMAIL>"
                    # Missing confirmation_code and new_password
                }
            )
        }

        result = handle_confirm_forgot_password(event)

        assert result["statusCode"] == 400
        body = json.loads(result["body"])
        assert "Missing required field" in body["message"]


class TestChangePassword:
    """Test change password functionality."""

    @patch("src.functions.api.auth.cognito_client")
    def test_change_password_success(self, mock_cognito):
        """Test successful password change for authenticated user."""
        # Mock successful Cognito response
        mock_cognito.change_password.return_value = {}

        event = {
            "requestContext": {
                "authorizer": {"sub": "user-123", "email": "<EMAIL>"}
            },
            "headers": {"Authorization": "Bearer test-access-token"},
            "body": json.dumps(
                {
                    "current_password": "OldPassword123!",
                    "new_password": "NewPassword123!",
                    "confirm_password": "NewPassword123!",
                }
            ),
        }

        result = handle_change_password(event)

        assert result["statusCode"] == 200
        body = json.loads(result["body"])
        assert "successfully changed" in body["data"]["message"]

        mock_cognito.change_password.assert_called_once_with(
            AccessToken="test-access-token",
            PreviousPassword="OldPassword123!",
            ProposedPassword="NewPassword123!",
        )

    @patch("src.functions.api.auth.cognito_client")
    def test_change_password_wrong_current_password(self, mock_cognito):
        """Test password change with incorrect current password."""
        # Mock Cognito error
        mock_cognito.change_password.side_effect = ClientError(
            error_response={
                "Error": {
                    "Code": "NotAuthorizedException",
                    "Message": "Incorrect password",
                }
            },
            operation_name="ChangePassword",
        )

        event = {
            "requestContext": {
                "authorizer": {"sub": "user-123", "email": "<EMAIL>"}
            },
            "headers": {"Authorization": "Bearer test-access-token"},
            "body": json.dumps(
                {
                    "current_password": "WrongPassword123!",
                    "new_password": "NewPassword123!",
                    "confirm_password": "NewPassword123!",
                }
            ),
        }

        result = handle_change_password(event)

        assert result["statusCode"] == 400
        body = json.loads(result["body"])
        assert "Current password is incorrect" in body["message"]

    def test_change_password_no_auth_token(self):
        """Test password change without authentication token."""
        event = {
            "requestContext": {
                "authorizer": {"sub": "user-123", "email": "<EMAIL>"}
            },
            "headers": {},  # No Authorization header
            "body": json.dumps(
                {
                    "current_password": "OldPassword123!",
                    "new_password": "NewPassword123!",
                    "confirm_password": "NewPassword123!",
                }
            ),
        }

        result = handle_change_password(event)

        assert result["statusCode"] == 401
        body = json.loads(result["body"])
        assert "Access token is required" in body["message"]

    def test_change_password_invalid_request_data(self):
        """Test password change with invalid request data."""
        event = {
            "requestContext": {
                "authorizer": {"sub": "user-123", "email": "<EMAIL>"}
            },
            "headers": {"Authorization": "Bearer test-access-token"},
            "body": json.dumps(
                {
                    "current_password": "OldPassword123!",
                    "new_password": "NewPassword123!",
                    "confirm_password": "DifferentPassword123!",  # Doesn't match
                }
            ),
        }

        result = handle_change_password(event)

        assert result["statusCode"] == 422
        body = json.loads(result["body"])
        assert body["error"] == "VALIDATION_ERROR"


class TestUtilityFunctions:
    """Test utility functions."""

    def test_mask_email_normal(self):
        """Test email masking with normal email."""
        result = _mask_email("<EMAIL>")
        assert result == "t**<EMAIL>"

    def test_mask_email_short(self):
        """Test email masking with short local part."""
        result = _mask_email("<EMAIL>")
        assert result == "a*@example.com"

    def test_mask_email_very_short(self):
        """Test email masking with very short local part."""
        result = _mask_email("<EMAIL>")
        assert result == "a*@example.com"

    def test_mask_email_invalid(self):
        """Test email masking with invalid email (no @)."""
        result = _mask_email("notanemail")
        assert result == "notanemail"

    def test_extract_access_token_bearer(self):
        """Test extracting access token from Bearer header."""
        event = {"headers": {"Authorization": "Bearer test-token-123"}}

        result = _extract_access_token(event)
        assert result == "test-token-123"

    def test_extract_access_token_case_insensitive(self):
        """Test extracting access token with case-insensitive header."""
        event = {"headers": {"authorization": "Bearer test-token-123"}}

        result = _extract_access_token(event)
        assert result == "test-token-123"

    def test_extract_access_token_no_header(self):
        """Test extracting access token when no Authorization header."""
        event = {"headers": {}}

        result = _extract_access_token(event)
        assert result is None

    def test_extract_access_token_no_bearer(self):
        """Test extracting access token when header doesn't start with Bearer."""
        event = {"headers": {"Authorization": "Basic dGVzdDp0ZXN0"}}

        result = _extract_access_token(event)
        assert result is None
