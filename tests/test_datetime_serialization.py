#!/usr/bin/env python
"""Test datetime serialization issue in Fund model."""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone
from decimal import Decimal
from src.shared.models.fund import Fund, FundType, FundStatus, Currency, FundDynamoDBItem

# Create a test fund with datetime
test_fund = Fund(
    fund_id="test-123",
    name="Test Fund",
    fund_type=FundType.EQUITY,
    status=FundStatus.ACTIVE,
    currency=Currency.USD,
    created_at=datetime.now(timezone.utc),
    updated_at=datetime.now(timezone.utc)
)

print("Original fund updated_at:", test_fund.updated_at)
print("Type:", type(test_fund.updated_at))

# Test model_dump() without mode
print("\n--- Testing model_dump() ---")
dumped = test_fund.model_dump()
print("updated_at after model_dump():", dumped.get('updated_at'))
print("Type:", type(dumped.get('updated_at')))

# Test model_dump() with mode='json'
print("\n--- Testing model_dump(mode='json') ---")
dumped_json = test_fund.model_dump(mode='json')
print("updated_at after model_dump(mode='json'):", dumped_json.get('updated_at'))
print("Type:", type(dumped_json.get('updated_at')))

# Test to_dynamodb_item
print("\n--- Testing to_dynamodb_item ---")
dynamodb_item = FundDynamoDBItem.to_dynamodb_item(test_fund)
print("updated_at after to_dynamodb_item:", dynamodb_item.get('updated_at'))
print("Type:", type(dynamodb_item.get('updated_at')))

# Test the fix
print("\n--- Testing Fixed Version ---")
# The issue is that model_dump() returns datetime objects, not serialized strings
# We need to ensure serialization happens