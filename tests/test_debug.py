#!/usr/bin/env python3
import sys
import os

sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from unittest.mock import Mock, patch
import json


def test_large_dataset():
    with patch("functions.api.funds.get_fund_repository") as mock_get_repo, patch(
        "functions.api.funds.SessionManager"
    ) as mock_session_manager, patch(
        "functions.api.funds.create_user_context"
    ) as mock_user_context:

        # Mock session validation
        mock_session_manager.return_value.validate_session.return_value = {
            "user_info": {
                "email": "<EMAIL>",
                "role": "fund_manager",
                "user_id": "user-123",
            },
            "valid": True,
        }
        mock_user_context.return_value = {"user_id": "user-123"}

        # Create proper Fund objects - import from main models module to ensure rebuild
        from shared.models import Fund, FundType, FundStatus
        from datetime import datetime, timezone
        from decimal import Decimal

        funds = []
        for i in range(25):
            fund = Fund(
                fund_id=f"FUND-{i:03d}",
                name=f"Test Fund {i}",
                fund_type=FundType.EQUITY if i % 2 == 0 else FundType.BOND,
                status=FundStatus.ACTIVE if i % 3 != 0 else FundStatus.CLOSED,
                nav=Decimal(str(100.00 + (i * 5))),
                total_assets=Decimal(str(500000.00 + (i * 25000))),
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
            )
            funds.append(fund)

        large_fund_list = {
            "items": funds,
            "count": 25,
            "last_evaluated_key": {"fund_id": "FUND-024"},
        }

        # Mock repository
        mock_repo = Mock()
        mock_repo.list_funds.return_value = large_fund_list
        mock_get_repo.return_value = mock_repo

        # Test event
        event = {"queryStringParameters": {"page_size": "10", "page": "2"}}

        try:
            from functions.api.funds import handle_list_funds

            result = handle_list_funds(event)
            print(f'Status Code: {result.get("statusCode")}')
            print(f'Response Body: {result.get("body")}')

            if result.get("statusCode") == 500:
                body = json.loads(result.get("body", "{}"))
                print(f'Error message: {body.get("message", "Unknown error")}')
                print(f'Error details: {body.get("details", "No details")}')
                if "traceback" in body:
                    print(f'Traceback: {body["traceback"]}')
        except Exception as e:
            print(f"Exception during test: {e}")
            import traceback

            traceback.print_exc()


if __name__ == "__main__":
    test_large_dataset()
