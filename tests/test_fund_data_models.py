"""
Test cases for fund data models.
"""

import pytest
from datetime import date, timezone
from decimal import Decimal

from src.shared.models.fund_data import (
    FundInfo,
    FundStats,
    MonthlyReturn,
    CalendarYearReturn,
    ExposureSnapshot,
    MarketCapExposure,
    generate_fund_info_pk,
    generate_fund_info_sk,
    generate_fund_stats_sk,
    generate_monthly_return_sk,
    generate_calendar_year_return_sk,
    generate_exposure_sk,
    generate_market_cap_exposure_sk,
    FundDataDynamoDBItem,
)


class TestFundInfo:
    """Test FundInfo model."""

    def test_fund_info_creation(self):
        """Test creating a valid FundInfo instance."""
        fund_info = FundInfo(
            PK="FUND#test-fund-1",
            SK="INFO#2024-01-01",
            entity_type="INFO",
            as_of=date(2024, 1, 1),
            fund_name="Test Fund",
            summary="A test fund for testing purposes",
            structure="UCITS",
            base_currency="USD",
            fund_managers=["<PERSON>", "<PERSON>"],
            share_classes=["A", "B", "C"],
            subscription_freq="Daily",
            redemption_freq="Daily",
            notice_period_days=1,
            management_fee={"A": Decimal("1.5"), "B": Decimal("1.0")},
            performance_fee_pct=Decimal("20.0"),
            status="active",
        )

        assert fund_info.entity_type == "INFO"
        assert fund_info.fund_name == "Test Fund"
        assert fund_info.management_fee["A"] == Decimal("1.5")

    def test_fund_info_validation_errors(self):
        """Test validation errors for FundInfo."""
        with pytest.raises(ValueError):
            FundInfo(
                PK="FUND#test-fund-1",
                SK="INFO#2024-01-01",
                entity_type="INFO",
                as_of=date(2024, 1, 1),
                fund_name="Test Fund",
                summary="A test fund",
                structure="UCITS",
                base_currency="USD",
                fund_managers=["John Doe"],
                share_classes=["A"],
                subscription_freq="Daily",
                redemption_freq="Daily",
                notice_period_days=1,
                management_fee={"A": Decimal("15.0")},  # Too high
                performance_fee_pct=Decimal("20.0"),
                status="active",
            )


class TestFundStats:
    """Test FundStats model."""

    def test_fund_stats_creation(self):
        """Test creating a valid FundStats instance."""
        fund_stats = FundStats(
            PK="FUND#test-fund-1",
            SK="STATS#2024-01-01",
            entity_type="STATS",
            as_of=date(2024, 1, 1),
            aum_usd_bn=Decimal("5.5"),
            sharpe_ratio=1.2,
            beta=0.8,
            correlation=0.75,
            volatility_pct=12.5,
            inception_date=date(2020, 1, 1),
        )

        assert fund_stats.entity_type == "STATS"
        assert fund_stats.aum_usd_bn == Decimal("5.5")
        assert fund_stats.sharpe_ratio == 1.2


class TestMonthlyReturn:
    """Test MonthlyReturn model."""

    def test_monthly_return_creation(self):
        """Test creating a valid MonthlyReturn instance."""
        monthly_return = MonthlyReturn(
            PK="FUND#test-fund-1",
            SK="RETURN#2024#01",
            entity_type="RETURN",
            year=2024,
            month=1,
            net_return_pct=2.5,
        )

        assert monthly_return.entity_type == "RETURN"
        assert monthly_return.year == 2024
        assert monthly_return.month == 1
        assert monthly_return.net_return_pct == 2.5

    def test_monthly_return_validation_errors(self):
        """Test validation errors for MonthlyReturn."""
        with pytest.raises(ValueError):
            MonthlyReturn(
                PK="FUND#test-fund-1",
                SK="RETURN#2024#01",
                entity_type="RETURN",
                year=2024,
                month=13,  # Invalid month
                net_return_pct=2.5,
            )


class TestCalendarYearReturn:
    """Test CalendarYearReturn model."""

    def test_calendar_year_return_creation(self):
        """Test creating a valid CalendarYearReturn instance."""
        cal_return = CalendarYearReturn(
            PK="FUND#test-fund-1",
            SK="CAL_YEAR_RET#2023",
            entity_type="CAL_YEAR_RET",
            year=2023,
            net_return_pct=15.5,
            benchmarks={"S&P 500": 12.3, "MSCI World": 14.1},
        )

        assert cal_return.entity_type == "CAL_YEAR_RET"
        assert cal_return.year == 2023
        assert cal_return.net_return_pct == 15.5
        assert cal_return.benchmarks["S&P 500"] == 12.3


class TestExposureSnapshot:
    """Test ExposureSnapshot model."""

    def test_exposure_snapshot_creation(self):
        """Test creating a valid ExposureSnapshot instance."""
        exposure = ExposureSnapshot(
            PK="FUND#test-fund-1",
            SK="EXPOSURE#2024-01-01",
            entity_type="EXPOSURE",
            as_of=date(2024, 1, 1),
            long_pct=85.0,
            short_pct=-15.0,
            net_public_pct=70.0,
            net_private_pct=0.0,
            gross_pct=100.0,
            positive_months=8,
            negative_months=4,
        )

        assert exposure.entity_type == "EXPOSURE"
        assert exposure.long_pct == 85.0
        assert exposure.short_pct == -15.0

    def test_exposure_validation_errors(self):
        """Test validation errors for ExposureSnapshot."""
        with pytest.raises(ValueError):
            ExposureSnapshot(
                PK="FUND#test-fund-1",
                SK="EXPOSURE#2024-01-01",
                entity_type="EXPOSURE",
                as_of=date(2024, 1, 1),
                long_pct=85.0,
                short_pct=15.0,  # Should be negative
                net_public_pct=70.0,
                net_private_pct=0.0,
                gross_pct=100.0,
                positive_months=8,
                negative_months=4,
            )


class TestMarketCapExposure:
    """Test MarketCapExposure model."""

    def test_market_cap_exposure_creation(self):
        """Test creating a valid MarketCapExposure instance."""
        mcap_exposure = MarketCapExposure(
            PK="FUND#test-fund-1",
            SK="MCAP_EXP#2024-01-01",
            entity_type="MCAP_EXP",
            as_of=date(2024, 1, 1),
            buckets={
                "large_cap": {"long": 60.0, "short": -10.0},
                "mid_cap": {"long": 25.0, "short": -5.0},
                "small_cap": {"long": 15.0, "short": 0.0},
            },
        )

        assert mcap_exposure.entity_type == "MCAP_EXP"
        assert mcap_exposure.buckets["large_cap"]["long"] == 60.0


class TestKeyGeneration:
    """Test key generation helper functions."""

    def test_generate_fund_info_pk(self):
        """Test fund info partition key generation."""
        pk = generate_fund_info_pk("test-fund-1")
        assert pk == "FUND#test-fund-1"

    def test_generate_fund_info_sk(self):
        """Test fund info sort key generation."""
        sk = generate_fund_info_sk(date(2024, 1, 1))
        assert sk == "INFO#2024-01-01"

    def test_generate_fund_stats_sk(self):
        """Test fund stats sort key generation."""
        sk = generate_fund_stats_sk(date(2024, 1, 1))
        assert sk == "STATS#2024-01-01"

    def test_generate_monthly_return_sk(self):
        """Test monthly return sort key generation."""
        sk = generate_monthly_return_sk(2024, 1)
        assert sk == "RETURN#2024#01"

    def test_generate_calendar_year_return_sk(self):
        """Test calendar year return sort key generation."""
        sk = generate_calendar_year_return_sk(2023)
        assert sk == "CAL_YEAR_RET#2023"

    def test_generate_exposure_sk(self):
        """Test exposure sort key generation."""
        sk = generate_exposure_sk(date(2024, 1, 1))
        assert sk == "EXPOSURE#2024-01-01"

    def test_generate_market_cap_exposure_sk(self):
        """Test market cap exposure sort key generation."""
        sk = generate_market_cap_exposure_sk(date(2024, 1, 1))
        assert sk == "MCAP_EXP#2024-01-01"


class TestDynamoDBConversion:
    """Test DynamoDB conversion utilities."""

    def test_to_dynamodb_item(self):
        """Test converting model to DynamoDB item."""
        fund_info = FundInfo(
            PK="FUND#test-fund-1",
            SK="INFO#2024-01-01",
            entity_type="INFO",
            as_of=date(2024, 1, 1),
            fund_name="Test Fund",
            summary="A test fund",
            structure="UCITS",
            base_currency="USD",
            fund_managers=["John Doe"],
            share_classes=["A"],
            subscription_freq="Daily",
            redemption_freq="Daily",
            notice_period_days=1,
            management_fee={"A": Decimal("1.5")},
            performance_fee_pct=Decimal("20.0"),
            status="active",
        )

        item = FundDataDynamoDBItem.to_dynamodb_item(fund_info)

        assert item["PK"] == "FUND#test-fund-1"
        assert item["entity_type"] == "INFO"
        assert item["management_fee"]["A"] == "1.5"  # Decimal converted to string
        assert item["as_of"] == "2024-01-01"  # Date converted to string

    def test_from_dynamodb_item(self):
        """Test converting DynamoDB item to model."""
        item = {
            "PK": "FUND#test-fund-1",
            "SK": "INFO#2024-01-01",
            "entity_type": "INFO",
            "as_of": "2024-01-01",
            "fund_name": "Test Fund",
            "summary": "A test fund",
            "structure": "UCITS",
            "base_currency": "USD",
            "fund_managers": ["John Doe"],
            "share_classes": ["A"],
            "subscription_freq": "Daily",
            "redemption_freq": "Daily",
            "notice_period_days": 1,
            "management_fee": {"A": "1.5"},
            "performance_fee_pct": "20.0",
            "status": "active",
        }

        fund_info = FundDataDynamoDBItem.from_dynamodb_item(item, FundInfo)

        assert fund_info.PK == "FUND#test-fund-1"
        assert fund_info.entity_type == "INFO"
        assert fund_info.fund_name == "Test Fund"
