"""
Test cases for Fund Management API endpoints.
Tests all CRUD operations with proper mocking of AWS dependencies.
"""

import json
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
from typing import Dict, Any


# Test fixtures and sample data
@pytest.fixture
def lambda_context():
    """Mock Lambda context."""
    context = Mock()
    context.function_name = "fund-api"
    context.function_version = "1"
    context.invoked_function_arn = (
        "arn:aws:lambda:us-east-1:123456789012:function:fund-api"
    )
    context.memory_limit_in_mb = 128
    context.remaining_time_in_millis = lambda: 30000
    context.aws_request_id = "test-request-id"
    return context


@pytest.fixture
def valid_session_result():
    """Mock valid session validation result."""
    return {
        "valid": True,
        "user_info": {
            "user_id": "user-123",
            "email": "<EMAIL>",
            "role": "fund_manager",
        },
    }


@pytest.fixture
def invalid_session_result():
    """Mock invalid session validation result."""
    return {"valid": False, "error": "Session expired"}


@pytest.fixture
def sample_fund_data():
    """Sample fund data for testing."""
    return {
        "fund_id": "FUND-001",
        "name": "Tech Growth Fund",
        "description": "A technology-focused growth fund",
        "fund_type": "equity",
        "status": "active",
        "target_amount": 1000000.00,
        "current_amount": 750000.00,
        "minimum_investment": 10000.00,
        "inception_date": "2024-01-15",
        "expense_ratio": 0.75,
        "manager_name": "John Smith",
        "investment_strategy": "Growth-oriented technology investments",
    }


@pytest.fixture
def sample_fund_object(sample_fund_data):
    """Sample Fund object for testing."""
    fund_data = sample_fund_data.copy()
    fund_data["created_at"] = datetime.now(timezone.utc)
    fund_data["updated_at"] = datetime.now(timezone.utc)

    # Mock Fund object
    fund = Mock()
    fund.dict.return_value = fund_data
    for key, value in fund_data.items():
        setattr(fund, key, value)
    return fund


@pytest.fixture
def sample_fund_list():
    """Sample list of funds for testing."""
    return {
        "items": [
            {
                "fund_id": "FUND-001",
                "name": "Tech Growth Fund",
                "fund_type": "equity",
                "status": "active",
                "target_amount": 1000000.00,
                "current_amount": 750000.00,
            },
            {
                "fund_id": "FUND-002",
                "name": "Bond Income Fund",
                "fund_type": "bond",
                "status": "active",
                "target_amount": 500000.00,
                "current_amount": 450000.00,
            },
        ],
        "count": 2,
    }


# Test the main handler routing
class TestFundsAPIHandler:
    """Test the main Lambda handler routing logic."""

    @patch("src.functions.api.funds.handle_list_funds")
    @patch("src.functions.api.funds.SessionManager")
    def test_handler_routes_get_funds_list(
        self, mock_session_manager, mock_handle_list, lambda_context
    ):
        """Test handler routes GET /funds to list handler."""
        from src.functions.api.funds import handler

        event = {
            "httpMethod": "GET",
            "path": "/api/funds",
            "queryStringParameters": None,
        }

        mock_handle_list.return_value = {"statusCode": 200}

        result = handler(event, lambda_context)

        mock_handle_list.assert_called_once_with(event)
        assert result["statusCode"] == 200

    @patch("src.functions.api.funds.handle_get_fund")
    def test_handler_routes_get_specific_fund(self, mock_handle_get, lambda_context):
        """Test handler routes GET /funds/{id} to get handler."""
        from src.functions.api.funds import handler

        event = {"httpMethod": "GET", "path": "/api/funds/FUND-001"}

        mock_handle_get.return_value = {"statusCode": 200}

        result = handler(event, lambda_context)

        mock_handle_get.assert_called_once_with(event)

    @patch("src.functions.api.funds.handle_create_fund")
    def test_handler_routes_post_create_fund(self, mock_handle_create, lambda_context):
        """Test handler routes POST /funds to create handler."""
        from src.functions.api.funds import handler

        event = {"httpMethod": "POST", "path": "/api/funds"}

        mock_handle_create.return_value = {"statusCode": 201}

        result = handler(event, lambda_context)

        mock_handle_create.assert_called_once_with(event)

    def test_handler_unsupported_method(self, lambda_context):
        """Test handler returns 405 for unsupported methods."""
        from src.functions.api.funds import handler

        event = {"httpMethod": "PATCH", "path": "/api/funds"}

        result = handler(event, lambda_context)

        assert result["statusCode"] == 405
        body = json.loads(result["body"])
        assert "Method PATCH not allowed" in body["message"]

    def test_handler_exception_returns_500(self, lambda_context):
        """Test handler returns 500 on exceptions."""
        from src.functions.api.funds import handler

        # Invalid event structure to trigger exception
        event = None

        result = handler(event, lambda_context)

        assert result["statusCode"] == 500
        body = json.loads(result["body"])
        assert body["error"] == "INTERNAL_SERVER_ERROR"


# Test list funds endpoint
class TestListFunds:
    """Test GET /funds endpoint for listing funds."""

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    def test_list_funds_success(
        self,
        mock_user_context,
        mock_session_manager,
        mock_get_repo,
        valid_session_result,
        sample_fund_list,
    ):
        """Test successful fund listing."""
        from src.functions.api.funds import handle_list_funds

        # Mock session validation
        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        # Mock repository
        mock_repo = Mock()

        # Create mock Fund objects with dict() method
        mock_fund1 = Mock()
        mock_fund1.dict.return_value = sample_fund_list["items"][0]
        mock_fund2 = Mock()
        mock_fund2.dict.return_value = sample_fund_list["items"][1]

        # Mock the repository response
        mock_funds_result = {"items": [mock_fund1, mock_fund2], "count": 2}
        mock_repo.list_funds.return_value = mock_funds_result
        mock_get_repo.return_value = mock_repo

        event = {"queryStringParameters": {"page_size": "10", "status": "active"}}

        result = handle_list_funds(event)

        assert result["statusCode"] == 200
        body = json.loads(result["body"])
        assert "data" in body
        assert len(body["data"]["funds"]) == 2
        assert body["data"]["pagination"]["total_count"] == 2

    @patch("src.functions.api.funds.SessionManager")
    def test_list_funds_unauthorized(
        self, mock_session_manager, invalid_session_result
    ):
        """Test list funds with invalid session."""
        from src.functions.api.funds import handle_list_funds

        mock_session_manager.return_value.validate_session.return_value = (
            invalid_session_result
        )

        event = {"queryStringParameters": None}

        result = handle_list_funds(event)

        assert result["statusCode"] == 401
        body = json.loads(result["body"])
        assert "Invalid or expired session" in body["message"]

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    def test_list_funds_with_filters(
        self,
        mock_user_context,
        mock_session_manager,
        mock_get_repo,
        valid_session_result,
        sample_fund_list,
    ):
        """Test fund listing with query filters."""
        from src.functions.api.funds import handle_list_funds

        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        mock_repo = Mock()

        # Create mock Fund objects with dict() method
        mock_fund1 = Mock()
        mock_fund1.dict.return_value = sample_fund_list["items"][0]
        mock_fund2 = Mock()
        mock_fund2.dict.return_value = sample_fund_list["items"][1]

        # Mock the repository response
        mock_funds_result = {"items": [mock_fund1, mock_fund2], "count": 2}
        mock_repo.list_funds.return_value = mock_funds_result
        mock_get_repo.return_value = mock_repo

        event = {
            "queryStringParameters": {
                "status": "active",
                "fund_type": "equity",
                "search": "Tech",
                "page_size": "5",
            }
        }

        result = handle_list_funds(event)

        # Verify repository was called with correct filters
        mock_repo.list_funds.assert_called_once_with(
            limit=5, status="active", fund_type="equity", search="Tech"
        )

        assert result["statusCode"] == 200
        body = json.loads(result["body"])
        assert "data" in body
        assert body["data"]["filters_applied"]["status"] == "active"
        assert body["data"]["filters_applied"]["fund_type"] == "equity"

    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    def test_list_funds_invalid_query_params(
        self, mock_user_context, mock_session_manager, valid_session_result
    ):
        """Test list funds with invalid query parameters."""
        from src.functions.api.funds import handle_list_funds

        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        event = {
            "queryStringParameters": {
                "page_size": "invalid_number",
                "status": "INVALID_STATUS",
            }
        }

        result = handle_list_funds(event)

        assert result["statusCode"] == 422
        body = json.loads(result["body"])
        assert "Invalid query parameters" in body["message"]


# Test get fund endpoint
class TestGetFund:
    """Test GET /funds/{fund_id} endpoint."""

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.extract_fund_id_from_path")
    def test_get_fund_success(
        self,
        mock_extract_id,
        mock_user_context,
        mock_session_manager,
        mock_get_repo,
        valid_session_result,
        sample_fund_object,
    ):
        """Test successful fund retrieval."""
        from src.functions.api.funds import handle_get_fund

        mock_extract_id.return_value = "FUND-001"
        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        mock_repo = Mock()
        mock_repo.get_by_fund_id.return_value = sample_fund_object
        mock_get_repo.return_value = mock_repo

        event = {"path": "/api/funds/FUND-001"}

        result = handle_get_fund(event)

        assert result["statusCode"] == 200
        body = json.loads(result["body"])
        assert body["success"] is True
        assert body["data"]["fund"]["fund_id"] == "FUND-001"

    @patch("src.functions.api.funds.extract_fund_id_from_path")
    def test_get_fund_missing_id(self, mock_extract_id):
        """Test get fund with missing fund ID."""
        from src.functions.api.funds import handle_get_fund

        mock_extract_id.return_value = None

        event = {"path": "/api/funds/"}

        result = handle_get_fund(event)

        assert result["statusCode"] == 400
        body = json.loads(result["body"])
        assert "Fund ID is required" in body["message"]

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.extract_fund_id_from_path")
    def test_get_fund_not_found(
        self,
        mock_extract_id,
        mock_user_context,
        mock_session_manager,
        mock_get_repo,
        valid_session_result,
    ):
        """Test get fund when fund doesn't exist."""
        from src.functions.api.funds import handle_get_fund

        mock_extract_id.return_value = "FUND-999"
        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        mock_repo = Mock()
        mock_repo.get_by_fund_id.return_value = None
        mock_get_repo.return_value = mock_repo

        event = {"path": "/api/funds/FUND-999"}

        result = handle_get_fund(event)

        assert result["statusCode"] == 404
        body = json.loads(result["body"])
        assert "Fund FUND-999 not found" in body["message"]


# Test create fund endpoint
class TestCreateFund:
    """Test POST /funds endpoint for creating funds."""

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.RequestValidator")
    def test_create_fund_success(
        self,
        mock_validator,
        mock_user_context,
        mock_session_manager,
        mock_get_repo,
        valid_session_result,
        sample_fund_data,
        sample_fund_object,
    ):
        """Test successful fund creation."""
        from src.functions.api.funds import handle_create_fund

        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}
        mock_validator.validate_json_body.return_value = sample_fund_data

        mock_repo = Mock()
        mock_repo.get_by_fund_id.return_value = None  # Fund doesn't exist
        mock_repo.create.return_value = sample_fund_object
        mock_get_repo.return_value = mock_repo

        event = {"body": json.dumps(sample_fund_data)}

        with patch(
            "src.functions.api.funds.FundValidationService"
        ) as mock_validation_service:
            # Mock validation service
            mock_validation_result = Mock()
            mock_validation_result.is_valid = True
            mock_validation_result.warnings = []
            mock_validation_service.return_value.validate_fund_creation.return_value = (
                mock_validation_result
            )

            result = handle_create_fund(event)

        assert result["statusCode"] == 201
        body = json.loads(result["body"])
        assert body["success"] is True
        assert body["data"]["fund"]["fund_id"] == "FUND-001"

    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.RequestValidator")
    def test_create_fund_invalid_data(
        self,
        mock_validator,
        mock_user_context,
        mock_session_manager,
        valid_session_result,
    ):
        """Test create fund with invalid data."""
        from src.functions.api.funds import handle_create_fund

        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        # Invalid fund data (missing required fields)
        invalid_data = {"name": "Test Fund"}  # Missing required fund_id
        mock_validator.validate_json_body.return_value = invalid_data

        event = {"body": json.dumps(invalid_data)}

        result = handle_create_fund(event)

        assert result["statusCode"] == 400
        body = json.loads(result["body"])
        assert "Invalid fund data" in body["message"]

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.RequestValidator")
    def test_create_fund_already_exists(
        self,
        mock_validator,
        mock_user_context,
        mock_session_manager,
        mock_get_repo,
        valid_session_result,
        sample_fund_data,
        sample_fund_object,
    ):
        """Test create fund when fund already exists."""
        from src.functions.api.funds import handle_create_fund

        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}
        mock_validator.validate_json_body.return_value = sample_fund_data

        mock_repo = Mock()
        mock_repo.get_by_fund_id.return_value = sample_fund_object  # Fund exists
        mock_get_repo.return_value = mock_repo

        event = {"body": json.dumps(sample_fund_data)}

        with patch(
            "src.functions.api.funds.FundValidationService"
        ) as mock_validation_service:
            mock_validation_result = Mock()
            mock_validation_result.is_valid = True
            mock_validation_result.warnings = []
            mock_validation_service.return_value.validate_fund_creation.return_value = (
                mock_validation_result
            )

            result = handle_create_fund(event)

        assert result["statusCode"] == 400
        body = json.loads(result["body"])
        assert "Fund FUND-001 already exists" in body["message"]


# Test update fund endpoint
class TestUpdateFund:
    """Test PUT /funds/{fund_id} endpoint."""

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.RequestValidator")
    @patch("src.functions.api.funds.extract_fund_id_from_path")
    def test_update_fund_success(
        self,
        mock_extract_id,
        mock_validator,
        mock_user_context,
        mock_session_manager,
        mock_get_repo,
        valid_session_result,
        sample_fund_object,
    ):
        """Test successful fund update."""
        from src.functions.api.funds import handle_update_fund

        mock_extract_id.return_value = "FUND-001"
        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        update_data = {"name": "Updated Tech Growth Fund", "target_amount": 1200000.00}
        mock_validator.validate_json_body.return_value = update_data

        mock_repo = Mock()
        mock_repo.get_by_fund_id.return_value = sample_fund_object
        mock_repo.update.return_value = sample_fund_object
        mock_get_repo.return_value = mock_repo

        event = {"path": "/api/funds/FUND-001", "body": json.dumps(update_data)}

        with patch(
            "src.functions.api.funds.FundValidationService"
        ) as mock_validation_service:
            mock_validation_result = Mock()
            mock_validation_result.is_valid = True
            mock_validation_result.warnings = []
            mock_validation_service.return_value.validate_fund_update.return_value = (
                mock_validation_result
            )

            result = handle_update_fund(event)

        assert result["statusCode"] == 200
        body = json.loads(result["body"])
        assert body["success"] is True
        assert "Fund updated successfully" in body["message"]

    @patch("src.functions.api.funds.extract_fund_id_from_path")
    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.get_fund_repository")
    def test_update_fund_not_found(
        self,
        mock_get_repo,
        mock_user_context,
        mock_session_manager,
        mock_extract_id,
        valid_session_result,
    ):
        """Test update fund when fund doesn't exist."""
        from src.functions.api.funds import handle_update_fund

        mock_extract_id.return_value = "FUND-999"
        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        mock_repo = Mock()
        mock_repo.get_by_fund_id.return_value = None
        mock_get_repo.return_value = mock_repo

        event = {"path": "/api/funds/FUND-999"}

        result = handle_update_fund(event)

        assert result["statusCode"] == 404
        body = json.loads(result["body"])
        assert "Fund FUND-999 not found" in body["message"]


# Test delete fund endpoint
class TestDeleteFund:
    """Test DELETE /funds/{fund_id} endpoint."""

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.extract_fund_id_from_path")
    def test_delete_fund_success(
        self,
        mock_extract_id,
        mock_user_context,
        mock_session_manager,
        mock_get_repo,
        valid_session_result,
        sample_fund_object,
    ):
        """Test successful fund deletion (soft delete)."""
        from src.functions.api.funds import handle_delete_fund

        mock_extract_id.return_value = "FUND-001"
        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        mock_repo = Mock()
        mock_repo.get_by_fund_id.return_value = sample_fund_object
        mock_repo.soft_delete_fund.return_value = True
        mock_get_repo.return_value = mock_repo

        event = {"path": "/api/funds/FUND-001"}

        result = handle_delete_fund(event)

        assert result["statusCode"] == 200
        body = json.loads(result["body"])
        assert body["success"] is True
        assert body["data"]["fund_id"] == "FUND-001"
        assert body["data"]["deleted"] is True

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.extract_fund_id_from_path")
    def test_delete_fund_not_found(
        self,
        mock_extract_id,
        mock_user_context,
        mock_session_manager,
        mock_get_repo,
        valid_session_result,
    ):
        """Test delete fund when fund doesn't exist."""
        from src.functions.api.funds import handle_delete_fund

        mock_extract_id.return_value = "FUND-999"
        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        mock_repo = Mock()
        mock_repo.get_by_fund_id.return_value = None
        mock_get_repo.return_value = mock_repo

        event = {"path": "/api/funds/FUND-999"}

        result = handle_delete_fund(event)

        assert result["statusCode"] == 404
        body = json.loads(result["body"])
        assert "Fund FUND-999 not found" in body["message"]


# Test bulk update endpoint
class TestBulkUpdateFunds:
    """Test POST /funds/bulk-update endpoint."""

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.RequestValidator")
    def test_bulk_update_success(
        self,
        mock_validator,
        mock_user_context,
        mock_session_manager,
        mock_get_repo,
        valid_session_result,
        sample_fund_object,
    ):
        """Test successful bulk fund update."""
        from src.functions.api.funds import handle_bulk_update_funds

        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        bulk_data = {
            "updates": [
                {"fund_id": "FUND-001", "data": {"name": "Updated Fund 1"}},
                {"fund_id": "FUND-002", "data": {"name": "Updated Fund 2"}},
            ]
        }
        mock_validator.validate_json_body.return_value = bulk_data

        mock_repo = Mock()
        mock_repo.get_by_fund_id.return_value = sample_fund_object
        mock_repo.update.return_value = sample_fund_object
        mock_get_repo.return_value = mock_repo

        event = {"body": json.dumps(bulk_data)}

        with patch(
            "src.functions.api.funds.FundValidationService"
        ) as mock_validation_service:
            mock_validation_result = Mock()
            mock_validation_result.is_valid = True
            mock_validation_result.warnings = []
            mock_validation_service.return_value.validate_bulk_fund_updates.return_value = {
                "FUND-001": mock_validation_result,
                "FUND-002": mock_validation_result,
            }
            mock_validation_service.return_value.validate_fund_update.return_value = (
                mock_validation_result
            )

            result = handle_bulk_update_funds(event)

        assert result["statusCode"] == 200
        body = json.loads(result["body"])
        assert body["success"] is True
        assert body["data"]["summary"]["successful"] == 2
        assert body["data"]["summary"]["failed"] == 0

    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.RequestValidator")
    def test_bulk_update_invalid_structure(
        self,
        mock_validator,
        mock_user_context,
        mock_session_manager,
        valid_session_result,
    ):
        """Test bulk update with invalid request structure."""
        from src.functions.api.funds import handle_bulk_update_funds

        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        # Missing 'updates' key
        invalid_data = {"data": []}
        mock_validator.validate_json_body.return_value = invalid_data

        event = {"body": json.dumps(invalid_data)}

        result = handle_bulk_update_funds(event)

        assert result["statusCode"] == 400
        body = json.loads(result["body"])
        assert "Request must contain 'updates' array" in body["message"]

    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.RequestValidator")
    def test_bulk_update_too_many_updates(
        self,
        mock_validator,
        mock_user_context,
        mock_session_manager,
        valid_session_result,
    ):
        """Test bulk update with too many updates."""
        from src.functions.api.funds import handle_bulk_update_funds

        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        # More than 100 updates
        bulk_data = {
            "updates": [{"fund_id": f"FUND-{i:03d}", "data": {}} for i in range(101)]
        }
        mock_validator.validate_json_body.return_value = bulk_data

        event = {"body": json.dumps(bulk_data)}

        result = handle_bulk_update_funds(event)

        assert result["statusCode"] == 400
        body = json.loads(result["body"])
        assert "Bulk update limited to 100 funds" in body["message"]


# Test utility functions
class TestUtilityFunctions:
    """Test utility functions."""

    def test_extract_fund_id_from_path_success(self):
        """Test successful fund ID extraction from path."""
        from src.functions.api.funds import extract_fund_id_from_path

        test_cases = [
            ("/api/funds/FUND-001", "FUND-001"),
            ("/api/v1/funds/FUND-123", "FUND-123"),
            ("/funds/ABC-999", "ABC-999"),
        ]

        for path, expected_id in test_cases:
            result = extract_fund_id_from_path(path)
            assert result == expected_id

    def test_extract_fund_id_from_path_failure(self):
        """Test fund ID extraction failure cases."""
        from src.functions.api.funds import extract_fund_id_from_path

        test_cases = [
            "/api/funds/",  # No ID after funds
            "/api/notfunds/FUND-001",  # No funds in path
            "/funds",  # No trailing ID
            "",  # Empty path
        ]

        for path in test_cases:
            result = extract_fund_id_from_path(path)
            assert result is None


# Integration-style tests
class TestFundsAPIIntegration:
    """Integration-style tests combining multiple components."""

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    def test_full_crud_workflow(
        self,
        mock_user_context,
        mock_session_manager,
        mock_get_repo,
        valid_session_result,
        sample_fund_data,
    ):
        """Test a complete CRUD workflow."""
        from src.functions.api.funds import handler

        # Setup common mocks
        mock_session_manager.return_value.validate_session.return_value = (
            valid_session_result
        )
        mock_user_context.return_value = {"user_id": "user-123"}

        mock_repo = Mock()
        mock_get_repo.return_value = mock_repo

        lambda_context = Mock()

        # 1. Create fund
        mock_repo.get_by_fund_id.return_value = None  # Fund doesn't exist

        fund_obj = Mock()
        fund_obj.dict.return_value = sample_fund_data
        mock_repo.create.return_value = fund_obj

        create_event = {
            "httpMethod": "POST",
            "path": "/api/funds",
            "body": json.dumps(sample_fund_data),
        }

        with patch("src.functions.api.funds.RequestValidator") as mock_validator:
            mock_validator.validate_json_body.return_value = sample_fund_data

            with patch(
                "src.functions.api.funds.FundValidationService"
            ) as mock_validation_service:
                mock_validation_result = Mock()
                mock_validation_result.is_valid = True
                mock_validation_result.warnings = []
                mock_validation_service.return_value.validate_fund_creation.return_value = (
                    mock_validation_result
                )

                create_result = handler(create_event, lambda_context)

        assert create_result["statusCode"] == 201

        # 2. Get the created fund
        mock_repo.get_by_fund_id.return_value = fund_obj

        get_event = {"httpMethod": "GET", "path": "/api/funds/FUND-001"}

        get_result = handler(get_event, lambda_context)
        assert get_result["statusCode"] == 200

        # 3. List funds (should include our fund)
        mock_repo.list_funds.return_value = {"items": [sample_fund_data], "count": 1}

        list_event = {
            "httpMethod": "GET",
            "path": "/api/funds",
            "queryStringParameters": {"status": "ACTIVE"},
        }

        list_result = handler(list_event, lambda_context)
        assert list_result["statusCode"] == 200

        # 4. Update fund
        mock_repo.update.return_value = fund_obj

        update_event = {
            "httpMethod": "PUT",
            "path": "/api/funds/FUND-001",
            "body": json.dumps({"name": "Updated Fund Name"}),
        }

        with patch("src.functions.api.funds.RequestValidator") as mock_validator:
            mock_validator.validate_json_body.return_value = {
                "name": "Updated Fund Name"
            }

            with patch(
                "src.functions.api.funds.FundValidationService"
            ) as mock_validation_service:
                mock_validation_result = Mock()
                mock_validation_result.is_valid = True
                mock_validation_result.warnings = []
                mock_validation_service.return_value.validate_fund_update.return_value = (
                    mock_validation_result
                )

                update_result = handler(update_event, lambda_context)

        assert update_result["statusCode"] == 200

        # 5. Delete fund
        mock_repo.soft_delete_fund.return_value = True

        delete_event = {"httpMethod": "DELETE", "path": "/api/funds/FUND-001"}

        delete_result = handler(delete_event, lambda_context)
        assert delete_result["statusCode"] == 200


if __name__ == "__main__":
    pytest.main([__file__])
