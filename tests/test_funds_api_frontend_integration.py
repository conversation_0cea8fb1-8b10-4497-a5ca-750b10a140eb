"""
Frontend Integration Test Cases for Fund Management API.
This file tests the API with actual frontend request patterns and parameter structures.
"""

import json
import pytest
import sys
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
import os
from typing import Dict, Any

# Mock all dependencies before any other imports
sys.modules["shared"] = Mock()
sys.modules["shared.models"] = Mock()
sys.modules["shared.models.requests"] = Mock()
sys.modules["shared.api"] = Mock()
sys.modules["shared.api.responses"] = Mock()
sys.modules["shared.models.fund"] = Mock()
sys.modules["shared.database"] = Mock()
sys.modules["shared.api.auth_dependencies"] = Mock()
sys.modules["shared.security"] = Mock()
sys.modules["shared.security.session_manager"] = Mock()
sys.modules["shared.validators"] = Mock()
sys.modules["shared.utils"] = Mock()
sys.modules["shared.utils.validation_response"] = Mock()

# Set up AWS Lambda Powertools environment
os.environ["POWERTOOLS_SERVICE_NAME"] = "FundFlow"
os.environ["POWERTOOLS_METRICS_NAMESPACE"] = "FundFlow"
os.environ["POWERTOOLS_LOG_LEVEL"] = "INFO"

# Mock APIResponse before importing
mock_api_response = Mock()


def mock_success_response(message="Success", data=None):
    return {
        "statusCode": 200,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
        },
        "body": json.dumps({"success": True, "message": message, "data": data or {}}),
    }


mock_api_response.success.side_effect = mock_success_response


def mock_created_response(message="Created", data=None):
    return {
        "statusCode": 201,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
        },
        "body": json.dumps({"success": True, "message": message, "data": data or {}}),
    }


mock_api_response.created.side_effect = mock_created_response
mock_api_response.validation_error.return_value = {
    "statusCode": 400,
    "headers": {"Content-Type": "application/json", "Access-Control-Allow-Origin": "*"},
    "body": json.dumps({"error": "VALIDATION_ERROR", "message": "Validation failed"}),
}
mock_api_response.unauthorized.return_value = {
    "statusCode": 401,
    "headers": {"Content-Type": "application/json", "Access-Control-Allow-Origin": "*"},
    "body": json.dumps({"error": "UNAUTHORIZED", "message": "Unauthorized"}),
}
mock_api_response.not_found.return_value = {
    "statusCode": 404,
    "headers": {"Content-Type": "application/json", "Access-Control-Allow-Origin": "*"},
    "body": json.dumps({"error": "NOT_FOUND", "message": "Not found"}),
}
sys.modules["shared.api.responses"].APIResponse = mock_api_response

# Mock validation services at module level
mock_validation_service = Mock()
mock_validation_result = Mock()
mock_validation_result.is_valid = True
mock_validation_result.errors = []
mock_validation_result.warnings = []
mock_validation_service.validate_fund_creation.return_value = mock_validation_result
mock_validation_service.validate_fund_update.return_value = mock_validation_result
mock_validation_service.validate_bulk_fund_updates.return_value = {}

# Mock FundValidationService class
mock_fund_validation_class = Mock()
mock_fund_validation_class.return_value = mock_validation_service
sys.modules["shared.validators"].FundValidationService = mock_fund_validation_class

# Mock ValidationResponseHandler
mock_validation_response_handler = Mock()
mock_validation_response_handler.create_validation_error_response.return_value = {
    "statusCode": 400,
    "headers": {"Content-Type": "application/json", "Access-Control-Allow-Origin": "*"},
    "body": json.dumps({"error": "VALIDATION_ERROR", "message": "Validation failed"}),
}
sys.modules["shared.utils.validation_response"].ValidationResponseHandler = (
    mock_validation_response_handler
)

# Mock request models
mock_fund_query_request = Mock()
mock_fund_query_request.page_size = 20
mock_fund_query_request.page = 1
mock_fund_query_request.status = None
mock_fund_query_request.fund_type = None
mock_fund_query_request.search = None

mock_fund_create_request = Mock()
mock_fund_update_request = Mock()

# Mock model classes
sys.modules["shared.models.requests"].FundQueryRequest = Mock(
    return_value=mock_fund_query_request
)
sys.modules["shared.models.requests"].FundCreateRequest = Mock(
    return_value=mock_fund_create_request
)
sys.modules["shared.models.requests"].FundUpdateRequest = Mock(
    return_value=mock_fund_update_request
)

# Mock Fund and FundResponse
mock_fund_response = Mock()
mock_fund_response.dict.return_value = {"fund_id": "test", "name": "Test Fund"}
sys.modules["shared.models.fund"].FundResponse = Mock(return_value=mock_fund_response)
sys.modules["shared.models.fund"].Fund = Mock()

# Import the module after mocking
from src.functions.api.funds import (
    handler,
    handle_list_funds,
    handle_get_fund,
    handle_create_fund,
    handle_update_fund,
    handle_delete_fund,
    handle_bulk_update_funds,
    extract_fund_id_from_path,
)


class TestFrontendIntegration:
    """Test cases based on actual frontend usage patterns."""

    def setup_method(self):
        """Set up common mocks for each test."""
        # Mock session validation
        self.mock_session_manager = Mock()
        self.mock_session_manager.validate_session.return_value = {
            "valid": True,
            "user_info": {"user_id": "frontend-user-123", "role": "user"},
        }

        # Mock user context creation
        self.mock_user_context = {"user_id": "frontend-user-123", "role": "user"}

        # Mock fund repository
        self.mock_fund_repo = Mock()

    # ========== Frontend API Call Pattern Tests ==========

    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.get_fund_repository")
    def test_get_fund_details_frontend_call(
        self, mock_get_repo, mock_create_context, mock_session_class
    ):
        """Test GET /funds/{id} with frontend parameters - matches getFundDetails() call."""
        # Setup mocks
        mock_session_class.return_value = self.mock_session_manager
        mock_create_context.return_value = self.mock_user_context
        mock_get_repo.return_value = self.mock_fund_repo

        # Mock fund data that matches frontend expectations
        mock_fund = Mock()
        fund_data = {
            "fund_id": "fund-1",
            "name": "HDFC Equity Fund",
            "fund_type": "equity",
            "nav": "125.50",
            "total_assets": "150000",  # Backend uses total_assets, frontend expects aum
            "expense_ratio": "1.25",
            "minimum_investment": "1000",
            "risk_level": "moderate",
            "fund_manager": "John Smith",
            "description": "Professional fund management",
            "bloomberg_ticker": "HDFC001",
            "inception_date": "2020-01-01",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z",
            "custom_fields": {
                "category": "Equity",
                "sub_category": "Large Cap",
                "rating": "4",
                "volume": "10000",
            },
            "performance_metrics": {
                "ytd_return": "8.5",
                "one_year_return": "12.0",
                "three_year_return": "15.0",
                "five_year_return": "18.0",
            },
            "holdings": {
                "top_holdings": [
                    {"name": "Reliance Industries", "percentage": 8.5},
                    {"name": "HDFC Bank", "percentage": 7.2},
                ],
                "sector_allocation": {
                    "Technology": "25.0",
                    "Banking": "20.0",
                    "Healthcare": "15.0",
                },
            },
        }
        mock_fund.dict.return_value = fund_data
        self.mock_fund_repo.get_by_fund_id.return_value = mock_fund

        # Mock FundResponse to return proper data
        with patch("src.functions.api.funds.FundResponse") as mock_fund_response_class:
            mock_fund_response_instance = Mock()
            mock_fund_response_instance.dict.return_value = fund_data
            mock_fund_response_class.return_value = mock_fund_response_instance

            # Frontend API call pattern
            event = {
                "httpMethod": "GET",
                "path": "/api/funds/fund-1",
                "headers": {
                    "Authorization": "Bearer frontend-token",
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                },
            }

            result = handle_get_fund(event)

            # Verify the call was made correctly
            self.mock_fund_repo.get_by_fund_id.assert_called_once_with("fund-1")
            assert result["statusCode"] == 200

            # Verify response structure matches frontend expectations
            response_data = json.loads(result["body"])
            assert "data" in response_data
            assert "fund" in response_data["data"]
            assert response_data["data"]["fund"]["fund_id"] == "fund-1"

    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.RequestValidator")
    def test_update_fund_frontend_call(
        self, mock_validator, mock_get_repo, mock_create_context, mock_session_class
    ):
        """Test PUT /funds/{id} with frontend update data - matches updateFund() call."""
        # Setup mocks
        mock_session_class.return_value = self.mock_session_manager
        mock_create_context.return_value = self.mock_user_context
        mock_get_repo.return_value = self.mock_fund_repo

        # Mock existing fund
        mock_existing_fund = Mock()
        mock_existing_fund.dict.return_value = {
            "fund_id": "fund-1",
            "name": "Original Fund Name",
            "fund_type": "equity",
        }
        self.mock_fund_repo.get_by_fund_id.return_value = mock_existing_fund

        # Mock updated fund
        mock_updated_fund = Mock()
        mock_updated_fund.dict.return_value = {
            "fund_id": "fund-1",
            "name": "Updated Fund Name",
            "fund_type": "equity",
        }
        self.mock_fund_repo.update.return_value = mock_updated_fund

        # Frontend update request body (converted from frontend format)
        frontend_update_data = {
            "name": "Updated Fund Name",
            "fund_type": "equity",  # Frontend sends 'mutual_fund', converted to 'equity'
            "nav": "130.75",
            "total_assets": "160000",  # Frontend aum converted to total_assets
            "expense_ratio": "1.15",
            "minimum_investment": "1500",
            "risk_level": "moderate",  # Frontend 'medium' converted to 'moderate'
            "fund_manager": "Jane Doe",
            "description": "Updated description",
            "bloomberg_ticker": "UPD001",
            "custom_fields": {
                "category": "Equity",
                "sub_category": "Mid Cap",
                "rating": "5",
                "volume": "15000",
            },
        }

        mock_validator.validate_json_body.return_value = frontend_update_data

        # Mock FundResponse and FundUpdateRequest
        with patch(
            "src.functions.api.funds.FundResponse"
        ) as mock_fund_response_class, patch(
            "src.functions.api.funds.FundUpdateRequest"
        ) as mock_update_request_class:

            mock_fund_response_instance = Mock()
            mock_fund_response_instance.dict.return_value = {
                "fund_id": "fund-1",
                "name": "Updated Fund Name",
            }
            mock_fund_response_class.return_value = mock_fund_response_instance

            mock_update_request_instance = Mock()
            mock_update_request_instance.dict.return_value = frontend_update_data
            mock_update_request_class.return_value = mock_update_request_instance

            event = {
                "httpMethod": "PUT",
                "path": "/api/funds/fund-1",
                "headers": {
                    "Authorization": "Bearer frontend-token",
                    "Content-Type": "application/json",
                },
                "body": json.dumps(frontend_update_data),
            }

            result = handle_update_fund(event)

            # Verify the update was processed
            self.mock_fund_repo.get_by_fund_id.assert_called_once_with("fund-1")
            self.mock_fund_repo.update.assert_called_once()
            assert result["statusCode"] == 200

    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.get_fund_repository")
    def test_list_funds_with_frontend_filters(
        self, mock_get_repo, mock_create_context, mock_session_class
    ):
        """Test GET /funds with frontend filter parameters."""
        # Setup mocks
        mock_session_class.return_value = self.mock_session_manager
        mock_create_context.return_value = self.mock_user_context
        mock_get_repo.return_value = self.mock_fund_repo

        # Mock fund list response
        mock_funds_result = {
            "items": [
                Mock(
                    dict=lambda: {
                        "fund_id": "fund-1",
                        "name": "HDFC Equity Fund",
                        "fund_type": "equity",
                        "nav": "125.50",
                    }
                ),
                Mock(
                    dict=lambda: {
                        "fund_id": "fund-2",
                        "name": "SBI Bond Fund",
                        "fund_type": "bond",
                        "nav": "10.25",
                    }
                ),
            ],
            "count": 2,
        }
        self.mock_fund_repo.list_funds.return_value = mock_funds_result

        # Frontend filter parameters (from FundFilters component)
        event = {
            "httpMethod": "GET",
            "path": "/api/funds",
            "queryStringParameters": {
                "search": "HDFC",
                "fund_type": "equity",  # Frontend type mapping
                "status": "ACTIVE",
                "page_size": "20",
                "page": "1",
                "sort_by": "name",
                "sort_order": "asc",
                "min_investment": "1000",
                "risk_level": "medium",  # Frontend uses 'medium', backend expects 'moderate'
            },
            "headers": {"Authorization": "Bearer frontend-token"},
        }

        # Mock FundQueryRequest to handle query parameters properly
        with patch(
            "src.functions.api.funds.FundQueryRequest"
        ) as mock_query_request_class:
            mock_query_instance = Mock()
            mock_query_instance.page_size = 20
            mock_query_instance.page = 1
            mock_query_instance.status = Mock()
            mock_query_instance.status.value = "ACTIVE"
            mock_query_instance.fund_type = Mock()
            mock_query_instance.fund_type.value = "equity"
            mock_query_instance.search = "HDFC"
            mock_query_request_class.return_value = mock_query_instance

            result = handle_list_funds(event)

            # Verify repository was called with correct parameters
            self.mock_fund_repo.list_funds.assert_called_once()
            call_args = self.mock_fund_repo.list_funds.call_args

            # Check that parameters were passed correctly
            assert call_args[1]["fund_type"] == "equity"
            assert call_args[1]["status"] == "ACTIVE"
            assert call_args[1]["search"] == "HDFC"
            assert result["statusCode"] == 200

    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.RequestValidator")
    def test_create_fund_frontend_format(
        self, mock_validator, mock_get_repo, mock_create_context, mock_session_class
    ):
        """Test POST /funds with frontend-formatted data."""
        # Setup mocks
        mock_session_class.return_value = self.mock_session_manager
        mock_create_context.return_value = self.mock_user_context
        mock_get_repo.return_value = self.mock_fund_repo

        # No existing fund
        self.mock_fund_repo.get_by_fund_id.return_value = None

        # Mock created fund
        mock_created_fund = Mock()
        mock_created_fund.dict.return_value = {
            "fund_id": "new-fund-001",
            "name": "New Frontend Fund",
        }
        self.mock_fund_repo.create.return_value = mock_created_fund

        # Frontend create request (converted from frontend Fund type)
        frontend_create_data = {
            "fund_id": "new-fund-001",
            "name": "New Frontend Fund",
            "fund_type": "equity",  # Converted from frontend 'mutual_fund'
            "nav": "10.00",
            "total_assets": "100000",  # Converted from frontend 'aum'
            "expense_ratio": "1.50",
            "minimum_investment": "1000",
            "risk_level": "moderate",  # Converted from frontend 'medium'
            "fund_manager": "Frontend Manager",
            "description": "Created via frontend",
            "bloomberg_ticker": "NEW001",
            "inception_date": "2024-02-01",
            "custom_fields": {
                "category": "Equity",
                "sub_category": "Large Cap",
                "rating": "4",
                "volume": "5000",
            },
        }

        mock_validator.validate_json_body.return_value = frontend_create_data

        # Mock FundResponse, FundCreateRequest, and Fund
        with patch(
            "src.functions.api.funds.FundResponse"
        ) as mock_fund_response_class, patch(
            "src.functions.api.funds.FundCreateRequest"
        ) as mock_create_request_class, patch(
            "src.functions.api.funds.Fund"
        ) as mock_fund_class:

            mock_fund_response_instance = Mock()
            mock_fund_response_instance.dict.return_value = {
                "fund_id": "new-fund-001",
                "name": "New Frontend Fund",
            }
            mock_fund_response_class.return_value = mock_fund_response_instance

            mock_create_request_instance = Mock()
            mock_create_request_instance.dict.return_value = frontend_create_data
            mock_create_request_instance.fund_id = "new-fund-001"
            mock_create_request_class.return_value = mock_create_request_instance

            mock_fund_instance = Mock()
            mock_fund_class.return_value = mock_fund_instance

            event = {
                "httpMethod": "POST",
                "path": "/api/funds",
                "headers": {
                    "Authorization": "Bearer frontend-token",
                    "Content-Type": "application/json",
                },
                "body": json.dumps(frontend_create_data),
            }

            result = handle_create_fund(event)

            # Verify fund creation
            self.mock_fund_repo.create.assert_called_once()
            assert result["statusCode"] == 201

    # ========== Parameter Mapping Tests ==========

    def test_frontend_backend_parameter_mapping(self):
        """Test that frontend parameters map correctly to backend expectations."""
        # Frontend to Backend mapping tests
        frontend_params = {
            "type": "mutual_fund",  # Frontend
            "riskLevel": "medium",  # Frontend
            "aum": 1000000,  # Frontend (in rupees)
            "symbol": "HDFC001",  # Frontend
            "category": "Equity",  # Frontend
            "subCategory": "Large Cap",  # Frontend
        }

        expected_backend_params = {
            "fund_type": "equity",  # Backend
            "risk_level": "moderate",  # Backend
            "total_assets": "1000000",  # Backend (as string)
            "bloomberg_ticker": "HDFC001",  # Backend
            "custom_fields": {"category": "Equity", "sub_category": "Large Cap"},
        }

        # Test the mapping logic (this would be in the actual conversion function)
        assert frontend_params["type"] == "mutual_fund"
        assert expected_backend_params["fund_type"] == "equity"

        assert frontend_params["riskLevel"] == "medium"
        assert expected_backend_params["risk_level"] == "moderate"

    def test_query_parameter_consistency(self):
        """Test that query parameters from frontend filters work correctly."""
        # Frontend filter query parameters
        frontend_query_params = {
            "search": "HDFC Equity",
            "type": "mutual_fund",
            "category": "Equity",
            "riskLevel": "medium",
            "minInvestment": "1000",
            "maxInvestment": "50000",
            "minRating": "3",
            "sortBy": "name",
            "sortOrder": "asc",
            "page": "1",
            "pageSize": "20",
        }

        # Verify all expected parameters are present
        assert "search" in frontend_query_params
        assert "type" in frontend_query_params
        assert "sortBy" in frontend_query_params
        assert "sortOrder" in frontend_query_params

        # Test parameter values
        assert frontend_query_params["minInvestment"] == "1000"
        assert frontend_query_params["sortOrder"] in ["asc", "desc"]

    # ========== Response Format Tests ==========

    def test_response_format_consistency(self):
        """Test that API responses match frontend expectations."""
        # Expected response format for getFundDetails
        expected_fund_details_response = {
            "statusCode": 200,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
            },
            "body": {
                "success": True,
                "message": "Fund retrieved successfully",
                "data": {
                    "fund": {
                        "fund_id": "fund-1",
                        "name": "Test Fund",
                        "fund_type": "equity",
                        "nav": "125.50",
                        # ... other fund fields
                    }
                },
            },
        }

        # Verify response structure
        assert expected_fund_details_response["statusCode"] == 200
        assert "data" in expected_fund_details_response["body"]
        assert "fund" in expected_fund_details_response["body"]["data"]

    # ========== Error Handling Tests ==========

    @patch("src.functions.api.funds.SessionManager")
    def test_frontend_error_handling(self, mock_session_class):
        """Test error responses match frontend expectations."""
        # Mock session validation failure
        mock_session_manager = Mock()
        mock_session_manager.validate_session.return_value = {
            "valid": False,
            "error": "Invalid token",
        }
        mock_session_class.return_value = mock_session_manager

        event = {
            "httpMethod": "GET",
            "path": "/api/funds/fund-1",
            "headers": {"Authorization": "Bearer invalid-token"},
        }

        result = handle_get_fund(event)

        # Verify error response format
        assert result["statusCode"] == 401
        response_body = json.loads(result["body"])
        assert "error" in response_body
        assert "message" in response_body

    def test_validation_error_format(self):
        """Test validation error responses match frontend expectations."""
        # Mock validation error response
        validation_error_response = {
            "statusCode": 400,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
            },
            "body": json.dumps(
                {
                    "error": "VALIDATION_ERROR",
                    "message": "Invalid fund data",
                    "details": [
                        {"field": "fund_id", "message": "Fund ID is required"},
                        {
                            "field": "minimum_investment",
                            "message": "Must be greater than 0",
                        },
                    ],
                }
            ),
        }

        # Verify error structure
        assert validation_error_response["statusCode"] == 400
        body = json.loads(validation_error_response["body"])
        assert "error" in body
        assert "message" in body
        assert "details" in body
        assert isinstance(body["details"], list)

    # ========== Historical Data Tests ==========

    def test_historical_data_request_format(self):
        """Test historical data requests match frontend expectations."""
        # Frontend historical data request patterns
        historical_requests = [
            {
                "path": "/api/funds/fund-1/historical",
                "queryStringParameters": {"period": "1Y"},
            },
            {
                "path": "/api/funds/fund-1/historical",
                "queryStringParameters": {"period": "3M"},
            },
            {
                "path": "/api/funds/fund-1/performance",
                "queryStringParameters": {"period": "1Y", "includeBenchmark": "true"},
            },
        ]

        for request in historical_requests:
            # Verify request structure
            assert "path" in request
            assert "queryStringParameters" in request

            params = request["queryStringParameters"]
            if "period" in params:
                assert params["period"] in [
                    "1D",
                    "1W",
                    "1M",
                    "3M",
                    "6M",
                    "1Y",
                    "3Y",
                    "5Y",
                    "ALL",
                ]

    # ========== Bulk Operations Tests ==========

    @patch("src.functions.api.funds.SessionManager")
    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.RequestValidator")
    def test_bulk_update_frontend_format(
        self, mock_validator, mock_get_repo, mock_create_context, mock_session_class
    ):
        """Test bulk update with frontend data format."""
        # Setup mocks
        mock_session_class.return_value = self.mock_session_manager
        mock_create_context.return_value = self.mock_user_context
        mock_get_repo.return_value = self.mock_fund_repo

        # Frontend bulk update format
        bulk_update_data = {
            "updates": [
                {
                    "fund_id": "fund-1",
                    "data": {
                        "name": "Updated Fund 1",
                        "fund_type": "equity",
                        "expense_ratio": "1.25",
                    },
                },
                {
                    "fund_id": "fund-2",
                    "data": {"name": "Updated Fund 2", "risk_level": "moderate"},
                },
            ]
        }

        mock_validator.validate_json_body.return_value = bulk_update_data

        # Mock existing funds
        mock_fund_1 = Mock()
        mock_fund_2 = Mock()
        self.mock_fund_repo.get_by_fund_id.side_effect = [mock_fund_1, mock_fund_2]
        self.mock_fund_repo.update.side_effect = [mock_fund_1, mock_fund_2]

        # Mock FundUpdateRequest and validation services
        with patch(
            "src.functions.api.funds.FundUpdateRequest"
        ) as mock_update_request_class, patch(
            "src.functions.api.funds.FundValidationService"
        ) as mock_validation_service_class:

            def create_update_request(**kwargs):
                mock_instance = Mock()
                mock_instance.dict.return_value = kwargs
                return mock_instance

            mock_update_request_class.side_effect = create_update_request

            # Mock validation service for bulk updates
            mock_validation_service_instance = Mock()
            mock_validation_service_instance.validate_bulk_fund_updates.return_value = {
                "fund-1": mock_validation_result,
                "fund-2": mock_validation_result,
            }
            mock_validation_service_instance.validate_fund_update.return_value = (
                mock_validation_result
            )
            mock_validation_service_class.return_value = (
                mock_validation_service_instance
            )

            event = {
                "httpMethod": "POST",
                "path": "/api/funds/bulk-update",
                "headers": {
                    "Authorization": "Bearer frontend-token",
                    "Content-Type": "application/json",
                },
                "body": json.dumps(bulk_update_data),
            }

            result = handle_bulk_update_funds(event)

            # Verify bulk update was processed
            assert self.mock_fund_repo.get_by_fund_id.call_count == 2
            assert self.mock_fund_repo.update.call_count == 2
            assert result["statusCode"] == 200
