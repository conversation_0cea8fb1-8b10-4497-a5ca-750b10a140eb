"""
Parameter Mapping Tests for Fund Management API.
Tests the conversion between frontend camelCase and backend snake_case parameters.
"""

import json
import pytest
import sys
from unittest.mock import Mock, patch
import os

# Mock all dependencies before any other imports
sys.modules["shared"] = Mock()
sys.modules["shared.models"] = Mock()
sys.modules["shared.models.requests"] = Mock()
sys.modules["shared.api"] = Mock()
sys.modules["shared.api.responses"] = Mock()
sys.modules["shared.models.fund"] = Mock()
sys.modules["shared.database"] = Mock()
sys.modules["shared.api.auth_dependencies"] = Mock()
sys.modules["shared.security"] = Mock()
sys.modules["shared.security.session_manager"] = Mock()
sys.modules["shared.validators"] = Mock()
sys.modules["shared.utils"] = Mock()
sys.modules["shared.utils.validation_response"] = Mock()

# Set up AWS Lambda Powertools environment
os.environ["POWERTOOLS_SERVICE_NAME"] = "FundFlow"
os.environ["POWERTOOLS_METRICS_NAMESPACE"] = "FundFlow"
os.environ["POWERTOOLS_LOG_LEVEL"] = "INFO"

# Mock APIResponse
mock_api_response = Mock()
mock_api_response.success.return_value = {
    "statusCode": 200,
    "headers": {"Content-Type": "application/json", "Access-Control-Allow-Origin": "*"},
    "body": json.dumps({"success": True, "message": "Success", "data": {}}),
}
sys.modules["shared.api.responses"].APIResponse = mock_api_response

from src.functions.api.funds import extract_fund_id_from_path


class TestParameterMapping:
    """Test parameter mapping between frontend and backend formats."""

    def test_frontend_to_backend_field_mapping(self):
        """Test mapping of frontend field names to backend field names."""
        # Frontend request data (camelCase)
        frontend_data = {
            "id": "fund-123",
            "fundManager": "John Smith",
            "expenseRatio": 1.25,
            "minimumInvestment": 1000,
            "riskLevel": "medium",
            "inceptionDate": "2020-01-01",
            "subCategory": "Large Cap",
        }

        # Expected backend mapping (snake_case)
        expected_backend_data = {
            "fund_id": "fund-123",
            "fund_manager": "John Smith",
            "expense_ratio": 1.25,
            "minimum_investment": 1000,
            "risk_level": "moderate",  # Value mapping too
            "inception_date": "2020-01-01",
            "custom_fields": {"sub_category": "Large Cap"},
        }

        # Test individual field mappings
        assert frontend_data["id"] == expected_backend_data["fund_id"]
        assert frontend_data["fundManager"] == expected_backend_data["fund_manager"]
        assert frontend_data["expenseRatio"] == expected_backend_data["expense_ratio"]
        assert (
            frontend_data["minimumInvestment"]
            == expected_backend_data["minimum_investment"]
        )
        assert frontend_data["inceptionDate"] == expected_backend_data["inception_date"]

    def test_frontend_to_backend_value_mapping(self):
        """Test mapping of frontend enum values to backend enum values."""
        # Risk level mapping
        risk_level_mapping = {"low": "low", "medium": "moderate", "high": "high"}

        for frontend_value, backend_value in risk_level_mapping.items():
            assert frontend_value in ["low", "medium", "high"]
            assert backend_value in ["low", "moderate", "high"]

        # Fund type mapping
        fund_type_mapping = {
            "mutual_fund": "equity",
            "etf": "etf",
            "index_fund": "index",
            "bond_fund": "bond",
            "money_market": "money_market",
        }

        for frontend_type, backend_type in fund_type_mapping.items():
            assert frontend_type in [
                "mutual_fund",
                "etf",
                "index_fund",
                "bond_fund",
                "money_market",
            ]
            assert backend_type in ["equity", "etf", "index", "bond", "money_market"]

    def test_backend_to_frontend_field_mapping(self):
        """Test mapping of backend field names to frontend field names."""
        # Backend response data (snake_case)
        backend_data = {
            "fund_id": "fund-123",
            "fund_manager": "John Smith",
            "expense_ratio": "1.25",
            "minimum_investment": "1000",
            "risk_level": "moderate",
            "inception_date": "2020-01-01",
            "total_assets": "1000000",
            "bloomberg_ticker": "FUND123",
            "custom_fields": {
                "category": "Equity",
                "sub_category": "Large Cap",
                "rating": "4",
            },
        }

        # Expected frontend mapping (camelCase)
        expected_frontend_data = {
            "id": "fund-123",
            "fundManager": "John Smith",
            "expenseRatio": 1.25,
            "minimumInvestment": 1000,
            "riskLevel": "medium",  # Value mapping too
            "inceptionDate": "2020-01-01",
            "aum": 1000000,
            "symbol": "FUND123",
            "category": "Equity",
            "subCategory": "Large Cap",
            "rating": 4,
        }

        # Test individual field mappings
        assert backend_data["fund_id"] == expected_frontend_data["id"]
        assert backend_data["fund_manager"] == expected_frontend_data["fundManager"]
        assert (
            float(backend_data["expense_ratio"])
            == expected_frontend_data["expenseRatio"]
        )
        assert (
            int(backend_data["minimum_investment"])
            == expected_frontend_data["minimumInvestment"]
        )
        assert backend_data["inception_date"] == expected_frontend_data["inceptionDate"]
        assert int(backend_data["total_assets"]) == expected_frontend_data["aum"]
        assert backend_data["bloomberg_ticker"] == expected_frontend_data["symbol"]

    def test_nested_field_mapping(self):
        """Test mapping of nested custom_fields to flat frontend structure."""
        backend_custom_fields = {
            "custom_fields": {
                "category": "Equity",
                "sub_category": "Large Cap",
                "rating": "4",
                "volume": "10000",
            }
        }

        expected_frontend_fields = {
            "category": "Equity",
            "subCategory": "Large Cap",
            "rating": 4,
            "volume": 10000,
        }

        # Test extraction and conversion
        custom_fields = backend_custom_fields["custom_fields"]
        assert custom_fields["category"] == expected_frontend_fields["category"]
        assert custom_fields["sub_category"] == expected_frontend_fields["subCategory"]
        assert int(custom_fields["rating"]) == expected_frontend_fields["rating"]
        assert int(custom_fields["volume"]) == expected_frontend_fields["volume"]

    def test_query_parameter_mapping(self):
        """Test mapping of frontend query parameters to backend format."""
        # Frontend query parameters (from FundFilter interface)
        frontend_query = {
            "search": "HDFC Equity",
            "type": "mutual_fund",
            "category": "Equity",
            "riskLevel": "medium",
            "minInvestment": "1000",
            "maxInvestment": "50000",
            "minRating": "3",
            "sortBy": "name",
            "sortOrder": "asc",
        }

        # Expected backend query parameters
        expected_backend_query = {
            "search": "HDFC Equity",
            "fund_type": "equity",  # Mapped value
            "category": "Equity",
            "risk_level": "moderate",  # Mapped value
            "min_investment": 1000,  # Converted to int
            "max_investment": 50000,  # Converted to int
            "min_rating": 3,  # Converted to int
            "sort_by": "name",
            "sort_order": "asc",
        }

        # Test parameter mapping
        assert frontend_query["search"] == expected_backend_query["search"]
        assert frontend_query["category"] == expected_backend_query["category"]
        assert frontend_query["sortOrder"] == expected_backend_query["sort_order"]

    def test_data_type_conversions(self):
        """Test proper data type conversions between frontend and backend."""
        # Backend typically stores numbers as strings
        backend_numeric_fields = {
            "nav": "125.50",
            "expense_ratio": "1.25",
            "minimum_investment": "1000",
            "total_assets": "1000000",
        }

        # Frontend expects actual numbers
        expected_frontend_numbers = {
            "nav": 125.50,
            "expenseRatio": 1.25,
            "minimumInvestment": 1000,
            "aum": 1000000,
        }

        # Test conversions
        assert float(backend_numeric_fields["nav"]) == expected_frontend_numbers["nav"]
        assert (
            float(backend_numeric_fields["expense_ratio"])
            == expected_frontend_numbers["expenseRatio"]
        )
        assert (
            int(backend_numeric_fields["minimum_investment"])
            == expected_frontend_numbers["minimumInvestment"]
        )
        assert (
            int(backend_numeric_fields["total_assets"])
            == expected_frontend_numbers["aum"]
        )

    def test_date_format_consistency(self):
        """Test date format consistency between frontend and backend."""
        # Backend date formats
        backend_dates = {
            "inception_date": "2020-01-01",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T10:30:00.123Z",
        }

        # Frontend expects Date objects or ISO strings
        for field, date_str in backend_dates.items():
            # Test that dates are valid ISO format
            assert isinstance(date_str, str)
            assert len(date_str) >= 10  # At least YYYY-MM-DD

            # Test date parsing would work
            if "T" in date_str:
                # Full datetime
                assert ":" in date_str
            else:
                # Date only
                assert date_str.count("-") == 2

    def test_array_field_mapping(self):
        """Test mapping of array fields between frontend and backend."""
        # Backend holdings structure
        backend_holdings = {
            "holdings": {
                "top_holdings": [
                    {"name": "Reliance Industries", "percentage": 8.5},
                    {"name": "HDFC Bank", "percentage": 7.2},
                ],
                "sector_allocation": {"Technology": "25.0", "Banking": "20.0"},
            }
        }

        # Frontend expects flattened arrays
        expected_frontend_holdings = [
            {"name": "Reliance Industries", "percentage": 8.5},
            {"name": "HDFC Bank", "percentage": 7.2},
        ]

        expected_frontend_sectors = [
            {"name": "Technology", "percentage": 25.0},
            {"name": "Banking", "percentage": 20.0},
        ]

        # Test holdings mapping
        backend_top_holdings = backend_holdings["holdings"]["top_holdings"]
        assert len(backend_top_holdings) == len(expected_frontend_holdings)
        assert backend_top_holdings[0]["name"] == expected_frontend_holdings[0]["name"]
        assert (
            backend_top_holdings[0]["percentage"]
            == expected_frontend_holdings[0]["percentage"]
        )

    def test_performance_metrics_mapping(self):
        """Test mapping of performance metrics between frontend and backend."""
        # Backend performance structure
        backend_performance = {
            "performance_metrics": {
                "ytd_return": "8.5",
                "one_year_return": "12.0",
                "three_year_return": "15.0",
                "five_year_return": "18.0",
            }
        }

        # Frontend performance structure
        expected_frontend_performance = {
            "performance": {
                "oneDay": 0.5,  # Estimated
                "oneWeek": 1.2,  # Estimated
                "oneMonth": 2.0,  # Estimated
                "threeMonths": 4.0,  # Estimated
                "sixMonths": 6.0,  # Estimated
                "oneYear": 12.0,
                "threeYears": 15.0,
                "fiveYears": 18.0,
            }
        }

        # Test performance mapping
        backend_metrics = backend_performance["performance_metrics"]
        frontend_performance = expected_frontend_performance["performance"]

        assert (
            float(backend_metrics["one_year_return"]) == frontend_performance["oneYear"]
        )
        assert (
            float(backend_metrics["three_year_return"])
            == frontend_performance["threeYears"]
        )
        assert (
            float(backend_metrics["five_year_return"])
            == frontend_performance["fiveYears"]
        )

    def test_fund_id_extraction_with_frontend_patterns(self):
        """Test fund ID extraction with frontend URL patterns."""
        # Frontend routing patterns
        frontend_paths = [
            "/api/funds/fund-1",  # Basic pattern
            "/api/v1/funds/HDFC-EQUITY-001",  # Versioned API
            "/funds/mutual-fund-123",  # Simplified path
            "/api/funds/fund-with-dashes-123",  # Complex ID
            "/stage/api/funds/PENSION-FUND-XYZ",  # Staging environment
        ]

        expected_ids = [
            "fund-1",
            "HDFC-EQUITY-001",
            "mutual-fund-123",
            "fund-with-dashes-123",
            "PENSION-FUND-XYZ",
        ]

        for path, expected_id in zip(frontend_paths, expected_ids):
            extracted_id = extract_fund_id_from_path(path)
            assert (
                extracted_id == expected_id
            ), f"Failed to extract {expected_id} from {path}"

    def test_error_response_mapping(self):
        """Test error response format mapping between frontend and backend."""
        # Backend error response
        backend_error = {
            "statusCode": 400,
            "body": json.dumps(
                {
                    "error": "VALIDATION_ERROR",
                    "message": "Invalid fund data",
                    "details": [
                        {"field": "fund_id", "message": "Fund ID is required"},
                        {
                            "field": "minimum_investment",
                            "message": "Must be greater than 0",
                        },
                    ],
                }
            ),
        }

        # Frontend expects this format
        expected_frontend_error = {
            "error": "VALIDATION_ERROR",
            "message": "Invalid fund data",
            "details": [
                {"field": "fund_id", "message": "Fund ID is required"},
                {"field": "minimum_investment", "message": "Must be greater than 0"},
            ],
        }

        # Test error format
        backend_body = json.loads(backend_error["body"])
        assert backend_body["error"] == expected_frontend_error["error"]
        assert backend_body["message"] == expected_frontend_error["message"]
        assert len(backend_body["details"]) == len(expected_frontend_error["details"])
        assert (
            backend_body["details"][0]["field"]
            == expected_frontend_error["details"][0]["field"]
        )
