"""
Tests for market data API endpoints.
"""

import json
import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timezone

# Mock the dependencies before importing the module
import sys
from unittest.mock import Mock

# Mock AWS Lambda Powertools
sys.modules["aws_lambda_powertools"] = Mock()
sys.modules["aws_lambda_powertools.utilities.typing"] = Mock()
sys.modules["aws_lambda_powertools.metrics"] = Mock()

# Mock shared modules
sys.modules["shared.models.requests"] = Mock()
sys.modules["shared.api.responses"] = Mock()
sys.modules["shared.models.fund"] = Mock()
sys.modules["shared.models.market_data"] = Mock()
sys.modules["shared.validation"] = Mock()
sys.modules["shared.utils.validation_response"] = Mock()
sys.modules["shared.repositories.fund_repository"] = Mock()
sys.modules["shared.auth.session_manager"] = Mock()
sys.modules["shared.auth.user_context"] = Mock()

# Mock APIResponse
mock_api_response = Mock()
mock_api_response.success.return_value = {"statusCode": 200, "body": "{}"}
mock_api_response.validation_error.return_value = {"statusCode": 400, "body": "{}"}
mock_api_response.not_found.return_value = {"statusCode": 404, "body": "{}"}
mock_api_response.internal_error.return_value = {"statusCode": 500, "body": "{}"}
sys.modules["shared.api.responses"].APIResponse = mock_api_response

# Mock RequestValidator
mock_request_validator = Mock()
mock_request_validator.validate_json_body.return_value = {}
sys.modules["shared.api.responses"].RequestValidator = mock_request_validator

# Mock Fund model
mock_fund = Mock()
mock_fund.fund_id = "fund-123"
mock_fund.name = "Test Fund"
sys.modules["shared.models.fund"].Fund = Mock(return_value=mock_fund)

# Mock MarketDataInput
mock_market_data_input = Mock()
sys.modules["shared.models.market_data"].MarketDataInput = Mock(return_value=mock_market_data_input)

# Import the module after mocking
from src.functions.api.funds import (
    handle_market_data_input,
    handle_get_market_data,
    get_market_data_repository,
    extract_fund_id_from_path,
)


class TestMarketDataAPI:
    """Test market data API endpoints."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mock_user_context = {"user_id": "test-user"}
        self.mock_fund = Mock()
        self.mock_fund.fund_id = "fund-123"
        self.mock_fund.name = "Test Fund"

    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.get_market_data_repository")
    @patch("src.functions.api.funds.extract_fund_id_from_path")
    def test_market_data_input_success(
        self, mock_extract_id, mock_get_market_repo, mock_get_fund_repo, mock_create_context
    ):
        """Test successful market data input submission."""
        # Setup mocks
        mock_extract_id.return_value = "fund-123"
        mock_create_context.return_value = self.mock_user_context
        
        mock_fund_repo = Mock()
        mock_fund_repo.get_by_fund_id.return_value = self.mock_fund
        mock_get_fund_repo.return_value = mock_fund_repo
        
        mock_market_repo = Mock()
        mock_market_repo.store_market_data_input.return_value = {"input_id": "input-456"}
        mock_get_market_repo.return_value = mock_market_repo

        # Mock request validator
        mock_request_validator.validate_json_body.return_value = {
            "fund_id": "fund-123",
            "data_timestamp": datetime.now(timezone.utc).isoformat(),
            "input_by": "test-user",
            "nav": 125.45,
            "validated": True
        }

        # Create test event
        event = {
            "pathParameters": {"fund_id": "fund-123"},
            "body": json.dumps({
                "nav": 125.45,
                "market_price": 125.50,
                "validated": True
            })
        }

        # Call the handler
        result = handle_market_data_input(event)

        # Verify the result
        mock_api_response.success.assert_called_once()
        mock_market_repo.store_market_data_input.assert_called_once()

    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.extract_fund_id_from_path")
    def test_market_data_input_missing_fund_id(self, mock_extract_id, mock_create_context):
        """Test market data input with missing fund ID."""
        mock_extract_id.return_value = None
        mock_create_context.return_value = self.mock_user_context

        event = {"pathParameters": {}}

        result = handle_market_data_input(event)

        mock_api_response.validation_error.assert_called_with("Fund ID is required")

    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.extract_fund_id_from_path")
    def test_market_data_input_fund_not_found(
        self, mock_extract_id, mock_get_fund_repo, mock_create_context
    ):
        """Test market data input for non-existent fund."""
        mock_extract_id.return_value = "fund-123"
        mock_create_context.return_value = self.mock_user_context
        
        mock_fund_repo = Mock()
        mock_fund_repo.get_by_fund_id.return_value = None
        mock_get_fund_repo.return_value = mock_fund_repo

        mock_request_validator.validate_json_body.return_value = {
            "nav": 125.45,
            "validated": True
        }

        event = {
            "pathParameters": {"fund_id": "fund-123"},
            "body": json.dumps({"nav": 125.45})
        }

        result = handle_market_data_input(event)

        mock_api_response.not_found.assert_called_with("Fund fund-123 not found")

    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.get_market_data_repository")
    @patch("src.functions.api.funds.extract_fund_id_from_path")
    def test_get_market_data_success(
        self, mock_extract_id, mock_get_market_repo, mock_get_fund_repo, mock_create_context
    ):
        """Test successful market data retrieval."""
        # Setup mocks
        mock_extract_id.return_value = "fund-123"
        mock_create_context.return_value = self.mock_user_context
        
        mock_fund_repo = Mock()
        mock_fund_repo.get_by_fund_id.return_value = self.mock_fund
        mock_get_fund_repo.return_value = mock_fund_repo
        
        mock_market_repo = Mock()
        mock_market_data = {
            "fund_id": "fund-123",
            "price_data": {"nav": "125.45"},
            "valuation_metrics": {"price_to_book": "2.45"}
        }
        mock_market_repo.get_market_data_summary.return_value = mock_market_data
        mock_get_market_repo.return_value = mock_market_repo

        # Create test event
        event = {"pathParameters": {"fund_id": "fund-123"}}

        # Call the handler
        result = handle_get_market_data(event)

        # Verify the result
        mock_api_response.success.assert_called_once_with(mock_market_data)
        mock_market_repo.get_market_data_summary.assert_called_once_with("fund-123")

    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.extract_fund_id_from_path")
    def test_get_market_data_missing_fund_id(self, mock_extract_id, mock_create_context):
        """Test market data retrieval with missing fund ID."""
        mock_extract_id.return_value = None
        mock_create_context.return_value = self.mock_user_context

        event = {"pathParameters": {}}

        result = handle_get_market_data(event)

        mock_api_response.validation_error.assert_called_with("Fund ID is required")

    def test_market_data_repository_mock(self):
        """Test the mock market data repository."""
        repo = get_market_data_repository()
        
        # Test store method
        mock_input = Mock()
        result = repo.store_market_data_input(mock_input)
        assert "input_id" in result
        
        # Test get method
        summary = repo.get_market_data_summary("fund-123")
        assert summary["fund_id"] == "fund-123"
        assert "price_data" in summary
        assert "valuation_metrics" in summary
        assert "technical_indicators" in summary
        assert "risk_analytics" in summary

    def test_extract_fund_id_from_path(self):
        """Test fund ID extraction from path parameters."""
        # Test valid fund ID
        path_params = {"fund_id": "fund-123"}
        result = extract_fund_id_from_path(path_params)
        assert result == "fund-123"
        
        # Test missing fund ID
        path_params = {}
        result = extract_fund_id_from_path(path_params)
        assert result is None
        
        # Test None path parameters
        result = extract_fund_id_from_path(None)
        assert result is None


class TestMarketDataValidation:
    """Test market data validation scenarios."""

    @patch("src.functions.api.funds.create_user_context")
    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.extract_fund_id_from_path")
    def test_invalid_market_data_input(
        self, mock_extract_id, mock_get_fund_repo, mock_create_context
    ):
        """Test validation error handling for invalid market data."""
        from pydantic import ValidationError
        
        mock_extract_id.return_value = "fund-123"
        mock_create_context.return_value = {"user_id": "test-user"}
        
        mock_fund_repo = Mock()
        mock_fund_repo.get_by_fund_id.return_value = Mock()
        mock_get_fund_repo.return_value = mock_fund_repo

        # Mock validation error
        validation_error = ValidationError.from_exception_data(
            "ValidationError",
            [{"type": "value_error", "loc": ("nav",), "msg": "Invalid value"}]
        )
        
        # Mock MarketDataInput to raise validation error
        with patch("src.functions.api.funds.MarketDataInput") as mock_input_class:
            mock_input_class.side_effect = validation_error
            
            mock_request_validator.validate_json_body.return_value = {
                "nav": "invalid_value"
            }

            event = {
                "pathParameters": {"fund_id": "fund-123"},
                "body": json.dumps({"nav": "invalid_value"})
            }

            result = handle_market_data_input(event)

            mock_api_response.validation_error.assert_called()


if __name__ == "__main__":
    pytest.main([__file__])
