/**
 * Tests for market data frontend components.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { jest } from '@jest/globals';

// Mock the types
const mockFund = {
  id: 'fund-123',
  name: 'Test Growth Fund',
  symbol: 'TGF',
  type: 'mutual_fund' as const,
  category: 'Equity',
  nav: 125.45,
  previousNav: 124.80,
  change: 0.65,
  changePercent: 0.52,
  volume: 150000,
  aum: 5000,
  expenseRatio: 1.25,
  minimumInvestment: 1000,
  riskLevel: 'medium' as const,
  rating: 4,
  inceptionDate: new Date('2020-01-01'),
  fundManager: '<PERSON>',
  description: 'A test growth fund',
  performance: {
    oneDay: 0.52,
    oneWeek: 2.1,
    oneMonth: 5.8,
    threeMonths: 12.3,
    sixMonths: 18.7,
    oneYear: 24.5,
    threeYears: 45.2,
    fiveYears: 78.9,
  },
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockFundDetails = {
  ...mockFund,
  analytics: {
    kpis: {
      totalReturn: 24.5,
      annualizedReturn: 18.2,
      volatility: 16.8,
      sharpeRatio: 1.25,
      sortinoRatio: 1.45,
      calmarRatio: 0.85,
      informationRatio: 0.65,
      treynorRatio: 0.125,
      alpha: 2.3,
      beta: 1.05,
      maxDrawdown: -15.2,
      trackingError: 3.8,
    },
    riskMetrics: {
      standardDeviation: 16.8,
      downSideRisk: 12.4,
      downsideDeviation: 11.2,
      varRisk: -18.5,
      var1d95: -2.5,
      var1d99: -3.8,
      cvar1d95: -3.2,
      cvar1d99: -4.5,
      sortRatio: 1.45,
      calmarRatio: 0.85,
      correlation: 0.85,
    },
    valuationMetrics: {
      priceToBook: 2.45,
      priceToEarnings: 18.5,
      priceToSales: 3.2,
      priceToCashFlow: 12.8,
      enterpriseValue: 6000000000,
      evToRevenue: 4.2,
      evToEbitda: 15.6,
      returnOnEquity: 15.2,
      returnOnAssets: 8.7,
      debtToEquity: 0.45,
      dividendYield: 2.8,
      bookValuePerShare: 51.2,
    },
    technicalIndicators: {
      sma20: 124.80,
      sma50: 123.45,
      sma200: 120.30,
      rsi14: 65.4,
      macdLine: 0.85,
      macdSignal: 0.72,
      bollingerUpper: 127.50,
      bollingerLower: 122.10,
      vwap: 125.20,
      supportLevel: 121.00,
      resistanceLevel: 128.00,
    },
    assetAllocation: {
      stocks: 85.5,
      bonds: 10.2,
      cash: 3.8,
      other: 0.5,
    },
    geographicAllocation: {
      domestic: 75.2,
      international: 20.8,
      emerging: 4.0,
    },
    marketCapAllocation: {
      largeCap: 65.5,
      midCap: 25.2,
      smallCap: 9.3,
    },
    currencyAllocation: {
      USD: 75.2,
      EUR: 15.8,
      GBP: 6.5,
      JPY: 2.5,
    },
    topHoldings: [],
    sectorAllocation: [],
  },
  currentPriceData: {
    fundId: 'fund-123',
    asOf: new Date(),
    nav: {
      timestamp: new Date(),
      value: 125.45,
      source: 'fund_company' as const,
      quality: 'excellent' as const,
      currency: 'USD',
    },
    marketPrice: {
      timestamp: new Date(),
      value: 125.50,
      source: 'yahoo_finance' as const,
      quality: 'good' as const,
      currency: 'USD',
    },
    volume: 150000,
    marketCap: 5000000000,
  },
  marketDataSummary: {
    lastUpdated: new Date(),
    dataSources: {},
    overallQuality: 'good' as const,
  },
  primaryBenchmark: {
    benchmarkId: 'nifty50',
    name: 'NIFTY 50',
    symbol: 'NIFTY50',
    asOf: new Date(),
    currentValue: 19500,
  },
  secondaryBenchmarks: [],
  historicalData: [],
  benchmark: {
    name: 'NIFTY 50',
    symbol: 'NIFTY50',
    performance: {
      oneDay: 0.3,
      oneWeek: 1.8,
      oneMonth: 4.2,
      threeMonths: 8.5,
      sixMonths: 15.2,
      oneYear: 20.8,
      threeYears: 38.5,
      fiveYears: 65.2,
    },
  },
  documents: [],
};

// Mock the UI components
jest.mock('@/components/ui/Card', () => {
  return {
    __esModule: true,
    default: ({ children, className }: any) => (
      <div className={`card ${className}`}>{children}</div>
    ),
    Card: {
      Header: ({ children }: any) => <div className="card-header">{children}</div>,
      Title: ({ children }: any) => <h3 className="card-title">{children}</h3>,
      Content: ({ children }: any) => <div className="card-content">{children}</div>,
    },
  };
});

jest.mock('@/components/ui/Button', () => {
  return {
    __esModule: true,
    default: ({ children, onClick, disabled, type, variant }: any) => (
      <button
        onClick={onClick}
        disabled={disabled}
        type={type}
        className={`button ${variant}`}
      >
        {children}
      </button>
    ),
  };
});

// Import components after mocking
import MarketDataDisplay from '../frontend/src/components/funds/MarketDataDisplay';
import MarketDataInputForm from '../frontend/src/components/funds/MarketDataInput';
import RiskAnalyticsDisplay from '../frontend/src/components/funds/RiskAnalyticsDisplay';

describe('MarketDataDisplay', () => {
  test('renders market data correctly', () => {
    render(<MarketDataDisplay fund={mockFundDetails} />);
    
    // Check if main sections are rendered
    expect(screen.getByText('Real-time Market Data')).toBeInTheDocument();
    expect(screen.getByText('Valuation Metrics')).toBeInTheDocument();
    expect(screen.getByText('Technical Indicators')).toBeInTheDocument();
    
    // Check if NAV is displayed
    expect(screen.getByText('Net Asset Value')).toBeInTheDocument();
    expect(screen.getByText('$125.45')).toBeInTheDocument();
    
    // Check if P/E ratio is displayed
    expect(screen.getByText('P/E Ratio')).toBeInTheDocument();
    expect(screen.getByText('18.50')).toBeInTheDocument();
  });

  test('handles missing market data gracefully', () => {
    const fundWithoutMarketData = {
      ...mockFundDetails,
      currentPriceData: undefined,
    };
    
    render(<MarketDataDisplay fund={fundWithoutMarketData} />);
    
    // Should still render the component structure
    expect(screen.getByText('Valuation Metrics')).toBeInTheDocument();
    expect(screen.getByText('Technical Indicators')).toBeInTheDocument();
  });
});

describe('MarketDataInputForm', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders form fields correctly', () => {
    render(
      <MarketDataInputForm
        fund={mockFund}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );
    
    // Check form title
    expect(screen.getByText('Market Data Input - Test Growth Fund')).toBeInTheDocument();
    
    // Check form fields
    expect(screen.getByLabelText(/Input By/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Data Timestamp/)).toBeInTheDocument();
    expect(screen.getByLabelText(/NAV/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Market Price/)).toBeInTheDocument();
    expect(screen.getByLabelText(/P\/E Ratio/)).toBeInTheDocument();
    
    // Check buttons
    expect(screen.getByText('Submit Market Data')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  test('validates required fields', async () => {
    render(
      <MarketDataInputForm
        fund={mockFund}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );
    
    // Try to submit without filling required fields
    const submitButton = screen.getByText('Submit Market Data');
    fireEvent.click(submitButton);
    
    // Should show validation error
    await waitFor(() => {
      expect(screen.getByText('Input by field is required')).toBeInTheDocument();
    });
    
    // Should not call onSubmit
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  test('submits valid data', async () => {
    render(
      <MarketDataInputForm
        fund={mockFund}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );
    
    // Fill in required fields
    fireEvent.change(screen.getByLabelText(/Input By/), {
      target: { value: 'test-user' },
    });
    
    fireEvent.change(screen.getByLabelText(/NAV/), {
      target: { value: '125.45' },
    });
    
    // Submit form
    const submitButton = screen.getByText('Submit Market Data');
    fireEvent.click(submitButton);
    
    // Should call onSubmit with form data
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          fundId: 'fund-123',
          inputBy: 'test-user',
          nav: 125.45,
        })
      );
    });
  });

  test('calls onCancel when cancel button is clicked', () => {
    render(
      <MarketDataInputForm
        fund={mockFund}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );
    
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    expect(mockOnCancel).toHaveBeenCalled();
  });
});

describe('RiskAnalyticsDisplay', () => {
  test('renders risk analytics correctly', () => {
    render(<RiskAnalyticsDisplay fund={mockFundDetails} />);
    
    // Check main sections
    expect(screen.getByText('Value at Risk (VaR)')).toBeInTheDocument();
    expect(screen.getByText('Risk-Adjusted Return Metrics')).toBeInTheDocument();
    expect(screen.getByText('Volatility Analysis')).toBeInTheDocument();
    
    // Check specific metrics
    expect(screen.getByText('1-Day VaR (95%)')).toBeInTheDocument();
    expect(screen.getByText('Sharpe Ratio')).toBeInTheDocument();
    expect(screen.getByText('Maximum Drawdown')).toBeInTheDocument();
  });

  test('applies correct risk color coding', () => {
    render(<RiskAnalyticsDisplay fund={mockFundDetails} />);
    
    // VaR values should have appropriate color classes
    const varElements = screen.getAllByText(/-\d+\.\d+%/);
    expect(varElements.length).toBeGreaterThan(0);
  });

  test('shows risk assessment', () => {
    render(<RiskAnalyticsDisplay fund={mockFundDetails} />);
    
    expect(screen.getByText('Risk Assessment')).toBeInTheDocument();
    expect(screen.getByText('Overall Risk Level')).toBeInTheDocument();
  });
});
