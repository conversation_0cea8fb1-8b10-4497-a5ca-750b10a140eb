"""
Tests for enhanced market data models.
"""

import pytest
from datetime import datetime, timezone, date
from decimal import Decimal
from pydantic import ValidationError

from src.shared.models.market_data import (
    MarketDataSource,
    DataQuality,
    PriceType,
    MarketDataPoint,
    PriceData,
    ValuationMetrics,
    TechnicalIndicators,
    RiskAnalytics,
    MarketDataInput,
    BenchmarkData,
    MarketDataSummary,
)


class TestMarketDataPoint:
    """Test MarketDataPoint model."""

    def test_valid_market_data_point(self):
        """Test creating a valid market data point."""
        data_point = MarketDataPoint(
            timestamp=datetime.now(timezone.utc),
            value=Decimal("125.45"),
            source=MarketDataSource.FUND_COMPANY,
            quality=DataQuality.EXCELLENT,
            currency="USD"
        )
        
        assert data_point.value == Decimal("125.45")
        assert data_point.source == MarketDataSource.FUND_COMPANY
        assert data_point.quality == DataQuality.EXCELLENT
        assert data_point.currency == "USD"

    def test_invalid_currency_format(self):
        """Test validation of currency format."""
        with pytest.raises(ValidationError):
            MarketDataPoint(
                timestamp=datetime.now(timezone.utc),
                value=Decimal("125.45"),
                source=MarketDataSource.FUND_COMPANY,
                quality=DataQuality.EXCELLENT,
                currency="INVALID"  # Should be 3 characters
            )

    def test_serialization(self):
        """Test JSON serialization."""
        data_point = MarketDataPoint(
            timestamp=datetime.now(timezone.utc),
            value=Decimal("125.45"),
            source=MarketDataSource.FUND_COMPANY,
            quality=DataQuality.EXCELLENT,
            currency="USD"
        )
        
        json_data = data_point.model_dump(mode="json")
        assert json_data["value"] == "125.45"
        assert json_data["source"] == "fund_company"
        assert json_data["quality"] == "excellent"


class TestPriceData:
    """Test PriceData model."""

    def test_valid_price_data(self):
        """Test creating valid price data."""
        nav_point = MarketDataPoint(
            timestamp=datetime.now(timezone.utc),
            value=Decimal("125.45"),
            source=MarketDataSource.FUND_COMPANY,
            quality=DataQuality.EXCELLENT,
            currency="USD"
        )
        
        price_data = PriceData(
            fund_id="fund-123",
            as_of=datetime.now(timezone.utc),
            nav=nav_point,
            volume=150000,
            market_cap=Decimal("5000000000")
        )
        
        assert price_data.fund_id == "fund-123"
        assert price_data.nav == nav_point
        assert price_data.volume == 150000
        assert price_data.market_cap == Decimal("5000000000")

    def test_negative_volume_validation(self):
        """Test that negative volume is rejected."""
        with pytest.raises(ValidationError):
            PriceData(
                fund_id="fund-123",
                as_of=datetime.now(timezone.utc),
                volume=-1000  # Should be >= 0
            )

    def test_serialization(self):
        """Test JSON serialization of price data."""
        price_data = PriceData(
            fund_id="fund-123",
            as_of=datetime.now(timezone.utc),
            market_cap=Decimal("5000000000"),
            price_change_1d=Decimal("2.50")
        )
        
        json_data = price_data.model_dump(mode="json")
        assert json_data["market_cap"] == "5000000000"
        assert json_data["price_change_1d"] == "2.50"


class TestValuationMetrics:
    """Test ValuationMetrics model."""

    def test_valid_valuation_metrics(self):
        """Test creating valid valuation metrics."""
        metrics = ValuationMetrics(
            fund_id="fund-123",
            as_of=date.today(),
            price_to_book=Decimal("2.45"),
            price_to_earnings=Decimal("18.5"),
            return_on_equity=Decimal("15.2"),
            dividend_yield=Decimal("2.8")
        )
        
        assert metrics.fund_id == "fund-123"
        assert metrics.price_to_book == Decimal("2.45")
        assert metrics.return_on_equity == Decimal("15.2")

    def test_negative_ratio_validation(self):
        """Test that negative ratios are rejected where appropriate."""
        with pytest.raises(ValidationError):
            ValuationMetrics(
                fund_id="fund-123",
                as_of=date.today(),
                price_to_book=Decimal("-1.0")  # Should be >= 0
            )

    def test_serialization(self):
        """Test JSON serialization."""
        metrics = ValuationMetrics(
            fund_id="fund-123",
            as_of=date.today(),
            price_to_earnings=Decimal("18.5"),
            dividend_yield=Decimal("2.8")
        )
        
        json_data = metrics.model_dump(mode="json")
        assert json_data["price_to_earnings"] == "18.5"
        assert json_data["dividend_yield"] == "2.8"


class TestTechnicalIndicators:
    """Test TechnicalIndicators model."""

    def test_valid_technical_indicators(self):
        """Test creating valid technical indicators."""
        indicators = TechnicalIndicators(
            fund_id="fund-123",
            as_of=datetime.now(timezone.utc),
            sma_20=Decimal("124.80"),
            rsi_14=Decimal("65.4"),
            support_level=Decimal("121.00")
        )
        
        assert indicators.fund_id == "fund-123"
        assert indicators.sma_20 == Decimal("124.80")
        assert indicators.rsi_14 == Decimal("65.4")

    def test_rsi_range_validation(self):
        """Test RSI range validation (0-100)."""
        with pytest.raises(ValidationError):
            TechnicalIndicators(
                fund_id="fund-123",
                as_of=datetime.now(timezone.utc),
                rsi_14=Decimal("150.0")  # Should be <= 100
            )


class TestRiskAnalytics:
    """Test RiskAnalytics model."""

    def test_valid_risk_analytics(self):
        """Test creating valid risk analytics."""
        risk = RiskAnalytics(
            fund_id="fund-123",
            as_of=date.today(),
            var_1d_95=Decimal("-2.5"),
            sharpe_ratio=Decimal("1.25"),
            max_drawdown=Decimal("-15.2"),
            correlation_vs_benchmark=Decimal("0.85")
        )
        
        assert risk.fund_id == "fund-123"
        assert risk.var_1d_95 == Decimal("-2.5")
        assert risk.correlation_vs_benchmark == Decimal("0.85")

    def test_correlation_range_validation(self):
        """Test correlation range validation (-1 to 1)."""
        with pytest.raises(ValidationError):
            RiskAnalytics(
                fund_id="fund-123",
                as_of=date.today(),
                correlation_vs_benchmark=Decimal("1.5")  # Should be <= 1
            )

    def test_max_drawdown_validation(self):
        """Test max drawdown validation (should be <= 0)."""
        with pytest.raises(ValidationError):
            RiskAnalytics(
                fund_id="fund-123",
                as_of=date.today(),
                max_drawdown=Decimal("5.0")  # Should be <= 0
            )


class TestMarketDataInput:
    """Test MarketDataInput model."""

    def test_valid_market_data_input(self):
        """Test creating valid market data input."""
        input_data = MarketDataInput(
            fund_id="fund-123",
            data_timestamp=datetime.now(timezone.utc),
            input_by="user-456",
            nav=Decimal("125.45"),
            volatility=Decimal("18.5"),
            validated=True
        )
        
        assert input_data.fund_id == "fund-123"
        assert input_data.nav == Decimal("125.45")
        assert input_data.validated is True

    def test_negative_values_validation(self):
        """Test that negative values are rejected where appropriate."""
        with pytest.raises(ValidationError):
            MarketDataInput(
                fund_id="fund-123",
                data_timestamp=datetime.now(timezone.utc),
                input_by="user-456",
                nav=Decimal("-10.0")  # Should be > 0
            )

    def test_notes_length_validation(self):
        """Test notes length validation."""
        with pytest.raises(ValidationError):
            MarketDataInput(
                fund_id="fund-123",
                data_timestamp=datetime.now(timezone.utc),
                input_by="user-456",
                notes="x" * 1001  # Should be <= 1000 characters
            )


class TestMarketDataSummary:
    """Test MarketDataSummary model."""

    def test_valid_market_data_summary(self):
        """Test creating valid market data summary."""
        price_data = PriceData(
            fund_id="fund-123",
            as_of=datetime.now(timezone.utc)
        )
        
        summary = MarketDataSummary(
            fund_id="fund-123",
            last_updated=datetime.now(timezone.utc),
            price_data=price_data,
            overall_quality=DataQuality.GOOD
        )
        
        assert summary.fund_id == "fund-123"
        assert summary.price_data == price_data
        assert summary.overall_quality == DataQuality.GOOD

    def test_empty_summary(self):
        """Test creating summary with minimal data."""
        summary = MarketDataSummary(
            fund_id="fund-123",
            last_updated=datetime.now(timezone.utc)
        )
        
        assert summary.fund_id == "fund-123"
        assert summary.price_data is None
        assert len(summary.secondary_benchmarks) == 0


if __name__ == "__main__":
    pytest.main([__file__])
