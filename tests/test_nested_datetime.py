#!/usr/bin/env python
"""Test nested datetime serialization issue."""

import sys
import os

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone
from decimal import Decimal
from src.shared.models.fund import (
    Fund,
    FundType,
    FundStatus,
    Currency,
    FundDynamoDBItem,
    FundUpdate,
    Holdings,
)

# Create a fund with holdings that has a datetime field
holdings = Holdings(
    top_holdings=[{"name": "Apple", "percentage": Decimal("5.5")}],
    sector_allocation={"Technology": Decimal("30.5")},
    total_holdings_count=10,
    holdings_concentration=Decimal("25.0"),
    last_updated=datetime.now(timezone.utc),  # This is a datetime field!
)

test_fund = Fund(
    fund_id="test-123",
    name="Test Fund",
    fund_type=FundType.EQUITY,
    holdings=holdings,  # Include holdings with datetime
)

print("1. Fund with nested holdings datetime:")
print(f"   holdings.last_updated: {test_fund.holdings.last_updated}")
print(f"   Type: {type(test_fund.holdings.last_updated)}")

# Test serialization
print("\n2. Testing to_dynamodb_item serialization:")
serialized = FundDynamoDBItem.to_dynamodb_item(test_fund)
print(
    f"   Serialized holdings.last_updated: {serialized.get('holdings', {}).get('last_updated')}"
)
print(f"   Type: {type(serialized.get('holdings', {}).get('last_updated'))}")

# Test model_dump
print("\n3. Testing model_dump:")
dumped = test_fund.model_dump()
holdings_data = dumped.get("holdings", {})
print(f"   holdings.last_updated from model_dump: {holdings_data.get('last_updated')}")
print(f"   Type: {type(holdings_data.get('last_updated'))}")

# This is the issue! model_dump() returns nested datetime objects
# The base repository update method might not handle nested datetime serialization properly
