"""
Test cases for portfolio data models.
"""

import pytest
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, Any

from src.shared.models.portfolio import (
    Portfolio,
    PortfolioHolding,
    PortfolioTransaction,
    PortfolioPerformance,
    PortfolioStatus,
    TransactionType,
    PortfolioType,
    PortfolioCreateRequest,
    PortfolioUpdateRequest,
    AddHoldingRequest,
    AddTransactionRequest,
    PortfolioDynamoDBItem,
)
from src.shared.models.fund import Currency, RiskLevel


class TestPortfolioModels:
    """Test portfolio data models."""

    def test_portfolio_creation(self):
        """Test creating a portfolio with valid data."""
        portfolio = Portfolio(
            portfolio_id="portfolio-123",
            name="Test Portfolio",
            description="A test portfolio",
            portfolio_type=PortfolioType.PERSONAL,
            status=PortfolioStatus.ACTIVE,
            user_id="user-123",
            base_currency=Currency.USD,
            inception_date=datetime.now(timezone.utc),
            total_value=Decimal("10000.00"),
            total_cost_basis=Decimal("9500.00"),
            cash_balance=Decimal("500.00"),
        )

        assert portfolio.portfolio_id == "portfolio-123"
        assert portfolio.name == "Test Portfolio"
        assert portfolio.portfolio_type == PortfolioType.PERSONAL
        assert portfolio.status == PortfolioStatus.ACTIVE
        assert portfolio.total_value == Decimal("10000.00")
        assert portfolio.total_gain_loss == Decimal("500.00")
        assert portfolio.total_gain_loss_pct == Decimal("5.263157894736842105263157895")

    def test_portfolio_validation(self):
        """Test portfolio validation rules."""
        # Test invalid inception date (future)
        with pytest.raises(ValueError, match="Inception date cannot be in the future"):
            Portfolio(
                portfolio_id="portfolio-123",
                name="Test Portfolio",
                portfolio_type=PortfolioType.PERSONAL,
                user_id="user-123",
                inception_date=datetime.now(timezone.utc).replace(year=2030),
            )

        # Test negative financial values
        with pytest.raises(ValueError, match="Financial values cannot be negative"):
            Portfolio(
                portfolio_id="portfolio-123",
                name="Test Portfolio",
                portfolio_type=PortfolioType.PERSONAL,
                user_id="user-123",
                total_value=Decimal("-100.00"),
            )

    def test_portfolio_holding_creation(self):
        """Test creating a portfolio holding."""
        holding = PortfolioHolding(
            fund_id="fund-123",
            fund_name="Test Fund",
            fund_symbol="TF",
            shares=Decimal("100.0"),
            average_cost=Decimal("50.00"),
            current_price=Decimal("55.00"),
            market_value=Decimal("5500.00"),
            cost_basis=Decimal("5000.00"),
            unrealized_gain_loss=Decimal("500.00"),
            unrealized_gain_loss_pct=Decimal("10.00"),
            weight=Decimal("55.00"),
            first_purchase_date=datetime.now(timezone.utc),
            last_updated=datetime.now(timezone.utc),
        )

        assert holding.fund_id == "fund-123"
        assert holding.shares == Decimal("100.0")
        assert holding.market_value == Decimal("5500.00")
        assert holding.unrealized_gain_loss == Decimal("500.00")

    def test_portfolio_transaction_creation(self):
        """Test creating a portfolio transaction."""
        transaction = PortfolioTransaction(
            transaction_id="tx-123",
            fund_id="fund-123",
            fund_name="Test Fund",
            transaction_type=TransactionType.BUY,
            transaction_date=datetime.now(timezone.utc),
            shares=Decimal("50.0"),
            price=Decimal("100.00"),
            amount=Decimal("5000.00"),
            fees=Decimal("10.00"),
            net_amount=Decimal("4990.00"),
            description="Test purchase",
            created_at=datetime.now(timezone.utc),
        )

        assert transaction.transaction_id == "tx-123"
        assert transaction.transaction_type == TransactionType.BUY
        assert transaction.shares == Decimal("50.0")
        assert transaction.amount == Decimal("5000.00")
        assert transaction.net_amount == Decimal("4990.00")

    def test_portfolio_performance_creation(self):
        """Test creating portfolio performance metrics."""
        performance = PortfolioPerformance(
            total_return=Decimal("1000.00"),
            total_return_pct=Decimal("10.00"),
            one_day_return=Decimal("0.50"),
            one_month_return=Decimal("2.00"),
            one_year_return=Decimal("8.00"),
            volatility=Decimal("15.00"),
            sharpe_ratio=Decimal("1.20"),
            max_drawdown=Decimal("-5.00"),
            as_of_date=datetime.now(timezone.utc),
        )

        assert performance.total_return == Decimal("1000.00")
        assert performance.total_return_pct == Decimal("10.00")
        assert performance.volatility == Decimal("15.00")
        assert performance.sharpe_ratio == Decimal("1.20")

    def test_portfolio_create_request(self):
        """Test portfolio creation request model."""
        request = PortfolioCreateRequest(
            name="New Portfolio",
            description="A new portfolio",
            portfolio_type=PortfolioType.RETIREMENT,
            base_currency=Currency.USD,
            cash_balance=Decimal("1000.00"),
            risk_level=RiskLevel.MODERATE,
            benchmark="S&P 500",
            tags=["retirement", "long-term"],
        )

        assert request.name == "New Portfolio"
        assert request.portfolio_type == PortfolioType.RETIREMENT
        assert request.cash_balance == Decimal("1000.00")
        assert request.risk_level == RiskLevel.MODERATE
        assert "retirement" in request.tags

    def test_portfolio_update_request(self):
        """Test portfolio update request model."""
        request = PortfolioUpdateRequest(
            name="Updated Portfolio",
            status=PortfolioStatus.INACTIVE,
            cash_balance=Decimal("2000.00"),
            tags=["updated", "inactive"],
        )

        assert request.name == "Updated Portfolio"
        assert request.status == PortfolioStatus.INACTIVE
        assert request.cash_balance == Decimal("2000.00")

    def test_add_holding_request(self):
        """Test add holding request model."""
        request = AddHoldingRequest(
            fund_id="fund-456",
            shares=Decimal("25.0"),
            purchase_price=Decimal("80.00"),
            purchase_date=datetime.now(timezone.utc),
            fees=Decimal("5.00"),
        )

        assert request.fund_id == "fund-456"
        assert request.shares == Decimal("25.0")
        assert request.purchase_price == Decimal("80.00")
        assert request.fees == Decimal("5.00")

    def test_add_transaction_request(self):
        """Test add transaction request model."""
        request = AddTransactionRequest(
            fund_id="fund-789",
            transaction_type=TransactionType.SELL,
            transaction_date=datetime.now(timezone.utc),
            shares=Decimal("10.0"),
            price=Decimal("90.00"),
            amount=Decimal("900.00"),
            fees=Decimal("2.00"),
            description="Test sale",
        )

        assert request.fund_id == "fund-789"
        assert request.transaction_type == TransactionType.SELL
        assert request.shares == Decimal("10.0")
        assert request.amount == Decimal("900.00")


class TestPortfolioDynamoDBItem:
    """Test Portfolio DynamoDB serialization/deserialization."""

    def test_portfolio_to_dynamodb_item(self):
        """Test converting Portfolio to DynamoDB item format."""
        portfolio = Portfolio(
            portfolio_id="portfolio-123",
            name="Test Portfolio",
            portfolio_type=PortfolioType.PERSONAL,
            user_id="user-123",
            total_value=Decimal("10000.00"),
            total_cost_basis=Decimal("9500.00"),
            cash_balance=Decimal("500.00"),
            created_at=datetime(2023, 1, 1, tzinfo=timezone.utc),
            updated_at=datetime(2023, 1, 2, tzinfo=timezone.utc),
        )

        item = PortfolioDynamoDBItem.to_dynamodb_item(portfolio)

        assert item["portfolio_id"] == "portfolio-123"
        assert item["name"] == "Test Portfolio"
        assert item["portfolio_type"] == "personal"
        assert item["total_value"] == "10000.00"
        assert item["created_at"] == "2023-01-01T00:00:00+00:00"

    def test_portfolio_from_dynamodb_item(self):
        """Test converting DynamoDB item to Portfolio object."""
        item = {
            "portfolio_id": "portfolio-123",
            "name": "Test Portfolio",
            "portfolio_type": "personal",
            "status": "active",
            "user_id": "user-123",
            "base_currency": "USD",
            "total_value": "10000.00",
            "total_cost_basis": "9500.00",
            "cash_balance": "500.00",
            "total_gain_loss": "500.00",
            "total_gain_loss_pct": "5.26",
            "holdings": [],
            "recent_transactions": [],
            "tags": ["test"],
            "custom_fields": {},
            "created_at": "2023-01-01T00:00:00+00:00",
            "updated_at": "2023-01-02T00:00:00+00:00",
            "inception_date": "2023-01-01T00:00:00+00:00",
        }

        portfolio = PortfolioDynamoDBItem.from_dynamodb_item(item)

        assert portfolio.portfolio_id == "portfolio-123"
        assert portfolio.name == "Test Portfolio"
        assert portfolio.portfolio_type == PortfolioType.PERSONAL
        assert portfolio.total_value == Decimal("10000.00")
        assert portfolio.created_at == datetime(2023, 1, 1, tzinfo=timezone.utc)

    def test_portfolio_roundtrip_conversion(self):
        """Test that Portfolio can be converted to DynamoDB and back without data loss."""
        original_portfolio = Portfolio(
            portfolio_id="portfolio-roundtrip",
            name="Roundtrip Test Portfolio",
            description="Testing roundtrip conversion",
            portfolio_type=PortfolioType.TAXABLE,
            status=PortfolioStatus.ACTIVE,
            user_id="user-roundtrip",
            base_currency=Currency.USD,
            total_value=Decimal("15000.50"),
            total_cost_basis=Decimal("14000.25"),
            cash_balance=Decimal("1000.75"),
            risk_level=RiskLevel.HIGH,
            benchmark="NASDAQ",
            tags=["tech", "growth"],
            custom_fields={"advisor": "John Doe"},
            created_at=datetime(2023, 6, 15, 10, 30, 0, tzinfo=timezone.utc),
            updated_at=datetime(2023, 6, 16, 14, 45, 0, tzinfo=timezone.utc),
            inception_date=datetime(2023, 1, 1, tzinfo=timezone.utc),
        )

        # Convert to DynamoDB item and back
        dynamodb_item = PortfolioDynamoDBItem.to_dynamodb_item(original_portfolio)
        restored_portfolio = PortfolioDynamoDBItem.from_dynamodb_item(dynamodb_item)

        # Verify all fields are preserved
        assert restored_portfolio.portfolio_id == original_portfolio.portfolio_id
        assert restored_portfolio.name == original_portfolio.name
        assert restored_portfolio.description == original_portfolio.description
        assert restored_portfolio.portfolio_type == original_portfolio.portfolio_type
        assert restored_portfolio.status == original_portfolio.status
        assert restored_portfolio.user_id == original_portfolio.user_id
        assert restored_portfolio.base_currency == original_portfolio.base_currency
        assert restored_portfolio.total_value == original_portfolio.total_value
        assert restored_portfolio.total_cost_basis == original_portfolio.total_cost_basis
        assert restored_portfolio.cash_balance == original_portfolio.cash_balance
        assert restored_portfolio.risk_level == original_portfolio.risk_level
        assert restored_portfolio.benchmark == original_portfolio.benchmark
        assert restored_portfolio.tags == original_portfolio.tags
        assert restored_portfolio.custom_fields == original_portfolio.custom_fields
        assert restored_portfolio.created_at == original_portfolio.created_at
        assert restored_portfolio.updated_at == original_portfolio.updated_at
        assert restored_portfolio.inception_date == original_portfolio.inception_date
