"""
Test cases for portfolio repository.
"""

import pytest
from datetime import datetime, timezone
from decimal import Decimal
from unittest.mock import Mock, patch, MagicMock
from moto import mock_aws
import boto3

from src.shared.repositories.portfolio_repository import PortfolioRepository
from src.shared.models.portfolio import (
    Portfolio,
    PortfolioType,
    PortfolioStatus,
    PortfolioCreateRequest,
    PortfolioUpdateRequest,
    AddHoldingRequest,
    TransactionType,
)
from src.shared.models.fund import Currency, RiskLevel


@pytest.fixture
def mock_portfolio():
    """Create a mock portfolio for testing."""
    return Portfolio(
        portfolio_id="portfolio-test-123",
        name="Test Portfolio",
        description="A test portfolio",
        portfolio_type=PortfolioType.PERSONAL,
        status=PortfolioStatus.ACTIVE,
        user_id="user-test-123",
        base_currency=Currency.USD,
        inception_date=datetime(2023, 1, 1, tzinfo=timezone.utc),
        total_value=Decimal("10000.00"),
        total_cost_basis=Decimal("9500.00"),
        cash_balance=Decimal("500.00"),
        holdings=[],
        recent_transactions=[],
        tags=["test"],
        custom_fields={},
        created_at=datetime(2023, 1, 1, tzinfo=timezone.utc),
        updated_at=datetime(2023, 1, 2, tzinfo=timezone.utc),
    )


@pytest.fixture
def portfolio_create_request():
    """Create a portfolio creation request for testing."""
    return PortfolioCreateRequest(
        name="New Test Portfolio",
        description="A new portfolio for testing",
        portfolio_type=PortfolioType.RETIREMENT,
        base_currency=Currency.USD,
        cash_balance=Decimal("1000.00"),
        risk_level=RiskLevel.MODERATE,
        benchmark="S&P 500",
        tags=["retirement", "test"],
    )


@mock_aws
class TestPortfolioRepository:
    """Test portfolio repository operations."""

    def setup_method(self):
        """Set up test environment."""
        # Create mock DynamoDB table
        self.dynamodb = boto3.resource("dynamodb", region_name="us-east-1")
        
        # Create table
        self.table = self.dynamodb.create_table(
            TableName="fundflow-test-portfolios",
            KeySchema=[{"AttributeName": "portfolio_id", "KeyType": "HASH"}],
            AttributeDefinitions=[{"AttributeName": "portfolio_id", "AttributeType": "S"}],
            BillingMode="PAY_PER_REQUEST",
        )
        
        # Initialize repository
        with patch.dict("os.environ", {"ENVIRONMENT": "test"}):
            self.repo = PortfolioRepository(region="us-east-1")

    def test_get_portfolio_success(self, mock_portfolio):
        """Test successful portfolio retrieval."""
        # Put item in table
        self.table.put_item(Item={
            "portfolio_id": "portfolio-test-123",
            "name": "Test Portfolio",
            "portfolio_type": "personal",
            "status": "active",
            "user_id": "user-test-123",
            "base_currency": "USD",
            "total_value": "10000.00",
            "total_cost_basis": "9500.00",
            "cash_balance": "500.00",
            "total_gain_loss": "500.00",
            "total_gain_loss_pct": "5.26",
            "holdings": [],
            "recent_transactions": [],
            "tags": ["test"],
            "custom_fields": {},
            "created_at": "2023-01-01T00:00:00+00:00",
            "updated_at": "2023-01-02T00:00:00+00:00",
            "inception_date": "2023-01-01T00:00:00+00:00",
        })

        # Test retrieval
        result = self.repo.get_portfolio("portfolio-test-123")

        assert result is not None
        assert result.portfolio_id == "portfolio-test-123"
        assert result.name == "Test Portfolio"
        assert result.portfolio_type == PortfolioType.PERSONAL

    def test_get_portfolio_not_found(self):
        """Test portfolio retrieval when portfolio doesn't exist."""
        result = self.repo.get_portfolio("nonexistent-portfolio")
        assert result is None

    def test_create_portfolio_success(self, portfolio_create_request):
        """Test successful portfolio creation."""
        user_id = "user-test-123"
        
        # Create portfolio
        result = self.repo.create_portfolio(portfolio_create_request, user_id)

        assert result is not None
        assert result.name == "New Test Portfolio"
        assert result.portfolio_type == PortfolioType.RETIREMENT
        assert result.user_id == user_id
        assert result.cash_balance == Decimal("1000.00")
        assert "retirement" in result.tags

        # Verify it was saved to DynamoDB
        response = self.table.get_item(Key={"portfolio_id": result.portfolio_id})
        assert "Item" in response
        assert response["Item"]["name"] == "New Test Portfolio"

    def test_list_portfolios_success(self, mock_portfolio):
        """Test successful portfolio listing."""
        # Put test portfolios in table
        portfolios_data = [
            {
                "portfolio_id": "portfolio-1",
                "name": "Portfolio 1",
                "portfolio_type": "personal",
                "status": "active",
                "user_id": "user-test-123",
                "base_currency": "USD",
                "total_value": "5000.00",
                "total_cost_basis": "4500.00",
                "cash_balance": "250.00",
                "total_gain_loss": "500.00",
                "total_gain_loss_pct": "11.11",
                "holdings": [],
                "recent_transactions": [],
                "tags": ["personal"],
                "custom_fields": {},
                "created_at": "2023-01-01T00:00:00+00:00",
                "updated_at": "2023-01-02T00:00:00+00:00",
                "inception_date": "2023-01-01T00:00:00+00:00",
            },
            {
                "portfolio_id": "portfolio-2",
                "name": "Portfolio 2",
                "portfolio_type": "retirement",
                "status": "active",
                "user_id": "user-test-123",
                "base_currency": "USD",
                "total_value": "15000.00",
                "total_cost_basis": "14000.00",
                "cash_balance": "500.00",
                "total_gain_loss": "1000.00",
                "total_gain_loss_pct": "7.14",
                "holdings": [],
                "recent_transactions": [],
                "tags": ["retirement"],
                "custom_fields": {},
                "created_at": "2023-01-01T00:00:00+00:00",
                "updated_at": "2023-01-02T00:00:00+00:00",
                "inception_date": "2023-01-01T00:00:00+00:00",
            },
            {
                "portfolio_id": "portfolio-3",
                "name": "Portfolio 3",
                "portfolio_type": "personal",
                "status": "inactive",
                "user_id": "other-user-456",  # Different user
                "base_currency": "USD",
                "total_value": "8000.00",
                "total_cost_basis": "7500.00",
                "cash_balance": "300.00",
                "total_gain_loss": "500.00",
                "total_gain_loss_pct": "6.67",
                "holdings": [],
                "recent_transactions": [],
                "tags": ["personal"],
                "custom_fields": {},
                "created_at": "2023-01-01T00:00:00+00:00",
                "updated_at": "2023-01-02T00:00:00+00:00",
                "inception_date": "2023-01-01T00:00:00+00:00",
            },
        ]

        for portfolio_data in portfolios_data:
            self.table.put_item(Item=portfolio_data)

        # Test listing portfolios for user-test-123
        result = self.repo.list_portfolios(user_id="user-test-123")

        assert result["count"] == 2  # Should only return portfolios for this user
        assert len(result["portfolios"]) == 2
        assert result["has_more"] is False

        # Verify portfolio objects are correctly deserialized
        portfolio_ids = [p.portfolio_id for p in result["portfolios"]]
        assert "portfolio-1" in portfolio_ids
        assert "portfolio-2" in portfolio_ids
        assert "portfolio-3" not in portfolio_ids  # Different user

    def test_list_portfolios_with_filters(self):
        """Test portfolio listing with filters."""
        # Put test portfolios with different types and statuses
        portfolios_data = [
            {
                "portfolio_id": "portfolio-personal-active",
                "name": "Personal Active",
                "portfolio_type": "personal",
                "status": "active",
                "user_id": "user-test-123",
                "base_currency": "USD",
                "total_value": "5000.00",
                "total_cost_basis": "4500.00",
                "cash_balance": "250.00",
                "total_gain_loss": "500.00",
                "total_gain_loss_pct": "11.11",
                "holdings": [],
                "recent_transactions": [],
                "tags": [],
                "custom_fields": {},
                "created_at": "2023-01-01T00:00:00+00:00",
                "updated_at": "2023-01-02T00:00:00+00:00",
                "inception_date": "2023-01-01T00:00:00+00:00",
            },
            {
                "portfolio_id": "portfolio-retirement-active",
                "name": "Retirement Active",
                "portfolio_type": "retirement",
                "status": "active",
                "user_id": "user-test-123",
                "base_currency": "USD",
                "total_value": "15000.00",
                "total_cost_basis": "14000.00",
                "cash_balance": "500.00",
                "total_gain_loss": "1000.00",
                "total_gain_loss_pct": "7.14",
                "holdings": [],
                "recent_transactions": [],
                "tags": [],
                "custom_fields": {},
                "created_at": "2023-01-01T00:00:00+00:00",
                "updated_at": "2023-01-02T00:00:00+00:00",
                "inception_date": "2023-01-01T00:00:00+00:00",
            },
            {
                "portfolio_id": "portfolio-personal-inactive",
                "name": "Personal Inactive",
                "portfolio_type": "personal",
                "status": "inactive",
                "user_id": "user-test-123",
                "base_currency": "USD",
                "total_value": "3000.00",
                "total_cost_basis": "2800.00",
                "cash_balance": "100.00",
                "total_gain_loss": "200.00",
                "total_gain_loss_pct": "7.14",
                "holdings": [],
                "recent_transactions": [],
                "tags": [],
                "custom_fields": {},
                "created_at": "2023-01-01T00:00:00+00:00",
                "updated_at": "2023-01-02T00:00:00+00:00",
                "inception_date": "2023-01-01T00:00:00+00:00",
            },
        ]

        for portfolio_data in portfolios_data:
            self.table.put_item(Item=portfolio_data)

        # Test filtering by portfolio type
        result = self.repo.list_portfolios(
            user_id="user-test-123",
            portfolio_type="personal"
        )
        assert result["count"] == 2  # 2 personal portfolios

        # Test filtering by status
        result = self.repo.list_portfolios(
            user_id="user-test-123",
            status="active"
        )
        assert result["count"] == 2  # 2 active portfolios

        # Test filtering by both type and status
        result = self.repo.list_portfolios(
            user_id="user-test-123",
            portfolio_type="personal",
            status="active"
        )
        assert result["count"] == 1  # 1 personal active portfolio

    def test_update_portfolio_success(self, mock_portfolio):
        """Test successful portfolio update."""
        # First create a portfolio
        self.table.put_item(Item={
            "portfolio_id": "portfolio-test-123",
            "name": "Original Name",
            "portfolio_type": "personal",
            "status": "active",
            "user_id": "user-test-123",
            "base_currency": "USD",
            "total_value": "10000.00",
            "total_cost_basis": "9500.00",
            "cash_balance": "500.00",
            "total_gain_loss": "500.00",
            "total_gain_loss_pct": "5.26",
            "holdings": [],
            "recent_transactions": [],
            "tags": ["original"],
            "custom_fields": {},
            "created_at": "2023-01-01T00:00:00+00:00",
            "updated_at": "2023-01-02T00:00:00+00:00",
            "inception_date": "2023-01-01T00:00:00+00:00",
        })

        # Update the portfolio
        update_request = PortfolioUpdateRequest(
            name="Updated Name",
            status=PortfolioStatus.INACTIVE,
            tags=["updated", "test"],
        )

        result = self.repo.update_portfolio(
            "portfolio-test-123", 
            update_request, 
            "user-test-123"
        )

        assert result is not None
        assert result.name == "Updated Name"
        assert result.status == PortfolioStatus.INACTIVE
        assert "updated" in result.tags

        # Verify it was updated in DynamoDB
        response = self.table.get_item(Key={"portfolio_id": "portfolio-test-123"})
        assert response["Item"]["name"] == "Updated Name"
        assert response["Item"]["status"] == "inactive"

    def test_delete_portfolio_success(self):
        """Test successful portfolio deletion."""
        # First create a portfolio
        self.table.put_item(Item={
            "portfolio_id": "portfolio-to-delete",
            "name": "Portfolio to Delete",
            "portfolio_type": "personal",
            "status": "active",
            "user_id": "user-test-123",
            "base_currency": "USD",
            "total_value": "5000.00",
            "total_cost_basis": "4500.00",
            "cash_balance": "250.00",
            "total_gain_loss": "500.00",
            "total_gain_loss_pct": "11.11",
            "holdings": [],
            "recent_transactions": [],
            "tags": [],
            "custom_fields": {},
            "created_at": "2023-01-01T00:00:00+00:00",
            "updated_at": "2023-01-02T00:00:00+00:00",
            "inception_date": "2023-01-01T00:00:00+00:00",
        })

        # Delete the portfolio
        result = self.repo.delete_portfolio("portfolio-to-delete")
        assert result is True

        # Verify it was deleted from DynamoDB
        response = self.table.get_item(Key={"portfolio_id": "portfolio-to-delete"})
        assert "Item" not in response

    def test_generate_id(self):
        """Test portfolio ID generation."""
        portfolio_id = self.repo._generate_id()
        assert portfolio_id.startswith("portfolio-")
        assert len(portfolio_id) > len("portfolio-")

        # Generate multiple IDs to ensure uniqueness
        ids = [self.repo._generate_id() for _ in range(10)]
        assert len(set(ids)) == 10  # All should be unique
