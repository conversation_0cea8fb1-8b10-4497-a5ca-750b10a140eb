"""
Test cases for portfolio frontend-backend integration.
Tests the actual API patterns used by the frontend.
"""

import pytest
import json
from datetime import datetime, timezone
from decimal import Decimal
from unittest.mock import Mock, patch, MagicMock

# Import frontend API patterns
from frontend.src.lib.api import portfolioApi
from frontend.src.types.index import (
    Portfolio,
    PortfolioCreateRequest,
    PortfolioUpdateRequest,
    AddHoldingRequest,
    PortfolioType,
    PortfolioStatus,
)


class TestPortfolioFrontendIntegration:
    """Test portfolio frontend-backend integration patterns."""

    @patch("frontend.src.lib.api.apiRequest")
    def test_get_portfolios_frontend_pattern(self, mock_api_request):
        """Test the frontend pattern for fetching portfolios."""
        # Mock API response in the format expected by frontend
        mock_response = {
            "success": True,
            "message": "Portfolios fetched successfully",
            "data": {
                "portfolios": [
                    {
                        "portfolio_id": "portfolio-123",
                        "name": "Test Portfolio",
                        "description": "A test portfolio",
                        "portfolio_type": "personal",
                        "status": "active",
                        "user_id": "user-123",
                        "base_currency": "USD",
                        "inception_date": "2023-01-01T00:00:00Z",
                        "total_value": 10000.00,
                        "total_cost_basis": 9500.00,
                        "cash_balance": 500.00,
                        "total_gain_loss": 500.00,
                        "total_gain_loss_pct": 5.26,
                        "holdings": [],
                        "recent_transactions": [],
                        "risk_level": "moderate",
                        "benchmark": "S&P 500",
                        "tags": ["test"],
                        "custom_fields": {},
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "holdings_count": 0,
                        "transactions_count": 0,
                    }
                ],
                "pagination": {
                    "page": 1,
                    "page_size": 20,
                    "total_count": 1,
                    "has_more": False,
                },
            },
        }

        mock_api_request.return_value = mock_response

        # Call frontend API function
        result = portfolioApi.getPortfolios()

        # Verify the request was made correctly
        mock_api_request.assert_called_once_with("/portfolios")

        # Verify the response format matches frontend expectations
        assert result["success"] is True
        assert "data" in result
        assert len(result["data"]) == 1
        assert result["data"][0]["portfolioId"] == "portfolio-123"  # camelCase conversion

    @patch("frontend.src.lib.api.apiRequest")
    def test_get_portfolio_by_id_frontend_pattern(self, mock_api_request):
        """Test the frontend pattern for fetching a single portfolio."""
        portfolio_id = "portfolio-123"
        
        mock_response = {
            "success": True,
            "message": "Portfolio fetched successfully",
            "data": {
                "portfolio_id": "portfolio-123",
                "name": "Test Portfolio",
                "portfolio_type": "personal",
                "status": "active",
                "total_value": 10000.00,
                "holdings": [],
                "recent_transactions": [],
            },
        }

        mock_api_request.return_value = mock_response

        # Call frontend API function
        result = portfolioApi.getPortfolioById(portfolio_id)

        # Verify the request was made correctly
        mock_api_request.assert_called_once_with(f"/portfolios/{portfolio_id}")

        # Verify the response format
        assert result["success"] is True
        assert result["data"]["portfolioId"] == "portfolio-123"

    @patch("frontend.src.lib.api.apiRequest")
    def test_create_portfolio_frontend_pattern(self, mock_api_request):
        """Test the frontend pattern for creating a portfolio."""
        portfolio_data = {
            "name": "New Portfolio",
            "description": "A new test portfolio",
            "portfolioType": "retirement",
            "baseCurrency": "USD",
            "cashBalance": 1000.0,
            "riskLevel": "moderate",
            "benchmark": "S&P 500",
            "tags": ["retirement", "long-term"],
        }

        mock_response = {
            "success": True,
            "message": "Portfolio created successfully",
            "data": {
                "portfolio_id": "portfolio-new-123",
                "name": "New Portfolio",
                "portfolio_type": "retirement",
                "status": "active",
                "total_value": 1000.0,
                "created_at": "2023-06-01T00:00:00Z",
            },
        }

        mock_api_request.return_value = mock_response

        # Call frontend API function
        result = portfolioApi.createPortfolio(portfolio_data)

        # Verify the request was made correctly
        mock_api_request.assert_called_once_with(
            "/portfolios",
            {
                "method": "POST",
                "body": json.dumps(portfolio_data),
            }
        )

        # Verify the response format
        assert result["success"] is True
        assert result["data"]["portfolioId"] == "portfolio-new-123"

    @patch("frontend.src.lib.api.apiRequest")
    def test_update_portfolio_frontend_pattern(self, mock_api_request):
        """Test the frontend pattern for updating a portfolio."""
        portfolio_id = "portfolio-123"
        update_data = {
            "name": "Updated Portfolio Name",
            "status": "inactive",
            "cashBalance": 2000.0,
        }

        mock_response = {
            "success": True,
            "message": "Portfolio updated successfully",
            "data": {
                "portfolio_id": "portfolio-123",
                "name": "Updated Portfolio Name",
                "status": "inactive",
                "cash_balance": 2000.0,
                "updated_at": "2023-06-02T00:00:00Z",
            },
        }

        mock_api_request.return_value = mock_response

        # Call frontend API function
        result = portfolioApi.updatePortfolio(portfolio_id, update_data)

        # Verify the request was made correctly
        mock_api_request.assert_called_once_with(
            f"/portfolios/{portfolio_id}",
            {
                "method": "PUT",
                "body": json.dumps(update_data),
            }
        )

        # Verify the response format
        assert result["success"] is True
        assert result["data"]["name"] == "Updated Portfolio Name"

    @patch("frontend.src.lib.api.apiRequest")
    def test_delete_portfolio_frontend_pattern(self, mock_api_request):
        """Test the frontend pattern for deleting a portfolio."""
        portfolio_id = "portfolio-123"

        mock_response = {
            "success": True,
            "message": "Portfolio deleted successfully",
            "data": {"deleted": True},
        }

        mock_api_request.return_value = mock_response

        # Call frontend API function
        result = portfolioApi.deletePortfolio(portfolio_id)

        # Verify the request was made correctly
        mock_api_request.assert_called_once_with(
            f"/portfolios/{portfolio_id}",
            {"method": "DELETE"}
        )

        # Verify the response format
        assert result["success"] is True
        assert result["data"]["deleted"] is True

    @patch("frontend.src.lib.api.apiRequest")
    def test_add_holding_frontend_pattern(self, mock_api_request):
        """Test the frontend pattern for adding a holding to a portfolio."""
        portfolio_id = "portfolio-123"
        holding_data = {
            "fundId": "fund-456",
            "shares": 25.0,
            "purchasePrice": 80.0,
            "purchaseDate": "2023-06-01T00:00:00Z",
            "fees": 5.0,
        }

        mock_response = {
            "success": True,
            "message": "Holding added successfully",
            "data": {
                "portfolio_id": "portfolio-123",
                "holdings": [
                    {
                        "fund_id": "fund-456",
                        "fund_name": "Test Fund",
                        "shares": 25.0,
                        "average_cost": 80.0,
                        "market_value": 2000.0,
                    }
                ],
                "updated_at": "2023-06-01T10:00:00Z",
            },
        }

        mock_api_request.return_value = mock_response

        # Call frontend API function
        result = portfolioApi.addHolding(portfolio_id, holding_data)

        # Verify the request was made correctly
        mock_api_request.assert_called_once_with(
            f"/portfolios/{portfolio_id}/holdings",
            {
                "method": "POST",
                "body": json.dumps(holding_data),
            }
        )

        # Verify the response format
        assert result["success"] is True
        assert len(result["data"]["holdings"]) == 1

    @patch("frontend.src.lib.api.USE_AWS_API", False)
    @patch("frontend.src.lib.api.generateMockPortfolios")
    def test_mock_data_fallback(self, mock_generate_portfolios):
        """Test that frontend falls back to mock data when AWS API is disabled."""
        mock_portfolios = [
            {
                "portfolioId": "mock-portfolio-1",
                "name": "Mock Portfolio 1",
                "portfolioType": "personal",
                "status": "active",
                "totalValue": 5000.0,
                "holdings": [],
                "recentTransactions": [],
            }
        ]

        mock_generate_portfolios.return_value = mock_portfolios

        # Call frontend API function
        result = portfolioApi.getPortfolios()

        # Verify mock data is used
        assert result["success"] is True
        assert len(result["data"]) == 1
        assert result["data"][0]["portfolioId"] == "mock-portfolio-1"
        assert "mock data" in result["message"].lower()

    @patch("frontend.src.lib.api.apiRequest")
    def test_error_handling_frontend_pattern(self, mock_api_request):
        """Test frontend error handling patterns."""
        # Mock API error response
        mock_api_request.side_effect = Exception("Network error")

        # Test with fallback enabled
        with patch("frontend.src.lib.api.ENABLE_MOCK_FALLBACK", True):
            with patch("frontend.src.lib.api.generateMockPortfolios") as mock_generate:
                mock_generate.return_value = []
                
                result = portfolioApi.getPortfolios()
                
                # Should fall back to mock data
                assert result["success"] is True
                assert "mock data" in result["message"].lower()

        # Test with fallback disabled
        with patch("frontend.src.lib.api.ENABLE_MOCK_FALLBACK", False):
            with pytest.raises(Exception, match="Network error"):
                portfolioApi.getPortfolios()

    def test_portfolio_type_consistency(self):
        """Test that portfolio types are consistent between frontend and backend."""
        frontend_types = ["personal", "retirement", "taxable", "trust", "corporate", "education"]
        
        # These should match the PortfolioType enum in the backend
        from src.shared.models.portfolio import PortfolioType
        backend_types = [t.value for t in PortfolioType]
        
        assert set(frontend_types) == set(backend_types)

    def test_portfolio_status_consistency(self):
        """Test that portfolio statuses are consistent between frontend and backend."""
        frontend_statuses = ["active", "inactive", "closed", "liquidating"]
        
        # These should match the PortfolioStatus enum in the backend
        from src.shared.models.portfolio import PortfolioStatus
        backend_statuses = [s.value for s in PortfolioStatus]
        
        assert set(frontend_statuses) == set(backend_statuses)

    def test_transaction_type_consistency(self):
        """Test that transaction types are consistent between frontend and backend."""
        frontend_types = ["buy", "sell", "dividend", "interest", "fee", "transfer_in", "transfer_out", "split", "merger"]
        
        # These should match the TransactionType enum in the backend
        from src.shared.models.portfolio import TransactionType
        backend_types = [t.value for t in TransactionType]
        
        assert set(frontend_types) == set(backend_types)
