#!/usr/bin/env python
"""Test repository update method with datetime serialization."""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone
from src.shared.models.fund import Fund, FundType, FundStatus, Currency
from src.shared.repositories.fund_repository import FundRepository

# Create a test fund
test_fund = Fund(
    fund_id="test-123",
    name="Test Fund",
    fund_type=FundType.EQUITY,
    status=FundStatus.ACTIVE,
    currency=Currency.USD,
    created_at=datetime.now(timezone.utc),
    updated_at=datetime.now(timezone.utc)
)

print("Test fund created")
print("updated_at:", test_fund.updated_at)
print("Type:", type(test_fund.updated_at))

# Test _serialize_item method
repo = FundRepository()
print("\nTesting _serialize_item:")
try:
    serialized = repo._serialize_item(test_fund)
    print("Serialized updated_at:", serialized.get('updated_at'))
    print("Type:", type(serialized.get('updated_at')))
except Exception as e:
    print("Error:", e)
    import traceback
    traceback.print_exc()

# Test the base update method flow
print("\nTesting base update method flow:")
# The base update method calls _serialize_item which should use FundDynamoDBItem.to_dynamodb_item