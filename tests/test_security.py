"""
Tests for security features including session management, rate limiting, and CSRF protection.
"""

import json
import time
import pytest
from unittest.mock import patch, <PERSON><PERSON><PERSON>, Mock
from botocore.exceptions import ClientError

# Import the security classes we want to test
from src.shared.security.session_manager import (
    SessionManager,
    SecurityEnforcer,
    RateLimiter,
    CSRFProtection,
    SessionExpiredError,
    InvalidSessionError,
    SecurityViolationError,
)


class TestSessionManager:
    """Test the SessionManager class."""

    def setup_method(self):
        """Set up test fixtures."""
        with patch.dict(
            "os.environ",
            {
                "USER_POOL_ID": "test-user-pool",
                "USER_POOL_CLIENT_ID": "test-client-id",
                "AWS_REGION": "us-east-1",
                "SESSION_TIMEOUT_SECONDS": "3600",
                "REFRESH_THRESHOLD_SECONDS": "900",
            },
        ):
            self.session_manager = SessionManager()

    def test_extract_access_token_from_header(self):
        """Test extracting access token from Authorization header."""
        event = {"headers": {"Authorization": "Bearer test-token-123"}}

        token = self.session_manager._extract_access_token(event)
        assert token == "test-token-123"

    def test_extract_access_token_case_insensitive(self):
        """Test extracting access token with case-insensitive header."""
        event = {"headers": {"authorization": "Bearer test-token-456"}}

        token = self.session_manager._extract_access_token(event)
        assert token == "test-token-456"

    def test_extract_access_token_no_header(self):
        """Test extracting access token when no header present."""
        event = {"headers": {}}

        token = self.session_manager._extract_access_token(event)
        assert token is None

    def test_extract_access_token_invalid_format(self):
        """Test extracting access token with invalid header format."""
        event = {"headers": {"Authorization": "InvalidFormat test-token"}}

        token = self.session_manager._extract_access_token(event)
        assert token is None

    @patch("boto3.client")
    def test_validate_cognito_token_success(self, mock_boto3_client):
        """Test successful Cognito token validation."""
        # Mock Cognito response
        mock_cognito = Mock()
        mock_boto3_client.return_value = mock_cognito
        mock_cognito.get_user.return_value = {
            "Username": "testuser",
            "UserStatus": "CONFIRMED",
            "UserAttributes": [
                {"Name": "sub", "Value": "user-123"},
                {"Name": "email", "Value": "<EMAIL>"},
                {"Name": "email_verified", "Value": "true"},
            ],
        }

        result = self.session_manager._validate_cognito_token("valid-token")

        assert result["username"] == "testuser"
        assert result["user_sub"] == "user-123"
        assert result["email"] == "<EMAIL>"
        assert result["email_verified"] is True
        assert result["user_status"] == "CONFIRMED"

    @patch("boto3.client")
    def test_validate_cognito_token_expired(self, mock_boto3_client):
        """Test Cognito token validation with expired token."""
        mock_cognito = Mock()
        mock_boto3_client.return_value = mock_cognito
        mock_cognito.get_user.side_effect = ClientError(
            {"Error": {"Code": "NotAuthorizedException"}}, "GetUser"
        )

        with pytest.raises(
            InvalidSessionError, match="Invalid or expired access token"
        ):
            self.session_manager._validate_cognito_token("expired-token")

    @patch("boto3.client")
    def test_validate_cognito_token_user_not_found(self, mock_boto3_client):
        """Test Cognito token validation with user not found."""
        mock_cognito = Mock()
        mock_boto3_client.return_value = mock_cognito
        mock_cognito.get_user.side_effect = ClientError(
            {"Error": {"Code": "UserNotFoundException"}}, "GetUser"
        )

        with pytest.raises(InvalidSessionError, match="User not found"):
            self.session_manager._validate_cognito_token("invalid-token")

    @patch("boto3.client")
    def test_invalidate_session_success(self, mock_boto3_client):
        """Test successful session invalidation."""
        mock_cognito = Mock()
        mock_boto3_client.return_value = mock_cognito
        mock_cognito.global_sign_out.return_value = {}

        result = self.session_manager.invalidate_session("valid-token")

        assert result is True
        mock_cognito.global_sign_out.assert_called_once_with(AccessToken="valid-token")

    @patch("boto3.client")
    def test_invalidate_session_failure(self, mock_boto3_client):
        """Test failed session invalidation."""
        mock_cognito = Mock()
        mock_boto3_client.return_value = mock_cognito
        mock_cognito.global_sign_out.side_effect = ClientError(
            {"Error": {"Code": "NotAuthorizedException"}}, "GlobalSignOut"
        )

        result = self.session_manager.invalidate_session("invalid-token")

        assert result is False

    def test_check_session_timeout_valid(self):
        """Test session timeout check with valid session."""
        user_info = {"user_sub": "user-123"}

        # Mock current time to simulate short session
        with patch("time.time", return_value=1000):
            result = self.session_manager._check_session_timeout(user_info)

        assert result["requires_refresh"] is False
        assert result["session_duration"] == 0  # Just started

    def test_check_session_timeout_requires_refresh(self):
        """Test session timeout check that requires refresh."""
        user_info = {"user_sub": "user-123"}

        # Mock time to simulate session needing refresh
        with patch("time.time", side_effect=[1000, 3700]):  # 37 minutes later
            # First call to get session start time (mocked as 1000)
            # Second call for current time check (3700 = 1000 + 2700 seconds)
            result = self.session_manager._check_session_timeout(user_info)

        # Should require refresh if within 15 minutes of expiry
        # Session timeout is 3600 seconds, refresh threshold is 900 seconds
        assert result["requires_refresh"] is True

    def test_validate_session_integration(self):
        """Test full session validation integration."""
        event = {"headers": {"Authorization": "Bearer valid-token"}}

        with patch.object(
            self.session_manager, "_validate_cognito_token"
        ) as mock_validate:
            mock_validate.return_value = {
                "username": "testuser",
                "user_sub": "user-123",
                "email": "<EMAIL>",
                "email_verified": True,
                "user_status": "CONFIRMED",
            }

            with patch("time.time", return_value=1000):
                result = self.session_manager.validate_session(event)

            assert result["valid"] is True
            assert result["user_info"]["username"] == "testuser"
            assert "session_info" in result


class TestRateLimiter:
    """Test the RateLimiter class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.rate_limiter = RateLimiter()

    def test_rate_limit_within_limits(self):
        """Test request within rate limits."""
        client_ip = "***********"
        method = "GET"

        # Make requests within limit
        for i in range(5):
            result = self.rate_limiter.check_rate_limit(client_ip, method)
            assert result is True

    def test_rate_limit_exceeded(self):
        """Test request exceeding rate limits."""
        client_ip = "***********"
        method = "POST"

        # Make requests up to limit (10 for POST)
        for i in range(10):
            result = self.rate_limiter.check_rate_limit(client_ip, method)
            assert result is True

        # Next request should be rate limited
        result = self.rate_limiter.check_rate_limit(client_ip, method)
        assert result is False

    def test_rate_limit_different_methods(self):
        """Test rate limiting for different HTTP methods."""
        client_ip = "***********"

        # Different methods have different limits
        # GET: 60, POST: 10, PUT: 10, DELETE: 5

        # Test DELETE limit (5)
        for i in range(5):
            result = self.rate_limiter.check_rate_limit(client_ip, "DELETE")
            assert result is True

        # Sixth DELETE should be blocked
        result = self.rate_limiter.check_rate_limit(client_ip, "DELETE")
        assert result is False

        # But GET should still work (different counter)
        result = self.rate_limiter.check_rate_limit(client_ip, "GET")
        assert result is True

    def test_rate_limit_cleanup(self):
        """Test cleanup of old rate limit entries."""
        client_ip = "***********"
        method = "POST"

        # Make some requests
        for i in range(5):
            self.rate_limiter.check_rate_limit(client_ip, method)

        # Simulate time passing (beyond window)
        with patch("time.time", return_value=time.time() + 120):  # 2 minutes later
            # This should clean up old entries
            result = self.rate_limiter.check_rate_limit(client_ip, method)
            assert result is True

            # Should have cleaned up the old entries
            key = f"{client_ip}:{method}"
            assert (
                len(self.rate_limiter.request_counts[key]) == 1
            )  # Only the new request


class TestCSRFProtection:
    """Test the CSRFProtection class."""

    def setup_method(self):
        """Set up test fixtures."""
        with patch.dict("os.environ", {"CSRF_SECRET_KEY": "test-secret-key"}):
            self.csrf_protection = CSRFProtection()

    def test_generate_csrf_token(self):
        """Test CSRF token generation."""
        user_id = "user-123"

        token = self.csrf_protection.generate_csrf_token(user_id)

        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 0

    def test_validate_csrf_token_valid(self):
        """Test validation of valid CSRF token."""
        user_id = "user-123"

        # Generate a token
        token = self.csrf_protection.generate_csrf_token(user_id)

        # Create event with the token
        event = {
            "path": "/api/data",
            "httpMethod": "POST",
            "headers": {"X-CSRF-Token": token},
        }

        result = self.csrf_protection.validate_csrf_token(event)
        assert result is True

    def test_validate_csrf_token_missing(self):
        """Test validation with missing CSRF token."""
        event = {"path": "/api/data", "httpMethod": "POST", "headers": {}}

        result = self.csrf_protection.validate_csrf_token(event)
        assert result is False

    def test_validate_csrf_token_invalid(self):
        """Test validation with invalid CSRF token."""
        event = {
            "path": "/api/data",
            "httpMethod": "POST",
            "headers": {"X-CSRF-Token": "invalid-token"},
        }

        result = self.csrf_protection.validate_csrf_token(event)
        assert result is False

    def test_validate_csrf_token_expired(self):
        """Test validation with expired CSRF token."""
        user_id = "user-123"

        # Generate token in the past
        with patch("time.time", return_value=time.time() - 7200):  # 2 hours ago
            token = self.csrf_protection.generate_csrf_token(user_id)

        # Try to validate now
        event = {
            "path": "/api/data",
            "httpMethod": "POST",
            "headers": {"X-CSRF-Token": token},
        }

        result = self.csrf_protection.validate_csrf_token(event)
        assert result is False

    def test_validate_csrf_token_auth_endpoint_bypass(self):
        """Test that auth endpoints bypass CSRF validation."""
        event = {
            "path": "/auth/login",
            "httpMethod": "POST",
            "headers": {},  # No CSRF token
        }

        result = self.csrf_protection.validate_csrf_token(event)
        assert result is True  # Should bypass for auth endpoints


class TestSecurityEnforcer:
    """Test the SecurityEnforcer class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.security_enforcer = SecurityEnforcer()

    def test_get_client_ip_forwarded_for(self):
        """Test extracting client IP from X-Forwarded-For header."""
        event = {"headers": {"X-Forwarded-For": "***********00, ********"}}

        ip = self.security_enforcer._get_client_ip(event)
        assert ip == "***********00"  # Should get first IP

    def test_get_client_ip_real_ip(self):
        """Test extracting client IP from X-Real-IP header."""
        event = {"headers": {"X-Real-IP": "***********00"}}

        ip = self.security_enforcer._get_client_ip(event)
        assert ip == "***********00"

    def test_get_client_ip_source_ip(self):
        """Test extracting client IP from request context."""
        event = {
            "headers": {},
            "requestContext": {"identity": {"sourceIp": "***********00"}},
        }

        ip = self.security_enforcer._get_client_ip(event)
        assert ip == "***********00"

    def test_get_client_ip_unknown(self):
        """Test fallback when no IP can be determined."""
        event = {"headers": {}, "requestContext": {}}

        ip = self.security_enforcer._get_client_ip(event)
        assert ip == "unknown"

    def test_sanitize_input_xss_prevention(self):
        """Test input sanitization for XSS prevention."""
        event = {
            "body": json.dumps(
                {
                    "name": 'John<script>alert("xss")</script>',
                    "comment": "This is a normal comment",
                    "data": {"nested": 'javascript:alert("nested")'},
                }
            )
        }

        sanitized_event = self.security_enforcer._sanitize_input(event)
        sanitized_body = json.loads(sanitized_event["body"])

        # Dangerous content should be removed
        assert "<script>" not in sanitized_body["name"]
        assert "javascript:" not in sanitized_body["data"]["nested"]
        # Normal content should remain
        assert sanitized_body["comment"] == "This is a normal comment"

    def test_validate_request_security_success(self):
        """Test successful security validation."""
        event = {
            "httpMethod": "GET",
            "path": "/api/data",
            "headers": {"X-Forwarded-For": "***********"},
            "body": json.dumps({"data": "clean content"}),
        }

        result = self.security_enforcer.validate_request_security(event)

        assert result["rate_limit_passed"] is True
        assert result["csrf_valid"] is True
        assert result["input_sanitized"] is True
        assert result["security_headers_valid"] is True

    def test_validate_request_security_rate_limit_exceeded(self):
        """Test security validation with rate limit exceeded."""
        event = {
            "httpMethod": "POST",
            "path": "/api/data",
            "headers": {"X-Forwarded-For": "***********"},
        }

        # Exhaust rate limit
        for i in range(10):
            self.security_enforcer.rate_limiter.check_rate_limit("***********", "POST")

        result = self.security_enforcer.validate_request_security(event)

        assert result["rate_limit_passed"] is False

    def test_recursive_sanitize_nested_data(self):
        """Test recursive sanitization of nested data structures."""
        data = {
            "level1": {
                "level2": ['<script>alert("test")</script>', "normal text"],
                "clean": "this is fine",
            },
            "list": ["item1", "<img src=x onerror=alert(1)>", "item3"],
        }

        sanitized = self.security_enforcer._recursive_sanitize(data)

        # Scripts should be removed
        assert "<script>" not in str(sanitized)
        assert "onerror=" not in str(sanitized)
        # Clean content should remain
        assert sanitized["level1"]["clean"] == "this is fine"
        assert sanitized["list"][0] == "item1"
        assert sanitized["list"][2] == "item3"


class TestSecurityIntegration:
    """Integration tests for security features."""

    def test_full_security_stack(self):
        """Test the complete security validation stack."""
        # This would test the integration of all security components
        # in a realistic scenario
        pass

    def test_session_timeout_handling(self):
        """Test complete session timeout handling flow."""
        # This would test the full session management lifecycle
        pass

    def test_brute_force_protection(self):
        """Test protection against brute force attacks."""
        # This would test rate limiting combined with other protections
        pass
