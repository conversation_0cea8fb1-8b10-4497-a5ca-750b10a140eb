"""
Test Lambda function for DynamoDB connection and CRUD operations.
This function can be used to validate the database setup.
"""

import json
import os
from typing import Dict, Any
from datetime import datetime, timezone
from decimal import Decimal

from aws_lambda_powertools import Logger, Tracer
from aws_lambda_powertools.utilities.typing import LambdaContext

from src.shared.database import get_database, get_fund_repository
from src.shared.models.fund import Fund, FundType, FundStatus, Currency, RiskLevel

# Initialize AWS Lambda Powertools
logger = Logger()
tracer = Tracer()


@logger.inject_lambda_context(log_event=True)
@tracer.capture_lambda_handler
def handler(event: Dict[str, Any], context: LambdaContext) -> Dict[str, Any]:
    """
    Test Lambda handler for database operations.

    Args:
        event: Lambda event (should contain 'action' parameter)
        context: Lambda context

    Returns:
        API Gateway response with test results
    """

    try:
        action = event.get("action", "health_check")

        if action == "health_check":
            return handle_health_check()
        elif action == "test_crud":
            return handle_test_crud()
        elif action == "test_query":
            return handle_test_query()
        else:
            return {
                "statusCode": 400,
                "body": json.dumps(
                    {
                        "error": f"Unknown action: {action}",
                        "available_actions": [
                            "health_check",
                            "test_crud",
                            "test_query",
                        ],
                    }
                ),
                "headers": {"Content-Type": "application/json"},
            }

    except Exception as e:
        logger.exception("Error in database test handler")
        return {
            "statusCode": 500,
            "body": json.dumps({"error": "Internal server error", "message": str(e)}),
            "headers": {"Content-Type": "application/json"},
        }


@tracer.capture_method
def handle_health_check() -> Dict[str, Any]:
    """Test database health check."""
    try:
        db = get_database()
        health_status = db.health_check()

        status_code = 200 if health_status["database"] == "healthy" else 503

        return {
            "statusCode": status_code,
            "body": json.dumps(
                {
                    "message": "Database health check completed",
                    "health_status": health_status,
                }
            ),
            "headers": {"Content-Type": "application/json"},
        }

    except Exception as e:
        logger.exception("Error in health check")
        return {
            "statusCode": 500,
            "body": json.dumps({"error": "Health check failed", "message": str(e)}),
            "headers": {"Content-Type": "application/json"},
        }


@tracer.capture_method
def handle_test_crud() -> Dict[str, Any]:
    """Test CRUD operations on the Fund table."""
    try:
        fund_repo = get_fund_repository()
        test_fund_id = f"TEST-FUND-{int(datetime.now(timezone.utc).timestamp())}"

        results = {"test_fund_id": test_fund_id, "operations": {}}

        # Test CREATE
        try:
            test_fund = Fund(
                fund_id=test_fund_id,
                name=f"Test Fund {test_fund_id}",
                fund_type=FundType.EQUITY,
                status=FundStatus.ACTIVE,
                currency=Currency.USD,
                nav=Decimal("100.50"),
                risk_level=RiskLevel.MEDIUM,
                fund_manager="Test Manager",
                description="Test fund for database validation",
            )

            created_fund = fund_repo.create(test_fund)
            results["operations"]["create"] = {
                "success": True,
                "fund_id": created_fund.fund_id,
                "message": "Fund created successfully",
            }

        except Exception as e:
            results["operations"]["create"] = {"success": False, "error": str(e)}

        # Test READ
        try:
            retrieved_fund = fund_repo.get_by_fund_id(test_fund_id)
            if retrieved_fund:
                results["operations"]["read"] = {
                    "success": True,
                    "fund_name": retrieved_fund.name,
                    "fund_type": retrieved_fund.fund_type.value,
                    "message": "Fund retrieved successfully",
                }
            else:
                results["operations"]["read"] = {
                    "success": False,
                    "error": "Fund not found after creation",
                }

        except Exception as e:
            results["operations"]["read"] = {"success": False, "error": str(e)}

        # Test UPDATE
        try:
            if retrieved_fund:
                retrieved_fund.nav = Decimal("105.75")
                retrieved_fund.description = "Updated test fund"
                updated_fund = fund_repo.update(retrieved_fund)

                results["operations"]["update"] = {
                    "success": True,
                    "new_nav": str(updated_fund.nav),
                    "message": "Fund updated successfully",
                }
            else:
                results["operations"]["update"] = {
                    "success": False,
                    "error": "Cannot update - fund not found",
                }

        except Exception as e:
            results["operations"]["update"] = {"success": False, "error": str(e)}

        # Test DELETE
        try:
            deleted = fund_repo.delete({"fund_id": test_fund_id})
            results["operations"]["delete"] = {
                "success": deleted,
                "message": (
                    "Fund deleted successfully"
                    if deleted
                    else "Fund not found for deletion"
                ),
            }

        except Exception as e:
            results["operations"]["delete"] = {"success": False, "error": str(e)}

        # Summary
        successful_ops = sum(
            1 for op in results["operations"].values() if op.get("success", False)
        )
        total_ops = len(results["operations"])

        return {
            "statusCode": 200,
            "body": json.dumps(
                {
                    "message": f"CRUD test completed: {successful_ops}/{total_ops} operations successful",
                    "results": results,
                }
            ),
            "headers": {"Content-Type": "application/json"},
        }

    except Exception as e:
        logger.exception("Error in CRUD test")
        return {
            "statusCode": 500,
            "body": json.dumps({"error": "CRUD test failed", "message": str(e)}),
            "headers": {"Content-Type": "application/json"},
        }


@tracer.capture_method
def handle_test_query() -> Dict[str, Any]:
    """Test query operations."""
    try:
        fund_repo = get_fund_repository()

        results = {"queries": {}}

        # Test list all funds
        try:
            all_funds = fund_repo.list_all(limit=5)
            results["queries"]["list_all"] = {
                "success": True,
                "count": all_funds["count"],
                "scanned_count": all_funds["scanned_count"],
                "message": f"Retrieved {all_funds['count']} funds",
            }

        except Exception as e:
            results["queries"]["list_all"] = {"success": False, "error": str(e)}

        # Test get active funds
        try:
            active_funds = fund_repo.get_active_funds(limit=5)
            results["queries"]["active_funds"] = {
                "success": True,
                "count": active_funds["count"],
                "message": f"Retrieved {active_funds['count']} active funds",
            }

        except Exception as e:
            results["queries"]["active_funds"] = {"success": False, "error": str(e)}

        # Test search by name (if there are funds)
        try:
            search_results = fund_repo.search_by_name("fund", limit=3)
            results["queries"]["search_by_name"] = {
                "success": True,
                "count": search_results["count"],
                "scanned_count": search_results["scanned_count"],
                "message": f"Search found {search_results['count']} funds",
            }

        except Exception as e:
            results["queries"]["search_by_name"] = {"success": False, "error": str(e)}

        successful_queries = sum(
            1 for q in results["queries"].values() if q.get("success", False)
        )
        total_queries = len(results["queries"])

        return {
            "statusCode": 200,
            "body": json.dumps(
                {
                    "message": f"Query test completed: {successful_queries}/{total_queries} queries successful",
                    "results": results,
                }
            ),
            "headers": {"Content-Type": "application/json"},
        }

    except Exception as e:
        logger.exception("Error in query test")
        return {
            "statusCode": 500,
            "body": json.dumps({"error": "Query test failed", "message": str(e)}),
            "headers": {"Content-Type": "application/json"},
        }
