"""
Test cases for enhanced fund validation service.
Comprehensive tests for server-side validation logic.
"""

import pytest
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from unittest.mock import Mock, patch

from src.shared.validators.fund_validation import (
    FundValidationService,
    ValidationResult,
)
from src.shared.models.requests import FundCreateRequest, FundUpdateRequest
from src.shared.models.fund import (
    Fund,
    FundType,
    FundStatus,
    RiskLevel,
    Currency,
    PerformanceMetrics,
    Holdings,
)


class TestFundValidationService:
    """Test cases for the FundValidationService."""

    def setup_method(self):
        """Setup test fixtures."""
        self.validation_service = FundValidationService()
        self.valid_user_context = {
            "user_id": "test-user-123",
            "role": "FUND_MANAGER",
            "permissions": ["create_fund", "update_fund"],
        }

        self.invalid_user_context = {
            "user_id": "test-user-456",
            "role": "VIEWER",
            "permissions": ["read_fund"],
        }

    def test_valid_fund_creation(self):
        """Test successful fund creation validation."""
        request = FundCreateRequest(
            fund_id="TEST-FUND-001",
            name="Test Growth Fund",
            fund_type=FundType.EQUITY,
            status=FundStatus.ACTIVE,
            nav=Decimal("10.50"),
            currency=Currency.USD,
            risk_level=RiskLevel.MODERATE,
            expense_ratio=Decimal("0.75"),
            minimum_investment=Decimal("1000.00"),
            isin="US1234567890",
            description="A test fund for growth investments",
        )

        result = self.validation_service.validate_fund_creation(
            request, self.valid_user_context
        )

        assert result.is_valid
        assert len(result.errors) == 0

    def test_invalid_fund_creation_permissions(self):
        """Test fund creation with insufficient permissions."""
        request = FundCreateRequest(
            fund_id="TEST-FUND-002", name="Test Fund", fund_type=FundType.EQUITY
        )

        result = self.validation_service.validate_fund_creation(
            request, self.invalid_user_context
        )

        assert not result.is_valid
        assert any("authorization" in error["field"] for error in result.errors)

    def test_invalid_fund_id_format(self):
        """Test validation of invalid fund ID format."""
        request = FundCreateRequest(
            fund_id="invalid id with spaces!",
            name="Test Fund",
            fund_type=FundType.EQUITY,
        )

        result = self.validation_service.validate_fund_creation(
            request, self.valid_user_context
        )

        assert not result.is_valid
        assert any("fund_id" in error["field"] for error in result.errors)

    def test_fund_name_validation(self):
        """Test fund name validation rules."""
        # Test too short name
        request = FundCreateRequest(
            fund_id="TEST-001", name="AB", fund_type=FundType.EQUITY  # Too short
        )

        result = self.validation_service.validate_fund_creation(
            request, self.valid_user_context
        )

        assert not result.is_valid
        assert any("name" in error["field"] for error in result.errors)

        # Test too long name
        request.name = "A" * 201  # Too long
        result = self.validation_service.validate_fund_creation(
            request, self.valid_user_context
        )

        assert not result.is_valid
        assert any("name" in error["field"] for error in result.errors)

    def test_nav_validation(self):
        """Test NAV validation rules."""
        request = FundCreateRequest(
            fund_id="TEST-001",
            name="Test Fund",
            fund_type=FundType.EQUITY,
            nav=Decimal("-5.00"),  # Negative NAV
        )

        result = self.validation_service.validate_fund_creation(
            request, self.valid_user_context
        )

        assert not result.is_valid
        assert any("nav" in error["field"] for error in result.errors)

    def test_expense_ratio_validation(self):
        """Test expense ratio validation rules."""
        request = FundCreateRequest(
            fund_id="TEST-001",
            name="Test Fund",
            fund_type=FundType.EQUITY,
            expense_ratio=Decimal("6.00"),  # Very high expense ratio
        )

        result = self.validation_service.validate_fund_creation(
            request, self.valid_user_context
        )

        # Should be valid but with warnings
        assert result.is_valid
        assert any("expense_ratio" in warning["field"] for warning in result.warnings)

    def test_isin_validation(self):
        """Test ISIN code validation."""
        request = FundCreateRequest(
            fund_id="TEST-001",
            name="Test Fund",
            fund_type=FundType.EQUITY,
            isin="INVALID-ISIN",  # Invalid ISIN format
        )

        result = self.validation_service.validate_fund_creation(
            request, self.valid_user_context
        )

        assert not result.is_valid
        assert any("isin" in error["field"] for error in result.errors)

    def test_fund_type_risk_consistency(self):
        """Test consistency between fund type and risk level."""
        request = FundCreateRequest(
            fund_id="TEST-001",
            name="Test Money Market Fund",
            fund_type=FundType.MONEY_MARKET,
            risk_level=RiskLevel.VERY_HIGH,  # Inconsistent with money market
        )

        result = self.validation_service.validate_fund_creation(
            request, self.valid_user_context
        )

        # Should be valid but with warnings about inconsistency
        assert result.is_valid
        assert any("risk_level" in warning["field"] for warning in result.warnings)

    def test_inception_date_validation(self):
        """Test inception date validation."""
        future_date = datetime.now(timezone.utc) + timedelta(days=30)
        request = FundCreateRequest(
            fund_id="TEST-001",
            name="Test Fund",
            fund_type=FundType.EQUITY,
            inception_date=future_date,  # Future date
        )

        result = self.validation_service.validate_fund_creation(
            request, self.valid_user_context
        )

        assert not result.is_valid
        assert any("inception_date" in error["field"] for error in result.errors)

    def test_performance_metrics_validation(self):
        """Test performance metrics validation."""
        invalid_metrics = PerformanceMetrics(
            ytd_return=Decimal("-150.00"),  # Invalid return below -100%
            volatility=Decimal("-5.00"),  # Negative volatility
            sharpe_ratio=Decimal("10.00"),  # Unusually high Sharpe ratio
        )

        request = FundCreateRequest(
            fund_id="TEST-001",
            name="Test Fund",
            fund_type=FundType.EQUITY,
            performance_metrics=invalid_metrics,
        )

        result = self.validation_service.validate_fund_creation(
            request, self.valid_user_context
        )

        assert not result.is_valid
        # Should have errors for invalid returns and negative volatility
        assert any("ytd_return" in error["field"] for error in result.errors)
        assert any("volatility" in error["field"] for error in result.errors)

    def test_holdings_allocation_validation(self):
        """Test holdings allocation validation."""
        invalid_holdings = Holdings(
            sector_allocation={
                "Technology": Decimal("60.0"),
                "Healthcare": Decimal("30.0"),
                "Finance": Decimal("20.0"),  # Total = 110%, should be 100%
            }
        )

        request = FundCreateRequest(
            fund_id="TEST-001",
            name="Test Fund",
            fund_type=FundType.EQUITY,
            holdings=invalid_holdings,
        )

        result = self.validation_service.validate_fund_creation(
            request, self.valid_user_context
        )

        assert not result.is_valid
        assert any("sector_allocation" in error["field"] for error in result.errors)

    def test_fund_update_validation(self):
        """Test fund update validation."""
        existing_fund = Fund(
            fund_id="TEST-001",
            name="Existing Fund",
            fund_type=FundType.EQUITY,
            status=FundStatus.ACTIVE,
            nav=Decimal("10.00"),
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )

        update_request = FundUpdateRequest(
            name="Updated Fund Name", nav=Decimal("15.00")  # 50% increase
        )

        result = self.validation_service.validate_fund_update(
            update_request, existing_fund, self.valid_user_context
        )

        # Should be valid but with warnings about large NAV change
        assert result.is_valid
        assert any("nav" in warning["field"] for warning in result.warnings)

    def test_status_transition_validation(self):
        """Test fund status transition validation."""
        existing_fund = Fund(
            fund_id="TEST-001",
            name="Test Fund",
            fund_type=FundType.EQUITY,
            status=FundStatus.CLOSED,  # Closed status
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )

        update_request = FundUpdateRequest(
            status=FundStatus.ACTIVE  # Cannot transition from CLOSED to ACTIVE
        )

        result = self.validation_service.validate_fund_update(
            update_request, existing_fund, self.valid_user_context
        )

        assert not result.is_valid
        assert any("status" in error["field"] for error in result.errors)

    def test_immutable_fields_validation(self):
        """Test that immutable fields cannot be changed."""
        existing_fund = Fund(
            fund_id="TEST-001",
            name="Test Fund",
            fund_type=FundType.EQUITY,
            status=FundStatus.ACTIVE,
            inception_date=datetime(2020, 1, 1),
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )

        update_request = FundUpdateRequest(
            inception_date=datetime(2021, 1, 1)  # Trying to change inception date
        )

        result = self.validation_service.validate_fund_update(
            update_request, existing_fund, self.valid_user_context
        )

        assert not result.is_valid
        assert any("inception_date" in error["field"] for error in result.errors)

    def test_bulk_validation_limits(self):
        """Test bulk operation validation limits."""
        # Test too many updates
        updates = [{"fund_id": f"FUND-{i:03d}", "data": {}} for i in range(150)]

        results = self.validation_service.validate_bulk_fund_updates(
            updates, self.valid_user_context
        )

        # Should have warnings about bulk operation limits
        assert len(results) == 150
        assert all(
            not result.is_valid or result.warnings for result in results.values()
        )

    def test_isin_check_digit_validation(self):
        """Test ISIN check digit validation algorithm."""
        # Valid ISIN (Apple Inc.)
        valid_isin = "US0378331005"
        assert self.validation_service._validate_isin_check_digit(valid_isin)

        # Invalid ISIN (wrong check digit)
        invalid_isin = "US0378331006"
        assert not self.validation_service._validate_isin_check_digit(invalid_isin)

    def test_cusip_check_digit_validation(self):
        """Test CUSIP check digit validation algorithm."""
        # Valid CUSIP (Apple Inc.)
        valid_cusip = "037833100"
        assert self.validation_service._validate_cusip_check_digit(valid_cusip)

        # Invalid CUSIP (wrong check digit)
        invalid_cusip = "037833101"
        assert not self.validation_service._validate_cusip_check_digit(invalid_cusip)

    def test_fund_edit_permissions(self):
        """Test fund-specific edit permissions."""
        # Test editing closed fund with insufficient permissions
        closed_fund = Fund(
            fund_id="TEST-001",
            name="Closed Fund",
            fund_type=FundType.EQUITY,
            status=FundStatus.CLOSED,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )

        update_request = FundUpdateRequest(name="New Name")

        result = self.validation_service.validate_fund_update(
            update_request, closed_fund, self.invalid_user_context
        )

        assert not result.is_valid
        assert any("fund_status" in error["field"] for error in result.errors)

    def test_comprehensive_validation_workflow(self):
        """Test a comprehensive validation workflow."""
        # Create a fund with multiple validation issues
        request = FundCreateRequest(
            fund_id="test fund invalid!",  # Invalid ID
            name="AB",  # Too short name
            fund_type=FundType.MONEY_MARKET,
            risk_level=RiskLevel.VERY_HIGH,  # Inconsistent with fund type
            nav=Decimal("-5.00"),  # Negative NAV
            expense_ratio=Decimal("8.00"),  # Very high expense ratio
            isin="INVALID-ISIN",  # Invalid ISIN
            inception_date=datetime.now(timezone.utc) + timedelta(days=30),  # Future date
        )

        result = self.validation_service.validate_fund_creation(
            request, self.valid_user_context
        )

        # Should have multiple validation errors
        assert not result.is_valid
        assert len(result.errors) >= 4  # At least 4 errors expected
        assert len(result.warnings) >= 1  # At least 1 warning expected

        # Verify specific error fields
        error_fields = [error["field"] for error in result.errors]
        assert "fund_id" in error_fields
        assert "name" in error_fields
        assert "nav" in error_fields
        assert "isin" in error_fields
        assert "inception_date" in error_fields


# Integration test for API handler
class TestFundValidationIntegration:
    """Integration tests for fund validation in API handlers."""

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    def test_create_fund_with_validation_errors(
        self, mock_session_manager, mock_fund_repo
    ):
        """Test create fund API with validation errors."""
        from src.functions.api.funds import handle_create_fund

        # Mock session validation
        mock_session_manager.return_value.validate_session.return_value = {
            "valid": True,
            "user_info": {"user_id": "test-user", "role": "FUND_MANAGER"},
        }

        # Mock fund repository
        mock_fund_repo.return_value.get_by_fund_id.return_value = None

        # Create event with invalid fund data
        event = {
            "httpMethod": "POST",
            "path": "/api/funds",
            "body": '{"fund_id": "invalid id!", "name": "AB", "fund_type": "equity", "nav": -5.0}',
        }

        response = handle_create_fund(event)

        # Should return validation error
        assert response["statusCode"] == 400
        assert "validation" in response["body"].lower()

    @patch("src.functions.api.funds.get_fund_repository")
    @patch("src.functions.api.funds.SessionManager")
    def test_update_fund_with_validation_warnings(
        self, mock_session_manager, mock_fund_repo
    ):
        """Test update fund API with validation warnings."""
        from src.functions.api.funds import handle_update_fund

        # Mock session validation
        mock_session_manager.return_value.validate_session.return_value = {
            "valid": True,
            "user_info": {"user_id": "test-user", "role": "FUND_MANAGER"},
        }

        # Mock existing fund
        existing_fund = Fund(
            fund_id="TEST-001",
            name="Test Fund",
            fund_type=FundType.EQUITY,
            status=FundStatus.ACTIVE,
            nav=Decimal("10.00"),
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )

        mock_fund_repo.return_value.get_by_fund_id.return_value = existing_fund
        mock_fund_repo.return_value.update.return_value = existing_fund

        # Create event with significant NAV change
        event = {
            "httpMethod": "PUT",
            "path": "/api/funds/TEST-001",
            "body": '{"nav": 20.0}',  # 100% increase
        }

        response = handle_update_fund(event)

        # Should return success with warnings
        assert response["statusCode"] == 200
        body = response["body"]
        assert "validation_warnings" in body
