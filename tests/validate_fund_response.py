#!/usr/bin/env python3
"""
Fund Response Validation Script
Tests the fund API endpoint to verify the 500 error fix.
"""

import json
import requests
import sys
from datetime import datetime

def test_fund_api():
    """Test the fund API endpoint to check if the 500 error is fixed."""
    
    # API endpoint
    api_url = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
    
    print("🔧 Testing Fund API Response Validation")
    print("=" * 50)
    print(f"API Base URL: {api_url}")
    print(f"Test Time: {datetime.now().isoformat()}")
    print()
    
    # Test 1: Health check
    print("📊 Test 1: Health Check")
    try:
        response = requests.get(f"{api_url}/health", timeout=10)
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text}")
        if response.status_code == 200:
            print("   ✅ Health check passed")
        else:
            print("   ❌ Health check failed")
    except Exception as e:
        print(f"   ❌ Health check error: {e}")
    
    print()
    
    # Test 2: Fund list endpoint without auth (expect 401)
    print("📊 Test 2: Fund List Endpoint (No Auth)")
    try:
        response = requests.get(f"{api_url}/funds?page_size=1", timeout=10)
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text}")
        if response.status_code == 401:
            print("   ✅ Expected 401 Unauthorized")
        elif response.status_code == 500:
            print("   ❌ Still getting 500 error - validation fix may not have worked")
        else:
            print(f"   ⚠️  Unexpected status code: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Request error: {e}")
    
    print()
    
    # Test 3: Fund list endpoint with mock auth
    print("📊 Test 3: Fund List Endpoint (Mock Auth)")
    headers = {
        "Authorization": "Bearer mock-token-for-testing",
        "Content-Type": "application/json"
    }
    try:
        response = requests.get(f"{api_url}/funds?page_size=1", headers=headers, timeout=10)
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text[:500]}...")  # Truncate long responses
        
        if response.status_code == 200:
            print("   ✅ Fund API working correctly")
            try:
                data = response.json()
                if 'funds' in data:
                    print(f"   📈 Found {len(data['funds'])} funds")
                else:
                    print("   📊 Response structure may have changed")
            except json.JSONDecodeError:
                print("   ⚠️  Response is not valid JSON")
        elif response.status_code == 401:
            print("   ✅ Expected 401 Unauthorized (auth working)")
        elif response.status_code == 500:
            print("   ❌ Still getting 500 error - validation fix did not work")
            print("   🔍 Check CloudWatch logs for more details")
        else:
            print(f"   ⚠️  Unexpected status code: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Request error: {e}")
    
    print()
    print("🎯 Summary:")
    print("   - If you see 401 errors, authentication is working correctly")
    print("   - If you see 500 errors, the validation fix needs more work")
    print("   - If you see 200 responses, the API is fully functional")
    print()

if __name__ == "__main__":
    test_fund_api()